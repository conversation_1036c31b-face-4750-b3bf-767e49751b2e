{"name": "JBooth.MicroSplat.Core.Editor", "references": ["GUID:4bdfb2239705740718731d0b4d54061d"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["Substance.Game.dll"], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.modules.ui", "expression": "", "define": "__MICROSPLAT__"}, {"name": "com.unity.render-pipelines.universal", "expression": "", "define": "USING_URP"}, {"name": "com.unity.render-pipelines.high-definition", "expression": "", "define": "USING_HDRP"}], "noEngineReferences": false}