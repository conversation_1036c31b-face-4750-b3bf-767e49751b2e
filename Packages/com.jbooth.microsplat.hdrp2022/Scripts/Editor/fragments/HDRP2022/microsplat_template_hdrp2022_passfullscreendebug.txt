        Pass
        {
            Name "FullScreenDebug"
            Tags
            {
               "LightMode" = "FullScreenDebug"
            }
    
            // Render State
            Cull [_CullMode]
            ZTest LEqual
            ZWrite Off
            
            //-------------------------------------------------------------------------------------
            // End Render Modes
            //-------------------------------------------------------------------------------------
        
            HLSLPROGRAM
        
            #pragma target %SHADERTARGET%
            #pragma only_renderers d3d11 playstation xboxone xboxseries vulkan metal switch
            #pragma multi_compile_local _ _ALPHATEST_ON


            #pragma shader_feature _ _SURFACE_TYPE_TRANSPARENT
            #pragma shader_feature_local _BLENDMODE_OFF _BLENDMODE_ALPHA _BLENDMODE_ADD _BLENDMODE_PRE_MULTIPLY
            #pragma shader_feature_local _ _DOUBLESIDED_ON
            #pragma shader_feature_local _ _ADD_PRECOMPUTED_VELOCITY
            #pragma shader_feature_local _ _TRANSPARENT_WRITES_MOTION_VEC
  
            #pragma shader_feature_local _ _DISABLE_DECALS
            #pragma shader_feature_local _ _DISABLE_SSR
            #pragma shader_feature_local _ _DISABLE_SSR_TRANSPARENT
            #pragma shader_feature_local _REFRACTION_OFF _REFRACTION_PLANE _REFRACTION_SPHERE _REFRACTION_THIN
                
        

            #define SHADERPASS SHADERPASS_FULL_SCREEN_DEBUG

            
            %DEFINES%

            %PRAGMAS%

            %HDRPINCLUDE%

            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Debug/DebugDisplay.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Material.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/NormalSurfaceGradient.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Lit/Lit.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/BuiltinUtilities.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/MaterialUtilities.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/ShaderLibrary/ShaderGraphFunctions.hlsl"
    

            %TEMPLATE_SHARED%

            %CUSTOMCBUFFER%

            %CUSTOMINSTANCEPROPS%

            %CODE%

            %SHADERDESC%

            %VERT%

            %TESSELLATION%

            %HDRPSHARED%



#define DEBUG_DISPLAY
#include "Packages/com.unity.render-pipelines.high-definition/Runtime/Debug/DebugDisplay.hlsl"
#include "Packages/com.unity.render-pipelines.high-definition/Runtime/Debug/FullScreenDebug.hlsl"

         #if !defined(_DEPTHOFFSET_ON)
         [earlydepthstencil] // quad overshading debug mode writes to UAV
         #endif
         void Frag(VertexToPixel v2f)
         {
             UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(v2f);
             FragInputs input = BuildFragInputs(v2f);

             PositionInputs posInput = GetPositionInput(input.positionSS.xy, _ScreenSize.zw, input.positionSS.z, input.positionSS.w, input.positionRWS.xyz);

         #ifdef PLATFORM_SUPPORTS_PRIMITIVE_ID_IN_PIXEL_SHADER
             if (_DebugFullScreenMode == FULLSCREENDEBUGMODE_QUAD_OVERDRAW)
             {
                 IncrementQuadOverdrawCounter(posInput.positionSS.xy, input.primitiveID);
             }
         #endif
         }

            ENDHLSL
        }
