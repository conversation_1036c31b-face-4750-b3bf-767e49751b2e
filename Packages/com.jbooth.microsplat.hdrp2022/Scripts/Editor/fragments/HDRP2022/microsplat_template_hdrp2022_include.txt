      // useful conversion functions to make surface shader code just work
      
      #ifndef SHADER_STAGE_FRAGMENT
        #if !defined(SHADOW_ULTRA_LOW) && !defined(SHADOW_LOW) && !defined(SHADOW_MEDIUM) && !defined(SHADOW_HIGH) // ultra low come from volumetricLighting.compute
            #define SHADOW_MEDIUM
        #endif
        #if !defined(AREA_SHADOW_LOW) && !defined(AREA_SHADOW_MEDIUM) && !defined(AREA_SHADOW_HIGH) // low come from volumetricLighting.compute
            #define AREA_SHADOW_MEDIUM
        #endif
      #endif

      #define UNITY_DECLARE_TEX2D(name) TEXTURE2D(name); SAMPLER(sampler##name);
      #define UNITY_DECLARE_TEX2D_NOSAMPLER(name) TEXTURE2D(name);
      #define UNITY_DECLARE_TEX2DARRAY(name) TEXTURE2D_ARRAY(name); SAMPLER(sampler##name);
      #define UNITY_DECLARE_TEX2DARRAY_NOSAMPLER(tex) TEXTURE2D_ARRAY(tex);

      #define UNITY_SAMPLE_TEX2DARRAY(tex,coord)            SAMPLE_TEXTURE2D_ARRAY(tex, sampler##tex, coord.xy, coord.z)
      #define UNITY_SAMPLE_TEX2DARRAY_LOD(tex,coord,lod)    SAMPLE_TEXTURE2D_ARRAY_LOD(tex, sampler##tex, coord.xy, coord.z, lod)
      #define UNITY_SAMPLE_TEX2D(tex, coord)                SAMPLE_TEXTURE2D(tex, sampler##tex, coord)
      #define UNITY_SAMPLE_TEX2D_SAMPLER(tex, samp, coord)  SAMPLE_TEXTURE2D(tex, sampler##samp, coord)

      #define UNITY_SAMPLE_TEX2D_LOD(tex,coord, lod)   SAMPLE_TEXTURE2D_LOD(tex, sampler_##tex, coord, lod)
      #define UNITY_SAMPLE_TEX2D_SAMPLER_LOD(tex,samplertex,coord, lod) SAMPLE_TEXTURE2D_LOD (tex, sampler##samplertex,coord, lod)

      #if defined(UNITY_COMPILER_HLSL)
         #define UNITY_INITIALIZE_OUTPUT(type,name) name = (type)0;
      #else
         #define UNITY_INITIALIZE_OUTPUT(type,name)
      #endif

      #define sampler2D_float sampler2D
      #define sampler2D_half sampler2D

      #undef WorldNormalVector
      #define WorldNormalVector(data, normal) mul(normal, float3x3(d.worldSpaceTangent, cross(d.worldSpaceTangent, d.worldSpaceNormal), d.worldSpaceNormal))


      #define UnityObjectToWorldNormal(normal) mul(GetObjectToWorldMatrix(), normal)



// HDRP Adapter stuff
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/ShaderLibrary/ShaderVariables.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/RenderPipeline/ShaderPass/FragInputs.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/RenderPipeline/ShaderPass/ShaderPass.cs.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/TextureStack.hlsl" // Required to be include before we include properties as it define DECLARE_STACK_CB
           // fuck you unity, LTS doesn't mean shit to your graphics team, they break anything, anytime, and don't care.
#if UNITY_VERSION >= 202239
        #include "Packages/com.unity.shadergraph/ShaderGraphLibrary/Functions.hlsl" // Need to be here for Gradient struct definition
#else
        #include "Packages/com.unity.render-pipelines.high-definition/Runtime/ShaderLibrary/ShaderGraphHeader.hlsl" 
#endif  
            #ifdef RAYTRACING_SHADER_GRAPH_DEFAULT 
            #define RAYTRACING_SHADER_GRAPH_HIGH
            #endif
    
            #ifdef RAYTRACING_SHADER_GRAPH_RAYTRACED
            #define RAYTRACING_SHADER_GRAPH_LOW
            #endif
            // end
    



            // If we use subsurface scattering, enable output split lighting (for forward pass)
            #if defined(_MATERIAL_FEATURE_SUBSURFACE_SCATTERING) && !defined(_SURFACE_TYPE_TRANSPARENT)
               #define OUTPUT_SPLIT_LIGHTING
            #endif

            #define HAVE_RECURSIVE_RENDERING

            #if SHADERPASS == SHADERPASS_TRANSPARENT_DEPTH_PREPASS
               #if !defined(_DISABLE_SSR_TRANSPARENT) && !defined(SHADER_UNLIT)
                  #define WRITE_NORMAL_BUFFER
               #endif
            #endif

            #ifndef DEBUG_DISPLAY
               // In case of opaque we don't want to perform the alpha test, it is done in depth prepass and we use depth equal for ztest (setup from UI)
               // Don't do it with debug display mode as it is possible there is no depth prepass in this case
               #if !defined(_SURFACE_TYPE_TRANSPARENT) && defined(_ALPHATEST)
                  #if SHADERPASS == SHADERPASS_FORWARD
                  #define SHADERPASS_FORWARD_BYPASS_ALPHA_TEST
                  #elif SHADERPASS == SHADERPASS_GBUFFER
                  #define SHADERPASS_GBUFFER_BYPASS_ALPHA_TEST
                  #endif
               #endif
            #endif
    
            // Translate transparent motion vector define
            #if defined(_TRANSPARENT_WRITES_MOTION_VEC) && defined(_SURFACE_TYPE_TRANSPARENT)
               #define _WRITE_TRANSPARENT_MOTION_VECTOR
            #endif



            // We need isFontFace when using double sided
            #if defined(_DOUBLESIDED_ON) && !defined(VARYINGS_NEED_CULLFACE)
               #define VARYINGS_NEED_CULLFACE
            #endif


            CBUFFER_START(UnityPerMaterial)
               float _UseShadowThreshold;
               float4 _DoubleSidedConstants;
               float _BlendMode;
               float _EnableBlendModePreserveSpecularLighting;
               float _RayTracing;
               float _RefractionModel;

              %CBUFFER%

            CBUFFER_END
    


             // -- Property used by ScenePickingPass
               #ifdef SCENEPICKINGPASS
               float4 _SelectionID;
               #endif
    
               // -- Properties used by SceneSelectionPass
               #ifdef SCENESELECTIONPASS
               int _ObjectId;
               int _PassValue;
               #endif
  
           
            // data across stages, stripped like the above.
            struct VertexToPixel
            {
               float4 pos : SV_POSITION;
               float3 worldPos : TEXCOORD0;
               float3 worldNormal : TEXCOORD1;
               float4 worldTangent : TEXCOORD2;
               float4 texcoord0 : TEXCCOORD3;
               #if !_MICROTERRAIN || _TERRAINBLENDABLESHADER
               float4 texcoord1 : TEXCCOORD4;
               float4 texcoord2 : TEXCCOORD5;
               #endif
               %V2FUV3% float4 texcoord3 : TEXCCOORD6;
               %SCREENPOS% float4 screenPos : TEXCOORD7;
               %V2FVERTEXCOLOR% float4 vertexColor : COLOR;

               %EXTRAV2F0% float4 extraV2F0 : TEXCOORD8;
               %EXTRAV2F1% float4 extraV2F1 : TEXCOORD9;
               %EXTRAV2F2% float4 extraV2F2 : TEXCOORD10;
               %EXTRAV2F3% float4 extraV2F3 : TEXCOORD11;
               %EXTRAV2F4% float4 extraV2F4 : TEXCOORD12;
               %EXTRAV2F5% float4 extraV2F5 : TEXCOORD13;
               %EXTRAV2F6% float4 extraV2F6 : TEXCOORD14;
               %EXTRAV2F7% float4 extraV2F7 : TEXCOORD15;

               #if UNITY_ANY_INSTANCING_ENABLED
                  UNITY_VERTEX_INPUT_INSTANCE_ID
               #endif // UNITY_ANY_INSTANCING_ENABLED

               #if _HDRP && (_PASSMOTIONVECTOR || (_PASSFORWARD && defined(_WRITE_TRANSPARENT_MOTION_VECTOR)))
                  float4 previousPositionCS : TEXCOORD16; // Contain previous transform position (in case of skinning for example)
                  float4 motionVectorCS : TEXCOORD17;
               #endif

               UNITY_VERTEX_OUTPUT_STEREO
            };
