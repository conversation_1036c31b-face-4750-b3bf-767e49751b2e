        Pass
        {
            Name "MotionVectors"
            Tags
            {
               "LightMode" = "MotionVectors"
            }
    
            // Render State
            Cull [_CullMode]
            ZWrite On
            Stencil
               {
                  WriteMask [_StencilWriteMaskMV]
                  Ref [_StencilRefMV]
                  CompFront Always
                  PassFront Replace
                  CompBack Always
                  PassBack Replace
               }

            %PASSMOTION%
            
            //-------------------------------------------------------------------------------------
            // End Render Modes
            //-------------------------------------------------------------------------------------
        
            HLSLPROGRAM
        
            #pragma target %SHADERTARGET%
            #pragma only_renderers d3d11 playstation xboxone xboxseries vulkan metal switch
            #pragma multi_compile_instancing
            //#pragma instancing_options renderinglayer// breaks terrain instancing
            #pragma multi_compile_local _ _ALPHATEST_ON

            #pragma multi_compile _ WRITE_MSAA_DEPTH
            #pragma shader_feature _ _SURFACE_TYPE_TRANSPARENT
            #pragma shader_feature_local _BLENDMODE_OFF _BLENDMODE_ALPHA _BLENDMODE_ADD _BLENDMODE_PRE_MULTIPLY
            #pragma shader_feature_local _ _DOUBLESIDED_ON
            #pragma shader_feature_local _ _ADD_PRECOMPUTED_VELOCITY
            #pragma shader_feature_local _ _TRANSPARENT_WRITES_MOTION_VEC
            #pragma multi_compile _ WRITE_NORMAL_BUFFER
            #pragma shader_feature_local _ _DISABLE_DECALS
            #pragma shader_feature_local _ _DISABLE_SSR
            #pragma shader_feature_local _ _DISABLE_SSR_TRANSPARENT
            #pragma multi_compile _ WRITE_DECAL_BUFFER
            #pragma shader_feature_local _REFRACTION_OFF _REFRACTION_PLANE _REFRACTION_SPHERE _REFRACTION_THIN
                
        

            #define SHADERPASS SHADERPASS_MOTION_VECTORS
            #define RAYTRACING_SHADER_GRAPH_DEFAULT
            #define VARYINGS_NEED_PASS
            #define _PASSMOTIONVECTOR 1

            
            %DEFINES%

            %PRAGMAS%

            %HDRPINCLUDE%

            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Debug/DebugDisplay.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Material.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/NormalSurfaceGradient.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Lit/Lit.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/BuiltinUtilities.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/MaterialUtilities.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Decal/DecalUtilities.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Lit/LitDecalData.hlsl"
            #include "Packages/com.unity.render-pipelines.high-definition/Runtime/ShaderLibrary/ShaderGraphFunctions.hlsl"
    
            %TEMPLATE_SHARED%

            %CUSTOMCBUFFER%

            %CUSTOMINSTANCEPROPS%

            %CODE%

            %SHADERDESC%

            %VERT%

            %TESSELLATION%

            %HDRPSHARED%



#if defined(WRITE_DECAL_BUFFER) && defined(WRITE_MSAA_DEPTH)
#define SV_TARGET_NORMAL SV_Target3
#elif defined(WRITE_DECAL_BUFFER) || defined(WRITE_MSAA_DEPTH)
#define SV_TARGET_NORMAL SV_Target2
#else
#define SV_TARGET_NORMAL SV_Target1
#endif

// Caution: Motion vector pass is different from Depth prepass, it render normal buffer last instead of decal buffer last
// and thus, we force a write of 0 if _DISABLE_DECALS so we always write in the decal buffer.
// This is required as we can't make distinction  between deferred (write normal buffer) and forward (write normal buffer)
// in the context of the motion vector pass. The cost is acceptable as it is only do object with motion vector (usualy skin object)
// that most of the time use Forward Material (so are already writing motion vector data).
// So note that here unlike for depth prepass we don't check && !defined(_DISABLE_DECALS)
void Frag(  VertexToPixel v2f
            #ifdef WRITE_MSAA_DEPTH
            // We need the depth color as SV_Target0 for alpha to coverage
            , out float4 depthColor : SV_Target0
            , out float4 outMotionVector : SV_Target1
                #ifdef WRITE_DECAL_BUFFER
                , out float4 outDecalBuffer : SV_Target2
                #endif
            #else
            // When no MSAA, the motion vector is always the first buffer
            , out float4 outMotionVector : SV_Target0
                #ifdef WRITE_DECAL_BUFFER
                , out float4 outDecalBuffer : SV_Target1
                #endif
            #endif

            // Decal buffer must be last as it is bind but we can optionally write into it (based on _DISABLE_DECALS)
            #ifdef WRITE_NORMAL_BUFFER
            , out float4 outNormalBuffer : SV_TARGET_NORMAL
            #endif

            #ifdef _DEPTHOFFSET_ON
            , out float outputDepth : SV_Depth
            #endif
        )
          {

              FragInputs input = BuildFragInputs(v2f);
              PositionInputs posInput = GetPositionInput(input.positionSS.xy, _ScreenSize.zw, input.positionSS.z, input.positionSS.w, input.positionRWS);

              float3 V = GetWorldSpaceNormalizeViewDir(input.positionRWS);


              SurfaceData surfaceData;
              BuiltinData builtinData;
              Surface l;
              ShaderData d;
              GetSurfaceAndBuiltinData(v2f, input, V, posInput, surfaceData, builtinData, l, d);

            #ifdef _DEPTHOFFSET_ON
                v2f.motionVectorCS.w += builtinData.depthOffset;
                v2f.previousPositionCS.w += builtinData.depthOffset;
            #endif

             // TODO: How to allow overriden motion vector from GetSurfaceAndBuiltinData ?
             float2 motionVector = CalculateMotionVector(v2f.motionVectorCS, v2f.previousPositionCS);

             // Convert from Clip space (-1..1) to NDC 0..1 space.
             // Note it doesn't mean we don't have negative value, we store negative or positive offset in NDC space.
             // Note: ((positionCS * 0.5 + 0.5) - (v2f.previousPositionCS * 0.5 + 0.5)) = (motionVector * 0.5)
             EncodeMotionVector(motionVector * 0.5, outMotionVector);

             // Note: unity_MotionVectorsParams.y is 0 is forceNoMotion is enabled
             bool forceNoMotion = unity_MotionVectorsParams.y == 0.0;

             // Setting the motionVector to a value more than 2 set as a flag for "force no motion". This is valid because, given that the velocities are in NDC,
             // a value of >1 can never happen naturally, unless explicitely set. 
             if (forceNoMotion)
                 outMotionVector = float4(2.0, 0.0, 0.0, 0.0);

         // Depth and Alpha to coverage
         #ifdef WRITE_MSAA_DEPTH
             // In case we are rendering in MSAA, reading the an MSAA depth buffer is way too expensive. To avoid that, we export the depth to a color buffer
                      depthColor = v2f.pos.z;

             #ifdef _ALPHATOMASK_ON
             // Alpha channel is used for alpha to coverage
             depthColor.a = SharpenAlpha(builtinData.opacity, builtinData.alphaClipTreshold);
             #endif
         #endif

         // Normal Buffer Processing
         #ifdef WRITE_NORMAL_BUFFER
             EncodeIntoNormalBuffer(ConvertSurfaceDataToNormalData(surfaceData), outNormalBuffer);
         #endif

         #if defined(WRITE_DECAL_BUFFER)
             DecalPrepassData decalPrepassData;
             // Force a write in decal buffer even if decal is disab. This is a neutral value which have no impact for later pass
             #ifdef _DISABLE_DECALS
             ZERO_INITIALIZE(DecalPrepassData, decalPrepassData);
             #else
             // We don't have the right to access SurfaceData in a shaderpass.
             // However it would be painful to have to add a function like ConvertSurfaceDataToDecalPrepassData() to every Material to return geomNormalWS anyway
             // Here we will put the constrain that any Material requiring to support Decal, will need to have geomNormalWS as member of surfaceData (and we already require normalWS anyway)
             decalPrepassData.geomNormalWS = surfaceData.geomNormalWS;
             decalPrepassData.decalLayerMask = GetMeshRenderingDecalLayer();
             #endif
             EncodeIntoDecalPrepassBuffer(decalPrepassData, outDecalBuffer);
         #endif

         #ifdef _DEPTHOFFSET_ON
             outputDepth = posInput.deviceDepth;
         #endif
          }

            ENDHLSL
        }
