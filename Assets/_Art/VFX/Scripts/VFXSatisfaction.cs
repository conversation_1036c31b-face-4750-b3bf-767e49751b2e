#if CHOICES
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class VFXSatisfaction : MonoBehaviour
{
	private NGCommanderBase m_commander;
    [SerializeField] private Transform m_currentHeightHolder;
	[SerializeField] private Renderer m_totalHeightRenderer;
	private MaterialPropertyBlock m_totalHeightPropBlock;
	[SerializeField] private Renderer m_currentHeightRenderer;
	private MaterialPropertyBlock m_currentHeightPropBlock;
	[SerializeField] private Renderer m_animRenderer;
	private MaterialPropertyBlock m_animPropBlock;
	[SerializeField] private Renderer m_outlineRenderer;
	private MaterialPropertyBlock m_outlinePropBlock;
	[SerializeField] private TMPro.TextMeshProUGUI m_percentageText;

	private float m_totalHeightAlpha = 0;
	private float m_currentHeightAlpha = 0;
	private float m_animAlpha = 0;
	private float m_outlineAlpha = 0;

	private bool m_showPercentage = false;
    private string m_cat;

    private bool m_isSimpleHighlight;

	private static readonly int HASH_TINT = Shader.PropertyToID("_Tint");
	private static readonly int HASH_LIGHTCOLOUR = Shader.PropertyToID("_LightColour");
	public void Activate(NGCommanderBase _commander, string _cat){
		m_commander = _commander;
		m_showPercentage = true;
        m_cat = _cat;

		gameObject.SetActive(true);
		
		m_totalHeightAlpha = m_totalHeightRenderer.sharedMaterial.GetColor(HASH_TINT).a;
		m_totalHeightPropBlock = new MaterialPropertyBlock();
		m_totalHeightRenderer.GetPropertyBlock(m_totalHeightPropBlock);
		
		m_currentHeightAlpha = m_currentHeightRenderer.sharedMaterial.GetColor(HASH_TINT).a;		
		m_currentHeightPropBlock = new MaterialPropertyBlock();
		m_currentHeightRenderer.GetPropertyBlock(m_currentHeightPropBlock);
		
		m_animAlpha = m_animRenderer.sharedMaterial.GetColor(HASH_LIGHTCOLOUR).a;		
		m_animPropBlock = new MaterialPropertyBlock();
		m_animRenderer.GetPropertyBlock(m_animPropBlock);

		m_outlineAlpha = m_outlineRenderer.sharedMaterial.GetColor(HASH_TINT).a;
		m_outlinePropBlock = new MaterialPropertyBlock();
		m_outlineRenderer.GetPropertyBlock(m_outlinePropBlock);
		
		RefreshDisplay(m_cat, true);
	}

    private void Update()
    {
        if (gameObject.activeSelf)
            RefreshDisplay(m_cat);
    }

    private static bool s_animateRange = false;
    
    public void RefreshDisplay(string _cat, bool _refreshSize = false) {
        m_cat = _cat;
        m_isSimpleHighlight = (m_cat == "Highlight");
        if (m_isSimpleHighlight) m_showPercentage = false;
        
		float fracValue = m_commander.BuildingQuality(m_cat);
		if (s_animateRange) fracValue = Mathf.Repeat(Time.time * .2f, 1f);
		
		if (fracValue < 0) {
			Deactivate();
			return;
		}
		
		m_percentageText.text = $"{fracValue * 100:n0}%";

		gameObject.SetActive(true);
		m_currentHeightRenderer.gameObject.SetActive(!m_isSimpleHighlight);

		//m_currentHeightHolder.transform.localScale = new Vector3(1f, fracValue, 1f);
		m_currentHeightHolder.transform.localPosition = Vector3.up * (m_totalHeightRenderer.bounds.size.y * fracValue);
		
		Color c = NGTownSatisfactionManager.Me.GetSatisfactionColour(fracValue);
		if (m_isSimpleHighlight) c = GlobalData.Me.m_buildingHighlightColour;
		
		var originalAlpha = c.a * .3f + .7f; // raise alpha in HDRP
		c.a = m_totalHeightAlpha * originalAlpha;
		m_totalHeightPropBlock.SetColor(HASH_TINT, c);
		m_totalHeightRenderer.SetPropertyBlock(m_totalHeightPropBlock);
		
		c.a = m_currentHeightAlpha * originalAlpha;
		m_currentHeightPropBlock.SetColor(HASH_TINT, c);
		m_currentHeightRenderer.SetPropertyBlock(m_currentHeightPropBlock);
		
		c.a = m_animAlpha * originalAlpha;
		m_animPropBlock.SetColor(HASH_LIGHTCOLOUR, c);
		m_animRenderer.SetPropertyBlock(m_animPropBlock);
		
		c.a = m_outlineAlpha * originalAlpha;
		m_outlinePropBlock.SetColor(HASH_TINT, c);
		m_outlineRenderer.SetPropertyBlock(m_outlinePropBlock);
		
		// move percentage display out of cylinder
		m_percentageText.transform.parent.position = m_currentHeightRenderer.transform.position + (Camera.main.transform.position - transform.position).normalized * (m_cylinderRadius + .1f);
		
		if (_refreshSize)
			SetPositionAndSize();
    }

	public void Deactivate(){
		gameObject.SetActive(false);
	}

	private float m_cylinderRadius = 0;
	private void SetPositionAndSize() {
		transform.localScale = Vector3.one;
		gameObject.SetActive(false);
		var bounds = ManagedBlock.GetTotalColliderBounds(m_commander.gameObject, "Hotspot");
		gameObject.SetActive(true);
		Vector3 min = bounds.min, max = bounds.max;
		min.y = max.y = transform.position.y;
		var dh = (max - min).xzMagnitude();
		var diameter = m_totalHeightRenderer.bounds.size.x;
		var scale = dh / diameter;
		m_cylinderRadius = dh * .5f;
		transform.localScale = new Vector3(scale, transform.localScale.y, scale);
		transform.position = (min + max) * .5f;

		RefreshPercentageSize();
	}
	
	void RefreshPercentageSize() {
		var percCanvas = m_percentageText.transform.parent;
		percCanvas.gameObject.SetActive(m_showPercentage);
		if (m_showPercentage) {
			var percHolder = percCanvas.parent;
			var holderScale = percHolder.lossyScale;
			var localScale = new Vector3(.5f / holderScale.x, .015f, .5f / holderScale.z);
			percCanvas.localScale = localScale;
		}
	}

	public static VFXSatisfaction Create(NGCommanderBase _commander, Transform _parentTransform, string cat){
		if (_commander.BuildingQuality(cat) >= 0)
		{
			var go = Instantiate(NGTownSatisfactionManager.Me.m_buildingSatisfactionVFXPrefab.gameObject, _parentTransform);
			go.transform.localPosition = new Vector3(0, -1f, 0);
			var vfx = go.GetComponent<VFXSatisfaction>();
			vfx.Activate(_commander, cat);
			return vfx;
		}
		return null;
	}

}
#endif