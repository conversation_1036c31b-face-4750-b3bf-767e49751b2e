using UnityEngine;

public class BlendshapeFluctuation : MonoBehaviour
{
    // Reference to the SkinnedMeshRenderer
    public SkinnedMeshRenderer skinnedMeshRenderer;

    // The index of the blendshape you want to fluctuate
    public int blendshapeIndex = 0;

    // The speed of the fluctuation (how fast the noise oscillates)
    public float fluctuationSpeed = 1.0f;

    // The range of the fluctuation (how much the blendshape value will change)
    public float fluctuationRange = 0.5f;

    // Time offset for the noise function to create variation across objects
    private float timeOffset;

    void Start()
    {
        // Initialize timeOffset with a random value to prevent all objects from fluctuating synchronously
        timeOffset = Random.Range(0f, 100f);
    }

    void Update()
    {
        // Generate a fluctuating value using Perlin noise
        float noiseValue = Mathf.PerlinNoise(Time.time * fluctuationSpeed + timeOffset, 0f);

        // Map the Perlin noise value to the desired fluctuation range (between 0 and 1 by default)
        float blendshapeValue = Mathf.Lerp(0f, fluctuationRange, noiseValue);

        // Apply the fluctuating value to the specified blendshape
        skinnedMeshRenderer.SetBlendShapeWeight(blendshapeIndex, blendshapeValue);
    }
}
