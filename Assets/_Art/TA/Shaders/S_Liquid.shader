Shader "_Scenery/S_Liquid"
{
	Properties
     {
		//[Header(Core)]
		_Transparency("Transparency", Range(0.0,1.0)) = 0.5
		_Reflection("Reflection", Range(0.0,1.0)) = 1
        _RefractionStrength("Refraction Strength", Range(0.0,4.0)) = 0
		_MaskTexture("Normal Map", 2D) = "bump" {}

		_SpecularColor("Specular Color",Color) = (1,1,1,1)
		_SpecularPower("Specular Power", Float) = 0.02
		//[Header(Fresnel)]
		_GlassColor ("Fresnel Color", Color ) = (0.5,0.5,0.5,1)
		_FresnelPower("Fresnel Power", Range(0.0,5.0)) = 1.3
		_FresnelExponent("Fresnel Exponent", Range(0.001, 30)) = 1
		

		//[Header(Dirt)]
		[Toggle(_DIRT)] _Dirt("Enable Dirt",Float) = 0
		_DirtMask("Dirt Mask",2D) = "White" {}
		_DirtSlider("Dirt Strength",Range(0.0,1.0)) = 0.2
		//
		//Shadows
		_ShadowPull("Shadow Distance Adjustment", Float) = 1

		_TintWindow1("Tint Window 1 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow2("Tint Window 2 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow3("Tint Window 3 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow4("Tint Window 4 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow5("Tint Window 5 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow6("Tint Window 6 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow7("Tint Window 7 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow8("Tint Window 8 (u1,v1,u2,v2)", Vector) = (0,0,0,0)

        _TintColour1("Tint colour 1", Color) = (1,1,1,0)
        _TintColour2("Tint colour 2", Color) = (1,1,1,0)
        _TintColour3("Tint colour 3", Color) = (1,1,1,0)
        _TintColour4("Tint colour 4", Color) = (1,1,1,0)
        _TintColour5("Tint colour 5", Color) = (1,1,1,0)
        _TintColour6("Tint colour 6", Color) = (1,1,1,0)
        _TintColour7("Tint colour 7", Color) = (1,1,1,0)
        _TintColour8("Tint colour 8", Color) = (1,1,1,0)
			  
        _UVOffset1("UV Offset 1 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset2("UV Offset 2 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset3("UV Offset 3 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset4("UV Offset 4 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset5("UV Offset 5 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset6("UV Offset 6 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset7("UV Offset 7 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset8("UV Offset 8 (u1,v1,u2,v2))", Vector) = (0,0,0,0)

        _AltTextureAtlas("Alt Texture Atlas", 2D) = "white" {}

        _AltTexture1("Alt Texture 1", 2D) = "white" {}
        _AltTexture2("Alt Texture 2", 2D) = "white" {}
        _AltTexture3("Alt Texture 3", 2D) = "white" {}
        _AltTexture4("Alt Texture 4", 2D) = "white" {}
        _AltTexture5("Alt Texture 5", 2D) = "white" {}
        _AltTexture6("Alt Texture 6", 2D) = "white" {}
        _AltTexture7("Alt Texture 7", 2D) = "white" {}
        _AltTexture8("Alt Texture 8", 2D) = "white" {}

        _AltTextureWeight1("Alt Texture Weight 1", float) = 0
        _AltTextureWeight2("Alt Texture Weight 2", float) = 0
        _AltTextureWeight3("Alt Texture Weight 3", float) = 0
        _AltTextureWeight4("Alt Texture Weight 4", float) = 0
        _AltTextureWeight5("Alt Texture Weight 5", float) = 0
        _AltTextureWeight6("Alt Texture Weight 6", float) = 0
        _AltTextureWeight7("Alt Texture Weight 7", float) = 0
        _AltTextureWeight8("Alt Texture Weight 8", float) = 0

        _AltTextureAtlasTransform1("Alt Texture Atlas Transform 1", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform2("Alt Texture Atlas Transform 2", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform3("Alt Texture Atlas Transform 3", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform4("Alt Texture Atlas Transform 4", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform5("Alt Texture Atlas Transform 5", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform6("Alt Texture Atlas Transform 6", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform7("Alt Texture Atlas Transform 7", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform8("Alt Texture Atlas Transform 8", Vector) = (1,1,0,0)

    }
    SubShader
    {

		Tags { "Queue" = "Transparent" "IgnoreProjector" = "True" "RenderType" = "Geometry" }
		Blend SrcAlpha OneMinusSrcAlpha
		ZWrite Off
        LOD 100

		

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag


            #include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"




			//Util
			half _Transparency;
			FresnelDefs;

			Base_Defs;
			


            struct Input
            {
			    float4 vertex : SV_POSITION;
                //float2 uv : TEXCOORD0;
				float2 refracuv : TEXCOORD0;
				float4 uvgrab : TEXCOORD1;
				fixed fresnel : TEXCOORD3;
				float3 worldPos : TEXCOORD4;
				half3 tspace0 : TEXCOORD5;
				half3 tspace1 : TEXCOORD6;
				half3 tspace2 : TEXCOORD7;
				//Base_Input
				half2 uv_MainTex : TEXCOORD8;
				half2 uv_BumpMap : TEXCOORD9;

				//tint
				//TintWindow_Input
				float4 tint : TEXCOORD10;
				float weight : TEXCOORD11;
				float windowIndex : TEXCOORD12;
				float2 atlasUV : TEXCOORD13;
				_PREMIUM_VARS(14)

            };

            Input vert (appdata_full v)
            {

                Input o;
				TintWindow_Vertex;
                o.vertex = UnityObjectToClipPos(v.vertex);

				//Fresnel


                return o;
            }
			
            fixed4 frag (Input IN) : SV_Target
            {
				fixed4 c = fixed4(1,0,0,1);
				TintWindow_Fragment;

				return fixed4(IN.tint.xyz, _Transparency);
            }
            ENDHLSL
        }
    }
	CustomEditor "S_GlassLayout"
}
