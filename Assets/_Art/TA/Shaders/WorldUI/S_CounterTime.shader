Shader "_UI/S_CounterTime"
{
    Properties
    {
        _NumbersTex("Numbers Texture", 2D) = "white" {}
        _NumberSpacing("Number Spacing", Range(0.0001, 0.05)) = 0.1
        _SectionSpacing("Section Spacing", Range(0.0001, 0.1)) = 0.1
        _Short("Short Val", Range(0, 99)) = 25
        _Medium("Medium Val", Range(0, 99)) = 25
        _Long("Long Val", Range(0, 99)) = 3

        _Alpha("Alpha", Range(0, 1)) = 1
        _ShortCol("Short Time Colour", Color) = (1.0,1.0,1.0,1.0)
        _MediumCol("Medium Time Colour", Color) = (1.0,1.0,1.0,1.0)
        _LongCol("Long Time Colour", Color) = (1.0,1.0,1.0,1.0)

        _OffsetLetters("Offset Letters", Range(-0.2, 0.2)) = 0.75
        [NoScaleOffset]_LettersTex("Letters Texture", 2D) = "white" {}
    }
    SubShader
    {
        Tags{ "RenderType" = "Transparent" "Queue" = "Transparent"}
        LOD 100
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert_TimeCounter
            #pragma fragment frag_TimeCounter
            #pragma multi_compile_instancing

            #pragma target 3.5

            #define WORLD_UI 1
            #define TIME_COUNTER 1

            #include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_VertShaders.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_FragShaders.cginc"


            ENDHLSL
        }
    }
}
