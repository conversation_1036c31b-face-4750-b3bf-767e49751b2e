void Node_CylinderScroll_float(
float2 UV,
float4 Window,
float2 UVScale,
float Scroll,
out float2 Out,
out float Lerp)
{
    float2 xy = (UV.xy - Window.xy) / (Window.zw - Window.xy);
    xy.y = asin(2 * xy.y - 1) / 3.14159;
    xy *= UVScale;
    xy.y += frac(Scroll);
    Out = xy;
    if (UV.x < Window.x || UV.x > Window.z || UV.y < Window.y || UV.y > Window.w)
        Lerp = 0;
    else
        Lerp = 1;
}
