Shader "_UI/S_BuildCounter_Hammer" {
	Properties 
	{
		[NoScaleOffset]_Albedo("Albedo Texture", 2D) = "white" {}
		_Color("Tint", Color) = (1, 1, 1, 1)
		_ShadowPull("Shadow Distance Adjustment", Float) = 1
		_SpinSpeed("Spin Speed", Range(-1,1)) = 0.1
		_TotalBuildTime("Total Build Time", Float) = 0
		_BuildProgress("Build Progress", Range(0,1)) = 0
	}
	SubShader 
	{
		Tags{ "RenderType" = "DepthNormalsExempt" "LightMode" = "ForwardBase" }
		LOD 100
		//ZWrite On
		Cull Back
		Pass
		{
			
            
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag

			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			

			
			#pragma target 3.5
			#pragma multi_compile_instancing
			

			struct v2f
			{
				_BASE_VARS(0)
				float2 uv_2 : TEXCOORD1;
				SHADOW_COORDS(3) // put shadows data into TEXCOORD6
				fixed4 color : COLOR1;
				_LIGHTING_VARS(0, 12)
				_WORLDPOS_VARS(6)
				_DISTANCE_VARS(7)
                _NTB_VARS(8,9,10)
			};



			sampler2D _Albedo;
			half4 _Albedo_ST;
			half _TotalBuildTime;
			half _BuildProgress;
			half _SpinSpeed;

			fixed4 _Color;



			v2f vert(appdata_instancing v)
			{
				v2f o;
				o.color = v.color;

				half spinSpeed = _TotalBuildTime * _BuildProgress * _SpinSpeed;
				half rotationDegrees = (1-(abs(sin(spinSpeed)))) * 70;
				rotationDegrees += 80;

				v.vertex = _RotateAroundZInDegrees(v.vertex, rotationDegrees);

				_VERT_INITIALIZE(o, v, v2f);
				_WORLD_NORMAL_VERT(v); 
				_CALC_WORLDPOS(o, v);
				// taken directly from Mike's shader. Would need to simplify
				
				_CALC_NTB(o, v);
				o.uv = TRANSFORM_TEX(v.uv, _Albedo);

				TRANSFER_SHADOW(o)
            
                
				
				half nl = 1;  // cancelling out vert shading in favour of per pixel shading

				_CALC_WORLDPOS(o, v);
				_CACHED_SHADOW_VERT(o);
				_CalculateDistanceFog(v.vertex, o.distanceFog);
				o.pos = UnityObjectToClipPos(v.vertex);
				return o;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				_PROP_FRAG_DEFINES(i);
                
              
                
				albedo = tex2D(_Albedo, i.uv.xy) * _Color;
				_WORLD_NORMAL_FRAG_LQ(i)
				
					
				_CACHED_SHADOW_MASKED_FRAG(i);
				_LIGHT_FROM_NORMAL_WORLD(i);
				_APPLY_SHADOW_FRAG(i);

                

				col.xyz = _LitAlbedo(albedo, shadow);

				_ApplyDistanceFog(i.distanceFog, col.xyz);

				col.a = 1;
				return col;
			}
			ENDCG
		}
		
		UsePass "_Shadowcasters/S_ShadowCasters/ShadowCaster_Props"
	}
}
