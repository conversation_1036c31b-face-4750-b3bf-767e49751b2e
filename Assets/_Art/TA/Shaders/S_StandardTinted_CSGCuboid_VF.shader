// Upgrade NOTE: upgraded instancing buffer 'Props' to new syntax.

Shader "_Standard/S_StandardTintedCSGCuboid_VF" 
{	
    SubShader
    {
        Pass
        {
            Name "StandardTintedCSGCuboid_LOD300"
            Tags { "LightMode"="UniversalForward" }
            Stencil
	        {
	            WriteMask [_StencilWriteMaskGBuffer]
	            Ref [_StencilRefGBuffer]
	            Comp Always
	            Pass Replace
	        }

            HLSLPROGRAM
            #pragma vertex vert_CSGCuboid
            #pragma fragment frag
            #pragma multi_compile_instancing
            #pragma multi_compile_fwdbase
            #define NORMALIZE_TANGENTS_PER_PIXEL 1
            #define CSGCUBOID_SHADER 1
            #define CSGCUBOID_ADDITION 1

            //#pragma multi_compile __ _IS_ON_FIRE
            #pragma multi_compile __ _PATTERNED
            //#pragma multi_compile __ _USE_DITHER_OPACITY

            #pragma shader_feature_local _ _IS_WINDOW

            #pragma target 3.5

            #define _CSG_FLOAT_RT

            #include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_PremiumRewards.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines_StandardVF.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_VertShaders.cginc"

        

            float4 frag (v2f_CSGCuboid i, float faceDir : VFACE) : SV_Target
            {
                Include_ScreenSpaceBooleanSubtractCuboid_Fragment;
                CircleClip_Fragment(i.worldPos);
                CircleClip_FragmentForward(faceDir);
            
                _BASE_COL_WITH_TINT;
                DoAlphaTest_VF;


                //return fixed4(i.premiumVars.xxx, 1);

            //Normal
                half3 worldNormal = UnpackNormal(tex2D(_BumpMap, i.uv));
                _WORLD_NORMAL_FRAG(i);

                float3 viewDir = normalize(_WorldSpaceCameraPos - i.worldPos.xyz);

                baseCol = lerp(baseCol, _OwnLand(baseCol, i.worldPos), _AffectedByDistricts);

                _METAL_LOD300(i, viewDir);

            	float smoothness = baseCol.a;
				float metallic = metalMask.r;
				baseCol.xyz = lerp(baseCol.xyz, metalCol, metallic);

                _CACHED_SHADOW_MASKED_FRAG(i);
                _LIGHT_FROM_NORMAL_WORLD(i);
                _APPLY_SHADOW_FRAG(i);
                _BOUNCELIGHT;

            	return ApplyLighting(baseCol.rgb, worldNormal, i.worldPos, smoothness, metallic);
            }
            ENDHLSL
        }
	}
	CustomEditor "StandardShaderTintedGUI"
}
