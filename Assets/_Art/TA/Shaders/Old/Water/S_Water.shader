Shader "Unlit/Water"
{
    Properties
    {
        [Header(Aspect)][Space]
        _RefractiveIndex("Refractive Index", float) = 1.33
        _PhaseG("Phase G", range(0,.99)) = 0
        _Depth("Absorption Density", range(0,100)) = 1
        _RefractionFactor("Refraction Factor",range(0,.1)) = .1
        _NormalStrength("Normal Strength", range(0,2)) = 0
        _NormalScales("Normal UVs Tiling", vector) = (0,0,0,0)
        _ReflectionFactor("Reflection Factor", float) = .2

        [Header(Flow)][Space]
        _FlowSpeed("Flow Speed", float) = 1
        _FlowIntensity("Flow Intensity", float) = 1
        [Toggle(_UV_FLOWMAP)] _UV_FLOWMAP ("UV Flowmap", Float) = 0.0

        [Header(Glitter)][Space]
        [HDR]_GlitterColor("Glitter Color", color) = (0,0,0,0)
        _GlitterAmount("Glitter Amount", float ) = 1

        [Header(Foam)][Space]
        _FoamTexture("Foam Texture", 2D) = "white" {}
        _FoamColor("Foam Color", color) = (0,0,0,0)
        _FoamFrequency("Foam Frequency", float) = 20
        _FoamDistance("Foam Distance", float) = 1
        _FoamSpeed("Foam Speed", float) = 3
        _MovingFoamOpacity("Moving Foam Opacity", float) = 0

        [Header(Color)][Space]
        _Absorption("Absorption Color", color)= (.2,.15,.1,0.05)
        _SkyColor("Sky Color", color) = (0,0,0,0)
        [HDR]_SpecColor("Spec Color", color) = (0,0,0,0)
        _DepthColor("Depth Color", color) = (0,0,0,0)

        [Header(Texture)][Space]
        _MainTex ("Sea Level SDF", 2D) = "white" {}
        _Caustics("Caustics", 2D) = "white" {}
        _CausticScale("Caustic Scale", float) = 80
        _CloudNoise("Cloud Noise", 2D) = "white" {}
        _WorleyNoise("Worle Noise", 2D) = "white" {}
        _NormalMap ("Normal Map", 2D) = "white" {}
        _FlowMap("Flow Map", 2D) = "white" {}

    }
    SubShader
    {
        Tags
        {
            "RenderType"="Opaque"
        }

        Pass
        {
            HLSLPROGRAM
            #pragma shader_feature _UV_FLOWMAP
            #pragma vertex vert
            #pragma fragment frag
            #define fixed4 float4
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"


            #include "../OwnedLand.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float4 tangentOS : TANGENT;
                float3 normalOS : NORMAL;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4 screenPos : TEXCOORD1;
                float3 positionWS : TEXCOORD2;
                float3x3 tangentToWorld : TEXCOORD3;
                float4 fragPosOS : TEXCOORD7;
            };

            sampler2D _MainTex;
            //sampler2D _HeightMap;
            float4 _MainTex_TexelSize;
            sampler2D _Caustics;
            sampler2D _NormalMap;
            sampler2D _FlowMap;
            sampler2D _CameraOpaqueTexture;
            sampler2D _ReflectionMap;
            sampler2D _CameraDepthTexture;
            sampler2D _CloudNoise;
            sampler2D _WorleyNoise;
            sampler2D _FoamTexture;
            float4 _FoamTexture_ST;
            samplerCUBE _CubeMap;

            float _FlowSpeed;
            float _Depth;
            float _FlowIntensity;
            float _NormalStrength;
            float _RefractiveIndex;
            float _ReflectionFactor;
            float _PhaseG;
            float _RefractionFactor;
            float _GlitterAmount;
            float _FoamDistance;
            float _FoamFrequency;
            float _FoamSpeed;
            float _CausticScale;

            float3 _NormalScales;
            float4 _Absorption;

            half4 _RedAbsorption;
            half4 _BlueAbsorption;
            half4 _GreenAbsorption;
            half4 _FoamColor;

            half4 _SkyColor;
            half4 _SpecColor;
            half4 _GlitterColor;
            half4 _DepthColor;

            float4 _TimeQ;
            
            v2f vert(appdata v)
            {
                v2f o;
                o.vertex = TransformObjectToHClip(v.vertex);
                o.uv = v.uv;
                o.screenPos = ComputeScreenPos(o.vertex);
                o.positionWS = TransformObjectToWorld(v.vertex);

                VertexNormalInputs normalInput = GetVertexNormalInputs(v.normalOS, v.tangentOS);
                o.tangentToWorld = half3x3(normalInput.tangentWS.xyz, normalInput.bitangentWS,
                                           normalInput.normalWS.xyz);
                o.fragPosOS = o.vertex;

                return o;
            }

            float ComputeDepth(float4 screenCoords)
            {
                float rawDepth = tex2D(_CameraDepthTexture, screenCoords.xy / screenCoords.w);
                float backgroundDepth = LinearEyeDepth(rawDepth, _ZBufferParams);
                float surfaceDepth = UNITY_Z_0_FAR_FROM_CLIPSPACE(screenCoords.z);
                float depthDifference = backgroundDepth - surfaceDepth;
                return depthDifference;
            }

            float2 ComputeRefractedScreenCoords(float3 normal, float4 screenCoords, float refractedDepth)
            {
                if (refractedDepth < 0) return screenCoords.xy / screenCoords.w;
                float2 refractedScreenPos = screenCoords.xy / screenCoords.w + normal.xz * _RefractionFactor;
                return refractedScreenPos;
            }

            float ComputeDepthRefracted(float3 normal, float3 viewDir, float4 screenCoords, float depth)
            {
                float2 refractedScreenPos = ComputeRefractedScreenCoords(normal, screenCoords, depth);
                float rawDepth = tex2D(_CameraDepthTexture, refractedScreenPos);
                float backgroundDepth = LinearEyeDepth(rawDepth, _ZBufferParams);
                float surfaceDepth = UNITY_Z_0_FAR_FROM_CLIPSPACE(screenCoords.z);
                float depthDifference = backgroundDepth - surfaceDepth;
                return depthDifference;
            }

            float3 ComputeAbsorption(float depth)
            {
                depth = depth * _Depth;
                float blueTransmission = exp(-depth * _Absorption.b);
                float greenTransmission = exp(-depth * _Absorption.g);
                float redTransmission = exp(-depth * _Absorption.r);

                float3 transmission = blueTransmission * float3(0, 0, 1) +
                    greenTransmission * float3(0, 1, 0) +
                    redTransmission * float3(1, 0, 0);

                return transmission;
            }

            float3 NormalStrength(float3 normal, float Strength)
            {
                return float3(normal.rg * Strength, lerp(1, normal.b, saturate(Strength)));
            }

            float2 ParallaxMapping(float2 uv, float3 viewDir, float depth)
            {
                float2 p = viewDir.xy / viewDir.z * (depth * .01);
                return uv - p;
            }

            float HenyeyGreenstein(float costh, float g)
            {
                return (1.0 - g * g) / (4.0 * PI * pow(1.0 + g * g - 2.0 * g * costh, 1.5));
            }

            float Reflectance(float incidence, float n1, float n2)
            {
                float cosIncident = cos(incidence);
                float cosRefract = n1 / n2 * sin(incidence);

                if (cosRefract > 1.0) return 1.0; // total internal reflection

                cosRefract = sqrt(1.0 - cosRefract * cosRefract);

                float Rs = (n1 * cosIncident - n2 * cosRefract) / (n1 * cosIncident + n2 * cosRefract);
                Rs = Rs * Rs;

                float Rp = (n1 * cosRefract - n2 * cosIncident) / (n1 * cosRefract + n2 * cosIncident);
                Rp = Rp * Rp;

                return lerp(Rs, Rp, 0.5);
            }

            float3 fresnelSchlick(float cosTheta, float3 F0)
            {
                return F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
            }

            // Trowbridge-Reitz
            float distribution(float3 n, float3 h, float roughness)
            {
                float a_2 = roughness * roughness;
                return a_2 / (PI * pow(pow(dot(n, h), 2.0) * (a_2 - 1.0) + 1.0, 2.0));
            }

            // GGX and Schlick-Beckmann
            float geometry(float cosTheta, float k)
            {
                return (cosTheta) / (cosTheta * (1.0 - k) + k);
            }

            float smiths(float NdotV, float NdotL, float roughness)
            {
                float k = pow(roughness + 1.0, 2.0) / 8.0;
                return geometry(NdotV, k) * geometry(NdotL, k);
            }

            // Fresnel-Schlick
            float3 fresnel(float cosTheta, float3 F0)
            {
                return F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
            }

            // Specular part of Cook-Torrance BRDF
            float3 BRDF(float3 n, float3 viewDir, float3 lightDir, float3 F0, float roughness, float reflectance)
            {
                float3 h = normalize(viewDir + lightDir);
                float NdotL = max(dot(lightDir, n), 0.0);
                float NdotV = max(dot(viewDir, n), 0.0);

                float D = distribution(n, h, roughness);

                // I use the fresnel equation instead of the Schlick approximation here
                // float cosTheta = dot(h, viewDir);
                // fresnel(cosTheta, F0);

                float3 F = reflectance;

                float G = smiths(NdotV, NdotL, roughness);

                float3 specular = D * F * G / max(0.0001, (4.0 * NdotV * NdotL));
                return specular;
            }

            float FloatingFoam(float2 uv, float2 flowMap)
            {
                float floatingFoam = tex2D(_FoamTexture, uv *_FoamTexture_ST.x + flowMap *50);
                //floatingFoam *= tex2D(_WorleyNoise, uv * .02 + flowMap * 50);
              //  floatingFoam *= tex2D(_WorleyNoise, uv * .01 + flowMap * 20);
              //  floatingFoam = smoothstep(.01, 0, floatingFoam);
                return 1-abs(sin(floatingFoam * 40 + _TimeQ.y));
            }

            float _MovingFoamOpacity;

            float4 frag(v2f i) : SV_Target
            {
                float3 tangentFragPos = TransformWorldToTangent(i.positionWS, i.tangentToWorld, false);
                float3 tangentViewPow = TransformWorldToTangent(_WorldSpaceCameraPos, i.tangentToWorld, false);
                float3 viewDirWS = normalize(i.positionWS - _WorldSpaceCameraPos);

                float2 rUv = i.uv * float2(-1, 1);
                float2 flow = (tex2D(_FlowMap, rUv).xy - 0.5) * 2.0;

                #if _UV_FLOWMAP
                flow = frac(i.uv * float2(.5,0));
                rUv.y *= 3;
                #endif

                float timePhase0 = frac(_TimeQ.x * _FlowSpeed);
                float timePhase1 = frac(timePhase0 + 0.5);
                float flowMix = abs((timePhase0 - .5) * 2.0);
                float depth = ComputeDepth(i.screenPos);
                float distanceToShore = saturate(.4 + depth * .1) * _FlowIntensity;

                float2 uv0 = rUv + flow * timePhase0 * distanceToShore;
                float2 uv1 = rUv + flow * timePhase1 * distanceToShore;

                float glitternoise = 1 - tex2D(_WorleyNoise,
                                               i.positionWS.xz * .05 + float2(_TimeQ.x * .2, -_TimeQ.x * .6));
                glitternoise += 1 - tex2D(_WorleyNoise, i.positionWS.xz * .03 - float2(_TimeQ.x * .2, -_TimeQ.x * .6));
                glitternoise = saturate(glitternoise);
                float glitter = step(glitternoise, _GlitterAmount );

                float3 normal0 = UnpackNormal(tex2D(_NormalMap, uv0 * _NormalScales.x)) * .5 +
                    UnpackNormal(tex2D(_NormalMap, uv0 * _NormalScales.y)) * .3 +
                    UnpackNormal(tex2D(_NormalMap, uv0 * _NormalScales.z)) * .2;
                float3 normal1 = UnpackNormal(tex2D(_NormalMap, uv1 * _NormalScales.x)) * .5 +
                    UnpackNormal(tex2D(_NormalMap, uv1 * _NormalScales.y)) * .3 +
                    UnpackNormal(tex2D(_NormalMap, uv1 * _NormalScales.z)) * .2;

                float3 normalWS = lerp(normal0, normal1, flowMix);
                normalWS = NormalStrength(normalWS, _NormalStrength);
                normalWS = normalize(mul(normalWS, i.tangentToWorld));

                float3 viewDirTS = normalize(tangentViewPow - tangentFragPos);
                float2 parallaxUV0 = ParallaxMapping(uv0 * _CausticScale, viewDirTS, depth);
                float2 parallaxUV1 = ParallaxMapping(uv1 * _CausticScale, viewDirTS, depth);

                float3 caustics = lerp(
                        tex2D(_Caustics, parallaxUV0),
                        tex2D(_Caustics, parallaxUV1),
                        flowMix)
                    * saturate(2 - depth * .1);

                float phaseG = HenyeyGreenstein(dot(_MainLightPosition, normalWS), _PhaseG);
                float angle = acos(abs(dot(viewDirWS, normalWS)));
                float reflectanceFactor = Reflectance(angle, 1, _RefractiveIndex);
                float spec = BRDF(normalWS, -viewDirWS, _MainLightPosition.xyz, 0.02, 0.01, reflectanceFactor).x;
                float refractanceFactor = saturate(1 - reflectanceFactor);
                float refractedDepth = ComputeDepthRefracted(normalWS, viewDirWS, i.screenPos, depth);
                float2 refractedScreenPos = ComputeRefractedScreenCoords(normalWS, i.screenPos, refractedDepth);

                float foamDepth = depth;
                foamDepth *= _FoamDistance;

                float noise = tex2D(_CloudNoise, i.positionWS.xz * .005 + _TimeQ.x * .2);
                float foam = smoothstep(1, 0, foamDepth) * smoothstep(
                    0, .3, max(sin((foamDepth + noise) * _FoamFrequency - (_TimeQ.y * _FoamSpeed)), 0.0));


                float floatingFoam0 = FloatingFoam(i.positionWS.xz, flow * timePhase0 * distanceToShore);
                float floatingFoam1 = FloatingFoam(i.positionWS.xz, flow * timePhase1 * distanceToShore);
                float floatingFoam = lerp(floatingFoam0, floatingFoam1, flowMix);
                // floatingFoam = (0, 1, 1 - abs(sin(foamTexture * 40 + _TimeQ.y)));

                // floatingFoam -= 1-tex2D(_WorleyNoise, i.positionWS.xz*.2);


                // return floatingFoam; 

                float4 refractedColor = tex2Dlod(_CameraOpaqueTexture, float4(refractedScreenPos,10,10));
                refractedColor.xyz *= ComputeAbsorption((depth < 0 ? refractedDepth : depth) * phaseG);
                refractedColor = lerp(refractedColor, _DepthColor, saturate(depth * _DepthColor.a));
                caustics *= 1 - saturate(depth * _DepthColor.a);
                refractedColor *= float4(caustics + .90, 1);

                float4 reflectedColor = tex2D(_ReflectionMap,
                                              i.screenPos.xy / i.screenPos.w);

                half4 color = saturate(refractedColor * refractanceFactor);
                color += saturate(reflectedColor * reflectanceFactor);
                color += spec * _SpecColor;
                color += glitter * _GlitterColor;

                color = lerp(color, .7, saturate(foam + floatingFoam * _MovingFoamOpacity));
                color.xyz = _OwnLandReal(float4(color.xyz, 1), float4(i.positionWS, 1), float3(1,1,1)).xyz;
                return lerp(color, _FoamColor, foam);
            }
            ENDHLSL
        }

        //        Pass
        //        {
        //            Name "ShadowCaster"
        //            Tags
        //            {
        //                "LightMode" = "ShadowCaster"
        //            }
        //
        //            HLSLPROGRAM
        //            // Required to compile gles 2.0 with standard srp library
        //            #pragma prefer_hlslcc gles
        //            #pragma exclude_renderers d3d11_9x
        //            #pragma target 2.0
        //
        //            #pragma vertex vert
        //            #pragma fragment frag
        //
        //            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        //
        //            struct appdata
        //            {
        //                float4 vertex : POSITION;
        //                float2 uv : TEXCOORD0;
        //            };
        //
        //            struct v2f
        //            {
        //                float4 vertex : SV_POSITION;
        //                float2 uv : TEXCOORD0;
        //            };
        //
        //            float4x4 _CameraProjectionMatrix;
        //            float4x4 _WorldToCameraMatrix;
        //            float4x4 _CameraMatrix;
        //
        //
        //            v2f vert(appdata v)
        //            {
        //                v2f fo;
        //                fo.vertex = TransformObjectToHClip(v.vertex);
        //                fo.uv = v.uv;
        //
        //                return fo;
        //            }
        //
        //
        //            float _FoamDistance;
        //            float _FoamFrequency;
        //            float _FoamSpeed;
        //            sampler2D _MainTex;
        //            sampler2D _CloudNoise;
        //
        //            float4 frag(v2f i) : SV_Target
        //            {
        //                float foamDepth = tex2D(_MainTex, i.uv);
        //                foamDepth *= _FoamDistance * 100;
        //                float foam = smoothstep(1, 0, foamDepth) * smoothstep(
        //                    0, .3, max(sin(foamDepth * _FoamFrequency - (_TimeQ.y * _FoamSpeed)), 0.0));
        //                foam = step(.2, foam);
        //                if (foam == 0)discard;
        //                return 1;
        //            }
        //            ENDHLSL
        //        }
    }
}