//UNITY_SHADER_NO_UPGRADE
#ifndef _GRASSPATCH_VERTEX_INCLUDED
#define _GRASSPATCH_VERTEX_INCLUDED

//#define _DEBUG_SPLAT 6
//#define _DEBUG_FADE 1

#define fixed float
#define fixed4 float4

#include "Assets/_Art/TA/Shaders/TerrainInc.cginc"

Texture2D _Splat1, _Splat2, _Splat3, _Splat4;
SamplerState sampler_Splat1, sampler_Splat2, sampler_Splat3, sampler_Splat4;
half _GrassFadeStart;
half _GrassFadeBlur;
half _GrassPlacementThreshold;
float4 _GrassTintTexture_ST;
half _GrassColRand;
Texture2D _GrassWindNoiseTexture;
SamplerState sampler_GrassWindNoiseTexture;
float4 _TimeQ;
Texture2D _WindNoise;
SamplerState sampler_WindNoise;

inline half _Mod1000(half val)
{
	val = frac(val * 0.001) * 1000;
	return val;
}

inline void _WindOffsetGrass(inout float4 worldPos, out half offsetAmplitude, half _WindSpeed, half _WindFrequency, half _WindAmplitude, float3 color)
{
	float windSpeed_LF = _Mod1000(_WindSpeed * 0.1 * _TimeQ.y);
	half windSpeed_HF = windSpeed_LF * 3;
	half windSize_LF = _WindFrequency;
	half windSize_HF = windSize_LF * 40;
	half4 offsetUV_LF = half4((worldPos.xz*windSize_LF) + windSpeed_LF, 0, 0);
	half4 offsetUV_HF = half4((worldPos.xz*windSize_HF) + windSpeed_HF, 0, 0);
	half2 noise_HF = (SAMPLE_TEXTURE2D_LOD(_WindNoise, sampler_WindNoise, offsetUV_HF, 0).xy - .5) * 0.3;
	half2 noise_LF = SAMPLE_TEXTURE2D_LOD(_WindNoise, sampler_WindNoise, offsetUV_LF, 0).xy - .5;
	half2 offset = (noise_HF + noise_LF) * _WindAmplitude*color.r;
	offsetAmplitude = offset;
	worldPos.xz -= offset;
}

float4 _CameraWorldPosition, _CameraWorldForward;
void _GrassDistanceFade(float4 clampAxis, float4 referencePos, inout float4 worldPos, half fadeEnd, half _GrassFadeBlur, half splatMapDist, out float dist)
{
	const float3 camFwd = _CameraWorldForward.xyz;
	const float3 camPos = _CameraWorldPosition.xyz;
	const float c_distanceBias = 1.15; // bias to make sure we don't show a cell that's about to disappear
	const float c_fadeRangeFraction = .25;
	const float fadeRange = fadeEnd * c_fadeRangeFraction;
	const float fadeStart = fadeEnd - fadeRange;
	
	const float distanceToVertex = dot(referencePos.xyz - camPos, camFwd) * c_distanceBias;
	dist = (distanceToVertex - fadeStart) / fadeRange;
	dist = saturate(dist);
	dist = max(dist, splatMapDist);
	#ifdef _DEBUG_FADE
	dist = _SinTime.w * .5 + .5;
	#endif
	worldPos.xyz = lerp(worldPos.xyz, clampAxis.xyz, dist);
	dist = (1 - dist) * distanceToVertex; // distance to vertex, faded with splatMatDist and fade-out
}

#define MAX_GRASS_TYPES 16
float4 _GrassColours[MAX_GRASS_TYPES];
float4 _GrassHeights[MAX_GRASS_TYPES];
float4 _GrassHeights2[MAX_GRASS_TYPES];
fixed4 GrassContribution(int _index, float _scale)
{
	fixed4 details = _GrassColours[_index];
	fixed4 heights = _GrassHeights[_index];
	fixed4 heights2 = _GrassHeights2[_index];
	details.a = dot(heights, _GrassPresence) + dot(heights2, _GrassPresence2);
	details.rgb *= step(.001, details.a);
	return details * _scale;
}

void _GrassDetails(float2 patchUVs, inout float vheight, out float splatMapDist, out float3 colour, float _inDistrict)
{
	fixed4 splat1 = SAMPLE_TEXTURE2D_LOD(_Splat1, sampler_Splat1, patchUVs, 0);
	fixed4 splat2 = SAMPLE_TEXTURE2D_LOD(_Splat2, sampler_Splat2, patchUVs, 0);
	fixed4 splat3 = SAMPLE_TEXTURE2D_LOD(_Splat3, sampler_Splat3, patchUVs, 0);
	fixed4 splat4 = SAMPLE_TEXTURE2D_LOD(_Splat4, sampler_Splat4, patchUVs, 0);
	#ifdef _DEBUG_SPLAT
	#if _DEBUG_SPLAT == 0
	splat1 *= float4(1,0,0,0); splat2 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 1
	splat1 *= float4(0,1,0,0); splat2 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 2
	splat1 *= float4(0,0,1,0); splat2 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 3
	splat1 *= float4(0,0,0,1); splat2 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 4
	splat2 *= float4(1,0,0,0); splat1 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 5
	splat2 *= float4(0,1,0,0); splat1 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 6
	splat2 *= float4(0,0,1,0); splat1 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 7
	splat2 *= float4(0,0,0,1); splat1 = 0; splat3 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 8
	splat3 *= float4(1,0,0,0); splat2 = 0; splat1 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 9
	splat3 *= float4(0,1,0,0); splat2 = 0; splat1 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 10
	splat3 *= float4(0,0,1,0); splat2 = 0; splat1 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 11
	splat3 *= float4(0,0,0,1); splat2 = 0; splat1 = 0; splat4 = 0;
	#elif _DEBUG_SPLAT == 12
	splat4 *= float4(1,0,0,0); splat2 = 0; splat3 = 0; splat1 = 0;
	#elif _DEBUG_SPLAT == 13
	splat4 *= float4(0,1,0,0); splat2 = 0; splat3 = 0; splat1 = 0;
	#elif _DEBUG_SPLAT == 14
	splat4 *= float4(0,0,1,0); splat2 = 0; splat3 = 0; splat1 = 0;
	#elif _DEBUG_SPLAT == 15
	splat4 *= float4(0,0,0,1); splat2 = 0; splat3 = 0; splat1 = 0;
	#endif
	#endif
	
	fixed4 grassDetails = GrassContribution(0, splat1.r) +
		GrassContribution(1, splat1.g) + 
		GrassContribution(2, splat1.b) + 
		GrassContribution(3, splat1.a) + 
		GrassContribution(4, splat2.r) + 
		GrassContribution(5, splat2.g) + 
		GrassContribution(6, splat2.b) + 
		GrassContribution(7, splat2.a) + 
		GrassContribution(8, splat3.r) + 
		GrassContribution(9, splat3.g) + 
		GrassContribution(10, splat3.b) + 
		GrassContribution(11, splat3.a) + 
		GrassContribution(12, splat4.r) + 
		GrassContribution(13, splat4.g) + 
		GrassContribution(14, splat4.b) + 
		GrassContribution(15, splat4.a);
	fixed totalWeights = dot(splat1+splat2+splat3+splat4, fixed4(1,1,1,1));
	totalWeights = max(totalWeights, 0.0001);
	grassDetails /= totalWeights;
	fixed discardMap = grassDetails.a * 16 * 2;
	discardMap *= max(_GrassPresence2.w, _inDistrict);
	float yScale = discardMap * .5;
	discardMap = max(0, 1 - discardMap);
	splatMapDist = smoothstep(0, _GrassPlacementThreshold, discardMap);
	vheight *= yScale;
	colour = grassDetails.rgb;
}

Texture2D _OwnLandMaskVS;
SamplerState sampler_OwnLandMaskVS;

void GrassPatchVertex_float(float4 pos, float vheight, float3 vertexColour, float2 posInPatch, float4 objectPos, out float4 worldPos, out float3 colour, out float3 wnormal)
{
	float splatMapDist = 0, dist;
	worldPos = pos;
	float4 uv = float4(pos.xz * _HeightmapToUV.xy + _HeightmapToUV.zw, 0, 0);
	float _PatchSize = 8;
	float2 quantPos = (.5 - posInPatch) * _PatchSize + objectPos.xz;
	float4 quantUV = float4(quantPos * _HeightmapToUV.xy + _HeightmapToUV.zw, 0, 0);
	float3 detailColour;
	float4 district = SAMPLE_TEXTURE2D_LOD(_OwnLandMaskVS, sampler_OwnLandMaskVS, quantUV.xy, 0);
	_GrassDetails(quantUV.xy, vheight, splatMapDist, detailColour, district.g);
	float4 heights = SAMPLE_TEXTURE2D_LOD(_HeightMapV, sampler_HeightMapV, quantUV.xy, 0);
	float groundHeight = dot(_HeightmapDot, heights);
	worldPos.y = vheight + groundHeight + .1;

	half offsetAmplitude;
	_WindOffsetGrass(worldPos, offsetAmplitude, _WindSpeed, _WindFrequency, _WindAmplitude, vertexColour);
	//o.sheen = offsetAmplitude * v.color.r;

	float4 instancePos = float4(quantPos.x, groundHeight, quantPos.y, 0);
	float4 flatClampPos = float4(quantPos.x, groundHeight - .2, quantPos.y, 0);//float4(worldPos.x, groundHeight - .1, worldPos.z, 0);
	_GrassDistanceFade(flatClampPos, instancePos, worldPos, _GrassFadeStart, _GrassFadeBlur, splatMapDist, dist);

	fixed4 tipCol = fixed4(1,1,1,1);//fixed4(SAMPLE_TEXTURE2D_LOD(_GrassTintTexture, sampler_GrassTintTexture, uv * float4(_GrassTintTexture_ST.xy,0,0), 0)*_GrassDetailCol.r) * 1.05;
	tipCol.rgb = detailColour.rgb * dot(tipCol.rgb, 1);
	fixed4 baseCol = tipCol - 0.05;
	fixed4 mainCol = lerp(baseCol,tipCol,saturate(vertexColour.g+0.5));

	float noise = SAMPLE_TEXTURE2D_LOD(_GrassWindNoiseTexture, sampler_GrassWindNoiseTexture, uv * 50, 0);
	fixed4 randCol = lerp(mainCol+_GrassColRand,mainCol-_GrassColRand,noise);
	colour = randCol.rgb * 3;
	wnormal = float3(0,1,0); // TODO
	//colour = float3(1,dist,0);
}

float _FogMaxDistance;
float _FogRaise;
float _FogBubbleInvSqrd;
float2 _FogWobble1; // 27, .7
float2 _FogWobble2; // 37, .3
float _FogWobbleBase; // .3
float _FogHeightWithDistanceFactor;

void FogPatchVertex_float(float4 pos, float vheight, float3 vertexColour, float2 posInPatch, float4 objectPos, out float4 worldPos, out float3 colour, out float3 wnormal)
{
	float splatMapDist = 0, dist;
	worldPos = pos;
	float _PatchSize = 8;
	float2 quantPos = pos.xz;//(.5 - posInPatch) * _PatchSize + objectPos.xz;
	float4 quantUV = float4(quantPos * _HeightmapToUV.xy + _HeightmapToUV.zw, 0, 0);
	float4 district = SAMPLE_TEXTURE2D_LOD(_OwnLandMaskVS, sampler_OwnLandMaskVS, quantUV.xy, 0);
	float4 heights = SAMPLE_TEXTURE2D_LOD(_HeightMapV, sampler_HeightMapV, quantUV.xy, 0);
	float groundHeight = dot(_HeightmapDot, heights);
	worldPos.y = vheight + groundHeight + .1;

	splatMapDist = district.g;

	float3 toCam = _CameraWorldPosition.xyz - worldPos.xyz;
	float distSqrd = dot(toCam.xyz, toCam.xyz);
	float bubble = min(1, distSqrd * _FogBubbleInvSqrd);
	splatMapDist = max(1 - bubble, splatMapDist);

	float4 instancePos = float4(quantPos.x, groundHeight, quantPos.y, 0);
	float4 flatClampPos = float4(quantPos.x, groundHeight - .6 - 2, quantPos.y, 0);

	float raise = (sin(_Time.x * _FogWobble1.x + pos.x * _FogWobble1.y) + cos(_Time.x * _FogWobble2.x + pos.z * _FogWobble2.y) + 2) * _FogWobbleBase + _FogRaise;
	worldPos.y += raise;
	
	_GrassDistanceFade(flatClampPos, instancePos, worldPos, _FogMaxDistance, _GrassFadeBlur, splatMapDist, dist);
	worldPos.y += dist * _FogHeightWithDistanceFactor;
	colour = district.xyz;//float3(1,1,1);
	wnormal = float3(0,1,0); // TODO
}

#endif // _GRASSPATCH_VERTEX_INCLUDED
