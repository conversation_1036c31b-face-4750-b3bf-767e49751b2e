// Upgrade NOTE: upgraded instancing buffer 'Props' to new syntax.

Shader "_Standard/StandardBoxClip" {
	Properties {
        // Standard properties
        _Color ("Color", Color) = (1,1,1,1)
        _MainTex ("Albedo (RGB)", 2D) = "white" {}
        _MetallicGlossMap ("Metal", 2D) = "black" {}
        _Glossiness("Smoothness", Range(0.0, 1.0)) = 0.5
        _GlossMapScale("Smoothness Scale", Range(0.0, 1.0)) = 1.0
        [Enum(Metallic Alpha,0,Albedo Alpha,1)] _SmoothnessTextureChannel ("Smoothness texture channel", Float) = 0
        _Metallic ("Metallic", Range(0,1)) = 0.0
        // General properties
        _DetailAlbedoMap ("Detail map", 2D) = "grey" {}
        _DetailMapIntensity ("Detail map intensity", Float) = 0
        _DetailMapWorldOrigin ("Detail map world origin", Vector) = (0,0,0,0)
        _Cutoff("Alpha Cutoff", Range(0.0, 1.0)) = 0
        _BumpScale("Scale", Float) = 1.0
        _BumpMap("Normal Map", 2D) = "bump" {}
        _Parallax ("Height Scale", Range (0.005, 0.08)) = 0.02
        _ParallaxMap ("Height Map", 2D) = "black" {}
        _OcclusionStrength("Strength", Range(0.0, 1.0)) = 1.0
        _OcclusionMap("Occlusion", 2D) = "white" {}
        _EmissionColor("Color", Color) = (0,0,0)
        _EmissionMap("Emission", 2D) = "white" {}        
        _DetailMask("Detail Mask", 2D) = "white" {}
        _DetailNormalMapScale("Scale", Float) = 1.0
        _DetailNormalMap("Normal Map", 2D) = "bump" {}
        [Enum(UV0,0,UV1,1)] _UVSec ("UV Set for secondary textures", Float) = 0
        // Blending state
        [HideInInspector] _Mode ("__mode", Float) = 0.0
        [HideInInspector] _SrcBlend ("__src", Float) = 1.0
        [HideInInspector] _DstBlend ("__dst", Float) = 0.0
        [HideInInspector] _ZWrite ("__zw", Float) = 1.0
		[HideInInspector] _Cull("_Cull", Float) = 2.0
        //
        _Flatten ("Flatten amount", Range(0,1)) = 0.0
        _FlattenPlane ("Flatten plane", Vector) = (0,1,0,0)
        //
        _TintWindow1("Tint Window 1 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow2("Tint Window 2 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow3("Tint Window 3 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow4("Tint Window 4 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow5("Tint Window 5 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow6("Tint Window 6 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow7("Tint Window 7 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow8("Tint Window 8 (u1,v1,u2,v2)", Vector) = (0,0,0,0)

        _TintColour1("Tint colour 1", Color) = (1,1,1,0)
        _TintColour2("Tint colour 2", Color) = (1,1,1,0)
        _TintColour3("Tint colour 3", Color) = (1,1,1,0)
        _TintColour4("Tint colour 4", Color) = (1,1,1,0)
        _TintColour5("Tint colour 5", Color) = (1,1,1,0)
        _TintColour6("Tint colour 6", Color) = (1,1,1,0)
        _TintColour7("Tint colour 7", Color) = (1,1,1,0)
        _TintColour8("Tint colour 8", Color) = (1,1,1,0)

        _UVOffset1("UV Offset 1 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset2("UV Offset 2 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset3("UV Offset 3 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset4("UV Offset 4 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset5("UV Offset 5 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset6("UV Offset 6 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset7("UV Offset 7 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset8("UV Offset 8 (u1,v1,u2,v2))", Vector) = (0,0,0,0)

        //
		[HideInInspector] _RenderQueueOverride("Allow Render Queue Override", Float) = 0
        _AlphaOverride("Alpha override", Range(0,1)) = 0
        _RandomIntensity("Random intensity", Range(0,2)) = 0
        //
        _Clip1("Clip1", Vector) = (0,1,0,0)
        _Clip2("Clip2", Vector) = (0,1,0,0)
        _Clip3("Clip3", Vector) = (0,1,0,0)
        _Clip4("Clip4", Vector) = (0,1,0,0)
        _CupedClip("CupedClip", Float) = 0
        _Clip5("Clip5", Vector) = (0,0,1,0)
        _ClipDirection("Clip Direction", Float) = 1
	}
    SubShader
    {
        Tags { "RenderType" = "Opaque" "Queue" = "Geometry"}
        LOD 100
        Blend[_SrcBlend][_DstBlend]
        ZWrite[_ZWrite]

        UsePass "_Standard/S_StandardBoxClip_VF/S_StandardBoxClip_VF_LOD100"
	}
	//FallBack "Diffuse"
    CustomEditor "StandardShaderTintedGUI"
}
