Shader "Custom/RenderTextureMask" {
	Properties 
    {
		_RenderTex ("Render Texture (RGB)", 2D) = "white" {}
        _Mask ("Mask (A)", 2D) = "white" {}
	}
	SubShader 
    {
		Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ PIXELSNAP_ON
            #include "UnityCG.cginc"
            
            struct appdata_t
            {
                float4 vertex   : POSITION;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                float2 texcoord  : TEXCOORD0;
            };          

            v2f vert(appdata_t IN)
            {
                v2f OUT;
                OUT.vertex = UnityObjectToClipPos(IN.vertex);
                OUT.texcoord = IN.texcoord;
                #ifdef PIXELSNAP_ON
                OUT.vertex = UnityPixelSnap (OUT.vertex);
                #endif

                return OUT;
            }

            sampler2D _RenderTex;
            sampler2D _Mask;

            fixed4 SampleSpriteTexture (float2 uv)
            {
                fixed4 color = tex2D (_RenderTex, uv);
                fixed4 mask = tex2D(_Mask, uv);
                color.a = mask.a;
               
                return color;
            }

            fixed4 frag(v2f IN) : SV_Target
            {
                fixed4 c = SampleSpriteTexture (IN.texcoord);
                c.rgb *= c.a;
                return c;
            }
        ENDCG
        }
    }
}
