// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Custom/CursorShader"
{
	Properties
	{
		_BorderColor ("Color", Color) = (1,1,1,1)
		_FillColor ("Color", Color) = (1,1,1,1)
		_MainTex ("Albedo (RGB)", 2D) = "white" {}
	}

	SubShader
	{
		
		Tags { "Queue"="Transparent" }

		Pass
		{
			Cull Front
			ZWrite Off
			Blend SrcAlpha OneMinusSrcAlpha // use alpha blending

			CGPROGRAM 
			#include "UnityCG.cginc"
			#include "AutoLight.cginc"
			#include "Lighting.cginc"
			#pragma vertex vert 
			#pragma fragment frag

			float4 _BorderColor;
			float4 _FillColor;
			sampler2D _MainTex;
			uniform float4 _MainTex_ST;

			//-----------------------------------------------------------------------------------------------------------
			struct appdata
			{
		       	float4 vertex 		: POSITION;
		   		float3 normal 		: NORMAL;
		      	float4 tangent 		: TANGENT;
		      	float4 texcoord		: TEXCOORD0;
		  	};

			//-----------------------------------------------------------------------------------------------------------
         	struct v2f
            {
            	float4 pos 				: SV_POSITION;
            	float2 uv 				: TEXCOORD0;
            	fixed3 norm 			: TEXCOORD1;
            	LIGHTING_COORDS(2,3)               	
          	};

          	//-----------------------------------------------------------------------------------------------------------
            v2f vert(appdata v)
            {
                v2f o;
                
                o.pos = UnityObjectToClipPos (v.vertex);
                o.norm = normalize(mul(UNITY_MATRIX_M, float4(v.normal,0)).xyz);
                o.uv = TRANSFORM_TEX(v.texcoord.xy, _MainTex);


                TRANSFER_VERTEX_TO_FRAGMENT(o)            

                return o;
            }			

			//-----------------------------------------------------------------------------------------------------------
            fixed4 frag(v2f i) : SV_Target
            {
            	fixed4 test = tex2D(_MainTex, i.uv) * _BorderColor;


            	float p = (1.f - test.w);

            	test = lerp(_FillColor, test, test.w);

            	return test;
            }

         	ENDCG  
		}

     	Pass
      	{
			Cull Back 
			ZWrite Off
			Blend SrcAlpha OneMinusSrcAlpha // use alpha blending

			CGPROGRAM 
			#include "UnityCG.cginc"
			#include "AutoLight.cginc"
			#include "Lighting.cginc"
			#pragma vertex vert 
			#pragma fragment frag

			float4 _BorderColor;
			float4 _FillColor;
			sampler2D _MainTex;
			uniform float4 _MainTex_ST;

			//-----------------------------------------------------------------------------------------------------------
			struct appdata
			{
			   	float4 vertex 		: POSITION;
				float3 normal 		: NORMAL;
			  	float4 tangent 		: TANGENT;
			  	float4 texcoord		: TEXCOORD0;
			};

			//-----------------------------------------------------------------------------------------------------------
			struct v2f
			{
				float4 pos 				: SV_POSITION;
				float2 uv 				: TEXCOORD0;
				fixed3 norm 			: TEXCOORD1;
				LIGHTING_COORDS(2,3)               	
			};

			//-----------------------------------------------------------------------------------------------------------
			v2f vert(appdata v)
			{
			    v2f o;
			    
			    o.pos = UnityObjectToClipPos (v.vertex);
			    o.norm = normalize(mul(UNITY_MATRIX_M, float4(v.normal,0)).xyz);
			    o.uv = TRANSFORM_TEX(v.texcoord.xy, _MainTex);


			    TRANSFER_VERTEX_TO_FRAGMENT(o)            

			    return o;
			}			

			//-----------------------------------------------------------------------------------------------------------
			fixed4 frag(v2f i) : SV_Target
			{
				fixed4 test = tex2D(_MainTex, i.uv) * _BorderColor;


				float p = (1.f - test.w);

				test = lerp(_FillColor, test, test.w);

				return test;
			}
 
        	ENDCG  
      	}
	}
	FallBack "Diffuse"
}
