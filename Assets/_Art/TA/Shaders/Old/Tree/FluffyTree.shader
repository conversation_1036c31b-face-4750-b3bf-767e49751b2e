Shader "Unlit/FluffyTree"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _BumpMap ("Normal", 2D) = "white" {}
        _BumpScale ("Normal Scale", range(0,1)) = 1
        _LeavesIndices ("LeafIndices", 2D) = "white" {}
        _AlphaThreshold("Alpha Threshold", range(0,1)) = 0.5
        //_LeafColor0("Leaf Color", color) = (0,0,0,0)
        //_LeafColor1("Leaf Color 2", color) = (0,0,0,0)
        _ShadowColor("Shadow Color", color) = (0,0,0,0)
        _LightColor("Light Color", color) = (0,0,0,0)
        _yGradientScale("YGradient", float) = 1
        _WindNoise("Wind Noise", 3D) = "white" {}
        _WindIntensity("Wind Intensity", range(0,3)) = 1
        _WindDirection("Wind Direction", vector) = (0,0,0)
        _RandomScale("RandomScale", float) = .2
        [Toggle]_DebugWind("DebugWind", float) = 0
    }
    SubShader
    {

        LOD 100
       
        Pass
        {
            Name "DepthNormals"
            ZWrite On
            Cull Back
            ZTest LEqual
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag


            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normalOS : NORMAL;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 normalWS : TEXCOORD1;
                float3 positonWS : TEXCOORD2;
            };

            sampler2D _MainTex;
            sampler2D _LeavesIndices;
            float4 _MainTex_ST;
            float _AlphaThreshold;
            half4 _Color0, _Color1, _Color2;
            float _RandomScale;

            float _yGradientScale;
            sampler3D _WindNoise;
            float4 _WindDirection;
            float _WindIntensity;

            float4x4 _LightToWorld;

            v2f vert(appdata v)
            {
               v2f o;
                o.positonWS = TransformObjectToWorld(v.vertex);
                o.normalWS = TransformObjectToWorldNormal(v.normalOS);

                float2 uv = v.uv * 2 - 1;
                float3 u = float3(uv.xy, 0);
                u = mul(unity_CameraToWorld, u);
                u = TransformWorldToObjectDir(u);
                v.vertex += float4(u * 2, 0);

                float3 windDir = normalize(TransformWorldToObjectDir(_WindDirection));
                float dotWindMain = 1 - max(dot(v.normalOS, -windDir) + .5, 0.0);
                float dotWind = 1 - max(dot(v.normalOS, -windDir) + 1.5, 0.0);
                float mainWind = tex3Dlod(_WindNoise, float4(o.positonWS * .005 - _WindDirection * _Time.x * .5, 0));
                float highFrequencyWind = tex3Dlod(
                    _WindNoise, float4(o.positonWS * .05 - _WindDirection * _Time.x * 4, 0));
                float3 windOffset = windDir * mainWind * dotWindMain * 1.5;
                windOffset += windDir * highFrequencyWind * dotWind * .5;
                windOffset *= _WindIntensity;
                v.vertex += float4(windOffset, 0);


                o.vertex = TransformObjectToHClip(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }


            float4 frag(v2f i) : SV_Target
            {
                half4 c = tex2D(_MainTex, i.uv * 3.33);
                if (c.a < _AlphaThreshold) discard;
                return 1;
            }
            ENDHLSL

        }

        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag


            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normalOS : NORMAL;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 normalWS : TEXCOORD1;
                float3 positonWS : TEXCOORD2;
            };

            sampler2D _MainTex;
            sampler2D _LeavesIndices;
            float4 _MainTex_ST;
            float _AlphaThreshold;
            half4 _Color0, _Color1, _Color2;
            float _RandomScale;

            float _yGradientScale;
            sampler3D _WindNoise;
            float4 _WindDirection;
            float _WindIntensity;

            float4x4 _LightToWorld;

            v2f vert(appdata v)
            {
               v2f o;
                o.positonWS = TransformObjectToWorld(v.vertex);
                o.normalWS = TransformObjectToWorldNormal(v.normalOS);

                float2 uv = v.uv * 2 - 1;
                float3 u = float3(uv.xy, 0);
                u = mul(_LightToWorld, u);
                u = TransformWorldToObjectDir(u);
                v.vertex += float4(u * 2, 0);

                float3 windDir = normalize(TransformWorldToObjectDir(_WindDirection));
                float dotWindMain = 1 - max(dot(v.normalOS, -windDir) + .5, 0.0);
                float dotWind = 1 - max(dot(v.normalOS, -windDir) + 1.5, 0.0);
                float mainWind = tex3Dlod(_WindNoise, float4(o.positonWS * .005 - _WindDirection * _Time.x * .5, 0));
                float highFrequencyWind = tex3Dlod(
                    _WindNoise, float4(o.positonWS * .05 - _WindDirection * _Time.x * 4, 0));
                float3 windOffset = windDir * mainWind * dotWindMain * 1.5;
                windOffset += windDir * highFrequencyWind * dotWind * .5;
                windOffset *= _WindIntensity;
                v.vertex += float4(windOffset, 0);


                o.vertex = TransformObjectToHClip(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }


            float4 frag(v2f i) : SV_Target
            {
                half4 c = tex2D(_MainTex, i.uv * 3.33);
                if (c.a < _AlphaThreshold) discard;
                return 1;
            }
            ENDHLSL
        }

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fog

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_PremiumRewards.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"

            #define _SHADOWS_SOFT
            #define _MAIN_LIGHT_SHADOWS
            #define _MAIN_LIGHT_SHADOWS_CASCADES
            #define MAIN_LIGHT_CALCULATE_SHADOWS
            #define _MAIN_LIGHT_SHADOWS_SCREEN

            struct appdata_tree
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
                float4 tangent : TANGENT;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 normalWS : TEXCOORD1;
                float3 positonWS : TEXCOORD2;
                _NTB_VARS(3,4,5)
            };

            sampler2D _MainTex;
            sampler2D _LeavesIndices;
            float4 _MainTex_ST;
            sampler2D _BumpMap;
            float _BumpScale;
            float _AlphaThreshold;
            half4 _LeafColor0, _LeafColor1, _ShadowColor, _LightColor;
            float _RandomScale;

            float _yGradientScale;
            sampler3D _WindNoise;
            float3 _WindDirection;
            float _WindIntensity;

            v2f vert(appdata_tree v)
            {
                v2f o;
                o.positonWS = TransformObjectToWorld(v.vertex);
                o.normalWS = TransformObjectToWorldNormal(v.normal);
                
                float2 uv = v.uv * 2 - 1;
                float3 u = float3(uv.xy, 0);
                u = mul(unity_CameraToWorld, u);
                u = TransformWorldToObjectDir(u);
                v.vertex += float4(u * 2, 0);
                
                _CALC_NTB(o, v);
                

                float3 windDir = normalize(TransformWorldToObjectDir(_WindDirection));
                float dotWindMain = 1 - max(dot(v.normal, -windDir) + .5, 0.0);
                float dotWind = 1 - max(dot(v.normal, -windDir) + 1.5, 0.0);
                float mainWind = tex3Dlod(_WindNoise, float4(o.positonWS * .005 - _WindDirection * _Time.x * .5, 0));
                float highFrequencyWind = tex3Dlod(
                    _WindNoise, float4(o.positonWS * .05 - _WindDirection * _Time.x * 4, 0));
                float3 windOffset = windDir * mainWind * dotWindMain * 1.5;
                windOffset += windDir * highFrequencyWind * dotWind * .5;
                windOffset *= _WindIntensity;
                v.vertex += float4(windOffset, 0);


                o.vertex = TransformObjectToHClip(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }


            float _DebugWind;

            float rand_1_05(in float2 uv)
            {
                float2 noise = (frac(sin(dot(uv, float2(12.9898, 78.233) * 2.0)) * 43758.5453));
                return abs(noise.x + noise.y) * 0.5;
            }

            float4 frag(v2f i) : SV_Target
            {
                half4 c = tex2D(_MainTex, i.uv * 5);
                if (c.a < _AlphaThreshold) discard;

                //float3 normal = normalize(i.normalWS);
                half3 worldNormal = lerp(half3(0,0,1), UnpackNormal(tex2D(_BumpMap, i.uv * 5)), _BumpScale);
                _WORLD_NORMAL_FRAG(i);
                float3 normal = worldNormal;
                
                if (_DebugWind)
                {
                    float3 windDir = normalize(_WindDirection);
                    float dotWindMain = 1 - max(dot(normal, windDir) + .5, 0.0);
                    float dotWind = 1 - max(dot(normal, windDir) + 1.5, 0.0);
                    float mainWind = tex3Dlod(_WindNoise, float4(i.positonWS * .005 - windDir * _Time.x * .5, 0));
                    float highFrequencyWind =
                        tex3Dlod(_WindNoise, float4(i.positonWS * .05 - windDir * _Time.x * 4, 0));
                    float3 windOffset = windDir * mainWind * dotWindMain * 1.5;
                    windOffset += windDir * highFrequencyWind * dotWind * .3;
                    windOffset *= _WindIntensity;
                    return float4(mainWind * dotWindMain * 1.5, highFrequencyWind * dotWind, 0, 0);
                }

                float index =0;// tex2D(_LeavesIndices, i.uv * 5);
                float random = rand_1_05(index * 10);

                float3 viewDir = normalize(i.positonWS - _WorldSpaceCameraPos);
                float3 reflectDir = reflect(worldNormal, -_MainLightPosition);
                float vDotR = max(dot(viewDir, reflectDir), 0.0);
                vDotR = pow(vDotR, abs(random * 128) + 1);
                float fresnel = max(dot(viewDir, normal) + 0.3, 0);
                float vDotL = max(dot(viewDir, _MainLightPosition), 0.0);
                float sss = pow(vDotL, 8);
                sss *= fresnel;

                // if(fresnel>0.5)discard;
                float nDotL = max(dot(normal, _MainLightPosition), 0.0);
                float skyDot = saturate(dot(normal, float3(0, 1, 0)) - .6);
                float4 shadowCoord = TransformWorldToShadowCoord(i.positonWS);
                ShadowSamplingData shadowSamplingData = GetMainLightShadowSamplingData();
                half4 shadowParams = GetMainLightShadowParams();
                float shadowAtten = SampleShadowmap(
                    TEXTURE2D_ARGS(_MainLightShadowmapTexture, sampler_MainLightShadowmapTexture), shadowCoord,
                    shadowSamplingData, shadowParams, false);

                // return shadowAtten;
                // return nDotL;
                shadowAtten = saturate(shadowAtten + .0);

                float s = max((nDotL + index * _yGradientScale) * shadowAtten, 0.0);
                //float4 leafColor = lerp(_LeafColor0, _LeafColor1, random);
                float4 leafColor = c * (.8 + .4 * random);
                float3 result = lerp(leafColor * _ShadowColor, leafColor, s).rgb;
                // result += skyDot * _Color2.rgb;
                result += sss * _LightColor.rgb * 3;
                result += vDotR * nDotL * _LightColor * 1;

                return float4(result, 0);
            }
            ENDHLSL
        }
    }
}