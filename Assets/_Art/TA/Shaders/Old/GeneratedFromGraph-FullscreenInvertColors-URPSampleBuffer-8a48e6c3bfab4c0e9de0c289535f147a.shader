Shader "Shader Graphs/FullscreenInvertColors"
{
    Properties
    {
        [HideInInspector][NoScaleOffset]unity_Lightmaps("unity_Lightmaps", 2DArray) = "" {}
        [HideInInspector][NoScaleOffset]unity_LightmapsInd("unity_LightmapsInd", 2DArray) = "" {}
        [HideInInspector][NoScaleOffset]unity_ShadowMasks("unity_ShadowMasks", 2DArray) = "" {}

        _Color ("Color", color) = (0,0,0,0)
        _Color0 ("Color", color) = (0,0,0,0)
        _SunDot("Sun Dot", float) = 1
        _Cube("Cube", cube) = "white"{}
        _Texture("Texture", 2D) = "white" {}
        _Worley("Worley", 3D) = "black"{}

        _CloudShadowColorMultiplier("_CloudShadowColorMultiplier", color) = (0,0,0,0)

        _A("A", range(1,10) ) = 0
        _B("B", range(0,.01)) = 0


        _CloudData0("Cloud Data 0 (x: Scale | y: Intensity | z: Speed)", vector) = (.1,.6,0.3,0)
        _CloudData1("Cloud Data 1 (x: Scale | y: Intensity | z: Speed)", vector) = (.3,.2,0.5,0)
        _CloudData2("Cloud Data 2 (x: Scale | y: Intensity | z: Speed)", vector) = (1,.1,.1,0)

        _CloudSTx("_CloudSTx", range(0,1) ) = 0
        _CloudSTy("_CloudSTy", range(0,1)) = 0
        _PlaneY("Plane Y", float) = 110


        [Header(God Rays)][Space]
        _GodRaysDistance("God Rays Distance", float) = 100
        _MaxGodRaysStep("God Rays Max Step", range(100,500)) = 10
        _GodRaysFactor("GodRaysImpact", range(0.1,3)) = 1

    }
    SubShader
    {
        Tags
        {
            "RenderPipeline"="UniversalPipeline"
            // RenderType: <None>
            // Queue: <None>
            // DisableBatching: <None>
            "ShaderGraphShader"="true"
            "ShaderGraphTargetId"="UniversalFullscreenSubTarget"
        }
        Pass
        {
            Name "DrawProcedural"

            // Render State
            Cull Off
            Blend Off
            ZTest Off
            ZWrite Off

            // Debug
            // <None>

            // --------------------------------------------------
            // Pass

            HLSLPROGRAM
            // Pragmas
            #pragma target 3.0
            #pragma vertex vert
            #pragma fragment frag
            // #pragma enable_d3d11_debug_symbols

            /* WARNING: $splice Could not find named fragment 'DotsInstancingOptions' */
            /* WARNING: $splice Could not find named fragment 'HybridV1InjectedBuiltinProperties' */

            // Keywords
            // PassKeywords: <None>
            // GraphKeywords: <None>

            #define FULLSCREEN_SHADERGRAPH

            // Defines
            #define ATTRIBUTES_NEED_TEXCOORD0
            #define ATTRIBUTES_NEED_TEXCOORD1
            #define ATTRIBUTES_NEED_VERTEXID
            #define VARYINGS_NEED_POSITION_WS
            #define VARYINGS_NEED_TEXCOORD0
            #define VARYINGS_NEED_TEXCOORD1

            // Force depth texture because we need it for almost every nodes
            // TODO: dependency system that triggers this define from position or view direction usage
            #define REQUIRE_DEPTH_TEXTURE
            #define REQUIRE_NORMAL_TEXTURE

            /* WARNING: $splice Could not find named fragment 'PassInstancing' */
            #define SHADERPASS SHADERPASS_DRAWPROCEDURAL

            // custom interpolator pre-include
            /* WARNING: $splice Could not find named fragment 'sgci_CustomInterpolatorPreInclude' */

            // Includes
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Texture.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/TextureStack.hlsl"
            #include "Packages/com.unity.shadergraph/Editor/Generation/Targets/Fullscreen/Includes/FullscreenShaderPass.cs.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Input.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/UnityInstancing.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderGraphFunctions.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/SpaceTransforms.hlsl"
            #include "Packages/com.unity.shadergraph/ShaderGraphLibrary/Functions.hlsl"

            // --------------------------------------------------
            // Structs and Packing

            // custom interpolators pre packing
            /* WARNING: $splice Could not find named fragment 'CustomInterpolatorPrePacking' */

            struct Attributes
            {
                #if UNITY_ANY_INSTANCING_ENABLED
             uint instanceID : INSTANCEID_SEMANTIC;
                #endif
                uint vertexID : VERTEXID_SEMANTIC;
            };

            struct SurfaceDescriptionInputs
            {
                float3 WorldSpacePosition;
                float4 ScreenPosition;
                float2 NDCPosition;
                float2 PixelPosition;
                float3 camRelativeWorldPos;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4 texCoord0;
                float4 texCoord1;
                #if UNITY_ANY_INSTANCING_ENABLED
             uint instanceID : CUSTOM_INSTANCE_ID;
                #endif
                #if (defined(UNITY_STEREO_MULTIVIEW_ENABLED)) || (defined(UNITY_STEREO_INSTANCING_ENABLED) && (defined(SHADER_API_GLES3) || defined(SHADER_API_GLCORE)))
             uint stereoTargetEyeIndexAsBlendIdx0 : BLENDINDICES0;
                #endif
                #if (defined(UNITY_STEREO_INSTANCING_ENABLED))
             uint stereoTargetEyeIndexAsRTArrayIdx : SV_RenderTargetArrayIndex;
                #endif
            };

            struct VertexDescriptionInputs
            {
            };

            struct PackedVaryings
            {
                float4 positionCS : SV_POSITION;
                float4 texCoord0 : INTERP0;
                float4 texCoord1 : INTERP1;
                #if UNITY_ANY_INSTANCING_ENABLED
             uint instanceID : CUSTOM_INSTANCE_ID;
                #endif
                #if (defined(UNITY_STEREO_MULTIVIEW_ENABLED)) || (defined(UNITY_STEREO_INSTANCING_ENABLED) && (defined(SHADER_API_GLES3) || defined(SHADER_API_GLCORE)))
             uint stereoTargetEyeIndexAsBlendIdx0 : BLENDINDICES0;
                #endif
                #if (defined(UNITY_STEREO_INSTANCING_ENABLED))
             uint stereoTargetEyeIndexAsRTArrayIdx : SV_RenderTargetArrayIndex;
                #endif
            };

            PackedVaryings PackVaryings(Varyings input)
            {
                PackedVaryings output;
                ZERO_INITIALIZE(PackedVaryings, output);
                output.positionCS = input.positionCS;
                output.texCoord0.xyzw = input.texCoord0;
                output.texCoord1.xyzw = input.texCoord1;
                #if UNITY_ANY_INSTANCING_ENABLED
            output.instanceID = input.instanceID;
                #endif
                #if (defined(UNITY_STEREO_MULTIVIEW_ENABLED)) || (defined(UNITY_STEREO_INSTANCING_ENABLED) && (defined(SHADER_API_GLES3) || defined(SHADER_API_GLCORE)))
            output.stereoTargetEyeIndexAsBlendIdx0 = input.stereoTargetEyeIndexAsBlendIdx0;
                #endif
                #if (defined(UNITY_STEREO_INSTANCING_ENABLED))
            output.stereoTargetEyeIndexAsRTArrayIdx = input.stereoTargetEyeIndexAsRTArrayIdx;
                #endif
                return output;
            }

            Varyings UnpackVaryings(PackedVaryings input)
            {
                Varyings output;
                output.positionCS = input.positionCS;
                output.texCoord0 = input.texCoord0.xyzw;
                output.texCoord1 = input.texCoord1.xyzw;
                #if UNITY_ANY_INSTANCING_ENABLED
            output.instanceID = input.instanceID;
                #endif
                #if (defined(UNITY_STEREO_MULTIVIEW_ENABLED)) || (defined(UNITY_STEREO_INSTANCING_ENABLED) && (defined(SHADER_API_GLES3) || defined(SHADER_API_GLCORE)))
            output.stereoTargetEyeIndexAsBlendIdx0 = input.stereoTargetEyeIndexAsBlendIdx0;
                #endif
                #if (defined(UNITY_STEREO_INSTANCING_ENABLED))
            output.stereoTargetEyeIndexAsRTArrayIdx = input.stereoTargetEyeIndexAsRTArrayIdx;
                #endif
                return output;
            }


            CBUFFER_START(UnityPerMaterial)
            CBUFFER_END

            float _FlipY;

            TEXTURE2D_X(_BlitTexture);

            float4 BlitSource(float2 uv)
            {
                uint2 pixelCoords = uint2(uv * _ScreenSize.xy);
                return LOAD_TEXTURE2D_X_LOD(_BlitTexture, pixelCoords, 0);
            }

            struct SurfaceDescription
            {
                float3 BaseColor;
                float Alpha;
            };

            float3 RayPlaneIntersection(float3 rayOrigin, float3 rayDirection, float3 planePoint, float3 planeNormal)
            {
                float denominator = dot(rayDirection, planeNormal);

                // Check for parallel or nearly parallel ray and plane
                if (abs(denominator) < 0.0001)
                    return rayOrigin;

                float t = dot(planePoint - rayOrigin, planeNormal) / denominator;

                return t;
            }

            half4 _CloudShadowColorMultiplier;
            half4 _Color;
            half4 _Color0;
            float _SunDot;
            samplerCUBE _Cube;
            sampler2D _Texture;
            float _A;
            float _B;

            float _CloudSTx;
            float _CloudSTy;
            sampler3D _Worley;
            float _PlaneY;

            float _GodRaysDistance;
            float _MaxGodRaysStep;
            float _GodRaysFactor;

            float3 RotateAroundYInDegrees(float3 vertex, float degrees)
            {
                float alpha = degrees * PI / 180.0;
                float sina, cosa;
                sincos(alpha, sina, cosa);
                float2x2 m = float2x2(cosa, -sina, sina, cosa);
                return float3(mul(m, vertex.xz), vertex.y).xzy;
            }

            float3 SampleClipToWorld(float3 _clipPos)
            {
                float2 uv = _clipPos.xy / _ScaledScreenParams.xy;
                #if UNITY_REVERSED_Z
                real depth = SampleSceneDepth(uv);
                #else
                    real depth = lerp(UNITY_NEAR_CLIP_VALUE, 1, SampleSceneDepth(UV));
                #endif
                float3 worldPos = ComputeWorldSpacePosition(uv, depth, UNITY_MATRIX_I_VP);
                return worldPos;
            }

            float3 ComputeWorldPos(float3 viewDir, float depth)
            {
                float eyeDepth = LinearEyeDepth(depth, _ZBufferParams);
                float3 cameraDirection = -1 * mul(
                    UNITY_MATRIX_M, transpose(mul(UNITY_MATRIX_I_M, UNITY_MATRIX_I_V))[2].xyz);
                float dirDotview = dot(cameraDirection, viewDir);
                float3 v = (viewDir / dirDotview) * eyeDepth;
                float3 positionWS = v + _WorldSpaceCameraPos;
                return positionWS;
            }

            float4 _CloudData0; // x: scale, y: intensity(0,1), z: Speed
            float4 _CloudData1;
            float4 _CloudData2;

            float ComputeCloudShadowAttenuation(float3 positionWS)
            {
                positionWS -= _MainLightPosition * positionWS.y;
                float4 p1 = float4(positionWS.x + _Time.y * _CloudData0.z, 0, positionWS.z, 0);
                float4 p2 = float4(positionWS.x + _Time.y * _CloudData1.z, 0, positionWS.z, 0);
                float4 p3 = float4(positionWS.x + _Time.y * _CloudData2.z, 0, positionWS.z, 0);
                float cloud = tex3Dlod(_Worley, p1 * (_CloudData0.x * 0.01)) * _CloudData0.y;
                cloud += tex3Dlod(_Worley, p2 * (_CloudData1.x * 0.01)) * _CloudData1.y;
                cloud += tex3Dlod(_Worley, p3 * (_CloudData2.x * 0.01)) * _CloudData2.y;
                cloud = smoothstep(_CloudSTx, _CloudSTy, cloud);
                return cloud;
            }

            //https://gist.github.com/h3r/3a92295517b2bee8a82c1de1456431dc
            float rand3dTo1d(float3 value, float3 dotDir = float3(12.9898, 78.233, 37.719))
            {
                float3 smallValue = sin(value);
                float random = dot(smallValue, dotDir);
                random = frac(sin(random) * 143758.5453);
                return random - .5;
            }

            //https://gist.github.com/h3r/3a92295517b2bee8a82c1de1456431dc
            float3 rand3dTo3d(float3 value)
            {
                return float3(
                    rand3dTo1d(value, float3(12.989, 78.233, 37.719)),
                    rand3dTo1d(value, float3(39.346, 11.135, 83.155)),
                    rand3dTo1d(value, float3(73.156, 52.235, 09.151))
                );
            }


            SurfaceDescription SurfaceDescriptionFunction(SurfaceDescriptionInputs IN)
            {
                SurfaceDescription surface;
                surface.Alpha = 1;

                float4 source = BlitSource(float4(IN.NDCPosition.xy, 0, 0).xy);

                #if UNITY_REVERSED_Z
                real depth = SampleSceneDepth(IN.ScreenPosition.xy / IN.ScreenPosition.w);
                #else
                    real depth = lerp(UNITY_NEAR_CLIP_VALUE, 1, SampleSceneDepth(UV));
                #endif

                float3 viewDir = normalize(IN.WorldSpacePosition - _WorldSpaceCameraPos);
                float3 positionWS = ComputeWorldPos(viewDir, depth);

                float3 planePoint = float3(0.0, 89.0, 0.0);
                float3 planeNormal = float3(0.0, 1.0, 0.0);
                float waterPlaneDist = RayPlaneIntersection(_WorldSpaceCameraPos, viewDir, planePoint, planeNormal);

                float dotDir = pow(max(dot(_MainLightPosition, viewDir), 0), _SunDot);
                half4 fogColor = lerp(_Color, _Color0, dotDir);

                float3 p = _WorldSpaceCameraPos;
                float depthWS = LinearEyeDepth(depth, _ZBufferParams);
                float stepDist = _GodRaysDistance / _MaxGodRaysStep;
                float3 viewDirStep = viewDir * stepDist;
                float density = 0;

                int stepCount = 0;
                float optimizedFactor = 1.0 / _MaxGodRaysStep * _GodRaysFactor;
                float dst = 0;
                while (stepCount < _MaxGodRaysStep && dst < depthWS && dst < waterPlaneDist)
                {
                    dst = length(viewDirStep * stepCount);
                    float depthMask = depthWS < _ProjectionParams.z; //Avoid computing clouds after far plane
                    float cloudSdwAtt = max(1 - ComputeCloudShadowAttenuation(p), 0.15);
                    density += cloudSdwAtt * (1 - exp(-dst)) * optimizedFactor * depthMask;

                    p += viewDirStep;
                    stepCount++;
                }

                //Lerp between next iteration to smooth out the steps
                dst = length(viewDirStep * stepCount);
                float cloudSdwAtt = max(1 - ComputeCloudShadowAttenuation(p), 0.15);
                float nextDensity = density + cloudSdwAtt * (1 - exp(-dst)) * optimizedFactor;
                depthWS = min(waterPlaneDist, depthWS);
                float t = frac(depthWS / (stepDist));

                density = lerp(density, nextDensity, t);
                // fogAmount = lerp(accDensity, accDensity + nextStep, frac(distance(p, p+ viewDirStep)));
                // surface.BaseColor = fogAmount;
                // return surface;
                // accDensity *= _GodRaysFactor;

                float depthMask = depthWS > _ProjectionParams.z; //Avoid computing clouds after far plane
                float cloudShadowAtt = ComputeCloudShadowAttenuation(positionWS);
                cloudShadowAtt = saturate(cloudShadowAtt - depthMask);


                source = lerp(source, source * _CloudShadowColorMultiplier, cloudShadowAtt);
                source = lerp(source, fogColor, saturate(density));

                surface.BaseColor = source;
                // surface.BaseColor = accDensity;
                // surface.BaseColor = depthMask;

                return surface;
            }

            SurfaceDescriptionInputs BuildSurfaceDescriptionInputs(Varyings input)
            {
                SurfaceDescriptionInputs output;
                ZERO_INITIALIZE(SurfaceDescriptionInputs, output);

                float3 normalWS = SHADERGRAPH_SAMPLE_SCENE_NORMAL(input.texCoord0.xy);
                float4 tangentWS = float4(0, 1, 0, 0); // We can't access the tangent in screen space


                float3 viewDirWS = normalize(input.texCoord1.xyz);
                float linearDepth = LinearEyeDepth(SHADERGRAPH_SAMPLE_SCENE_DEPTH(input.texCoord0.xy), _ZBufferParams);
                float3 cameraForward = -UNITY_MATRIX_V[2].xyz;
                float camearDistance = linearDepth / dot(viewDirWS, cameraForward);
                float3 positionWS = viewDirWS * camearDistance + GetCameraPositionWS();


                output.WorldSpacePosition = positionWS;
                output.ScreenPosition = float4(input.texCoord0.xy, 0, 1);
                output.NDCPosition = input.texCoord0.xy;
                output.camRelativeWorldPos = mul(unity_ObjectToWorld, float4(input.positionCS.xyz, 1.0)).xyz -
                    _WorldSpaceCameraPos;


                #if defined(SHADER_STAGE_FRAGMENT) && defined(VARYINGS_NEED_CULLFACE)
        #define BUILD_SURFACE_DESCRIPTION_INPUTS_OUTPUT_FACESIGN output.FaceSign =                    IS_FRONT_VFACE(input.cullFace, true, false);
                #else
                #define BUILD_SURFACE_DESCRIPTION_INPUTS_OUTPUT_FACESIGN
                #endif
                #undef BUILD_SURFACE_DESCRIPTION_INPUTS_OUTPUT_FACESIGN

                return output;
            }

            #include "Packages/com.unity.shadergraph/Editor/Generation/Targets/Fullscreen/Includes/FullscreenCommon.hlsl"
            #include "Packages/com.unity.shadergraph/Editor/Generation/Targets/Fullscreen/Includes/FullscreenDrawProcedural.hlsl"
            ENDHLSL
        }
    }
    CustomEditor "UnityEditor.Rendering.Fullscreen.ShaderGraph.FullscreenShaderGUI"
    FallBack "Hidden/Shader Graph/FallbackError"
}