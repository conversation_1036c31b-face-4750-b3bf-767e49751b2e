using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace UnityEditor
{
    internal class StandardShaderWindowOutlineGUI : StandardShaderPatternedGUI 
    {
        MaterialProperty fadeColour1 = null;
        MaterialProperty fadeColour2 = null;
        MaterialProperty fadeSpeed = null;

        public override void FindProperties(MaterialProperty[] props)
        {
            base.FindProperties(props);

            fadeColour1 = FindProperty("_FadeColour1", props);
            fadeColour2 = FindProperty("_FadeColour2", props);
            fadeSpeed = FindProperty("_FadeSpeed", props);
        }

        public override void ShaderPropertiesGUI(Material material)
        {
            // Primary properties
            GUILayout.Label("Glow Options", EditorStyles.boldLabel);
            fadeColour1.colorValue = EditorGUILayout.ColorField("Fade Colour 1", fadeColour1.colorValue);
            fadeColour2.colorValue = EditorGUILayout.ColorField("Fade Colour 2", fadeColour2.colorValue);
            fadeSpeed.floatValue = EditorGUILayout.FloatField("Fade Speed", fadeSpeed.floatValue);

            GUILayout.Space(10);

            base.ShaderPropertiesGUI(material);
        }
    }
}
