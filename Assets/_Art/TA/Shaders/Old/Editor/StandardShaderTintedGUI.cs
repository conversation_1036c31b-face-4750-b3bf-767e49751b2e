// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

namespace UnityEditor
{
    internal class StandardShaderTintedGUI : ShaderGUI
    {
        private enum WorkflowMode
        {
            Specular,
            Metallic,
            Dielectric
        }

        public enum BlendMode
        {
            Opaque,
			DepthNormalsExempt,
			Burning,
			TransparentCutout,
			CSG,
			CSGCuboid
		}

        public enum SmoothnessMapChannel
        {
            SpecularMetallicAlpha,
            AlbedoAlpha,
        }

        protected static class Styles
        {
            public static GUIContent uvSetLabel = new GUIContent("UV Set");

            public static GUIContent albedoText = new GUIContent("Albedo", "Albedo (RGB) and Transparency (A)");
			public static GUIContent alphaCutoffText = new GUIContent("Alpha Cutoff", "Threshold for alpha cutoff");
            public static GUIContent specularMapText = new GUIContent("Specular", "Specular (RGB) and Smoothness (A)");
            public static GUIContent metallicMapText = new GUIContent("Metallic", "Metallic (R) and Smoothness (A)");
            public static GUIContent smoothnessText = new GUIContent("Smoothness", "Smoothness value");
            public static GUIContent smoothnessScaleText = new GUIContent("Smoothness", "Smoothness scale factor");
            public static GUIContent smoothnessMapChannelText = new GUIContent("Source", "Smoothness texture and channel");
            public static GUIContent highlightsText = new GUIContent("Specular Highlights", "Specular Highlights");
            public static GUIContent reflectionsText = new GUIContent("Reflections", "Glossy Reflections");
            public static GUIContent normalMapText = new GUIContent("Normal Map", "Normal Map");
            public static GUIContent heightMapText = new GUIContent("Height Map", "Height Map (G)");
            public static GUIContent occlusionText = new GUIContent("Occlusion", "Occlusion (G)");
            public static GUIContent emissionText = new GUIContent("Color", "Emission (RGB)");
            public static GUIContent detailMaskText = new GUIContent("Detail Mask", "Mask for Secondary Maps (A)");
            public static GUIContent detailAlbedoText = new GUIContent("Detail Albedo x2", "Albedo (RGB) multiplied by 2");
            public static GUIContent detailNormalMapText = new GUIContent("Normal Map", "Normal Map");

            public static string primaryMapsText = "Main Maps";
            public static string secondaryMapsText = "Secondary Maps";
            public static string forwardText = "Forward Rendering Options";
            public static string renderingMode = "Rendering Mode";
            public static string advancedText = "Advanced Options";
			public static GUIContent emissiveWarning = new GUIContent("Emissive value is animated but the material has not been configured to support emissive. Please make sure the material itself has some amount of emissive.");
            public static readonly string[] blendNames = Enum.GetNames(typeof(BlendMode));
			public static string cullMode = "Culling Mode";
			public static readonly string[] cullNames = Enum.GetNames(typeof(CullMode));

			public static string surfaceTypeLabel = "Surface Type";
			public static readonly string[] surfaceTypeNames = new string[4] { "Wood", "Cloth", "Metal","Food" };

			//public static GUIContent alphaOverrideText = new GUIContent("Alpha Override", "Alpha Override");
			public static GUIContent randomIntensityText = new GUIContent("Random Intensity", "Random Intensity");
			public static GUIContent xrayColourText = new GUIContent("XRay Colour", "XRay Colour");
			public static GUIContent allowRenderQueueOverrideText = new GUIContent("Allow Render Queue Override - Caution", "Allow Render Queue Override - Caution");


			public static GUIContent colourWindow = new GUIContent();
            
            
            public static GUIContent altTextureAtlasText = new GUIContent("Alt Texture Atlas", "Alternative Texture Atlas");
            public static GUIContent altTextureText = new GUIContent("Alt Texture", "Alternative texture");
        }

        MaterialProperty blendMode = null;
        MaterialProperty albedoMap = null;
        MaterialProperty albedoColor = null;
        MaterialProperty alphaCutoff = null;
        MaterialProperty specularMap = null;
        MaterialProperty specularColor = null;
        MaterialProperty metallicMap = null;
        MaterialProperty metallic = null;
        MaterialProperty smoothness = null;
        MaterialProperty smoothnessScale = null;
        MaterialProperty smoothnessMapChannel = null;
        MaterialProperty highlights = null;
        MaterialProperty reflections = null;
        MaterialProperty bumpScale = null;
        MaterialProperty bumpMap = null;
        MaterialProperty occlusionStrength = null;
        MaterialProperty occlusionMap = null;
        MaterialProperty heigtMapScale = null;
        MaterialProperty heightMap = null;
        MaterialProperty emissionColorForRendering = null;
        MaterialProperty emissionMap = null;
        MaterialProperty detailMask = null;
        MaterialProperty detailAlbedoMap = null;
        MaterialProperty detailNormalMapScale = null;
        MaterialProperty detailNormalMap = null;
        MaterialProperty uvSetSecondary = null;

		MaterialProperty alphaOverride = null;
		MaterialProperty randomIntensity = null;
		MaterialProperty xrayColour = null;
		MaterialProperty[] tintWindows = new MaterialProperty[8];
		MaterialProperty[] tintColours = new MaterialProperty[8];
		MaterialProperty[] premiumVars = new MaterialProperty[8];
		MaterialProperty[] uvOffsets = new MaterialProperty[8];

		MaterialProperty textureContribution;
		MaterialProperty occludedColour;
		MaterialProperty clipShadowsCSG;
		MaterialProperty allowRenderQueueOverride;
		MaterialProperty cullMode = null;


		protected MaterialEditor m_MaterialEditor;
        WorkflowMode m_WorkflowMode = WorkflowMode.Specular;
		BlendMode m_BlendMode = BlendMode.Opaque;
        ColorPickerHDRConfig m_ColorPickerHDRConfig = new ColorPickerHDRConfig(0f, 99f, 1 / 99f, 3f);

        bool m_FirstTimeApply = true;

		public static bool EditKeyword(Material _material, string _propertyName, string _propertyDefine, string _propertyLabel, bool _showSeparator = true)
		{
			if (_material.HasProperty(_propertyName))
			{
				if (_showSeparator)
					GUILayout.Label("------------", EditorStyles.whiteMiniLabel);
				bool b = _material.IsKeywordEnabled(_propertyDefine);
				EditorGUI.BeginChangeCheck();
				b = EditorGUILayout.Toggle(_propertyLabel, b);
				if (EditorGUI.EndChangeCheck())
				{
					// enable or disable the keyword based on checkbox
					if (b)
					{
						_material.EnableKeyword(_propertyDefine);
						_material.SetFloat(_propertyName, 1);
					}
					else
					{
						_material.DisableKeyword(_propertyDefine);
						_material.SetFloat(_propertyName, 0);
					}
				}
				return true;
			}
			return false;
		}

		public static void SetupMaterialWithCullMode(Material material, CullMode cullMode)
		{
			material.SetInt("_Cull", (int)cullMode);
		}

		public static void SetupMaterialWithBlendMode(Material material, BlendMode blendMode)
		{
			bool renderQueueOverride = material.GetFloat("_RenderQueueOverride") > 0;
			if (!renderQueueOverride)
			{
				material.renderQueue = VerifyCurrentRenderQueue(material.renderQueue, -1, (int)UnityEngine.Rendering.RenderQueue.AlphaTest);
			}

			switch (blendMode)
			{
				case BlendMode.Opaque:
					material.SetOverrideTag("RenderType", "Opaque");
					material.DisableKeyword("_ALPHATEST_ON");
					break;
				
				case BlendMode.Burning:
					material.SetOverrideTag("RenderType", "Burning");
					material.DisableKeyword("_ALPHATEST_ON");
					break;
				case BlendMode.DepthNormalsExempt:
					material.SetOverrideTag("RenderType", "DepthNormalsExempt");
					material.DisableKeyword("_ALPHATEST_ON");
					break;
				case BlendMode.TransparentCutout:
					material.SetOverrideTag("RenderType", "TransparentCutout");
					material.EnableKeyword("_ALPHATEST_ON");
					break;
				case BlendMode.CSG:
					material.SetOverrideTag("RenderType", "CSG");
					material.DisableKeyword("_ALPHATEST_ON");
					break;
				case BlendMode.CSGCuboid:
					material.SetOverrideTag("RenderType", "CSGCuboid");
					material.DisableKeyword("_ALPHATEST_ON");
					break;
			}


			material.SetInt("_Mode", (int)blendMode);
		}

		public static MaterialProperty TryFindProperty(string _name, MaterialProperty[] _props) {
			try {
				return FindProperty(_name, _props);
			} catch (System.Exception e) {
			}
			return null;
		}

        public virtual void FindProperties(MaterialProperty[] props)
        {
            blendMode = FindProperty("_Mode", props);
			cullMode = FindProperty("_Cull", props, false);
			albedoMap = FindProperty("_MainTex", props);
			albedoColor = FindProperty("_Color", props);
            alphaCutoff = FindProperty("_Cutoff", props);
            specularMap = FindProperty("_SpecGlossMap", props, false);
            specularColor = FindProperty("_SpecColor", props, false);
            metallicMap = FindProperty("_MetallicGlossMap", props, false);
            metallic = FindProperty("_Metallic", props, false);
            if (specularMap != null && specularColor != null)
                m_WorkflowMode = WorkflowMode.Specular;
            else if (metallicMap != null && metallic != null)
                m_WorkflowMode = WorkflowMode.Metallic;
            else
                m_WorkflowMode = WorkflowMode.Dielectric;
            smoothness = FindProperty("_Glossiness", props);
            smoothnessScale = FindProperty("_GlossMapScale", props, false);
            smoothnessMapChannel = FindProperty("_SmoothnessTextureChannel", props, false);
            highlights = FindProperty("_SpecularHighlights", props, false);
            reflections = FindProperty("_GlossyReflections", props, false);
            bumpScale = FindProperty("_BumpScale", props);
            bumpMap = FindProperty("_BumpMap", props);
            heigtMapScale = FindProperty("_Parallax", props);
            heightMap = FindProperty("_ParallaxMap", props);
            occlusionStrength = FindProperty("_OcclusionStrength", props);
            occlusionMap = FindProperty("_OcclusionMap", props);
            emissionColorForRendering = FindProperty("_EmissionColor", props);
            emissionMap = FindProperty("_EmissionMap", props);
            detailMask = FindProperty("_DetailMask", props);
            detailAlbedoMap = FindProperty("_DetailAlbedoMap", props);
            detailNormalMapScale = FindProperty("_DetailNormalMapScale", props);
            detailNormalMap = FindProperty("_DetailNormalMap", props);
            uvSetSecondary = FindProperty("_UVSec", props);

			allowRenderQueueOverride = FindProperty("_RenderQueueOverride", props);
			alphaOverride = FindProperty("_AlphaOverride", props);
			randomIntensity = FindProperty("_RandomIntensity", props);
			xrayColour = TryFindProperty("_XRayColour", props);
			for (int i = 0; i < 8; i ++) {
				tintWindows[i] = FindProperty("_TintWindow" + (i+1).ToString(), props);
				tintColours[i] = FindProperty("_TintColour" + (i+1).ToString(), props);
                uvOffsets[i] = FindProperty("_UVOffset" + (i+1).ToString(), props);
				premiumVars[i] = FindProperty("_PremiumVars" + (i + 1).ToString(), props, false);
			}
			textureContribution = TryFindProperty("_TextureContribution", props);
			occludedColour = TryFindProperty("_OccludedColor", props);
			clipShadowsCSG = TryFindProperty("_CSGShadowClip", props);
        }

		Color m_replaceXRayColour;
        public override void OnGUI(MaterialEditor materialEditor, MaterialProperty[] props)
        {
            if(Selection.objects.Length > 1)
            {
                // RB - 05/07/18 - Using standard GUI input fields causes overriding when multi selecting, we should use ShaderProperty
                // However using ShaderProperty causes us to lose a lot of formatting functionality, I am disabling multi-editing until
                // we find a better solution
                
                GUILayout.Label("Multi-material editing not supported for this shader.");
                GUILayout.Label("");
                
                const string c_xrayColourProp = "_XRayColour";
                Color existing = Color.clear;
                bool haveXRay = false;
                foreach (var o in Selection.objects)
                {
	                var om = o as Material;
	                if (om != null && om.HasProperty(c_xrayColourProp))
		            {
		                existing = om.GetColor(c_xrayColourProp);
		                haveXRay = true;
		                break;
		            }
                }
                if (haveXRay)
	            {
			        GUILayout.BeginHorizontal();
			        GUILayout.Label("XRay colour");
	                if (m_replaceXRayColour.a == 0) m_replaceXRayColour = existing;
	                m_replaceXRayColour = EditorGUILayout.ColorField(m_replaceXRayColour);
	                if (GUILayout.Button($"Replace in {Selection.objects.Length} shaders"))
		            {
			            foreach (var o in Selection.objects)
			            {
				            var om = o as Material;
				            if (om != null && om.HasProperty(c_xrayColourProp))
								om.SetColor(c_xrayColourProp, m_replaceXRayColour);
						}
			        }
	                GUILayout.EndHorizontal();
				}
                
                return;
            }
            
            FindProperties(props); // MaterialProperties can be animated so we do not cache them but fetch them every event to ensure animated values are updated correctly
            m_MaterialEditor = materialEditor;
            Material material = materialEditor.target as Material;

            // Make sure that needed setup (ie keywords/renderqueue) are set up if we're switching some existing
            // material to a standard shader.
            // Do this before any GUI code has been issued to prevent layout issues in subsequent GUILayout statements (case 780071)
            if (m_FirstTimeApply)
            {
                MaterialChanged(material, m_WorkflowMode);
                m_FirstTimeApply = false;
            }

            ShaderPropertiesGUI(material);
        }

        public virtual void ShaderPropertiesGUI(Material material)
        {
            // Use default labelWidth
            EditorGUIUtility.labelWidth = 0f;
			
			EditorGUI.BeginChangeCheck();
			{
				BlendModePopup();
				CullModePopup();
			}

			if (EditorGUI.EndChangeCheck())
			{
				foreach (var obj in blendMode.targets)
					MaterialChanged(material, m_WorkflowMode);
			}


			// Detect any changes to the material
			EditorGUI.BeginChangeCheck();
            {
                

				// Primary properties
				GUILayout.Label(Styles.primaryMapsText, EditorStyles.boldLabel);
                DoAlbedoArea(material);
                DoSpecularMetallicArea();
                m_MaterialEditor.TexturePropertySingleLine(Styles.normalMapText, bumpMap, bumpMap.textureValue != null ? bumpScale : null);
                m_MaterialEditor.TexturePropertySingleLine(Styles.heightMapText, heightMap, heightMap.textureValue != null ? heigtMapScale : null);
                m_MaterialEditor.TexturePropertySingleLine(Styles.occlusionText, occlusionMap, occlusionStrength);
                if (material.HasProperty("_AOonUV2"))
                {
	                EditKeyword(material, "_AOonUV2", "_AO_ON_UV_2", "   ..is curve map", false);
	                if (material.GetFloat("_AOonUV2") > 0)
	                {
		                EditFloatProperty(material, "_CurvePower", "");
		                EditFloatProperty(material, "_CurveIntensity", "");
		                EditColourProperty(material, "_CurveColour", "");
	                }
                }
                m_MaterialEditor.TexturePropertySingleLine(Styles.detailMaskText, detailMask);
                DoEmissionArea(material);
                EditorGUI.BeginChangeCheck();
                m_MaterialEditor.TextureScaleOffsetProperty(albedoMap);
                if (EditorGUI.EndChangeCheck())
                    emissionMap.textureScaleAndOffset = albedoMap.textureScaleAndOffset; // Apply the main texture scale and offset to the emission texture as well, for Enlighten's sake

				EditorGUILayout.Space();

				if (textureContribution != null) {
					GUILayout.BeginHorizontal();
					GUILayout.Label("Texture Contribution");
					textureContribution.floatValue = GUILayout.HorizontalSlider(textureContribution.floatValue, 0, 1);
					GUILayout.EndHorizontal();
				}
				if (occludedColour != null) {
					GUILayout.BeginHorizontal();
					GUILayout.Label("Occluded color");
					occludedColour.colorValue = EditorGUILayout.ColorField(occludedColour.colorValue, GUILayout.Width(50));
					GUILayout.EndHorizontal();
				}
				if (clipShadowsCSG != null) {
					clipShadowsCSG.vectorValue = EditorGUILayout.Vector4Field("CSG shd clip", clipShadowsCSG.vectorValue);
				}

                EditorGUILayout.Space();

                // Secondary properties
                GUILayout.Label(Styles.secondaryMapsText, EditorStyles.boldLabel);
                m_MaterialEditor.TexturePropertySingleLine(Styles.detailAlbedoText, detailAlbedoMap);
                m_MaterialEditor.TexturePropertySingleLine(Styles.detailNormalMapText, detailNormalMap, detailNormalMapScale);
                m_MaterialEditor.TextureScaleOffsetProperty(detailAlbedoMap);
                m_MaterialEditor.ShaderProperty(uvSetSecondary, Styles.uvSetLabel.text);

                // Third properties
                GUILayout.Label(Styles.forwardText, EditorStyles.boldLabel);
                if (highlights != null)
                    m_MaterialEditor.ShaderProperty(highlights, Styles.highlightsText);
                if (reflections != null)
                    m_MaterialEditor.ShaderProperty(reflections, Styles.reflectionsText);

				
			}
            if (EditorGUI.EndChangeCheck())
            {
                foreach (var obj in blendMode.targets)
                    MaterialChanged((Material)obj, m_WorkflowMode);
            }

			EditorGUILayout.Space();

			GUILayout.BeginHorizontal();
            //m_MaterialEditor.ShaderProperty(alphaOverride, Styles.alphaOverrideText);
			GUILayout.EndHorizontal();


			
			GUILayout.BeginHorizontal();
            m_MaterialEditor.ShaderProperty(randomIntensity, Styles.randomIntensityText);
			GUILayout.EndHorizontal();
			
			if (xrayColour != null)
			{
				GUILayout.BeginHorizontal();
				m_MaterialEditor.ShaderProperty(xrayColour, Styles.xrayColourText);
				GUILayout.EndHorizontal();
			}
			
			EditorGUILayout.Space();

			GUILayout.BeginHorizontal();
			GUILayout.Label("Tint areas:");
			ShowSizes = GUILayout.Toggle(ShowSizes, "show size");
			GUILayout.EndHorizontal();

			for (int i = 0; i < 8; i ++) {
                DisplayWindowGUI(i);
			}

            EditorGUILayout.Space();

            GUILayout.Label(Styles.advancedText, EditorStyles.boldLabel);
			bool renderQueueOverride = allowRenderQueueOverride.floatValue > 0.0f;
			renderQueueOverride = EditorGUILayout.Toggle("(!)Allow Render Queue Oveerride", renderQueueOverride);
			allowRenderQueueOverride.floatValue = renderQueueOverride ? 1 : 0;

			m_MaterialEditor.RenderQueueField();
            m_MaterialEditor.EnableInstancingField();

			GUILayout.Label("Effects", EditorStyles.boldLabel);
			DoEffectsArea(material);

            s_debug = EditorGUILayout.Foldout(s_debug, "Debug");
			if (s_debug) {
				var shader = material.shader;
				int numProps = ShaderUtil.GetPropertyCount(shader);
				var propSort = new List<int>();
                for (int i = 0; i < numProps; i++) {
					propSort.Add(i);
				}
                MaterialPropComparer cmp = new MaterialPropComparer(shader);
				propSort.Sort(cmp);
				var prefix = "  ";
                for (int i = 0; i < numProps; i ++) {
					int index = propSort[i];
					var propName = ShaderUtil.GetPropertyName(shader, index);
					var propType = ShaderUtil.GetPropertyType(shader, index);
					GUILayout.BeginHorizontal();
					switch (propType) {
						case ShaderUtil.ShaderPropertyType.Float:
							EditFloatProperty(material, propName, prefix);
							break;
						case ShaderUtil.ShaderPropertyType.Color:
                            EditVectorProperty(material, propName, prefix);
                            break;
						case ShaderUtil.ShaderPropertyType.Vector:
                            EditVectorProperty(material, propName, prefix);
                            break;
						case ShaderUtil.ShaderPropertyType.Range:
                            EditFloatProperty(material, propName, prefix);
                            break;
						case ShaderUtil.ShaderPropertyType.TexEnv:
							break;
					}
					GUILayout.EndHorizontal();
				}
			}
		}
		static bool ShowSizes = true;
		static bool s_debug = false;

        public class MaterialPropComparer : IComparer<int> {
            Shader m_shader;
            public MaterialPropComparer(Shader _shader) {
                m_shader = _shader;
            }
            public int Compare(int x, int y) {
                var nameX = ShaderUtil.GetPropertyName(m_shader, x);
                var nameY = ShaderUtil.GetPropertyName(m_shader, y);
                return string.Compare(nameX, nameY);
            }
        }

        void EditFloatProperty(Material _mat, string _name, string _prefix) {
			float v = _mat.GetFloat(_name), vOut;
            vOut = EditorGUILayout.FloatField(_prefix + _name, v);
			if (Mathf.Abs(vOut - v) > .0001f)
				_mat.SetFloat(_name, vOut);
        }
        void EditVectorProperty(Material _mat, string _name, string _prefix) {
            Vector4 v = _mat.GetVector(_name), vOut;
            vOut = EditorGUILayout.Vector4Field(_prefix + _name, v);
            if ((vOut - v).sqrMagnitude > .0001f)
                _mat.SetVector(_name, vOut);
        }

        void EditColourProperty(Material _mat, string _name, string _prefix)
        {
	        var c = _mat.GetColor(_name);
	        GUILayout.BeginHorizontal();
	        GUILayout.Label(_prefix + _name);
	        var cNew = EditorGUILayout.ColorField(new GUIContent(), c, true, false, true, GUILayout.Width(50));
	        if (!(c.r - cNew.r).IsZero() || !(c.g - cNew.g).IsZero() || !(c.b - cNew.b).IsZero())
				_mat.SetColor(_name, cNew);
	        GUILayout.EndHorizontal();
        }
        

        protected virtual void DisplayWindowGUI(int _i)
        {
            GUILayout.BeginHorizontal();

            var mat = m_MaterialEditor.target as Material;
            var labelTag = $"_TintWindowName{_i + 1}=";
            var label = "";
            var labels = AssetDatabase.GetLabels(mat);
            int labelIndex = -1;
            for (int i = 0; i < labels.Length; ++i)
            {
	            var l = labels[i];
	            if (l.StartsWith(labelTag))
	            {
		            label = l.Substring(labelTag.Length);
		            labelIndex = i;
	            }
            }

            // Windows
            GUILayout.Label("  " + (_i+1).ToString() + ":", GUILayout.Width(20));
            var newLabel = GUILayout.TextField(label, GUILayout.Width(64));
            if (newLabel != label)
            {
	            if (labelIndex == -1)
	            {
		            labelIndex = labels.Length;
		            Array.Resize(ref labels, labels.Length + 1);
	            }
	            labels[labelIndex] = $"{labelTag}{newLabel}";
	            AssetDatabase.SetLabels(mat, labels);
            }
            
            var v = tintWindows[_i].vectorValue;
            if (ShowSizes) { v.z -= v.x; v.w -= v.y; }
            GUILayout.Label("UV1:", GUILayout.Width(28));
            v.x = EditorGUILayout.FloatField(v.x, GUILayout.Width(32));
            v.y = EditorGUILayout.FloatField(v.y, GUILayout.Width(32));
            // m_MaterialEditor.ShaderProperty(tintWindows[_i], "");
            GUILayout.Label(ShowSizes ? "Size:" : "UV2:", GUILayout.Width(28));
             v.z = EditorGUILayout.FloatField(v.z, GUILayout.Width(32));
             v.w = EditorGUILayout.FloatField(v.w, GUILayout.Width(32));
            if (ShowSizes) { v.z += v.x; v.w += v.y; }
            tintWindows[_i].vectorValue = v;

            // Colours
            
            tintColours[_i].colorValue = EditorGUILayout.ColorField(tintColours[_i].colorValue, GUILayout.Width(50));
			if(premiumVars[_i] != null) { 
				premiumVars[_i].colorValue = EditorGUILayout.ColorField(premiumVars[_i].colorValue, GUILayout.Width(50));
			}
			// m_MaterialEditor.ShaderProperty(tintColours[_i], "");

			// Offsets
			var offset = uvOffsets[_i].vectorValue;
            GUILayout.Label("Offset:", GUILayout.Width(28));
            offset.x = EditorGUILayout.FloatField(offset.x, GUILayout.Width(32));
            offset.y = EditorGUILayout.FloatField(offset.y, GUILayout.Width(32));
            uvOffsets[_i].vectorValue = offset;

            GUILayout.EndHorizontal();
        }

        internal void DetermineWorkflow(MaterialProperty[] props)
        {
            if (FindProperty("_SpecGlossMap", props, false) != null && FindProperty("_SpecColor", props, false) != null)
                m_WorkflowMode = WorkflowMode.Specular;
            else if (FindProperty("_MetallicGlossMap", props, false) != null && FindProperty("_Metallic", props, false) != null)
                m_WorkflowMode = WorkflowMode.Metallic;
            else
                m_WorkflowMode = WorkflowMode.Dielectric;
        }

        public override void AssignNewShaderToMaterial(Material material, Shader oldShader, Shader newShader)
        {
            // _Emission property is lost after assigning Standard shader to the material
            // thus transfer it before assigning the new shader
            if (material.HasProperty("_Emission"))
            {
                material.SetColor("_EmissionColor", material.GetColor("_Emission"));
            }

            base.AssignNewShaderToMaterial(material, oldShader, newShader);

            if (oldShader == null || !oldShader.name.Contains("Legacy Shaders/"))
            {
                SetupMaterialWithBlendMode(material, (BlendMode)material.GetFloat("_Mode"));
                return;
            }

            BlendMode blendMode = BlendMode.Opaque;
            material.SetFloat("_Mode", (float)blendMode);

            DetermineWorkflow(MaterialEditor.GetMaterialProperties(new Material[] { material }));
            MaterialChanged(material, m_WorkflowMode);
        }


		void CullModePopup()
		{
			if(cullMode != null){
				EditorGUI.showMixedValue = cullMode.hasMixedValue;
				var mode = (CullMode)cullMode.floatValue;

				EditorGUI.BeginChangeCheck();
				mode = (CullMode)EditorGUILayout.Popup(Styles.cullMode, (int)mode, Styles.cullNames);
				if (EditorGUI.EndChangeCheck())
				{
					m_MaterialEditor.RegisterPropertyChangeUndo("Culling Mode");
					cullMode.floatValue = (float)mode;
				}

				EditorGUI.showMixedValue = false;
			}
		}


		void BlendModePopup()
        {
            EditorGUI.showMixedValue = blendMode.hasMixedValue;
            var mode = (BlendMode)blendMode.floatValue;

            EditorGUI.BeginChangeCheck();
            mode = (BlendMode)EditorGUILayout.Popup(Styles.renderingMode, (int)mode, Styles.blendNames);
            if (EditorGUI.EndChangeCheck())
            {
                m_MaterialEditor.RegisterPropertyChangeUndo("Rendering Mode");
                blendMode.floatValue = (float)mode;
            }

            EditorGUI.showMixedValue = false;
        }

        void DoAlbedoArea(Material material)
        {
            m_MaterialEditor.TexturePropertySingleLine(Styles.albedoText, albedoMap, albedoColor, alphaCutoff);
        }

        void DoEmissionArea(Material material)
        {
            // Emission for GI?
            if (m_MaterialEditor.EmissionEnabledProperty())
            {
                bool hadEmissionTexture = emissionMap.textureValue != null;

                // Texture and HDR color controls
                m_MaterialEditor.TexturePropertyWithHDRColor(Styles.emissionText, emissionMap, emissionColorForRendering, m_ColorPickerHDRConfig, false);

                // If texture was assigned and color was black set color to white
                float brightness = emissionColorForRendering.colorValue.maxColorComponent;
                if (emissionMap.textureValue != null && !hadEmissionTexture && brightness <= 0f)
                    emissionColorForRendering.colorValue = Color.white;

                // change the GI flag and fix it up with emissive as black if necessary
                m_MaterialEditor.LightmapEmissionFlagsProperty(MaterialEditor.kMiniTextureFieldLabelIndentLevel, true);
            }
        }

        void DoSpecularMetallicArea()
        {
            bool hasGlossMap = false;
            if (m_WorkflowMode == WorkflowMode.Specular)
            {
                hasGlossMap = specularMap.textureValue != null;
                m_MaterialEditor.TexturePropertySingleLine(Styles.specularMapText, specularMap, hasGlossMap ? null : specularColor);
            }
            else if (m_WorkflowMode == WorkflowMode.Metallic)
            {
                hasGlossMap = metallicMap.textureValue != null;
                m_MaterialEditor.TexturePropertySingleLine(Styles.metallicMapText, metallicMap, hasGlossMap ? null : metallic);
            }

            bool showSmoothnessScale = hasGlossMap;
            if (smoothnessMapChannel != null)
            {
                int smoothnessChannel = (int)smoothnessMapChannel.floatValue;
                if (smoothnessChannel == (int)SmoothnessMapChannel.AlbedoAlpha)
                    showSmoothnessScale = true;
            }

            int indentation = 2; // align with labels of texture properties
            m_MaterialEditor.ShaderProperty(showSmoothnessScale ? smoothnessScale : smoothness, showSmoothnessScale ? Styles.smoothnessScaleText : Styles.smoothnessText, indentation);

            ++indentation;
            if (smoothnessMapChannel != null)
                m_MaterialEditor.ShaderProperty(smoothnessMapChannel, Styles.smoothnessMapChannelText, indentation);
        }

		void DoEffectsArea(Material _material)
		{
			if(_material.HasProperty("_IsOnFire"))
			{
				GUILayout.Label("------------", EditorStyles.whiteMiniLabel);
				bool isOnFire = _material.IsKeywordEnabled("_IS_ON_FIRE");
				EditorGUI.BeginChangeCheck();
				isOnFire = EditorGUILayout.Toggle("Is On Fire", isOnFire);
				if (EditorGUI.EndChangeCheck())
				{
					// enable or disable the keyword based on checkbox
					if (isOnFire)
					{
						_material.EnableKeyword("_IS_ON_FIRE");
					}
					else
					{
						_material.DisableKeyword("_IS_ON_FIRE");
					}
					
				}
				if (isOnFire)
				{
					GUILayout.BeginHorizontal();
					float fireProgress = _material.GetFloat("_FireProgress");
					EditorGUILayout.PrefixLabel("Fire Progression");
					EditorGUI.BeginChangeCheck();
					fireProgress = EditorGUILayout.Slider(fireProgress, 0, 1);
					_material.SetFloat("_FireProgress", fireProgress);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float fireIntensity = _material.GetFloat("_FireIntensity");
					EditorGUILayout.PrefixLabel("Fire Intensity");
					EditorGUI.BeginChangeCheck();
					fireIntensity = EditorGUILayout.Slider(fireIntensity, 0, 1);
					_material.SetFloat("_FireIntensity", fireIntensity);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					EditorGUI.BeginChangeCheck();
					float buildingTop = _material.GetFloat("_BuildingTop");
					EditorGUILayout.PrefixLabel("Building Top");
					buildingTop = EditorGUILayout.FloatField(buildingTop);
					_material.SetFloat("_BuildingTop", buildingTop);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					EditorGUI.BeginChangeCheck();
					float buildingBase = _material.GetFloat("_BuildingBase");
					EditorGUILayout.PrefixLabel("Building Base");
					buildingBase = EditorGUILayout.FloatField(buildingBase);
					_material.SetFloat("_BuildingBase", buildingBase);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float wallDirt = _material.GetFloat("_WallDirt");
					EditorGUILayout.PrefixLabel("Wall Dirt");
					EditorGUI.BeginChangeCheck();
					wallDirt = EditorGUILayout.Slider(wallDirt, 0, 1);
					_material.SetFloat("_WallDirt", wallDirt);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float enableErosion = _material.GetFloat("_EnableErosion");
					EditorGUILayout.PrefixLabel("Enable Erosion");
					EditorGUI.BeginChangeCheck();
					enableErosion = EditorGUILayout.Slider(enableErosion, 0, 1);
					_material.SetFloat("_EnableErosion", enableErosion);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float erosionWidth = _material.GetFloat("_ErosionWidth");
					EditorGUILayout.PrefixLabel("Erosion Width");
					EditorGUI.BeginChangeCheck();
					erosionWidth = EditorGUILayout.Slider(erosionWidth, 0, 30);
					_material.SetFloat("_ErosionWidth", erosionWidth);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float numberOfParts = _material.GetFloat("_NumberOfParts");
					EditorGUILayout.PrefixLabel("Number Of Parts");
					EditorGUI.BeginChangeCheck();
					numberOfParts = EditorGUILayout.FloatField(numberOfParts);
					_material.SetFloat("_NumberOfParts", numberOfParts);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();
				}
			}

			if(_material.HasProperty("_AffectedByDistricts"))
			{
				GUILayout.BeginHorizontal();
				float districts = _material.GetFloat("_AffectedByDistricts");
				EditorGUILayout.PrefixLabel("Affected By Districts");
				EditorGUI.BeginChangeCheck();
				districts = EditorGUILayout.Slider(districts, 0, 1);
				_material.SetFloat("_AffectedByDistricts", districts);
				EditorGUI.EndChangeCheck();
				GUILayout.EndHorizontal();
			}

			if (_material.HasProperty("_UVScrolling"))
			{
				GUILayout.BeginHorizontal();
				Vector2 uvScrolling = new Vector2( _material.GetVector("_UVScrolling").x, _material.GetVector("_UVScrolling").y);
				EditorGUILayout.PrefixLabel("UV Scrolling");
				EditorGUI.BeginChangeCheck();
				GUILayoutOption widthOfField = GUILayout.Width(125);
				uvScrolling = EditorGUILayout.Vector2Field("" , uvScrolling, widthOfField);
				_material.SetVector("_UVScrolling", uvScrolling);
				EditorGUI.EndChangeCheck();
				GUILayout.EndHorizontal();
			}

			EditKeyword(_material, "_VertexColour", "_VERTEX_COLOUR", "Use Vertex Colours");
			EditKeyword(_material, "_WorldUVs", "_WORLD_UVS", "UVs from world position");

			if (_material.HasProperty("_UseSSS"))
			{
				GUILayout.Label("------------", EditorStyles.whiteMiniLabel);
				bool useSSS = _material.IsKeywordEnabled("_USE_SSS");
				EditorGUI.BeginChangeCheck();
				useSSS = EditorGUILayout.Toggle("Use SSS", useSSS);
				if (EditorGUI.EndChangeCheck())
				{
					// enable or disable the keyword based on checkbox
					if (useSSS)
					{
						_material.EnableKeyword("_USE_SSS");
					}
					else
					{
						_material.DisableKeyword("_USE_SSS");
					}
				}
				if(useSSS)
				{
					GUILayout.BeginHorizontal();
					float useMeshNormals = _material.GetFloat("_SSS_UseMeshNormals");
					EditorGUILayout.PrefixLabel("Use Mesh Normals");
					EditorGUI.BeginChangeCheck();
					useMeshNormals = EditorGUILayout.Slider(useMeshNormals, 0, 1);
					_material.SetFloat("_SSS_UseMeshNormals", useMeshNormals);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float sssVertPixelNormals = _material.GetFloat("_SSS_VertexPixelNormals");
					EditorGUILayout.PrefixLabel("Vert or Pixel Normals");
					EditorGUI.BeginChangeCheck();
					sssVertPixelNormals = EditorGUILayout.Slider(sssVertPixelNormals, 0, 1);
					_material.SetFloat("_SSS_VertexPixelNormals", sssVertPixelNormals);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float sssPower = _material.GetFloat("_SSS_Power");
					EditorGUILayout.PrefixLabel("Power");
					EditorGUI.BeginChangeCheck();
					sssPower = EditorGUILayout.Slider(sssPower, 0.01f, 10);
					_material.SetFloat("_SSS_Power", sssPower);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float sssScale = _material.GetFloat("_SSS_Scale");
					EditorGUILayout.PrefixLabel("Scale");
					EditorGUI.BeginChangeCheck();
					sssScale = EditorGUILayout.FloatField(sssScale);
					_material.SetFloat("_SSS_Scale", sssScale);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();

					GUILayout.BeginHorizontal();
					float sssDebug = _material.GetFloat("_SSS_Debug");
					EditorGUILayout.PrefixLabel("Debug");
					EditorGUI.BeginChangeCheck();
					sssDebug = EditorGUILayout.Slider(sssDebug, 0, 1);
					_material.SetFloat("_SSS_Debug", sssDebug);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();
					
					GUILayout.BeginHorizontal();
					float sssUseCameraVector = _material.GetFloat("_SSS_UseCameraVector");
					EditorGUILayout.PrefixLabel("Use Camera Vector");
					EditorGUI.BeginChangeCheck();
					sssUseCameraVector = EditorGUILayout.Slider(sssUseCameraVector, 0, 1);
					_material.SetFloat("_SSS_UseCameraVector", sssUseCameraVector);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();
				}
			}
			if (_material.HasProperty("_DitherOpacity"))
			{
				bool useDitherFade = _material.IsKeywordEnabled("_USE_DITHER_OPACITY");
				EditorGUI.BeginChangeCheck();
				useDitherFade = EditorGUILayout.Toggle("Use Dither Opacity", useDitherFade);
				if (EditorGUI.EndChangeCheck())
				{
					// enable or disable the keyword based on checkbox
					if (useDitherFade)
					{
						_material.EnableKeyword("_USE_DITHER_OPACITY");
					}
					else
					{
						_material.DisableKeyword("_USE_DITHER_OPACITY");
					}
				}
				if (useDitherFade)
				{
					GUILayout.BeginHorizontal();
					float ditherFade = _material.GetFloat("_DitherOpacity");
					EditorGUILayout.PrefixLabel("Dither Opacity");
					EditorGUI.BeginChangeCheck();
					ditherFade = EditorGUILayout.Slider(ditherFade, 0, 1);
					_material.SetFloat("_DitherOpacity", ditherFade);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();
				}
			}

			if(_material.HasProperty("_LocalDisplacement"))
			{
				GUILayout.Label("------------", EditorStyles.whiteMiniLabel);
				bool localDisplacement = _material.IsKeywordEnabled("_LOCAL_DISPLACEMENT");
				EditorGUI.BeginChangeCheck();
				localDisplacement = EditorGUILayout.Toggle("Local Displacement", localDisplacement);
				if (EditorGUI.EndChangeCheck())
				{
					// enable or disable the keyword based on checkbox
					if (localDisplacement)
					{
						_material.EnableKeyword("_LOCAL_DISPLACEMENT");
					}
					else
					{
						_material.DisableKeyword("_LOCAL_DISPLACEMENT");
					}
					
				}
			}

			if (_material.HasProperty("_IsWindow"))
			{
				bool isWindow = _material.IsKeywordEnabled("_IS_WINDOW");
				EditorGUI.BeginChangeCheck();
				isWindow = EditorGUILayout.Toggle("Is Window", isWindow);
				if (EditorGUI.EndChangeCheck())
				{
					// enable or disable the keyword based on checkbox
					if (isWindow)
					{
						_material.EnableKeyword("_IS_WINDOW");
					}
					else
					{
						_material.DisableKeyword("_IS_WINDOW");
					}
				}

				if (isWindow)
				{
					GUILayout.BeginHorizontal();
					float normalOffset = _material.GetFloat("_NormalOffset");
					EditorGUILayout.PrefixLabel("Normal Offset Amount");
					EditorGUI.BeginChangeCheck();
					normalOffset = EditorGUILayout.Slider(normalOffset, 0, 1);
					_material.SetFloat("_NormalOffset", normalOffset);
					EditorGUI.EndChangeCheck();
					GUILayout.EndHorizontal();
				}
			}

			if (_material.HasProperty("_Surface"))
			{
				GUILayout.BeginHorizontal();
				float aging = _material.GetFloat("_Aging");
				EditorGUILayout.PrefixLabel("Surface Aging");
				EditorGUI.BeginChangeCheck();
				aging = EditorGUILayout.Slider(aging, 0, 1);
				_material.SetFloat("_Aging", aging);
				EditorGUI.EndChangeCheck();
				GUILayout.EndHorizontal();

				//string[] keywords = new string[4];
				//keywords[0] = "_SURFACE_WOOD";
				//keywords[1] = "_SURFACE_CLOTH";
				//keywords[2] = "_SURFACE_METAL";
				//keywords[3] = "_SURFACE_FOOD";

				//MaterialGUIFunctions.ShowEnumKeywordItem(Styles.surfaceTypeLabel, ref _material, "_Surface", Styles.surfaceTypeNames, keywords);

				Vector4[] offsetValues = new Vector4[4];
				offsetValues[0] = new Vector4(0.0f, 0.5f, 0, 0);
				offsetValues[1] = new Vector4(0.5f, 0.5f, 0, 0);
				offsetValues[2] = new Vector4(0.0f, 0.0f, 0, 0);
				offsetValues[3] = new Vector4(0.5f, 0.0f, 0, 0);


				MaterialGUIFunctions.ShowEnumValueItem(Styles.surfaceTypeLabel, ref _material, "_Surface", "_UVOffsetAging", Styles.surfaceTypeNames, offsetValues);
			}

			if (_material.HasProperty("_PremiumReward"))
			{
				bool premiumReward = _material.IsKeywordEnabled("_PREMIUM_REWARD");
				EditorGUI.BeginChangeCheck();
				premiumReward = EditorGUILayout.Toggle("Premium Reward", premiumReward);
				if (EditorGUI.EndChangeCheck())
				{
					// enable or disable the keyword based on checkbox
					if (premiumReward)
					{
						_material.EnableKeyword("_PREMIUM_REWARD");
					}
					else
					{
						_material.DisableKeyword("_PREMIUM_REWARD");
					}
				}
			}

			if (_material.HasProperty("_WindAmplitude"))
			{
				GUILayout.BeginHorizontal();
				Texture windTex = _material.GetTexture("_WindTex");
				EditorGUILayout.PrefixLabel("Wind Texture");
				EditorGUI.BeginChangeCheck();
				windTex = (Texture)EditorGUILayout.ObjectField(windTex, typeof(Texture));
				_material.SetTexture("_WindTex", windTex);
				EditorGUI.EndChangeCheck();
				GUILayout.EndHorizontal();

				GUILayout.BeginHorizontal();
				float windFrequency = _material.GetFloat("_WindFrequency");
				EditorGUILayout.PrefixLabel("Wind Frequency");
				EditorGUI.BeginChangeCheck();
				windFrequency = EditorGUILayout.FloatField(windFrequency);
				_material.SetFloat("_WindFrequency", windFrequency);
				EditorGUI.EndChangeCheck();
				GUILayout.EndHorizontal();

				GUILayout.BeginHorizontal();
				Vector3 windAmplitude = new Vector3(_material.GetVector("_WindAmplitude").x, _material.GetVector("_WindAmplitude").y, _material.GetVector("_WindAmplitude").z);
				EditorGUILayout.PrefixLabel("Wind Amplitude");
				EditorGUI.BeginChangeCheck();
				GUILayoutOption widthOfField = GUILayout.Width(160);
				windAmplitude = EditorGUILayout.Vector3Field("", windAmplitude, widthOfField);
				_material.SetVector("_WindAmplitude", windAmplitude);
				EditorGUI.EndChangeCheck();
				GUILayout.EndHorizontal();

				GUILayout.BeginHorizontal();
				Vector2 windScrollSpeed = new Vector2(_material.GetVector("_WindScrollSpeed").x, _material.GetVector("_WindScrollSpeed").y);
				EditorGUILayout.PrefixLabel("Wind Scroll Speed");
				EditorGUI.BeginChangeCheck();
				widthOfField = GUILayout.Width(125);
				windScrollSpeed = EditorGUILayout.Vector2Field("", windScrollSpeed, widthOfField);
				_material.SetVector("_WindScrollSpeed", windScrollSpeed);
				EditorGUI.EndChangeCheck();
				GUILayout.EndHorizontal();
			}
		}


		static int VerifyCurrentRenderQueue(int _currentRenderQueue, int _min, int _max) {
			if (_currentRenderQueue >= _min && _currentRenderQueue < _max) return _currentRenderQueue;
			return _min;
		}
        

        static SmoothnessMapChannel GetSmoothnessMapChannel(Material material)
        {
            int ch = (int)material.GetFloat("_SmoothnessTextureChannel");
            if (ch == (int)SmoothnessMapChannel.AlbedoAlpha)
                return SmoothnessMapChannel.AlbedoAlpha;
            else
                return SmoothnessMapChannel.SpecularMetallicAlpha;
        }

        static void SetMaterialKeywords(Material material, WorkflowMode workflowMode)
        {
            // Note: keywords must be based on Material value not on MaterialProperty due to multi-edit & material animation
            // (MaterialProperty value might come from renderer material property block)
            SetKeyword(material, "_NORMALMAP", material.GetTexture("_BumpMap") || material.GetTexture("_DetailNormalMap"));
            if (workflowMode == WorkflowMode.Specular)
                SetKeyword(material, "_SPECGLOSSMAP", material.GetTexture("_SpecGlossMap"));
            else if (workflowMode == WorkflowMode.Metallic)
                SetKeyword(material, "_METALLICGLOSSMAP", material.GetTexture("_MetallicGlossMap"));
            SetKeyword(material, "_PARALLAXMAP", material.GetTexture("_ParallaxMap"));
            SetKeyword(material, "_DETAIL_MULX2", material.GetTexture("_DetailAlbedoMap") || material.GetTexture("_DetailNormalMap"));

            // A material's GI flag internally keeps track of whether emission is enabled at all, it's enabled but has no effect
            // or is enabled and may be modified at runtime. This state depends on the values of the current flag and emissive color.
            // The fixup routine makes sure that the material is in the correct state if/when changes are made to the mode or color.
            MaterialEditor.FixupEmissiveFlag(material);
            bool shouldEmissionBeEnabled = (material.globalIlluminationFlags & MaterialGlobalIlluminationFlags.EmissiveIsBlack) == 0;
            SetKeyword(material, "_EMISSION", shouldEmissionBeEnabled);

            if (material.HasProperty("_SmoothnessTextureChannel"))
            {
                SetKeyword(material, "_SMOOTHNESS_TEXTURE_ALBEDO_CHANNEL_A", GetSmoothnessMapChannel(material) == SmoothnessMapChannel.AlbedoAlpha);
            }
        }

        static void MaterialChanged(Material material, WorkflowMode workflowMode)
        {
            SetupMaterialWithBlendMode(material, (BlendMode)material.GetFloat("_Mode"));
			SetupMaterialWithCullMode(material, (CullMode)material.GetFloat("_Cull"));
			SetMaterialKeywords(material, workflowMode);
        }

        static void SetKeyword(Material m, string keyword, bool state)
        {
            if (state)
                m.EnableKeyword(keyword);
            else
                m.DisableKeyword(keyword);
        }
    }

	public static class StandardTintedUtils {
		[MenuItem("22Cans/Art/Export Tint Window")]
		static void ExportTintWindow() {
			var items = Selection.transforms;
			if (items == null || items.Length == 0) {
				Debug.LogErrorFormat("Select item to export tint window from");
				return;
			}
			string s = "";
			foreach (var item in items) {
				var rs = item.GetComponentsInChildren<MeshRenderer>();
				foreach (var r in rs) {
					var ms = r.materials;
					foreach (var m in ms) {
						if (m.HasProperty("_TintWindow1")) {
							s += r.name + "::" + m.name.Replace(" (instance)", "") + "::";
							for (int i = 0; i < 8; i++) {
								var suff = (i+1).ToString();
								var window = m.GetVector("_TintWindow" + suff);
								var colour = m.GetVector("_TintColour" + suff);
								var uvOffs = m.GetVector("_UVOffset" + suff);
								s += window.ToString("f3") + " " + colour.ToString("f3") + " " + uvOffs.ToString("f3") + "::";
							}
							s += "\n";
						}
					}
				}
			}
			var path = EditorUtility.SaveFilePanel("Save tint window info to:", "", "", "txt");
			if (path.Length > 0)
				System.IO.File.WriteAllText(path, s);
		}
		[MenuItem("22Cans/Art/Import Tint Window")]
		static void ImportTintWindow() {
			var items = Selection.transforms;
			if (items == null || items.Length == 0) {
				Debug.LogErrorFormat("Select item to export tint window from");
				return;
			}

			var path = EditorUtility.OpenFilePanel("Load tint window info from:", "", "txt");
			if (path.Length > 0) {
				var ss = System.IO.File.ReadAllLines(path);
				if (ss != null && ss.Length > 0) {
					var lookup = new Dictionary<string, string[]>();
					foreach (var s in ss) {
						var bits = s.Split(new string[] { "::" }, StringSplitOptions.None);
						if (bits.Length >= 10) {
							lookup[bits[1]] = bits;
						}
					}
					foreach (var item in items) {
						var rs = item.GetComponentsInChildren<MeshRenderer>();
						foreach (var r in rs) {
							var ms = r.materials;
							foreach (var m in ms) {
								if (m.HasProperty("_TintWindow1")) {
									var name = m.name.Replace(" (instance)", "");
									string[] data;
									if (lookup.TryGetValue(name, out data)) {
										for (int i = 0; i < 8; i ++) {
											var window = data[i+2];
											window = window.Replace("(", "").Replace(")", "").Replace(",", "");
											var bits = window.Split(' ');
											var floats = new float[4*3];
											for (int j = 0; j < 4*3; j ++) {
												float.TryParse(bits[j], out floats[j]);
											}
											var suff = (i + 1).ToString();
											m.SetVector("_TintWindow" + suff, new Vector4(floats[0], floats[1], floats[2], floats[3]));
											m.SetVector("_TintColour" + suff, new Vector4(floats[4], floats[5], floats[6], floats[7]));
											m.SetVector("_UVOffset" + suff, new Vector4(floats[8], floats[9], floats[10], floats[11]));
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

} // namespace UnityEditor
