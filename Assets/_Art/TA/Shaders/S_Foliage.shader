Shader "_Scenery/S_Foliage"
{
	Properties
	{
		_MainTex("Albedo (RGB)", 2D) = "white" {}
		_NormalTex("NormalMap", 2D) = "white" {}
		_Cutoff("Alpha Cutoff", Range(0,1)) = 0.5
		_RandomFade("Random Fade", Range(0,1)) = 0
		_NormShadowBrightness("Normal Map Shadow Brightness", Range(0,1)) = 0.5
		_NormShadowContrast("Normal Map Shadow Contrast", Range(0,0.55)) = 0.45
		_ShadowPull("Shadow Pull", Range(1, 1.05)) = 1.01
		[Header(Wind)]
		[Toggle(WindToggle)]_Wind("Toggle Wind", Float) = 0
		_WindSpeed("Wind Speed",Float) = 1
        _WindFrequency("Wind Frequency",Float) = 0.2
        _WindAmplitude("Wind Amplitude",Float) = 0.4
		_WindSize("Wind Size", Float) = 1
		_TintTex("Tint Tex", 2D) = "white" {}
		[HideInInspector]_DetailMap("Terrain Detail Map",2D) = "white" {}
		[Toggle(_USE_DISCARD_MAP)]_UseDiscardMap("Use Discard Map", Float) = 0
		_AffectedByDistricts("Affected By Districts", Range(0, 1)) = 1
		
		_cuboidCenter1("_cuboidCenter1", Vector) = (0,0,0,0)
		_cuboidExtents1a("_cuboidExtents1a", Vector) = (0,1,0,0)
		_cuboidExtents1b("_cuboidExtents1b", Vector) = (0,1,0,0)
		_cuboidExtents1c("_cuboidExtents1c", Vector) = (0,1,0,0)
		_cuboidCenter2("_cuboidCenter2", Vector) = (0,0,0,0)
		_cuboidExtents2a("_cuboidExtents2a", Vector) = (0,1,0,0)
		_cuboidExtents2b("_cuboidExtents2b", Vector) = (0,1,0,0)
		_cuboidExtents2c("_cuboidExtents2c", Vector) = (0,1,0,0)
		_cuboidCenter3("_cuboidCenter3", Vector) = (0,0,0,0)
		_cuboidExtents3a("_cuboidExtents3a", Vector) = (0,1,0,0)
		_cuboidExtents3b("_cuboidExtents3b", Vector) = (0,1,0,0)
		_cuboidExtents3c("_cuboidExtents3c", Vector) = (0,1,0,0)
		_cuboidColor("_cuboidColor", Color) = (1,0,1,0)
		
        // GBuffer
        [HideInInspector] _StencilRefGBuffer("_StencilRefGBuffer", Int) = 2 // StencilUsage.RequiresDeferredLighting
        [HideInInspector] _StencilWriteMaskGBuffer("_StencilWriteMaskGBuffer", Int) = 3 // StencilUsage.RequiresDeferredLighting | StencilUsage.SubsurfaceScattering
	}
	SubShader
	{
		LOD 100
    	Tags { "RenderPipeline" = "UniversalPipeline" "UniversalMaterialType" = "Lit" "RenderType" = "Opaque" "Queue" = "AlphaTest" }//"AlphaTest+0" }

		Pass
		{
			Tags { "LightMode"="UniversalForward" }
			Stencil
            {
                WriteMask [_StencilWriteMaskGBuffer]
                Ref [_StencilRefGBuffer]
                Comp Always
                Pass Replace
            }

			Cull Off

			ZWrite On 
			HLSLPROGRAM

			#pragma vertex vert
			#pragma fragment frag
			#pragma instancing_options lodfade
			#pragma multi_compile_fwdbase
			#pragma multi_compile_instancing
			#pragma multi_compile _ WindToggle
			#pragma multi_compile _ _USE_DISCARD_MAP
			//#pragma multi_compile _ LOD_FADE_CROSSFADE
    		#pragma multi_compile_local _ CSGCUBOID_ADDITION
			#pragma multi_compile_local _ _CIRCLECLIP
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE
			#pragma multi_compile_fragment _ _SHADOWS_SOFT

			#pragma skip_variants DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON
			
			#pragma target 3.5

			#include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_BoxClip.cginc"
						
			struct v2f
			{
				_BASE_VARS(0)
				SHADOW_COORDS(1)
				_LIGHTING_VARS(1,2)
				_DISTANCE_VARS(3)
				_WORLDPOS_VARS(4)
				fixed4 color : TEXCOORD5;
				float lightDir : TEXCOORD6;
				half2 uv_2 : TEXCOORD7;
				_SCREENPOS_VAR(8)
				_INSTANCEID_VAR(9)
				float ID : TEXCOORD10;
			};

			_BASE_DEFS;
			half _WindSpeed;
			half _WindFrequency;
			half _WindAmplitude;
			half _WindSize;
			half _FoliageColShift;
			sampler2D _TintTex;

			UNITY_INSTANCING_BUFFER_START(FoliageProps)
				UNITY_DEFINE_INSTANCED_PROP(half, _RandomFade)
			UNITY_INSTANCING_BUFFER_END(FoliageProps)

			v2f vert (appdata_instancing v)
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v); 
				UNITY_INITIALIZE_OUTPUT(v2f, o);  
				o.uv = v.uv;
				o.uv_2 = v.uv_2;

				_CALC_WORLDPOS(o, v);
				#if _USE_DISCARD_MAP
					float4 axisWorldPosition;
					_CALC_WORLDPOS_POINT(float4(0, 0, 0, 1), axisWorldPosition);
					float4 clampUVs = float4((axisWorldPosition.x + 512)*0.0009765625, (axisWorldPosition.z + 512)*0.0009765625, 0, 0);
					float discardMap = _SampleUtilityMap_VTX(_DetailMap, clampUVs);
					discardMap = 1 - discardMap;

					if (discardMap > 0.95)
					{
						o.pos = 0; // create a degenerate triangle that will be eliminated by the GPU.
						return o;
					}
				#endif

				half2 uvPos = (v.uv_3.x + v.uv_3.y)  * 0.35;
				float2 origin = mul(UNITY_MATRIX_M, float4(uvPos.x,0, uvPos.y,1)).xz;
				half worldNoise = tex2Dlod(_WindNoise, float4(origin * 0.05, 0, 0));
				worldNoise = pow(worldNoise, 2);
				o.ID = ((origin.x + origin.y) * 0.5) * worldNoise;
				o.ID = abs(o.ID);
				o.ID = max(frac(o.ID), 0.01);
				//o.ID = worldNoise;

				o.color = v.color;
				half windAmplitude;

				#if WindToggle
					_WindOffsetGrass(o.worldPos, windAmplitude, _WindSpeed, _WindFrequency, _WindAmplitude, _WindNoise, v.color);
				#endif

				o.pos = UnityObjectToClipPos(mul(UNITY_MATRIX_I_M, o.worldPos));


				_WORLD_NORMAL_VERT(v);
				_DOT_LIGHT_VERT(v);
				_CACHED_SHADOW_VERT(o);
				_CalculateDistanceFog(v.vertex, o.distanceFog);

				//Light SSS
				//o.lightDir = _CameraToLightDot();
				_CALC_SCREENPOS(o);
				TRANSFER_SHADOW(o)
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				return o;
			}

			float4 frag(v2f i) : SV_Target
			{
				Include_ScreenSpaceBooleanSubtractCuboid_Fragment;
				CircleClip_Fragment(i.worldPos);
				
				UNITY_SETUP_INSTANCE_ID(i);
				#if defined(LOD_FADE_CROSSFADE)
					float2 vpos = _ScreenPosToVPos(i.screenPos);
					UnityApplyDitherCrossFade(vpos);
				#endif
				fixed alpha = tex2D(_MainTex, i.uv).x;
				

				half randomPositionID = i.ID;

				half randFade =  UNITY_ACCESS_INSTANCED_PROP(FoliageProps, _RandomFade);
				half alphaFade = step(randomPositionID * alpha, randFade);
				//return alphaFade;

				clip(alpha - alphaFade - _Cutoff);

				fixed4 colouring = tex2D(_TintTex, i.uv_2);


				fixed3 sssCol = colouring.xyz;//TODO - HDRP - fix this //lerp(colouring - _FoliageColShift, colouring, i.lightDir);

				sssCol = lerp(sssCol, _OwnLand(fixed4(sssCol, 1), i.worldPos).xyz, _AffectedByDistricts);

				//_CACHED_SHADOW_FRAG(i);
				_LIGHT_FROM_NORMAL_TANGENT(i);
				//_APPLY_SHADOW_FRAG(i);

				//return fixed4(shadow.xxx, 1);

				//fixed3 col = _LitAlbedo(sssCol, shadow);
				//_ApplyDistanceFog(i.distanceFog, col.rgb);

				float3 normal = float3(0,1,0); // tangentNormal.xyz;
				return ApplyLighting(sssCol, normal, i.worldPos, .1, .1);
			}
			ENDHLSL
		}
		
		/*Pass
		{
			Tags { "LightMode"="Forward" }

			Cull Off

			ZWrite On 
			CGPROGRAM

			#pragma vertex vert
			#pragma fragment frag
			#pragma instancing_options lodfade
			#pragma multi_compile_fwdbase
			#pragma multi_compile_instancing
			#pragma multi_compile _ WindToggle
			#pragma multi_compile _ _USE_DISCARD_MAP
			#pragma multi_compile _ LOD_FADE_CROSSFADE

			#pragma target 3.5

			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
						
			struct v2f
			{
				_BASE_VARS(0)
				SHADOW_COORDS(1)
				_LIGHTING_VARS(1,2)
				_DISTANCE_VARS(3)
				_WORLDPOS_VARS(4)
				fixed4 color : TEXCOORD5;
				float lightDir : TEXCOORD6;
				half2 uv_2 : TEXCOORD7;
				_SCREENPOS_VAR(8)
				_INSTANCEID_VAR(9)
				float ID : TEXCOORD10;
			};

			_BASE_DEFS;
			half _WindSpeed;
			half _WindFrequency;
			half _WindAmplitude;
			half _WindSize;
			half _FoliageColShift;
			sampler2D _TintTex;

			UNITY_INSTANCING_BUFFER_START(FoliageProps)
				UNITY_DEFINE_INSTANCED_PROP(half, _RandomFade)
			UNITY_INSTANCING_BUFFER_END(FoliageProps)

			v2f vert (appdata_instancing v)
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v); 
				UNITY_INITIALIZE_OUTPUT(v2f, o);  
				o.uv = v.uv;
				o.uv_2 = v.uv_2;

				_CALC_WORLDPOS(o, v);
				#if _USE_DISCARD_MAP
					float4 axisWorldPosition;
					_CALC_WORLDPOS_POINT(float4(0, 0, 0, 1), axisWorldPosition);
					float4 clampUVs = float4((axisWorldPosition.x + 512)*0.0009765625, (axisWorldPosition.z + 512)*0.0009765625, 0, 0);
					float discardMap = _SampleUtilityMap_VTX(_DetailMap, clampUVs);
					discardMap = 1 - discardMap;

					if (discardMap > 0.95)
					{
						o.pos = 0; // create a degenerate triangle that will be eliminated by the GPU.
						return o;
					}
				#endif

				half2 uvPos = (v.uv_3.x + v.uv_3.y)  * 0.35;
				float2 origin = mul(UNITY_MATRIX_M, float4(uvPos.x,0, uvPos.y,1)).xz;
				half worldNoise = tex2Dlod(_WindNoise, float4(origin * 0.05, 0, 0));
				worldNoise = pow(worldNoise, 2);
				o.ID = ((origin.x + origin.y) * 0.5) * worldNoise;
				o.ID = abs(o.ID);
				o.ID = max(frac(o.ID), 0.01);
				//o.ID = worldNoise;

				o.color = v.color;
				half windAmplitude;

				#if WindToggle
					_WindOffsetGrass(o.worldPos, windAmplitude, _WindSpeed, _WindFrequency, _WindAmplitude, _WindNoise, v.color);
				#endif

				o.pos = UnityObjectToClipPos(mul(UNITY_MATRIX_I_M, o.worldPos));


				_WORLD_NORMAL_VERT(v);
				_DOT_LIGHT_VERT(v);
				_CACHED_SHADOW_VERT(o);
				_CalculateDistanceFog(v.vertex, o.distanceFog);

				//Light SSS
				o.lightDir = _CameraToLightDot();
				_CALC_SCREENPOS(o);
				TRANSFER_SHADOW(o)
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				return o;
			}
			
			fixed4 frag (v2f i) : SV_Target
			{

				UNITY_SETUP_INSTANCE_ID(i);
				#if defined(LOD_FADE_CROSSFADE)
					float2 vpos = _ScreenPosToVPos(i.screenPos);
					UnityApplyDitherCrossFade(vpos);
				#endif
				fixed alpha = tex2D(_MainTex, i.uv).x;
				

				half randomPositionID = i.ID;

				half randFade =  UNITY_ACCESS_INSTANCED_PROP(FoliageProps, _RandomFade);
				half alphaFade = step(randomPositionID * alpha, randFade);
				//return alphaFade;

				clip(alpha - alphaFade - _Cutoff);

				fixed4 colouring = tex2D(_TintTex, i.uv_2);


				fixed3 sssCol = lerp(colouring - _FoliageColShift, colouring, i.lightDir);

				sssCol = lerp(sssCol, _OwnLand(fixed4(sssCol, 1), i.worldPos).xyz, _AffectedByDistricts);

				_CACHED_SHADOW_FRAG(i);
				_LIGHT_FROM_NORMAL_TANGENT(i);
				_APPLY_SHADOW_FRAG(i);

				//return fixed4(shadow.xxx, 1);

				fixed3 col = _LitAlbedo(sssCol, shadow);
				_ApplyDistanceFog(i.distanceFog, col.rgb);

				return float4(col.xyz * .001, 1);
			}
			ENDCG
		}*/
		//UsePass "_Shadowcasters/S_ShadowCasters/ShadowCaster_Foliage"
		UsePass "_Standard/S_StandardTinted_VF/StandardShadow"
	}
}
