Shader "_VFX/S_VertexAnim_Opaque"
{
	Properties
	{
		[NoScaleOffset]_VertAnimPos("Vert Anim Pos", 2D) = "white" {}
		[NoScaleOffset]_VertAnimNorm("Vert Anim Normal", 2D) = "white" {}
		_AnimMagnitude("Anim Magnitude", Float) = 1
		[NoScaleOffset]_UtilityTex("Mask Tex", 2D) = "white" {}

		[NoScaleOffset]_MainTex("Albedo Texture", 2D) = "white" {}
		_SpecularColor("Specular Color",Color) = (1,1,1,1)
		_SpecularPower("Specular Power", Float) = 1
		_SpecularIntensity("Specular Intensity", Float) = 2


		_FresnelColor("Fresnel Color", Color) = (0.5,0.5,0.5,1)
		_FresnelPower("Fresnel Power", Range(0.0,10.0)) = 2
		_FresnelExponent("Fresnel Exponent", Range(0.001, 30)) = 2

		_Reflection("Reflection", Range(0.0,1.0)) = 0  // new
		_ReflectionFresnel("Reflection Fresnel", Range(0,4)) = 0
		[NoScaleOffset]_ErosionTex("Erosion Texture", 2D) = "black" {}
		_ErodeFrequency("Noise Frequency", Float) = 0.5



		//[Toggle(FLIP_FRESNEL)] _FlipFresnel("Flip Fresnel",Float) = 1
		[Toggle(FRESNEL_PER_PIXEL)] _FresnelPerPixel("Per Pixel Fresnel",Float) = 0
		//[Toggle(USE_SMOOTHNESS)] _UseSmoothness("Use Smoothness Parameter",Float) = 1
		_Smoothness("Smoothness", Range(0,1)) = 0


		_ShadowPull("Shadow Distance Adjustment", Float) = 1
		[Toggle(NORMALIZE_TANGENTS_PER_PIXEL)] _NormalizeTangentsPerPixel("Normalize Tangents Per Pixel",Float) = 0



		_ErodeDelay("Erode Delay", Range(0,1)) = 0
		_ErodeVDMagnitude("Erode Vert Displace Magnitude", Float) = 1
		[Toggle(_REFRACTION_PLANE)]_RefractionPlane("Refraction Plane - T(XY), F(XZ)", Float) = 0
		_RefractionStrength("Refraction Strength", Float) = 1
		_RefractionAmountClose("Refraction Amount Close", Range(0,1)) = 1
		_RefractionAmountFar("Refraction Amount Far", Range(0,1)) = 0
		_RefractionDistanceClose("Refraction Distance Close", Float) = 10
		_RefractionDistanceFar("Refraction Distance Far", Float) = 100

		_AdjustStart("Adjust Start Time", Range(-0.1, 1)) = 0
		_AdjustEnd("Adjust End Time", Range(0, 1.1)) = 1

		[Toggle(_FADE_ON_START)]_FadeOnStart("Fade On Start", Float) = 0
		[Toggle(_FADE_ON_END)]_FadeOnEnd("Fade On End", Float) = 0
		_FadeSpeed("Fade Speed", Range(1,10)) = 5

		[Toggle(_CONTINUOUS_ANIM)]_ContinuousAnim("Continuous Anim", Float) = 0
		_AnimProgress("Pickup Progress", Range(0,1)) = 0
		_AnimSpeed("Continuous Anim Speed", Float) = 1
		_Erode("Erode Slider", Range(0,1)) = 0

		_TintColour("Constant Tint", Color) = (1,1,1,1)
		[HideInInspector][Toggle(_REFRACTS)]_Refracts("Refracts", Float) = 1

		_WindDisplacementAmount("Wind Displacement Amount", Range(0,1)) = 0

		// Blending state
		[HideInInspector] _Mode("__mode", Float) = 4.0
		[HideInInspector] _SrcBlend("__src", Float) = 1.0
		[HideInInspector] _DstBlend("__dst", Float) = 0.0
		[HideInInspector] _ZWrite("__zw", Float) = 1.0
		[HideInInspector] _Cull("_cull", Float) = 0.0
		[HideInInspector] _ZTest("_ztest", Float) = 0.0

	}
	
	SubShader
	{
    	Tags { "RenderPipeline" = "UniversalPipeline" "UniversalMaterialType" = "Lit" "RenderType" = "Opaque" }
		//Tags{"DisableBatching" = "True"}
		LOD 300
		Blend[_SrcBlend][_DstBlend]
		ZTest[_ZTest]
		Cull[_Cull]
		ZWrite[_ZWrite]

		Pass
		{
			Tags { "LightMode"="UniversalForward" }
			HLSLPROGRAM
			#pragma vertex vert_VA
			#pragma fragment frag
			#pragma target 3.5
			#pragma shader_feature_local _FADE_ON_START
			#pragma shader_feature_local _FADE_ON_END
			#pragma shader_feature_local _CONTINUOUS_ANIM
			#pragma multi_compile _VA_INSTANCING_ON _VA_INSTANCING_OFF

			#pragma shader_feature_local FRESNEL_PER_PIXEL

			#pragma multi_compile_instancing
			#pragma multi_compile_fwdbase

			#pragma skip_variants DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON

			#include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_VertAnimDefs.cginc"

			
			fixed4 frag(v2f_VA i) : SV_Target
			{
				#if _VA_INSTANCING_ON
					UNITY_SETUP_INSTANCE_ID(i);
					//return fixed4(1,0,0,1);
					half erodeProgVert = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _Erode);
					fixed4 tintColor = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _TintColour);
				#else
					half erodeProgVert =  _Erode;
					fixed4 tintColor = _TintColour;
					//return fixed4(0, 1, 0, 1);
				#endif

				fixed4 albedo = tex2D(_MainTex, i.uv_1);


				


				_VertAnimClip(i);
				
				_SpecularIntensity *= i.specularRemoveSlider;
				float3 specular = saturate(_Specularity_BlinnPhong(i.worldPos, i.worldNormal, _SpecularPower, _SpecularColor).xyz * _SpecularIntensity);

				

				#if FRESNEL_PER_PIXEL
					_CALC_Fresnel_Pixel(i, i.worldNormal);
					i.fresnel = fresnel;
				#endif // FRESNEL_PER_PIXEL
				i.fresnel *= i.color.y;
				

				half3 worldNormal = i.worldNormal;
				half3 worldViewDir = _WorldViewDir(i.worldPos);
				half3 worldReflect = reflect(-worldViewDir, worldNormal);

				fixed3 skyColor = _ReflectCubemap_LOD(worldReflect,(1-_Smoothness ) * 5);
				fixed4 baseCol;
				
				baseCol.xyz = lerp(albedo, _FresnelColor.xyz, i.fresnel);


				half normDiff = i.clouds;

				_CACHED_SHADOW_FRAG(i);
				_APPLY_SHADOW_FRAG(i);


				baseCol.w = 1;

				// quick hack!
				#if _CONTINUOUS_ANIM
					baseCol.rgb = lerp(baseCol.xyz, tintColor.xyz, 0.2);
				#endif
				baseCol.rgb = lerp(baseCol.rgb, skyColor, i.fresnel * _Reflection);

				return ApplyLighting(baseCol.rgb, worldNormal, i.worldPos, .1, .1);
            }
            ENDHLSL
        }
		UsePass "_Shadowcasters/S_ShadowCasters/ShadowCaster_VertAnim"
    }


	// LOD 100 -  No refraction, no dynamic shadow
	/*SubShader
	{
		Tags {
				"RenderType" = "Opaque"
				"Queue" = "Geometry"
				"IgnoreProjector" = "True"
				"DisableBatching" = "True"
			}
		LOD 100
		//Blend SrcAlpha OneMinusSrcAlpha
		ZTest[_ZTest]
		Cull[_Cull]
		Pass
		{
			CGPROGRAM
			#pragma vertex vert_VA
			#pragma fragment frag
			#pragma target 3.5
			#pragma shader_feature_local _FADE_ON_START
			#pragma shader_feature_local _FADE_ON_END
			#pragma shader_feature_local _CONTINUOUS_ANIM
			#pragma multi_compile _VA_INSTANCING_ON _VA_INSTANCING_OFF

			#pragma multi_compile_instancing
			#pragma multi_compile_fwdbase

			//#include "UnityCG.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_VertAnimDefs.cginc"

		
			

            fixed4 frag (v2f_VA i) : SV_Target
            {
				#if _VA_INSTANCING_ON
					UNITY_SETUP_INSTANCE_ID(i);
					//return fixed4(1,0,0,1);
					half erodeProgVert = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _Erode);
					fixed4 tintColor = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _TintColour);
				#else
					half erodeProgVert =  _Erode;
					fixed4 tintColor = _TintColour;
					//return fixed4(0, 1, 0, 1);
				#endif


				half erodeSlider = saturate(_RangeRemapFloat(_ErodeDelay, 1, 0, 1, erodeProgVert));

				

				half erosion = tex2D(_ErosionTex, i.uv_1 * _ErodeFrequency).x;

				half visibility = i.color.x;
				half clipVal = lerp(1, 0, erodeSlider) * visibility;
				//clipVal = clipVal - erosion -  - (1 - albedo.w);
				clipVal = clipVal - erosion - (1 - i.specularRemoveSlider);
				
				clip(clipVal);
				

				fixed specGrayscale = i.specular.x * 0.3 + i.specular.y * 0.6 + i.specular.z * 0.1;
				specGrayscale = saturate(specGrayscale *  5);

				fixed4 baseCol;
				fixed4 albedo = tex2D(_MainTex, i.uv_1);


				baseCol.xyz = lerp(albedo, _FresnelColor.xyz, i.fresnel);

				i.specular *= _SpecularIntensity;
				fixed3 col = baseCol.xyz + (i.specular);
				col *= tintColor.xyz;

				
				
				_ApplyDistanceFog(i.distanceFog, col.rgb);

				fixed4 output = fixed4(col, 1);

                return output;
            }
            ENDCG
        }
    }*/
	CustomEditor "GUI_VertAnim"
}