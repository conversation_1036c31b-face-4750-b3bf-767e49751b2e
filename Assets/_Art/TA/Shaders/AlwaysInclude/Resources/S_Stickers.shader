Shader "_Standard/S_Stickers"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
		_Cutoff("Alpha Cutoff", Range(0.0, 1.0)) = 0.5
		[Toggle(IS_R8)]_IsR8("Texture is R8 format", Float) = 0
		_Tint("Tint", Color) = (1,1,1,1)
		_ShadowPull("Cached Shadows Adjustment", Range(0.9, 1.1)) = 1.01
		[Toggle(_WIND_DISPLACE)]_WindDisplace("Wind Displace", Float) = 0
		[Toggle(_LOCAL_DISPLACEMENT)]_LocalDisplacement("Local Displacement", Float) = 0
		[Toggle(_PREMIUM_REWARD)]_PremiumReward("Premium Reward", Float) = 0

		[HideInInspector]_PremiumVars1("Premium Vars 1", Color) = (0, 0, 0, 0)
	}
	SubShader
	{
		Tags { "RenderType" = "Opaque" }
		LOD 100

		Pass
		{
			HLSLPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma shader_feature_local IS_R8
			#pragma shader_feature_local __ _WIND_DISPLACE
			#pragma shader_feature_local __ _LOCAL_DISPLACEMENT
			#pragma multi_compile __ _PREMIUM_REWARD
			#pragma multi_compile_instancing

			#include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_PremiumRewards.cginc"


			sampler2D _MainTex;
			float4 _MainTex_ST;
			#if _PREMIUM_REWARD
				float4 _PremiumVars1;
			#endif
			half _Cutoff;

			struct v2f
			{
				_BASE_VARS(0)
				float3 viewDir : TEXCOORD1;
				_WORLDPOS_VARS(2)
				_DISTANCE_VARS(3)
				_LIGHTING_VARS(0, 4)
				fixed4 color : COLOR1;
				SHADOW_COORDS(5)
				half3 worldNormal : TEXCOORD6;
			};

			fixed4 _Tint;

			v2f vert (appdata_instancing v)
			{
				_FRAC_TIME_Y;
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				_VERT_INITIALIZE(o, v, v2f);
				o.uv = TRANSFORM_TEX(o.uv, _MainTex );

				_WORLD_NORMAL_VERT(v);
				_CALC_WORLDPOS(o, v);

				#if _WIND_DISPLACE
				//apply wind
					float3 noise = float3(0, 0, 0);
					#if _LOCAL_DISPLACEMENT
						noise = _BannerWindNoise(v.vertex.xyz + _Time.y , 3, 0.05);
					#else
						noise = _BannerWindNoise(o.worldPos, 3, 0.05);
					#endif
					v.vertex.xyz += noise;
				#endif

				TRANSFER_SHADOW(o)
				o.worldNormal = normalize(worldNormal);
				o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos.xyz);
				fixed nl = 1;
				_CACHED_SHADOW_VERT(o);
				_CalculateDistanceFog(v.vertex, o.distanceFog);
				o.pos = UnityObjectToClipPos(v.vertex);
				return o;
			}

			fixed4 frag (v2f i) : SV_Target
			{
				#if IS_R8
					fixed4 baseCol = fixed4(_Tint.xyz, tex2D(_MainTex, i.uv).x * _Tint.w);
				#else
					fixed4 baseCol = tex2D(_MainTex, i.uv) * _Tint;
				#endif


				baseCol.a *= (step(_MainTex_ST.z, i.uv.x) *  step(_MainTex_ST.w, i.uv.y) * step(i.uv.x , _MainTex_ST.x + _MainTex_ST.z) * step(i.uv.y , _MainTex_ST.y + _MainTex_ST.w));


				clip(baseCol.a - _Cutoff);
				half3 worldNormal = normalize(i.worldNormal);



				//Lighting
				_CACHED_SHADOW_MASKED_FRAG(i);
				_LIGHT_FROM_NORMAL_WORLD(i);
				_APPLY_SHADOW_FRAG(i);

				//Color
				baseCol.xyz = saturate(_LitAlbedo(baseCol.xyz, saturate(shadow)));

				// Bounce light
				fixed3 bounceLight = dot(worldNormal, -_BounceLightDirection.xyz) * _BounceLightColor * _BounceLightIntensity;

				fixed4 finalCol = baseCol + fixed4(bounceLight, 0);


				#if _PREMIUM_REWARD
					fixed3 iridescence = _Iridescence(i.viewDir, worldNormal);
					finalCol.xyz += _Glitter_LQ(i.uv, worldNormal, fixed4(1,1,1,1), _PremiumVars1, i.viewDir, i.worldPos);

					finalCol.xyz = lerp(finalCol.xyz ,iridescence , _PremiumVars1.y);
				#endif



				return finalCol;
			}
			ENDHLSL
		}
	}
}
