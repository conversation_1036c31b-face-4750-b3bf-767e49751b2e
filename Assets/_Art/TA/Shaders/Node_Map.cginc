#include "Assets/_Art/TA/Shaders/Old/OwnedLand.cginc"

float HeightAtPoint(float3 _pos)
{
    float2 quantPos = _pos.xz;
    float4 quantUV = float4(quantPos * _HeightmapToUV.xy + _HeightmapToUV.zw, 0, 0);
    //float4 district = SAMPLE_TEXTURE2D_LOD(_OwnLandMaskVS, sampler_OwnLandMaskVS, quantUV.xy, 0);
    float4 heights = SAMPLE_TEXTURE2D_LOD(_HeightMapV, sampler_HeightMapV, quantUV.xy, 0);
    return dot(_HeightmapDot, heights);
}

float _MapMode;

void Node_Map_float(
float4 WorldPos,
float3 ViewDirection,
float4 WorldColour,
float3 WorldNormal,
float WorldSmoothness,
out float4 Colour,
out float3 Normal,
out float Smoothness
)
{
    const float c_step = 1.5;
    const float c_seaLevel = 90;
    const float c_quant = 1.0 / 10.0;
    const float c_quantStep = -.25;
    const float c_quantMax = -5;
    const float c_fadeUV = .002 * 15 * 3;
    const float c_borderUV = 0;//.0075;
    const float c_fadeInset = 1;
    float3 viewUp = ViewDirection; viewUp.y = 0; viewUp = normalize(viewUp);
    float3 viewSide = float3(-viewUp.z, 0, viewUp.x);
    float2 quantPos = WorldPos.xz;
    float4 quantUV = float4(quantPos * _HeightmapToUV.xy + _HeightmapToUV.zw, 0, 0);
    float fade = 1;//smoothstep(-c_fadeUV * (1 - c_fadeInset), c_fadeUV * c_fadeInset, quantUV.x) * smoothstep(-c_fadeUV * (1 - c_fadeInset), c_fadeUV * c_fadeInset, quantUV.y) * smoothstep(1 + c_fadeUV * (1 - c_fadeInset), 1 - c_fadeUV * c_fadeInset, quantUV.x) * smoothstep(1 + c_fadeUV * (1 - c_fadeInset), 1 - c_fadeUV * c_fadeInset, quantUV.y);
    float border = smoothstep(c_borderUV, c_borderUV + c_fadeUV, quantUV.x) * smoothstep(c_borderUV, c_borderUV + c_fadeUV, quantUV.y) * smoothstep(1 - c_borderUV, 1 - c_borderUV - c_fadeUV, quantUV.x) * smoothstep(1 - c_borderUV, 1 - c_borderUV - c_fadeUV, quantUV.y);
    //float height00 = HeightAtPoint(WorldPos.xyz);
    float heightN0 = HeightAtPoint(WorldPos.xyz - viewSide * c_step);
    float heightP0 = HeightAtPoint(WorldPos.xyz + viewSide * c_step);
    float height0N = HeightAtPoint(WorldPos.xyz - viewUp * c_step);
    float height0P = HeightAtPoint(WorldPos.xyz + viewUp * c_step);
    float heightShadow = HeightAtPoint(WorldPos.xyz - viewUp * (c_step * 6));
    float heightMin = min(min(min(heightN0, heightP0), height0N), height0P);
    float heightMax = max(max(max(heightN0, heightP0), height0N), height0P);
    float qMin = floor((heightMin - c_seaLevel) * c_quant);
    float qMax = floor((heightMax - c_seaLevel) * c_quant);
    float crossed = step(.05, qMax - qMin) * fade;
    qMin = lerp(qMin - .2, c_quantMax, 1 - fade);
    float4 baseColour = WorldColour;
    float4 seaColourShallow = baseColour * 3;
    float4 seaColourDeep = baseColour * 1;
    float lerpQuant = min(2, (qMin - 1) * c_quantStep);
    lerpQuant = lerp(1, lerpQuant, border);
    float4 seaColour = lerp(seaColourShallow, seaColourDeep, lerpQuant);
    float shadow = step(c_seaLevel, heightShadow) * .7;
    float lerpValue = min(1, crossed + shadow);
    lerpValue *= border;
    float4 finalColour = lerp(seaColour, float4(0, 0, 0, 1), lerpValue * .6);
    Colour = lerp(WorldColour, finalColour, fade);
    Normal = WorldNormal;//lerp(WorldNormal, float3(0, 1, 0), fade);
    Smoothness = lerp(WorldSmoothness, 0, fade);
}
