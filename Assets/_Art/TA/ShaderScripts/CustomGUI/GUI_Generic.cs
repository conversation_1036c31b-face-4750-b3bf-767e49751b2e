#if UNITY_EDITOR
using System;
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;

 
public class GUI_Generic : ShaderGUI
{
	//Setting up the different Blend modes for the shader
	public enum BlendMode
	{
		Transparent,
		Additive,
		Opaque,
		SkipPass
	}


	bool m_FirstTimeApply = true;
	private string[] m_exclusions_Scroll = { "_UseScrollingTexture", "_ScrollU", "_ScrollV" };
	private string[] m_exclusions_Depth = { "_UseDepthBlending", "_Blending_Falloff", "_Blending_Threshold", "_Blending_Blur", "_DebugDepthBlending" };
	private string[] m_exclusions_AlphaErode = { "_UseAlphaErode", "_Threshold", "_ThresholdBlur" };
	private string[] m_exclusions_Flow = { "_UseFlowMap" , "_FlowTex", "_FlowAmplitude", "_FlowSpeed", "_FlowScrolling_X", "_FlowScrolling_Y", "_FlowMask" };
	private string[] m_exclusions_Fresnel = { "_UseFresnel", "_FresnelExponent", "_FresnelPower", "_InvertFresnel" };
	private string[] m_exclusions_Mask = { "_UseMask", "_MaskMultiplier", "_InvertMask", "_MaskTex", "_MaskScrollingSpeed_U" , "_MaskScrollingSpeed_V" , "_AffetedByFlowmap_A" };
	private string[] m_exclusions_SecMask = { "_UseSecMask", "_MaskSecMultiplier", "_InvertSecMask", "_MaskSecTex", "_MaskSecScrollingSpeed_U", "_MaskSecScrollingSpeed_V" };
	private string[] m_exclusions_PullToCam = { "_PullToCamera", "_Pull" };
	private string[] m_exclusions_Cubemap = { "_CubemapReflection", "_CubeTex" };
	private string[] m_exclusions_DistanceFade = { "_DistanceFade", "_FadeAffectsScale" , "_FadeAffectsAlpha", "_FadeStart", "_FadeEnd" };
	private string[] m_exclusions_WorldIcon = { "_WorldIconSize", "_WorldIconAlpha" };

	private string[] m_exclusions;
	Material m_material;
	private bool m_materialOverrides;

	//Setting up the tooltip texts
	private static class Styles
	{
		public static string whiteSpaceString = " ";
		public static string renderingMode = "Rendering Mode";
		public static readonly string[] blendNames = Enum.GetNames(typeof (BlendMode));

		public static string cullMode = "Culling Mode";
		public static readonly string[] cullNames = Enum.GetNames(typeof(CullMode));

		public static string zTestMode = "ZTest Mode";
		public static readonly string[] zTestNames = Enum.GetNames(typeof(CompareFunction));
	}
 
	MaterialProperty blendMode = null;
	MaterialProperty cullMode = null;
	MaterialProperty zTestMode = null;

	MaterialEditor m_MaterialEditor;
 
	public override void OnGUI (MaterialEditor materialEditor, MaterialProperty[] props)
	{
		m_exclusions = new string[m_exclusions_AlphaErode.Length + m_exclusions_Depth.Length + m_exclusions_Flow.Length + m_exclusions_Scroll.Length + m_exclusions_Fresnel.Length + m_exclusions_Mask.Length + m_exclusions_SecMask.Length + m_exclusions_PullToCam.Length + m_exclusions_Cubemap.Length + m_exclusions_DistanceFade.Length + m_exclusions_WorldIcon.Length];
		int currentIndex = 0;
		m_exclusions_AlphaErode.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_AlphaErode.Length;
		m_exclusions_Depth.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_Depth.Length;
		m_exclusions_Flow.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_Flow.Length;
		m_exclusions_Scroll.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_Scroll.Length;
		m_exclusions_Fresnel.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_Fresnel.Length;
		m_exclusions_Mask.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_Mask.Length;
		m_exclusions_SecMask.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_SecMask.Length;
		m_exclusions_PullToCam.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_PullToCam.Length;
		m_exclusions_Cubemap.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_Cubemap.Length;
		m_exclusions_DistanceFade.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_DistanceFade.Length;
		m_exclusions_WorldIcon.CopyTo(m_exclusions, currentIndex);
		currentIndex += m_exclusions_WorldIcon.Length;

		

		_FindModeOverrides(props);
		m_MaterialEditor = materialEditor;
		m_material = materialEditor.target as Material;
 
		if (m_FirstTimeApply) {
			MaterialChanged (m_material);
			m_FirstTimeApply = false;
		}
 
		ShaderPropertiesGUI (materialEditor, props);
	}
 
	//Setting up the workspace for the different maps, except those with more advanced setup
	public void ShaderPropertiesGUI (MaterialEditor materialEditor, MaterialProperty[] properties)
	{
		EditorGUIUtility.labelWidth = 0f;
 
		
		m_MaterialEditor = materialEditor;
		DrawMainGUI(materialEditor, properties, m_exclusions);
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_Scroll, "_UseScrollingTexture", "USE_SCROLLING_TEXTURE", "Scroll Main Texture", "Properties - Main Texture Scrolling");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_Depth, "_UseDepthBlending", "USE_DEPTH_BLENDING", "Use Depth Blending", "Properties - Depth Blending");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_AlphaErode, "_UseAlphaErode", "USE_ALPHA_ERODE", "Use Alpha Erode", "Properties - Alpha Erosion");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_Flow, "_UseFlowMap", "USE_FLOW_MAP", "Use Flow Map", "Properties - Flow");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_Fresnel, "_UseFresnel", "USE_FRESNEL", "Use Fresnel", "Properties - Fresnel");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_Mask, "_UseMask", "USE_MASK", "Use Mask", "Properties - Mask - Primary");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_SecMask, "_UseSecMask", "USE_SEC_MASK", "Use Secondary Mask", "Properties - Mask - Secondary");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_PullToCam, "_PullToCamera", "PULL_TO_CAMERA", "Pull Towards Camera", "Properties - Pull Towards Camera");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_Cubemap, "_CubemapReflection", "CUBEMAP_REFLECTION", "Cubemap Reflection", "Properties - Cubemap Reflection");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_DistanceFade, "_DistanceFade", "DISTANCE_FADE", "Distance Fade", "Properties - Distance Fade");
		DrawExclusionsGUI(materialEditor, properties, m_exclusions_WorldIcon, "_WorldIconSize", "_WorldIconSize", "World Icon", "Properties - World Icon");

		

		m_MaterialEditor.RenderQueueField();
		m_MaterialEditor.EnableInstancingField();

		if(m_materialOverrides == true)
		{
			EditorGUI.BeginChangeCheck();
			{
				BlendModePopup();
				CullModePopup();
				ZTestModePopup();
			}

			if (EditorGUI.EndChangeCheck())
			{
				foreach (var obj in blendMode.targets)
					MaterialChanged((Material)obj);
			}
		}
	}

	public void DrawMainGUI(MaterialEditor materialEditor, MaterialProperty[] properties, string[] exclusions)
	{
		GUILayout.Label("Properties - Main", EditorStyles.boldLabel);
		foreach (MaterialProperty property in properties)
		{
			bool isExcludedFromInitialDraw = false;
			string propertyFlag = property.flags.ToString();
			foreach (string excludedItem in exclusions)
			{

				if (property.name == excludedItem || propertyFlag.Equals("HideInInspector"))
				{
					isExcludedFromInitialDraw = true;
				}
			}
			if (isExcludedFromInitialDraw == false)
			{
				materialEditor.ShaderProperty(property, property.displayName);
			}
		}
		
	}

	public void DrawExclusionsGUI(MaterialEditor materialEditor, MaterialProperty[] properties, string[] exclusions, string toggleName, string keyword, string toggleLabel, string categoryLabel)
	{
		if (m_material.HasProperty(toggleName))
		{
			
			bool useFeature = m_material.IsKeywordEnabled(keyword);
			if (useFeature)
			{
				GUILayout.Label(categoryLabel, EditorStyles.boldLabel);
			}
			EditorGUI.BeginChangeCheck();
			useFeature = EditorGUILayout.Toggle(toggleLabel, useFeature);
			if (EditorGUI.EndChangeCheck())
			{
				// enable or disable the keyword based on checkbox
				if (useFeature)
				{
					m_material.EnableKeyword(keyword);

				}
				else
				{
					m_material.DisableKeyword(keyword);
				}
				m_material.SetFloat(toggleName, useFeature ? 1 : 0);
			}

			if (useFeature)
			{

				foreach (MaterialProperty property in properties)
				{
					bool isExcludedFromInitialDraw = false;
					foreach (string excludedItem in exclusions)
					{
						if (property.name == excludedItem && property.name != toggleName)
						{
							isExcludedFromInitialDraw = true;
						}
					}
					if (isExcludedFromInitialDraw == true)
					{
						materialEditor.ShaderProperty(property, property.displayName);
					}
				}
				GUILayout.Label("------------", EditorStyles.whiteMiniLabel);
			}

			
		}
	}


	void BlendModePopup ()
	{
		EditorGUI.showMixedValue = blendMode.hasMixedValue;
		var mode = (BlendMode)blendMode.floatValue;
 
		EditorGUI.BeginChangeCheck ();
		mode = (BlendMode)EditorGUILayout.Popup (Styles.renderingMode, (int)mode, Styles.blendNames);
		if (EditorGUI.EndChangeCheck()) {
			m_MaterialEditor.RegisterPropertyChangeUndo ("Rendering Mode");
			blendMode.floatValue = (float)mode;
		}
 
		EditorGUI.showMixedValue = false;
	}
	
	void CullModePopup()
	{
		EditorGUI.showMixedValue = cullMode.hasMixedValue;
		var mode = (CullMode)cullMode.floatValue;

		EditorGUI.BeginChangeCheck();
		mode = (CullMode)EditorGUILayout.Popup(Styles.cullMode, (int)mode, Styles.cullNames);
		if (EditorGUI.EndChangeCheck())
		{
			m_MaterialEditor.RegisterPropertyChangeUndo("Culling Mode");
			cullMode.floatValue = (float)mode;
		}

		EditorGUI.showMixedValue = false;
	}

	void ZTestModePopup()
	{
		EditorGUI.showMixedValue = zTestMode.hasMixedValue;
		var mode = (CompareFunction)zTestMode.floatValue;

		EditorGUI.BeginChangeCheck();
		mode = (CompareFunction)EditorGUILayout.Popup(Styles.zTestMode, (int)mode, Styles.zTestNames);
		if (EditorGUI.EndChangeCheck())
		{
			m_MaterialEditor.RegisterPropertyChangeUndo("ZTest Mode");
			zTestMode.floatValue = (float)mode;
		}

		EditorGUI.showMixedValue = false;
	}

	public static void SetupMaterialWithBlendMode (Material material, BlendMode blendMode) {
		switch (blendMode) {
		case BlendMode.Additive:
			//material.SetOverrideTag ("Queue", "Transparent");
			material.SetOverrideTag ("RenderType", "Transparent");
			material.SetOverrideTag ("IgnoreProjector", "True");
			material.SetInt ("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.One);
			material.SetInt ("_DstBlend", (int)UnityEngine.Rendering.BlendMode.One);
			material.SetInt ("_ZWrite", 0);
			material.EnableKeyword("IS_ADDITIVE");
			//material.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent;
			break;
 
		case BlendMode.Transparent:
			//material.SetOverrideTag ("Queue", "Transparent");
			material.SetOverrideTag ("RenderType", "Transparent");
			material.SetOverrideTag ("IgnoreProjector", "True");
			material.SetInt ("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
			material.SetInt ("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
			material.SetInt ("_ZWrite", 0);
			material.DisableKeyword("IS_ADDITIVE");
			//material.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent;
			break;
 
		case BlendMode.Opaque:
			//material.SetOverrideTag ("Queue", "Transparent");
			material.SetOverrideTag ("RenderType", "Opaque");
			material.SetOverrideTag ("IgnoreProjector", "True");
			material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.One);
			material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.Zero);
				material.SetInt ("_ZWrite", 1);
			material.DisableKeyword("IS_ADDITIVE");
			break;

		case BlendMode.SkipPass:
			//material.SetOverrideTag ("Queue", "Transparent");
			material.SetOverrideTag ("RenderType", "DepthNormalsExempt");
			material.SetOverrideTag ("IgnoreProjector", "True");
			material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.One);
			material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.Zero);
				material.SetInt ("_ZWrite", 1);
			material.DisableKeyword("IS_ADDITIVE");
			break;

		}
	}
	
	public static void SetupMaterialWithCullMode(Material material, CullMode cullMode)
	{
		material.SetInt( "_Cull", (int) cullMode ) ;
	}

	public static void SetupMaterialWithZTestMode(Material material, CompareFunction zTestMode)
	{
		material.SetInt("_ZTest", (int)zTestMode );
	}

	static void MaterialChanged (Material material) {
		SetupMaterialWithBlendMode (material, (BlendMode)material.GetFloat ("_Mode"));
		SetupMaterialWithCullMode(material, (CullMode)material.GetFloat("_Cull"));
		SetupMaterialWithZTestMode(material, (CompareFunction)material.GetFloat("_ZTest"));
	}

	public void _FindModeOverrides(MaterialProperty[] props)
	{
		foreach (MaterialProperty property in props)
		{
			if (property.name == "_Mode")
			{
				m_materialOverrides = true;
				blendMode = FindProperty("_Mode", props);
			}

			if (property.name == "_Cull")
			{
				m_materialOverrides = true;
				cullMode = FindProperty("_Cull", props);
			}

			if (property.name == "_ZTest")
			{
				m_materialOverrides = true;
				zTestMode = FindProperty("_ZTest", props);
			}
		}
	}
}
#endif