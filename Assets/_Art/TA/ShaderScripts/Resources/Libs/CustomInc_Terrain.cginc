#ifndef CUSTOMINC_TERRAIN_INCLUDED
#define CUSTOMINC_TERRAIN_INCLUDED

#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"

// =========================== Terrain Declares 
sampler2D _DetailMap;
SamplerState sampler_DetailMap;
sampler2D _SplatMap;
SamplerState sampler_SplatMap;
#include "Assets/_Art/TA/Shaders/TerrainInc.cginc"
float4 _HeightMap_TexelSize;
sampler2D _GradientMap;


half _GrassFadeStart;
half _GrassFadeBlur;
half _GrassColShift;
half _C_Height;
half _B_C_Pinch;

half _AffectedByDistricts;

sampler2D _Norm_A_Tex;
float4 _Norm_A_Tex_ST;
SamplerState sampler_Norm_A_Tex;
sampler2D _Norm_B_Tex;
float4 _Norm_B_Tex_ST;
SamplerState sampler_Norm_B_Tex;
sampler2D _Norm_C_Tex;
float4 _Norm_C_Tex_ST;
SamplerState sampler_Norm_C_Tex;
sampler2D _Norm_D_Tex;
float4 _Norm_D_Tex_ST;
SamplerState sampler_Norm_D_Tex;
// Grass placement
sampler2D _DetailTex;
SamplerState sampler_DetailTex;

//Footpaths

sampler2D _HeightTex;
SamplerState sampler_HeightTex;
sampler2D _OwnLandMask;
SamplerState sampler_OwnLandMask;
float4 _OwnLandMask_ST;
half2 _DistrictsOutlineNoiseST;

half _TexTiling;

float4 _Color_A;
float4 _Color_B;
float4 _Color_C;
float4 _Color_D;

float4 _Color_A_Tex_ST;
float4 _Color_B_Tex_ST;
float4 _Color_C_Tex_ST;
float4 _Color_D_Tex_ST;

half4 _TintTexTiling;

half _FootpathDistort;
half _FootpathDistortFrequency;


half _TerrainRockGrassPinch;
half _TerrainSandHeight;

sampler2D _GrassWindNoiseTexture;
sampler2D _WindNoise;
uniform float _WindStrength;
uniform float _WindDirection;
half _HeightmapBlending;

 TEXTURE2D_ARRAY(_TXA_Albedo);
SAMPLER(sampler_TXA_Albedo);
TEXTURE2D_ARRAY(_TXA_Normals);
SAMPLER(sampler_TXA_Normals);
float _DeltaEpsilon;


// =========================== End : Terrain Declares

#define _CONTROL_VARS(TC1) \
	float4 control : TEXCOORD##TC1;

#define _TERRAIN_FRAG_DEFINES(i) \
	half maxHeight; \
	half secondHeight; \
	float4 detail; \
	float4 height; \
	float4 col; \
	float4 controlTex; \
	half3 worldNormal; \
	half4x4 col_Norm;

#define _PROP_FRAG_DEFINES(i) \
	float4 col; \
	half3 tangentNormal; \
	float4 albedo; \
	float4 matcapColor = 0; \
	half metallic = 0; \
	half3 worldNormal;

#define _VD_PROPERTY_DEFS \
	half _VD_Y_Offset;  \
	half _VD_XZ_Offset; \
	half _VD_Y_Frequency; \
	half _VD_XZ_Frequency; \
	half _LargeWave_Offset; \
	half _LargeWave_Frequency;



#define _TERRAIN_DETAIL_HEIGHT_HQ(i) \
	_Calc_Height_Detail(height, detail, i.dot_XY_XZ_YZ, i.worldPos); \
	float4 combinedHeight = height + controlTex; \
	maxHeight = _FindMaxValue4(combinedHeight); \
	secondHeight = _FindSecondValue4(combinedHeight); 


#define _WORLD_NORMAL_TERRAIN_HQ(i) \
	col_Norm = _HighestTex_Col_Norm_Terrain(combinedHeight, i.worldPos, i.dot_XY_XZ_YZ, detail, maxHeight, deltaEpsilon); \
	col_Norm = _SecondTex_Col_Norm_Terrain(combinedHeight, i.worldPos, i.dot_XY_XZ_YZ, detail, col_Norm, secondHeight, deltaEpsilon); \
	worldNormal.xyz = _Heightblend_float4_x2(col_Norm[1], maxHeight, col_Norm[3], secondHeight).xyz; 


#define _WORLD_NORMAL_ROAD_HQ(i) \
	col_Norm = _HighestTex_Col_Norm_Roads(combinedHeight, i.worldPos, detail, maxHeight, deltaEpsilon, _GrassTex, _DirtTex, _NormTex, _NormTex, _GrassTex_ST, _DirtTex_ST, _GrassTexBrightness, _DirtTexBrightness); \
	col_Norm = _SecondTex_Col_Norm_Roads(combinedHeight, i.worldPos, detail, secondHeight, deltaEpsilon, _GrassTex, _DirtTex, _NormTex, _NormTex, _GrassTex_ST, _DirtTex_ST, col_Norm, _GrassTexBrightness, _DirtTexBrightness); \
	worldNormal.xyz = _Heightblend_float4_x2(col_Norm[1], maxHeight, col_Norm[3], secondHeight).xyz; 

#define _TERRAIN_DETAIL_HEIGHT_LQ(i) \
	_Calc_Height_Detail(height, detail, i.dot_XY_XZ_YZ, i.worldPos); \
	half4 combinedHeight = height + controlTex; \
	maxHeight = _FindMaxValue4(combinedHeight); 

#define _WORLD_NORMAL_TERRAIN_LQ(i) \
	col_Norm = _HighestTex_Col_Norm_Terrain(combinedHeight, i.worldPos, i.dot_XY_XZ_YZ, detail, maxHeight, deltaEpsilon); \
	worldNormal.xyz = half3(0,0,1);

#define _WORLD_NORMAL_ROAD_LQ(i) \
	col_Norm = _HighestTex_Col_Norm_Roads(combinedHeight, i.worldPos, detail, maxHeight, deltaEpsilon, _GrassTex, _DirtTex, _NormTex, _NormTex, _GrassTex_ST, _DirtTex_ST, _GrassTexBrightness, _DirtTexBrightness); \
	worldNormal.xyz = half3(0,0,1);

#if _SPLAT_PROCEDURAL
	#define _CALC_CONTROL_PARAMS(o,v) \
		o.dot_XY_XZ_YZ = _Triblend(worldNormal).zyx; \
		o.control.x = pow(saturate(o.dot_XY_XZ_YZ.y), _B_C_Pinch); \
		o.control.y = 1 - o.control.x; \
		o.control.z = 1 - saturate(o.worldPos.y - _C_Height); \
		o.control.w = 0;
#elif _SPLAT_VERTEXCOLOUR
	#define _CALC_CONTROL_PARAMS(o, v) \
		o.control.xyz = v.color; \
		o.control.w = 0;
#else
	#define _CALC_CONTROL_PARAMS(o,v)
#endif

#define _CALC_WORLDUVS(o) \
	float4 worldUVs = float4((o.worldPos.x + 512) * 0.0009765625, (o.worldPos.z + 512) * 0.0009765625, 0, 0);

#define _CAMERA_DISTANCE_TINT(i) \
	half dist = _CalculateCameraDistanceClip(i.worldPos); \
	dist = _RangeRemapFloat(_GrassFadeStart - 0.01, _GrassFadeStart - 0.01 + _GrassFadeBlur, 0, 1, dist); \
	float4 distanceTint = lerp(combineAlbedo - 0.02, combineAlbedo, saturate(dist)); \
	float distanceTintMask = tex2D(_DetailMap, i.worldUVs).x; \
	col = lerp(combineAlbedo, distanceTint, controlTex.r * distanceTintMask);


#define _CAMERA_DIRECTION_TINT(i) \
	float lightDir = _CameraToLightDot(); \
	float4 directionTint = lerp(col - _GrassColShift, col, lightDir); \
	col = lerp(col, directionTint, controlTex.r);


#define _TERRAIN_LIGHTING(o) \
	half nl = 1;  \
	_CACHED_SHADOW_VERT(o); \
	TRANSFER_SHADOW(o)


inline half3 _Triblend(half3 wNorm)
{
	// in wNorm is the world-space normal of the fragment
	half3 blending = abs(wNorm);
	blending = normalize(max(blending, 0.00001)); // Force weights to sum to 1.0
	half b = (blending.x + blending.y + blending.z);
	blending /= half3(b, b, b);
	
	return blending;
}

inline float4 _PlanarTexture(float3 worldPosition, sampler2D tex, half4 tiling)
{
	// Texture A tiling
	half2 UV_XZ = (worldPosition.xz * tiling.xy) + tiling.zw;

	float4 texOutput = tex2D(tex, UV_XZ);
	//float4 texOutput = tex.Sample(tex, UV_XZ);
	//sampler_DetailMap
	return texOutput;
}

inline float4 _PlanarTexture_TexArray(float3 worldPosition, float index, half4 tiling)
{
	// Texture A tiling
	half2 UV_XZ = (worldPosition.xz * tiling.xy) + tiling.zw;
	//UNITY_SAMPLE_TEX2DARRAY(_TextureArray, half3(UV_XY, index));

	float4 texOutput = SAMPLE_TEXTURE2D_ARRAY(_TXA_Albedo,sampler_TXA_Albedo, UV_XZ, index);
	//float4 texOutput = tex.Sample(tex, UV_XZ);
	//sampler_DetailMap
	return texOutput;
}

inline half3 _PlanarNormal_TexArray(float3 worldPosition, float index, half4 tiling)
{
	// Texture A tiling
	half2 UV_XZ = (worldPosition.xz * tiling.xy) + tiling.zw;

	float4 texOutput = SAMPLE_TEXTURE2D_ARRAY(_TXA_Normals,sampler_TXA_Normals, UV_XZ, index);
	//texOutput = normalize(texOutput);

	half3 output = normalize(UnpackNormal(texOutput));
	//half3 output = ((texOutput-0.5) * 2);
	//half3 output = texOutput.xyz;

	return output.xyz;
}

inline half3 _BiplanarNormal_TexArray(float3 worldPosition, half3 dot_XY_XZ_YZ, float index, half4 tiling)
{
	half2 UV_XY = (worldPosition.xy * tiling.xy) + tiling.zw;
	half2 UV_ZY = (worldPosition.zy * tiling.xy) + tiling.zw;


	half4 texOutput_A = SAMPLE_TEXTURE2D_ARRAY(_TXA_Normals,sampler_TXA_Normals, UV_XY, index);
	half4 texOutput_B = SAMPLE_TEXTURE2D_ARRAY(_TXA_Normals,sampler_TXA_Normals, UV_ZY, index);
	half4 texOutput = lerp(texOutput_A, texOutput_B, dot_XY_XZ_YZ.z);

	half3 output = normalize(UnpackNormal(texOutput));
	//half3 output = ((texOutput - 0.5) * 2);
	//half3 output = texOutput.xyz;

	return output;
}

inline float4 _BiplanarTexture(float3 worldPosition, sampler2D tex, half3 dot_XY_XZ_YZ, half4 tiling)
{
	// Texture A tiling
	half2 UV_XY = (worldPosition.xy * tiling.xy) + tiling.zw;
	half2 UV_ZY = (worldPosition.zy * tiling.xy) + tiling.zw;

	float4 texOutput_A = tex2D(tex, UV_XY);
	float4 texOutput_B = tex2D(tex, UV_ZY);

	
	float4 texOutput = lerp(texOutput_A, texOutput_B, saturate(dot_XY_XZ_YZ.z));

	//return dot_XY_XZ_YZ.zzzz;

	return texOutput;
}

inline float4 _TriplanarTexture(float3 worldPosition, sampler2D tex, half3 dot_XY_XZ_YZ, half4 tiling)
{
	// Texture A tiling
	half2 UV_XY = (worldPosition.xy * tiling.xy) + tiling.zw;
	half2 UV_XZ = (worldPosition.xz * tiling.xy) + tiling.zw;
	half2 UV_ZY = (worldPosition.zy * tiling.xy) + tiling.zw;



	float4 texOutput = tex2D(tex, UV_XY) * dot_XY_XZ_YZ.x;
	texOutput += tex2D(tex, UV_XZ) * dot_XY_XZ_YZ.y;
	texOutput += tex2D(tex, UV_ZY) * dot_XY_XZ_YZ.z;

	return texOutput;
}

inline half3 _PlanarNormal(float3 worldPosition, sampler2D tex, half4 tiling)
{
	// Texture A tiling
	half2 UV_XZ = (worldPosition.xz * tiling.xy) + tiling.zw;

	half4 texOutput = tex2D(tex, UV_XZ);
	//texOutput = normalize(texOutput);

	half3 output = normalize(UnpackNormal(texOutput));

	return output.xyz;
}

inline half3 _BiplanarNormal(float3 worldPosition, sampler2D tex, half3 dot_XY_XZ_YZ, half4 tiling)
{
	half2 UV_XY = (worldPosition.xy * tiling.xy) + tiling.zw;
	half2 UV_ZY = (worldPosition.zy * tiling.xy) + tiling.zw;

	
	half4 texOutput_A = tex2D(tex, UV_XY);
	half4 texOutput_B = tex2D(tex, UV_ZY);
	half4 texOutput = lerp(texOutput_A, texOutput_B, dot_XY_XZ_YZ.z);

	half3 output = normalize(UnpackNormal(texOutput));

	return output;
}

inline half3 _TriplanarNormal(float3 worldPosition, sampler2D tex, half3 dot_XY_XZ_YZ, half4 tiling)
{
	half2 UV_XY = (worldPosition.xy * tiling.xy) + tiling.zw;
	half2 UV_XZ = (worldPosition.xz * tiling.xy) + tiling.zw;
	half2 UV_ZY = (worldPosition.zy * tiling.xy) + tiling.zw;
	
	

	half4 texOutput;
	texOutput = tex2D(tex, UV_XY) * dot_XY_XZ_YZ.x;
	texOutput += tex2D(tex, UV_XZ) * dot_XY_XZ_YZ.y;
	texOutput += tex2D(tex, UV_ZY) * dot_XY_XZ_YZ.z;
	
	//texOutput = normalize(texOutput);

	half3 output = UnpackNormal(texOutput);

	return output;
}

#define MINIMAL_OWNED_LAND 1
#include "Assets/_Art/TA/Shaders/Old/OwnedLand.cginc"


inline float3 _BannerWindNoise(float2 coord, half freq, half3 amp, sampler2D windTex)
{
	float4 noiseTex = tex2Dlod(windTex, float4(coord.xy * freq, 0,0) );
	half3 noise = (noiseTex.xyz - 0.5) * 2;
	noise *= amp;

	return noise;
}

half _SampleWindAmplitude(float4 worldPosition, half timeMultiplier)
{
	_FRAC_TIME_Y
	float windStrength = _WindStrength;
	half2 windUVs = worldPosition.xz + worldPosition.y;
	windUVs = frac((windUVs * 0.003) + half2(0.05, 0.1) * _TimeQ.y * timeMultiplier);
	float wind = tex2Dlod(_WindNoise, half4(windUVs, 0, 0)).x * windStrength;

	return wind;
}

inline half4 _WindTreeDisplacement(float4 localVertexPosition, float4 worldPosition)
{
	_FRAC_TIME_Y
	//float4 localVertexPosition = v.vertex;
	//float4 objectOrigin = mul(UNITY_MATRIX_M, float3(0, 5, 0));
	//float distanceFromOrigin = length(localVertexPosition - objectOrigin);

	// important ! : Checks for ortho view and disables effect if in ortho
	float perspective = UNITY_MATRIX_P[3][3];

	float distanceFromOrigin = localVertexPosition.y;
	distanceFromOrigin = saturate(distanceFromOrigin*0.01);

	float4 offset = mul(UNITY_MATRIX_M, float4(_WindDirection, 0, 0, 0))  * distanceFromOrigin;

	float windStrength = _WindStrength;

	half wind = _SampleWindAmplitude(worldPosition, 1);
	half windTurbulence_Large = wind * 1.2;

	
	half2 windUVs = frac(((worldPosition.xz + worldPosition.y) * 0.5) + half2(0.5, 1) * _TimeQ.y);
	wind = tex2Dlod(_WindNoise, half4(windUVs, 0, 0)).x;
	half windTurbulence_Micro = wind * windStrength * 0.25;


	half4 windMovement = half4(offset * (windTurbulence_Large  + windTurbulence_Micro)) * (1- perspective);

	return float4(windMovement.xyz, 0);
}

inline half4 _WindTreeDisplacement_Macro(float4 localVertexPosition, float4 worldPosition)
{
	_FRAC_TIME_Y
		//float4 localVertexPosition = v.vertex;
		//float4 objectOrigin = mul(UNITY_MATRIX_M, float3(0, 5, 0));
		//float distanceFromOrigin = length(localVertexPosition - objectOrigin);

		// important ! : Checks for ortho view and disables effect if in ortho
		float perspective = UNITY_MATRIX_P[3][3];

	float distanceFromOrigin = localVertexPosition.y;
	distanceFromOrigin = saturate(distanceFromOrigin * 0.01);

	float4 offset = mul(UNITY_MATRIX_M, float4(_WindDirection, 0, 0, 0)) * distanceFromOrigin;

	float windStrength = _WindStrength;

	half wind = _SampleWindAmplitude(worldPosition * 2, 1);
	half windTurbulence_Large = wind * 1.2;



	half4 windMovement = half4(offset * (windTurbulence_Large)) * (1 - perspective);

	return float4(windMovement.xyz, 0);
}

inline half4 _WindTreeDisplacement_Shadowcaster(float4 localVertexPosition, float4 worldPosition)
{
	float distanceFromOrigin = localVertexPosition.y;
	distanceFromOrigin = saturate(distanceFromOrigin*0.01);
	float4 windDirection = mul(UNITY_MATRIX_M, normalize(float4(_WindDirection, 0, 0, 0)));
	float windStrength = _WindStrength;

	half2 windUVs = worldPosition.xz + worldPosition.y;
	windUVs = frac((windUVs * 0.003) + half2(0.05, 0.1) * _TimeQ.y);
	float4 wind = tex2Dlod(_WindNoise, half4(windUVs, 0, 0));
	half4 windTurbulence_Primary = wind * distanceFromOrigin * windStrength * 2.25;
	half4 windTurbulence_Secondary = wind * distanceFromOrigin * windStrength * 0.175;

	half4 windTurbulence_Large = windTurbulence_Primary * windTurbulence_Secondary * 15;

	float perspective = UNITY_MATRIX_P[3][3];
	half4 windMovement = half4(windDirection * (windTurbulence_Large)) * (1 - perspective);

	return windMovement;
}

float4 _Heightblend_float4_x2(float4 input1, float height1, float4 input2, float height2)
{
	float height_start = max(height1, height2) - _HeightmapBlending;
	float b1 = max(height1 - height_start, 0);
	float b2 = max(height2 - height_start, 0);
	return ((input1 * b1) + (input2 * b2)) / (b1 + b2);
}

float3 heightblend_float3_x4(float3 input1, float height1, float3 input2, float height2, float3 input3, float height3, float3 input4, float height4)
{
	float height_start = max(max(height1, height2), max(height3, height4)) - _HeightmapBlending;
	float b1 = max(height1 - height_start, 0);
	float b2 = max(height2 - height_start, 0);
	float b3 = max(height3 - height_start, 0);
	float b4 = max(height4 - height_start, 0);
	return ((input1 * b1) + (input2 * b2) + (input3 * b3) + (input4 * b4)) / (b1 + b2 + b3 + b4);
}

float4 heightblend_float4_x4(float4 input1, float height1, float4 input2, float height2, float4 input3, float height3, float4 input4, float height4)
{
	float height_start = max(max(height1, height2), max(height3, height4)) - _HeightmapBlending;
	float b1 = max(height1 - height_start, 0);
	float b2 = max(height2 - height_start, 0);
	float b3 = max(height3 - height_start, 0);
	float b4 = max(height4 - height_start, 0);
	return ((input1 * b1) + (input2 * b2) + (input3 * b3) + (input4 * b4)) / (b1 + b2 + b3 + b4);
}


inline float4x4 _HighestTex_Col_Norm_Roads(float4 height, float4 position_in_world_space, float4 detail, half maxHeight, half deltaEpsilon, sampler2D _GrassTex, sampler2D _DirtTex, sampler2D norm_A, sampler2D norm_B, float4 tiling_A, float4 tiling_B, half grassBrightness, half dirtBrightness)
{
	float4x4 col_Norm; 
	float surfaceTest_A = height.x - maxHeight;
	surfaceTest_A *= surfaceTest_A;
	float surfaceTest_B = height.y - maxHeight;
	surfaceTest_B *= surfaceTest_B;
	 
	if (surfaceTest_B < deltaEpsilon)
	{
		col_Norm[0].xyzw = _PlanarTexture(position_in_world_space.xyz, _DirtTex, tiling_B) * detail.z * dirtBrightness;
		col_Norm[1].xyzw = float4(_PlanarNormal(position_in_world_space.xyz, norm_B, _TexTiling), 1);
	}
	else
	{
		col_Norm[0].xyzw = _PlanarTexture(position_in_world_space.xyz, _GrassTex, tiling_A) * detail.x * grassBrightness;
		col_Norm[1].xyzw = float4(_PlanarNormal(position_in_world_space.xyz, norm_A, _TexTiling), 1);
	}

	return col_Norm;
}



inline float4x4 _SecondTex_Col_Norm_Roads(float4 height, float4 position_in_world_space, float4 detail, half maxHeight, half deltaEpsilon, sampler2D _GrassTex, sampler2D _DirtTex, sampler2D norm_A, sampler2D norm_B, float4 tiling_A, float4 tiling_B, float4x4 col_Norm, half grassBrightness, half dirtBrightness)
{
	float surfaceTest_A = height.x - maxHeight;
	surfaceTest_A *= surfaceTest_A;
	float surfaceTest_B = height.y - maxHeight;
	surfaceTest_B *= surfaceTest_B;

	if (surfaceTest_B < deltaEpsilon)
	{
		col_Norm[2].xyzw = _PlanarTexture(position_in_world_space.xyz, _DirtTex, tiling_B) * detail.z * dirtBrightness;
		col_Norm[3].xyzw = float4(_PlanarNormal(position_in_world_space.xyz, norm_B, _TexTiling), 1);
	}
	else 
	{
		col_Norm[2].xyzw = _PlanarTexture(position_in_world_space.xyz, _GrassTex, tiling_A) * detail.x * grassBrightness;
		col_Norm[3].xyzw = float4(_PlanarNormal(position_in_world_space.xyz, norm_A, _TexTiling), 1);
	}
	return col_Norm;
}




inline float4x4 _HighestTex_Col_Norm_Terrain(half4 height, float4 position_in_world_space, half3 dot_XY_XZ_YZ, float4 detail, half maxHeight, half deltaEpsilon)
{
	float4x4 col_Norm; 
	float surfaceTest_A = height.x - maxHeight;
	surfaceTest_A *= surfaceTest_A;
	float surfaceTest_B = height.y - maxHeight;
	surfaceTest_B *= surfaceTest_B;
	float surfaceTest_C = height.z - maxHeight;
	surfaceTest_C *= surfaceTest_C;
	float surfaceTest_D = height.w - maxHeight;
	surfaceTest_D *= surfaceTest_D;

	if (surfaceTest_B < deltaEpsilon)
	{
		col_Norm[0].xyzw = _PlanarTexture_TexArray(position_in_world_space.xyz, 1, _Color_B_Tex_ST) * detail.y;
		//col_Norm[1].xyzw = float4(_BiplanarNormal_TexArray(position_in_world_space.xyz * _Norm_B_Tex_ST.xyx, 1, dot_XY_XZ_YZ, _TexTiling), 1);
		col_Norm[1].xyzw = float4(_PlanarNormal_TexArray(position_in_world_space.xyz, 1, _Norm_B_Tex_ST), 1);
	}
	else if (surfaceTest_A < deltaEpsilon)
	{
		col_Norm[0].xyzw = _PlanarTexture_TexArray(position_in_world_space.xyz, 0, _Color_A_Tex_ST) * detail.x;
		col_Norm[1].xyzw = float4(_PlanarNormal_TexArray(position_in_world_space.xyz, 0, _Norm_A_Tex_ST), 1);
	}
	else if (surfaceTest_D < deltaEpsilon)
	{
		col_Norm[0].xyzw = detail.z * _Color_D;
		col_Norm[1].xyzw = float4(_BiplanarNormal_TexArray(position_in_world_space.xyz, 1, dot_XY_XZ_YZ, _Norm_B_Tex_ST), 1);
	}
	else
	{
		col_Norm[0].xyzw = _PlanarTexture_TexArray(position_in_world_space.xyz, 2, _Color_C_Tex_ST) * detail.z;
		col_Norm[1].xyzw = float4(_PlanarNormal_TexArray(position_in_world_space.xyz, 2, _Norm_C_Tex_ST), 1);
	}
	

	return col_Norm;
}

inline float4x4 _SecondTex_Col_Norm_Terrain(half4 height, float4 position_in_world_space, half3 dot_XY_XZ_YZ, float4 detail, half4x4 col_Norm, half secondHeight, half deltaEpsilon)
{
	float surfaceTest_A = height.x - secondHeight;
	surfaceTest_A *= surfaceTest_A;
	float surfaceTest_B = height.y - secondHeight;
	surfaceTest_B *= surfaceTest_B;
	float surfaceTest_C = height.z - secondHeight;
	surfaceTest_C *= surfaceTest_C;
	float surfaceTest_D = height.w - secondHeight;
	surfaceTest_D *= surfaceTest_D;

	if (surfaceTest_B < deltaEpsilon)
	{
		col_Norm[2].xyzw = _PlanarTexture_TexArray(position_in_world_space.xyz, 1, _Color_B_Tex_ST) * detail.y;
		col_Norm[3].xyzw = float4(_BiplanarNormal_TexArray(position_in_world_space.xyz * half3(_Norm_B_Tex_ST.xx, _Norm_B_Tex_ST.y), 1, dot_XY_XZ_YZ, _TexTiling), 1);
	}
	else if (surfaceTest_A < deltaEpsilon)
	{
		col_Norm[2].xyzw = _PlanarTexture_TexArray(position_in_world_space.xyz, 0, _Color_A_Tex_ST) * detail.x;
		col_Norm[3].xyzw = float4(_PlanarNormal_TexArray(position_in_world_space.xyz * half3(_Norm_A_Tex_ST.xx, _Norm_A_Tex_ST.y), 0, _TexTiling), 1);
	}
	else if (surfaceTest_D < deltaEpsilon)
	{
		col_Norm[2].xyzw = detail.z * _Color_D;
		col_Norm[3].xyzw = float4(_BiplanarNormal_TexArray(position_in_world_space.xyz * half3(_Norm_B_Tex_ST.xx, _Norm_B_Tex_ST.y), 1, dot_XY_XZ_YZ, _TexTiling), 1);
	}
	else
	{
		col_Norm[2].xyzw = _PlanarTexture_TexArray(position_in_world_space.xyz, 2, _Color_C_Tex_ST) * detail.z;
		col_Norm[3].xyzw = float4(_PlanarNormal_TexArray(position_in_world_space.xyz * half3(_Norm_C_Tex_ST.xx, _Norm_C_Tex_ST.y), 2, _TexTiling), 1);
	}


	return col_Norm;
}

inline void _Calc_Height_Detail(out float4 height, out float4 detail, half3 dot_XY_XZ_YZ,  float4 worldPos)
{
	#if UTILITY_TRIPLANAR_PLANAR
		detail = _TriplanarTexture(worldPos, _DetailTex, dot_XY_XZ_YZ, _TexTiling);
		height = _TriplanarTexture(worldPos, _HeightTex, dot_XY_XZ_YZ, _TexTiling);
	#else
		detail = _PlanarTexture(worldPos, _DetailTex, _TexTiling);
		height = _PlanarTexture(worldPos, _HeightTex, _TexTiling);
	#endif
}


#define _GRASS_TERRAIN_SNAP(o,v, _RandomScatterTex, _RandomScatterTex_ST, _RandomScatterDistance) \
	float4 clampWorldPosition, axisWorldPosition, worldPos, patchUVs; \
	float2 randomOffset; \
	_GrassAxis(axisWorldPosition, clampWorldPosition, v.uv_2); \
	_GrassRandomOffset(randomOffset, axisWorldPosition, clampWorldPosition, _RandomScatterTex, _RandomScatterTex_ST); \
	_GrassClampWorldPos(worldPos, clampWorldPosition, patchUVs, v.vertex, axisWorldPosition, randomOffset, _RandomScatterDistance);


 
#define _GRASS_PLACEMENT_MAP(_DetailMap, patchUVs, _GrassPlacementThreshold); \
	float discardMap = _SampleUtilityMap_VTX(_DetailMap, patchUVs); \
	discardMap = 1 - discardMap; \
	float splatMapDist = smoothstep(0, _GrassPlacementThreshold, discardMap);

void _GrassDistanceFade(float3 clampAxis, inout float4 worldPos, half _GrassFadeStart, half _GrassFadeBlur, half splatMapDist, out float dist)
{
	dist = _CalculateCameraDistanceClip(worldPos);
	float grassFadeBlur = lerp(0, _GrassFadeStart * 0.99, _GrassFadeBlur);
	dist = _RangeRemapFloat(grassFadeBlur, _GrassFadeStart, 0, 1, dist);
	dist = saturate(max(dist, splatMapDist)); 
	worldPos.xyz = lerp(worldPos.xyz, clampAxis.xyz,dist);
}

float _GrassYOffset(sampler2D _HeightMap, float4 patchUVs)
{
	float4 heightTex = tex2Dlod(_HeightMap, patchUVs);
	float height = dot(heightTex, _HeightmapDot);
	float offset = -height;
	return offset;
}


void _GrassClampWorldPos(out float4 worldPos, inout float4 clampWorldPosition, out float4 patchUVs,  float4 vertex, float4 axisWorldPosition, float2 randomOffset, float _RandomScatterDistance)
{
	worldPos = float4(axisWorldPosition.x, 0, axisWorldPosition.z, vertex.w);
	worldPos.xz += randomOffset * _RandomScatterDistance;
	clampWorldPosition.xz += randomOffset * _RandomScatterDistance;

	patchUVs = float4(clampWorldPosition.xz * _HeightmapToUV.xy + _HeightmapToUV.zw, 0, 0);

	worldPos.xyz += vertex.xyz;
}


void _GrassAxis(out float4 axisWorldPosition, out float4 clampWorldPosition,  float2 uv)
{
	axisWorldPosition = mul(UNITY_MATRIX_M, float4(0, 0, 0, 1)); 
	clampWorldPosition = float4(axisWorldPosition.x + uv.x, 0, axisWorldPosition.z + uv.y, 0); 
}

void _GrassRandomOffset(out float2 randomOffset, float4 axisWorldPosition, float4 clampWorldPosition, sampler2D _RandomScatterTex, float4 _RandomScatterTex_ST)
{
	#if SCATTER_WHOLE
		float4 randomOffsetUVs = axisWorldPosition;
	#else
		float4 randomOffsetUVs = clampWorldPosition;
	#endif //SCATTER_WHOLE
	randomOffsetUVs.xz *= _RandomScatterTex_ST.xy;
	randomOffset = tex2Dlod(_RandomScatterTex, float4(randomOffsetUVs.x, randomOffsetUVs.z, 0, 0)).xy;
	randomOffset = (randomOffset * 2) - 1;
}

void _TerrainVertexDisplacement(float3 worldPos, float4 vertex, half4 magnitudesAndFrequencies, half2 largeWaveParams, half4 color,out half3 vertexDisplacement)
{
	float xWave = sin(magnitudesAndFrequencies.z * worldPos.x) * magnitudesAndFrequencies.x;
	half zWave = sin(magnitudesAndFrequencies.z * worldPos.z) * magnitudesAndFrequencies.x;
	half yWave1 = sin(magnitudesAndFrequencies.w * worldPos.z) * magnitudesAndFrequencies.y;
	half yWave2 = sin((magnitudesAndFrequencies.w * worldPos.z) + (worldPos.z * 0.4)) * magnitudesAndFrequencies.y;
	half yWave3 = sin((magnitudesAndFrequencies.w * worldPos.z) + (worldPos.z * 0.4) + (worldPos.z * 0.2)) * magnitudesAndFrequencies.y;
	half zVal = xWave * color.a;
	half xVal = zWave * color.a;
	half yVal1 = yWave1 * color.r;
	half yVal2 = lerp(yVal1, yWave2, color.g);
	half yVal3 = lerp(yVal2, yWave3, color.b);
	half3 wave = half3(xVal, yVal3, zVal);
	half3 LargeWave = sin(largeWaveParams.y * worldPos.zyx) * largeWaveParams.x;
	LargeWave = half3(LargeWave.x, 0, LargeWave.y);
	vertexDisplacement = LargeWave + wave;
}




inline void _WindOffsetGrass(inout float4 worldPos, out half offsetAmplitude, half _WindSpeed, half _WindFrequency, half _WindAmplitude, sampler2D _GrassWindNoiseTexture, float4 color)
{
	float windSpeed_LF = _Mod1000(_WindSpeed * 0.1 * _TimeQ.y);
	half windSpeed_HF = windSpeed_LF * 3;
	half windSize_LF = _WindFrequency;
	half windSize_HF = windSize_LF * 40;
	half4 offsetUV_LF = half4((worldPos.xz*windSize_LF) + windSpeed_LF, 0, 0);
	half4 offsetUV_HF = half4((worldPos.xz*windSize_HF) + windSpeed_HF, 0, 0);
	half2 noise_HF = (tex2Dlod(_GrassWindNoiseTexture, offsetUV_HF).xy - .5) * 0.3;
	half2 noise_LF = tex2Dlod(_GrassWindNoiseTexture, offsetUV_LF).xy - .5;
	half2 offset = (noise_HF + noise_LF) * _WindAmplitude*color.r;
	offsetAmplitude = offset;
	worldPos.xz -= offset;
}


inline void _CalculateDistanceFog(float4 vertex, out float depth)
{
	float pos = _UnityObjectToClipPos(vertex).z;
	depth = _ClipPosZToDepth01(pos);
	depth = saturate(_RangeRemapFloat(_DistanceFogStart, _DistanceFogEnd, 0, 1, depth));
}
float4 _RTHandleScaleDirect;
float4 _RTHandleScaleHistoryDirect;
inline float4 ComputeGrabScreenPosHDRP(float4 vertex)
{
	//float4 o = ComputeGrabScreenPos(vertex);
	//o.xy *= _RTHandleScaleHistoryDirect.xy;
	//return o;
	float4 positionCS = TransformObjectToHClip(vertex);
	//  Transform from clip space to NDC
	#if UNITY_UV_STARTS_AT_TOP
	positionCS.y = -positionCS.y;
	#endif
	return positionCS;
	 //return ComputeGrabScreenPos(positionCS);
}

#endif // CUSTOMINC_TERRAIN_INCLUDED