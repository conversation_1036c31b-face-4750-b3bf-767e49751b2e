#ifndef CUSTOMINC_DEFINES_LIGHTING_INCLUDED
#define CUSTOMINC_DEFINES_LIGHTING_INCLUDED

sampler2D _ShadowProjectionTexture;
sampler2D _ShadowProjectionTexture_Blurred;
float4x4 _ShadowProjectionMatrix;
half _ShadowProjectionFrustumSize;
half3 _ShadowProjectionNearFarClip;
half4 _ShadowProjectionParams;
half4 _ShadowProjectionForward;

half _ShadowProjectionTexture_Blurred_Amount;

sampler2D _cloudTex;
half _cloudsScrollingSpeed;
half _cloudsTiling;
half _cloudsIntensity;
half _cloudsIntensityGroundLevel;
half _cloudsHeightLow;
half _cloudsHeightHigh;
half _ShadowPull;
half _NormShadowBrightness;
half _NormShadowContrast;

half _ShadowsThreshold;
half _ShadowsThreshold_Blur;
half4 _BounceLightDirection;
float4 _BounceLightColor;
float _BounceLightIntensity;

half4 _SkyboxCubemap_HDR;



#endif // CUSTOMINC_DEFINES_LIGHTING_INCLUDED