#ifndef CUSTOMINC_WATER_INCLUDED
#define CUSTOMINC_WATER_INCLUDED

#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"

half _UV_ScrollingSpeed;
sampler2D _NormalTex;
float4 _NormalTex_ST;
float4 _NormalTex_TexelSize;

#if WATER_REFLECTION
	half _SkyDistortionAmount_UVs;
	half _ReflectionAmount;
	half _Fresnel;
#endif

#if SHORELINE_FOAM
	half _ShorelineFoamIntensity;
	sampler2D _BumpMap;
#endif

#if CAUSTICS
	sampler2D _CausticsTex;
	float4 _CausticsTex_ST;

	half _CausticsDepth;
	half _CausticsIntensity;
#endif

#if WATER_REFRACTION
	half _RefractionIntensity;
	half _BlurAmount;
	half _DepthExponent_Blur;
#endif


#if WATER_FLOWMAP || CAUSTICS
	sampler2D _FlowMap;
	float4 _FlowMap_ST;
	half _FlowSpeed;
	half _FlowAmplitude;
#endif

#if WATER_VERTEX_DISPLACEMENT_UV
	half _DisplacementFrequency;
	half _DisplacementAmplitude;
	half _DisplacementSpeed;
	sampler2D _DisplacementTex;
#endif
	
struct v2f_river
{
    _BASE_VARS(0)
    float depth : TEXCOORD1;
    _SCREENPOS_VAR(2)
    _FLOWMAP_VARS(3, 4)
    float2 scrollingUV : TEXCOORD5;
    half3x3 NTB : TEXCOORD6;
	_VIEWDIR_VARS(10)
    _WORLDPOS_VARS(11)
    _DISTANCE_VARS(12)
	#if USING_CACHED_SHADOWS
		_LIGHTING_VARS(13, 14)
	#endif

	#if SHORELINE_FOAM
		float2 bumpuv0 : TEXCOORD15;
		float2 bumpuv1 : TEXCOORD16;
		float2 foamuv : TEXCOORD17;
	#endif

	half3 temp : TEXCOORD18;
};


v2f_river vert_river (appdata v)
{
	_FRAC_TIME_XY;
	v2f_river o;
    UNITY_INITIALIZE_OUTPUT(v2f_river, o);
	_CALC_WORLDPOS(o, v);
    o.uv = v.uv;
    o.scrollingUV = (float2(_Time.y * _UV_ScrollingSpeed, 0) + o.uv);

	#if WATER_VERTEX_DISPLACEMENT_UV
		float3 worldNormal = mul((float3x3)UNITY_MATRIX_M, v.normal);
		float4 texUVs = o.worldPos * _DisplacementFrequency;
		texUVs.xyz += _Time.y * _DisplacementSpeed;
		half3 displacement = tex2Dlod(_DisplacementTex, texUVs).xxx;
		displacement = (displacement - 0.5) * 2;
		displacement *= (1 - v.uv.x)* _DisplacementAmplitude * worldNormal;
		v.normal = normalize(v.normal + displacement * 10);
		//o.temp = v.normal;
		_InputVertexOffset(v.vertex, displacement);
	#endif


    o.pos = UnityObjectToClipPos(v.vertex);
    o.screenPos = _ComputeScreenPos(o.pos);

	#if WATER_FLOWMAP
		_CALC_FLOWLERP(o , _FlowSpeed)
	#endif



				
	#if SHORELINE_FOAM
		half _WaveScale4 = 0.025;
		half _WaveOffset = 0;

		float4 temp;
		temp.xyzw = o.worldPos.xzxz * _WaveScale4 + _WaveOffset;
		o.bumpuv0 = temp.xy;
		o.bumpuv1 = temp.wz;
		o.foamuv = 0.05f * o.worldPos.xz + 0.05 * float2(_SinTime.w, _SinTime.w);
	#endif

    _CalculateDistanceFog(v.vertex, o.distanceFog);

    o.viewDir = WorldSpaceViewDir(v.vertex);

    o.NTB = _CalculateNTB(v.tangent, v.normal);
	#if USING_CACHED_SHADOWS
		fixed nl = 1;
		_CACHED_SHADOW_VERT(o);
	#endif
    float pos = _UnityObjectToClipPos(v.vertex).z;
    o.depth = _ClipPosZToDepth01(pos);

    return o;
}



half3 _Caustics(v2f_river i, sampler2D _FlowMap, sampler2D _CausticsTex, float4 _CausticsTex_ST, float _CausticsDepth, float _CausticsIntensity)
{
    half depthParallax = _DepthBlending_AngleCorrected(i.screenPos, 1, 0.05, i.NTB[0], i.viewDir, i.depth);

    half2 worldUVs = i.worldPos.xz * _CausticsTex_ST.xy;

    half2 uvDistortionOffset = UnpackNormal(tex2D(_FlowMap, ((i.uv * 30 + _Time.y * 0.1)))).xy;
    half2 distortUVs = tex2D(_FlowMap, worldUVs * 0.001) * 0.0025 * uvDistortionOffset;

    half2 parallaxUVs = _UVOffset_Constant(((worldUVs * 0.0025 * _CausticsTex_ST.xy) + distortUVs + frac(float2(_Time.y * -0.0025, _Time.y * 0.001))) * 50, normalize(i.viewDir), -0.5, depthParallax * _CausticsDepth * 50);
    half2 parallaxUVs_2 = _UVOffset_Constant(((worldUVs * 0.0025 * _CausticsTex_ST.xy) - distortUVs - frac(float2(-_Time.y * 0.0023, _Time.y * 0.0012))) * 50, normalize(i.viewDir), -0.5, depthParallax * _CausticsDepth * 50);

    half3 caustics_A = tex2D(_CausticsTex, parallaxUVs).xyz * 2;
    half3 caustics_B = tex2D(_CausticsTex, parallaxUVs_2).xyz * 2;

    half3 causticsOutput = caustics_A * caustics_B;
    causticsOutput += caustics_A * 0.1;
    causticsOutput += caustics_B * 0.1;

    causticsOutput *= 4 * _CausticsIntensity;


    return causticsOutput.xyz;
}

half _ShorelineFoam(v2f_river i, sampler2D _BumpMap, half _ShorelineFoamIntensity)
{
	#if SHORELINE_FOAM
		float4 worldUV = float4((i.worldPos.x + 512) * 0.0009765625, (i.worldPos.z + 512) * 0.0009765625, 0, 0);
		half bumpAmount = 2048;
		half3 terrainNormal = _NormalFromGrayscale_VTX(worldUV, _HeightMap, _HeightMap_TexelSize, bumpAmount);

		fixed4 heightTex = tex2Dlod(_HeightMap, worldUV);
		float heightOrig = dot(heightTex, _HeightmapDot);
		float height_A = _RangeRemapFloat(96, 100.8, 0, 1, heightOrig);
		float height_B = _RangeRemapFloat(97, 99, 0, 1, heightOrig);
		half waveDisplay = _RangeRemapFloat(96, 99, 0, 1, heightOrig);

		// combine two scrolling bumpmaps into one
		half3 bump1 = UnpackNormal(tex2D(_BumpMap, i.bumpuv0)).rgb;
		half3 bump2 = UnpackNormal(tex2D(_BumpMap, i.bumpuv1)).rgb;
		half3 bump = (bump1 + bump2) * 0.5;

		float2 foamDistortUV = bump.xy * 1;
		half delayTex = tex2D(_WindNoise, worldUV * 2.5).x;
		half delay = (sin((delayTex * 5) + _Time.y * 0.25) + 1) * 0.5;
		delay = saturate(delay - 0.75) * 4;

		half timer_A = _ModTau(delay + _Time.y + height_A + foamDistortUV.x);
		half timer_B = _ModTau(delay + _Time.y + height_B + foamDistortUV.y);

		timer_A = min(max(0, timer_A), UNITY_PI * 2);
		timer_B = min(max(0, timer_B), UNITY_PI * 2);

		half slider_A = (sin(timer_A) + 1) * 0.5;
		half slider_B = (sin(timer_B) + 1) * 0.5;

		slider_A *= delay;
		slider_B *= delay;


		half waveBorder_A = 1 - _Smoothstep(height_A, slider_A, 0.1);
		half waveBorder_B = _Smoothstep(height_B, slider_B, 0.1);

		half wave = saturate(waveBorder_A * waveBorder_B * waveDisplay) * _ShorelineFoamIntensity;
	#else
		half wave = 0;
	#endif
	return wave;
}

void  Water_Reflection(inout fixed3 col, out half3 worldNormal, v2f_river i, half3 normalTex, half _SkyDistortionAmount_UVs, half _ReflectionAmount)
{
	#if WATER_REFLECTION
		worldNormal = _NormalAndLightColor(i.NTB, _WorldSpaceLightPos0, normalTex.xyz).xyz;
		half3 meshReflectionNormal = worldNormal;
		meshReflectionNormal = lerp(float3(0, 1, 0), meshReflectionNormal, _SkyDistortionAmount_UVs);
		half reflectionDot = pow(1 - dot(normalize(i.viewDir), normalize(meshReflectionNormal)), _Fresnel);
		half3 worldRefl = reflect(-i.viewDir, meshReflectionNormal);
		fixed4 skyReflection = texCUBEbias(_SkyboxCubemap, float4(worldRefl.xyz, 2));
		skyReflection.xyz = DecodeHDR(skyReflection, _SkyboxCubemap_HDR);
		col = lerp(col, skyReflection.xyz, _ReflectionAmount * reflectionDot);
	#endif
}

float _Water_Refraction(out fixed4 refractionProjUVs, float4 _uvMul, half3 normalTex, float depth, out float depthDistorted, v2f_river i, half _DepthFade, half _DepthExponent, half _DepthExponent_Blur, half _RefractionIntensity)
{
	#if WATER_REFRACTION
		float2 screenDistorUVs = half2(normalTex.xy * _RefractionIntensity * depth);
		float4 screenPosDistort = float4(i.screenPos.xy + screenDistorUVs, i.screenPos.zw);

		depthDistorted = _DepthBlending_AngleCorrected(screenPosDistort, _DepthExponent, _DepthFade, i.NTB[0], i.viewDir, i.depth.x);
		depthDistorted = saturate(pow(depthDistorted * depth, _DepthExponent_Blur));

		float4 projUVs = i.screenPos * _uvMul + (depthDistorted * screenPosDistort * saturate(depth));

		float distanceBlurAmount = saturate(i.depth * 10);// * 1/0.015);

		half blurAmount = lerp(_BlurAmount * 0.1, _BlurAmount, distanceBlurAmount) * depthDistorted;
		refractionProjUVs = projUVs;
		return blurAmount;
		//refractionBlurred = blurGrabPass_RGB_Proj(_BackgroundTexture, projUVs, blurAmount);
	#endif
}

#endif // CUSTOMINC_WATER_INCLUDED