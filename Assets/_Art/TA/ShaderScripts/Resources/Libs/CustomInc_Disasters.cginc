inline half _Contrast(fixed val, half contrast)
{
	return(((val - 0.5f) * max(contrast, 0)) + 0.5f);
}

inline half _BurnHeight(half partIndex, half fireProgress, half numberOfParts, half baseHeight, half topHeight)
{
	half progress = 1 - fireProgress;
	half output = saturate(numberOfParts * progress - partIndex);
	output *= (topHeight - baseHeight);
	output += baseHeight;

	return output;
}

inline half _BurnHeightSlider(half _FireProgress, half delay, half partUV, half _BuildingBase, half _BuildingTop, half _ErosionWidth, float3 worldPos, half numberOfParts)
{
	half progress = _FireProgress + delay;
	half partNumber = abs(floor(partUV));
	half burnHeight = (_BurnHeight(partNumber, progress, numberOfParts, _BuildingBase, _BuildingTop) - (worldPos.y - _ErosionWidth));
	burnHeight = smoothstep(0, _ErosionWidth, burnHeight);
	burnHeight = 1 - saturate(burnHeight);

	return burnHeight;
}

inline half2 _BurnClipValWithMask(float3 worldPos, half _BuildingBase, half _BuildingTop, half delay, half _FireProgress, half3 masks_XY_ZX_ZY, half partUV, half numberOfParts, half _ErosionWidth)
{
	float masksOriginal = masks_XY_ZX_ZY.x * masks_XY_ZX_ZY.y * masks_XY_ZX_ZY.z;
	float masks = smoothstep(0, 0.1, masksOriginal);

	half burnHeight = _BurnHeightSlider(_FireProgress, delay, partUV, _BuildingBase, _BuildingTop, _ErosionWidth, worldPos, numberOfParts);

	return half2(masks, burnHeight);
}

inline half3 _BurnMasksTriplanarMapping(sampler2D _DamageTexture, float3 worldPos, half _DamageTextureTiling)
{
	//_DamageTextureTiling = 0.15;
	half mask_XY = tex2D(_DamageTexture, worldPos.xy * _DamageTextureTiling * 0.9).x;
	half mask_ZX = tex2D(_DamageTexture, worldPos.zx * _DamageTextureTiling).y;
	half mask_ZY = tex2D(_DamageTexture, worldPos.zy * _DamageTextureTiling * 1.1).z;

	half3 masks_XY_ZX_ZY = half3(mask_XY, mask_ZX, mask_ZY);

	return masks_XY_ZX_ZY;
}

inline void _BuildingGradient(out half distanceFromBase, float3 worldPos, float base, float top, half partUV, half numberOfParts)
{
	half buildingHeight = abs(top - base);
	distanceFromBase = saturate(distance(worldPos.y, base) / buildingHeight);
}

inline void _ErosionClipping(half2 burnValues, half _EnableErosion)
{
	half clipValue = burnValues.x - burnValues.y;
	clipValue = lerp(1, clipValue, _EnableErosion);
	clip(clipValue);
}

inline half _CalculateCharring(half distanceFromBase, half _FireProgress, half2 burnValues, half3 masks, half numberOfParts, half partUV, half _BuildingBase, half _BuildingTop, half3 worldPos, half _ErosionWidth)
{
	half borderDelay = 0.00275;
	half burnHeight = _BurnHeightSlider(_FireProgress, borderDelay, partUV, _BuildingBase, _BuildingTop, _ErosionWidth, worldPos, numberOfParts);

	half burnBorder = (burnValues.x - burnHeight);
	burnBorder = smoothstep(-0.075, 0, burnBorder);


	half masksDiffuse = masks.x - masks.y - masks.z;
	half decalMasksSlider = 2 * (1 - _FireProgress);
	fixed decalMasks = saturate(masksDiffuse + decalMasksSlider);
	decalMasks = saturate(decalMasks);

	fixed charredColour = decalMasks * burnBorder;

	return charredColour;
}

inline void _BurningVarsInit(out half distanceFromBase, out half3 masks, out half2 burnValues, half delay, half _BuildingTop, half _BuildingBase, half _WallDirt, float3 worldPos, sampler2D _DamageTexture, half _DamageTextureTiling, half _EnableErosion, half _FireProgress, half _ErosionWidth, half partUV, half numberOfParts)
{
	_BuildingGradient(distanceFromBase, worldPos, _BuildingBase, _BuildingTop, partUV, numberOfParts);
	masks = _BurnMasksTriplanarMapping(_DamageTexture, worldPos, _DamageTextureTiling);
	burnValues = _BurnClipValWithMask(worldPos, _BuildingBase, _BuildingTop, delay, _FireProgress, masks, partUV, numberOfParts, _ErosionWidth);
}

inline fixed3 _BurningBackfacesCol(sampler2D _HouseInteriorTexture, half2 uv, half _FireIntensity)
{
	half fireTiling = 2;
	half emberTintTiling = 0.525;
	half scrollingSpeed = 0.035;

	half2 emberUVs = frac((uv * fireTiling));
	half emberMask = tex2D(_HouseInteriorTexture, emberUVs).x;
	emberMask = smoothstep(0.75, 1, emberMask);

	half2 fireUVs = frac((uv * emberTintTiling) + (_Time.y * scrollingSpeed));
	fixed3 emberTint = tex2D(_HouseInteriorTexture, fireUVs);

	half contrastTex = 1.75;
	emberTint.x = _Contrast(emberTint.x, contrastTex);
	emberTint.y = _Contrast(emberTint.y, contrastTex);
	emberTint.z = _Contrast(emberTint.z, contrastTex);


	emberTint *= emberMask * 3;

	fixed3 darkerAreas = tex2D(_HouseInteriorTexture, uv);

	darkerAreas *= 0.05;


	fixed3 outputColor = lerp(0.01, emberTint + darkerAreas, _FireIntensity);

	return outputColor;
}

inline half _BurningFront(half _BuildingTop, half _BuildingBase, half _WallDirt, float3 worldPos, sampler2D _DamageTexture, half _DamageTextureTiling, half _EnableErosion, half _FireProgress, half _ErosionWidth, half partUV, half numberOfParts)
{
	half delay = 0;
	 
	_ErosionWidth = lerp(0, _ErosionWidth, _WallDirt);
	half distanceFromBase;
	_BuildingGradient(distanceFromBase, worldPos, _BuildingBase, _BuildingTop, partUV, numberOfParts);
	

	half3 masks = _BurnMasksTriplanarMapping(_DamageTexture, worldPos, _DamageTextureTiling);
	half2 burnValues = _BurnClipValWithMask(worldPos,  _BuildingBase, _BuildingTop, delay, _FireProgress, masks, partUV, numberOfParts, _ErosionWidth);
	
	_ErosionClipping(burnValues, _EnableErosion);

	fixed charredColour = _CalculateCharring(distanceFromBase, _FireProgress, burnValues, masks, numberOfParts, partUV, _BuildingBase, _BuildingTop, worldPos, _ErosionWidth);
	
	 
	return charredColour;
}

inline void _BurningDepth(half _BuildingTop, half _BuildingBase, half _WallDirt, float3 worldPos, sampler2D _DamageTexture, half _DamageTextureTiling, half _EnableErosion, half _FireProgress, half _ErosionWidth, half partUV, half numberOfParts)
{
	half delay = 0;
	half3 masks;
	half2 burnValues;
	half distanceFromBase;

	_BurningVarsInit(distanceFromBase, masks, burnValues, delay, _BuildingTop, _BuildingBase, _WallDirt, worldPos,  _DamageTexture, _DamageTextureTiling, _EnableErosion, _FireProgress, _ErosionWidth, partUV, numberOfParts);
	_BuildingGradient(distanceFromBase, worldPos, _BuildingBase, _BuildingTop, partUV, numberOfParts);

	_ErosionClipping(burnValues, _EnableErosion);
}

inline fixed3 _BurningBack(half _BuildingTop, half _BuildingBase, half _WallDirt, float3 worldPos,  sampler2D _HouseInteriorTexture, half _DamageTextureTiling, half _EnableErosion, half _FireProgress, half _FireIntensity,half _ErosionWidth, half2 uv, sampler2D _DamageTexture, half partUV, half numberOfParts)
{
	half delay = -0.0015; 
	delay = lerp(0, delay, _WallDirt);
	_ErosionWidth = lerp(0, _ErosionWidth, _WallDirt);
	half distanceFromBase;
	_BuildingGradient(distanceFromBase, worldPos, _BuildingBase, _BuildingTop, partUV, numberOfParts);

	half3 masks = _BurnMasksTriplanarMapping(_DamageTexture, worldPos, _DamageTextureTiling);
	half2 burnValues = _BurnClipValWithMask(worldPos, _BuildingBase, _BuildingTop, delay, _FireProgress, masks, partUV, numberOfParts, _ErosionWidth);

	_ErosionClipping(burnValues, _EnableErosion);


	fixed3 outputCol = _BurningBackfacesCol(_HouseInteriorTexture, uv, _FireIntensity);
	return outputCol; 
}

