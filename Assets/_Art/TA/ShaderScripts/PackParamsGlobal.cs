using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.IO;


public class PackParamsGlobal : MonoBehaviour
{
	[SerializeField] private NamedParameter[] m_floatParams;
	public string m_packedParamsName;
	private Matrix4x4 m_packedParams;

	private void Start()
	{
		
		GenerateTexture();
	}

	void GenerateTexture()
	{
		int i = 0;
		for(int y =0; y< 4; y++)
		{
			for (int x = 0; x < 4; x++)
			{
				m_packedParams[y, x] = m_floatParams[i].m_value;
				i++;
			}
		}

		Shader.SetGlobalMatrix(m_packedParamsName, m_packedParams) ;
	}


	[System.Serializable]
	public class NamedParameter
	{
		public string m_name;
		public float m_value;
	}


#if UNITY_EDITOR
	//[ExecuteInEditMode]
	void Update()
	{
		GenerateTexture();
	}

#endif //UNITY_EDITOR

}

