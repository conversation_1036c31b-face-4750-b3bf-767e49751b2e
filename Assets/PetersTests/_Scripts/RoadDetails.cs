using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable]
public class RoadDetails
{
    public string id;
    public bool m_debugChanged;
    public string m_name;	
    public float m_costPerRoadTile;
    public float m_costPerBridgeTile;
    public float m_gemsPerRoadTile;
    public float m_gemsPerBridgeTile;
    public float m_tapsPerRoadTile;
    public float m_tapsPerBridgeTile;
    public float m_clayPerRoadTile;
    public float m_clayPerBridgeTile;
    public float m_repairCostMultiplier = 1.0f;
    public float m_constructionMultiplier = 20;
    public float m_defenseMultiplier = 8.0f;
    public static List<RoadDetails> s_roadDetails = new();
    public static List<RoadDetails> GetList => s_roadDetails;
    public string DebugDisplayName => m_name;
    private static RoadDetails s_fallback = new RoadDetails()
    {
        m_name = "Fallback",
        m_costPerRoadTile = 50, m_costPerBridgeTile = 50,
        m_gemsPerRoadTile = 2, m_gemsPerBridgeTile = 4,
        m_tapsPerRoadTile = 1, m_tapsPerBridgeTile = 2,
        m_clayPerRoadTile = 1, m_clayPerBridgeTile = 2,
    };
    private static HashSet<string> s_shownErrorFor = new();
    public static RoadDetails GetInfo(string _name)
    {
        var details = s_roadDetails.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
        if (details == null)
        {
            if (s_shownErrorFor.Contains(_name) == false)
            {
                Debug.Log($"Error: couldn't find road set {_name} in Knack RoadDetails");
                s_shownErrorFor.Add(_name);
            }
            return s_fallback;
        }
        return details;
    }
    public static List<RoadDetails> LoadInfo()
    { 
        s_roadDetails = NGKnack.ImportKnackInto<RoadDetails>(); 
        return s_roadDetails;
    }
}
