using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
public class NGRename : MonoSingleton<NGRename>
{
    private Action<string> m_callback;
    public TMP_InputField m_input;
    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public void ClickedFinish()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        if (m_callback != null)
            m_callback(m_input.text);
        DestroyMe();
    }

    public void ClickedClose()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        DestroyMe();
    }
    public void Activate(string _value, Action<string> _callback)
    {
        m_callback = _callback;
        m_input.text = _value;
    }
    public static NGRename Create(string _currentValue, Action<string> _callback)
    {
        var go = Instantiate(NGManager.Me.m_NGRenamePrefab.gameObject, NGManager.Me.m_NGRenameHolder);
        var r = go.GetComponent<NGRename>();
        r.Activate(_currentValue, _callback);
        return r;
    }
}
