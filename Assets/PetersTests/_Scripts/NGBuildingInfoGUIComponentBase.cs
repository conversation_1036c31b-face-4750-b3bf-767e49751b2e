using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.Assertions;

public class NGBuildingInfoGUIComponentBase : MonoBehaviour
{
    public TMP_Text m_title;
    [HideInInspector] public NGCommanderBase m_building;
    [HideInInspector] public MABuilding m_maBuilding;
    virtual public void Activate(NGCommanderBase _building)
    {
        m_building = _building;
    }

    virtual protected void Update()
    {
        
    }
    public static NGBuildingInfoGUIComponentBase Create(NGCommanderBase _building, NGBuildingInfoGUIComponentBase _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var bigcb = go.GetComponent<NGBuildingInfoGUIComponentBase>();
        bigcb.Activate(_building);
        return bigcb;
    }
    
    
    [System.Serializable]
    public class InfoField
    {
        public TextMeshProUGUI m_Title;
        public TextMeshProUGUI m_Value;

        public string Title { set => m_Title.text = value; }
        public string Value { set => m_Value.text = value; }
        public bool Enable 
        {
            set
            {
                Assert.IsNotNull(m_Title, $"NGBuildingInfoGUIComponentBase - InfoField - m_title - reference needs to be set in the inspector");
                Assert.IsNotNull(m_Value, $"NGBuildingInfoGUIComponentBase - InfoField '{m_Title.text}' - m_value - reference needs to be set in the inspector");
                m_Title.gameObject.SetActive(value);
                m_Value.gameObject.SetActive(value);
            }
        }
    }
}
