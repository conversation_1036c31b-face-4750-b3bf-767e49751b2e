using System;
using UnityEngine;

public class DecorationActionHistory : IEquatable<DecorationActionHistory>
{
    private const int MAX_DISTANCE_CONSIDER_EQUAL = 1;

    public float Scale { get; private set; }
    public float Rotate { get; private set; }
    public float RotateX { get; private set; }
    public Vector3 Position { get; private set; }

    public DecorationActionHistory(float scale, float rotate, float rotateX, Vector3 position)
    {
        Scale = scale;
        Rotate = rotate;
        RotateX = rotateX;
        Position = position;
    }

    public override string ToString()
    {
        return $"Pos: {Position} | Scale: {Scale} | Rotate: {Rotate} | RotateX: {RotateX}";
    }

    public override int GetHashCode()
    {
        int hashCode = 1364110755;
        hashCode = hashCode * -1521134295 + Scale.GetHashCode();
        hashCode = hashCode * -1521134295 + Rotate.GetHashCode();
        hashCode = hashCode * -1521134295 + RotateX.GetHashCode();
        hashCode = hashCode * -1521134295 + Position.GetHashCode();
        return hashCode;
    }

    public bool Equals(DecorationActionHistory other)
        => ReferenceEquals(this, other) ||
            (other != null &&
            Scale == other.Scale &&
            Rotate == other.Rotate &&
            RotateX == other.RotateX &&
            Vector3.SqrMagnitude(Position - other.Position) < MAX_DISTANCE_CONSIDER_EQUAL);

    public bool NotEquals(DecorationActionHistory other)
        => !Equals(other);
}