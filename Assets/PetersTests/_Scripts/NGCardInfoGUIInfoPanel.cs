using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
public class NGCardInfoGUIInfoPanel : NGCardInfoGUIComponentBase
{
    public TMP_Text m_description;
    public TMP_Text m_dateBuilt;
    public TMP_Text m_whoBuilt;
    public Image m_cardImage;

    public void GetGiftSprite(NGBusinessGift _gift)
    {
        if(_gift == null) return;
        
        if (!string.IsNullOrEmpty(_gift.m_spritePath))
        {
            m_cardImage.sprite = _gift.GetSprite;
        }
        else if (_gift.Type == NGBusinessGift.GiftType.Decoration)
        {
            var dec = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Find((o => o.m_prefabName.Equals(_gift.m_cardPower)));
            string path = dec.m_spritePath.Substring(0, dec.m_spritePath.Length - 4);
            m_cardImage.sprite = SpriteAtlasMapLoader.LoadClonedSprite(path);
            m_description.text = dec.m_description;
        }
        else if(!string.IsNullOrEmpty(_gift.m_buildingDesign))
        {
            GameManager.Me.GetDesignSpriteAsync(_gift.m_buildingDesign, CaptureObjectImage.Use.Building, _s =>
            {
                if (m_cardImage != null) // image has been destroyed since requesting
                    m_cardImage.sprite = _s;
            });
        }
        else
        {
            Debug.LogError("The gift must have a sprite path or a design in knack");
        }
    }

    override public void Activate(NGDirectionCardBase _card)
    {
        base.Activate(_card);
        var info = _card.GetDescriptionText();
        m_description.text = info.m_description;
        GetGiftSprite(_card.Gift);
    }
}
