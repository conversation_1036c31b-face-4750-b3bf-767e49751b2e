#if CHOICES
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGTutorialContractMgr : MonoSingleton<NGTutorialContractMgr>
{
    public TMPro.TextMeshProUGUI m_remainingCash;
    public TMPro.TextMeshProUGUI m_houseCost;

    void Start()
    {
        var startingCash = NGManager.Me.SetStartingMoney;
        var houseCost = 196000 - startingCash;
        m_remainingCash.text = $"${startingCash:n0}";
        m_houseCost.text = $"-${houseCost:n0}";
    }
    
    public void OnPressedSignContract() 
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.SignContract);
        AudioClipManager.Me.PlaySoundOld("PlaySound_Sign_Contract", GameManager.Me.transform);
        NGTutorialManager.Me.FireExternalTrigger();
        Destroy(gameObject, 1.5f);
    }
}
#endif