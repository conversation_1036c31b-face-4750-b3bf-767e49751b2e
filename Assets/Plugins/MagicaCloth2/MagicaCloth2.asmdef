{"name": "MagicaClothV2", "rootNamespace": "", "references": ["Unity.Burst", "Unity.Collections", "Unity.Mathematics"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["MC2_BURST", "MC2_COLLECTIONS"], "versionDefines": [{"name": "com.unity.burst", "expression": "1.8.1", "define": "MC2_BURST"}, {"name": "com.unity.collections", "expression": "1.4.0", "define": "MC2_COLLECTIONS"}, {"name": "com.unity.collections", "expression": "2.0.0", "define": "MC2_COLLECTIONS_200"}], "noEngineReferences": false}