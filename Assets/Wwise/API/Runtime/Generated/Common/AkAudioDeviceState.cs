#if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (https://www.swig.org).
// Version 4.3.0
//
// Do not make changes to this file unless you know what you are doing - modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public enum AkAudioDeviceState {
  AkDeviceState_Unknown = 0,
  AkDeviceState_Active = 1 << 0,
  AkDeviceState_Disabled = 1 << 1,
  AkDeviceState_NotPresent = 1 << 2,
  AkDeviceState_Unplugged = 1 << 3,
  AkDeviceState_Last,
  AkDeviceState_All = AkDeviceState_Active|AkDeviceState_Disabled|AkDeviceState_NotPresent|AkDeviceState_Unplugged
}
#endif // #if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.