#if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (https://www.swig.org).
// Version 4.3.0
//
// Do not make changes to this file unless you know what you are doing - modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public enum AkMIDIEventTypes {
  NOTE_OFF = 128,
  NOTE_ON = 144,
  NOTE_AFTERTOUCH = 160,
  CONTROLLER = 176,
  PROGRAM_CHANGE = 192,
  CHANNEL_AFTERTOUCH = 208,
  PITCH_BEND = 224,
  SYSEX = 240,
  ESCAPE = 247,
  META = 255
}
#endif // #if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.