#if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (https://www.swig.org).
// Version 4.3.0
//
// Do not make changes to this file unless you know what you are doing - modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public enum AkChannelConfigType {
  AK_ChannelConfigType_Anonymous = 0,
  AK_ChannelConfigType_Standard = 1,
  AK_ChannelConfigType_Ambisonic = 2,
  AK_ChannelConfigType_Objects = 3,
  AK_ChannelConfigType_Last,
  AK_ChannelConfigType_UseDeviceMain = 14,
  AK_ChannelConfigType_UseDevicePassthrough = 15
}
#endif // #if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.