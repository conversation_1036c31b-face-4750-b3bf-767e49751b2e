#if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (https://www.swig.org).
// Version 4.3.0
//
// Do not make changes to this file unless you know what you are doing - modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public enum AkCurveInterpolation {
  AkCurveInterpolation_Log3 = 0,
  AkCurveInterpolation_Sine = 1,
  AkCurveInterpolation_Log1 = 2,
  AkCurveInterpolation_InvSCurve = 3,
  AkCurveInterpolation_Linear = 4,
  AkCurveInterpolation_SCurve = 5,
  AkCurveInterpolation_Exp1 = 6,
  AkCurveInterpolation_SineRecip = 7,
  AkCurveInterpolation_Exp3 = 8,
  AkCurveInterpolation_LastFadeCurve = 8,
  AkCurveInterpolation_Constant = 9,
  AkCurveInterpolation_Last
}
#endif // #if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.