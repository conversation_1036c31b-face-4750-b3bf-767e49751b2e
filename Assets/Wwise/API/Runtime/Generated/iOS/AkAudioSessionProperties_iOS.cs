#if UNITY_IOS && ! UNITY_EDITOR
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (https://www.swig.org).
// Version 4.3.0
//
// Do not make changes to this file unless you know what you are doing - modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class AkAudioSessionProperties : global::System.IDisposable {
  private global::System.IntPtr swigCPtr;
  protected bool swigCMemOwn;

  internal AkAudioSessionProperties(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  internal static global::System.IntPtr getCPtr(AkAudioSessionProperties obj) {
    return (obj == null) ? global::System.IntPtr.Zero : obj.swigCPtr;
  }

  internal virtual void setCPtr(global::System.IntPtr cPtr) {
    Dispose();
    swigCPtr = cPtr;
  }

  ~AkAudioSessionProperties() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          AkUnitySoundEnginePINVOKE.CSharp_delete_AkAudioSessionProperties(swigCPtr);
        }
        swigCPtr = global::System.IntPtr.Zero;
      }
      global::System.GC.SuppressFinalize(this);
    }
  }

  public AkAudioSessionCategory eCategory { set { AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eCategory_set(swigCPtr, (int)value); }  get { return (AkAudioSessionCategory)AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eCategory_get(swigCPtr); } 
  }

  public AkAudioSessionCategoryOptions eCategoryOptions { set { AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eCategoryOptions_set(swigCPtr, (int)value); }  get { return (AkAudioSessionCategoryOptions)AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eCategoryOptions_get(swigCPtr); } 
  }

  public AkAudioSessionMode eMode { set { AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eMode_set(swigCPtr, (int)value); }  get { return (AkAudioSessionMode)AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eMode_get(swigCPtr); } 
  }

  public AkAudioSessionRouteSharingPolicy eRouteSharingPolicy { set { AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eRouteSharingPolicy_set(swigCPtr, (int)value); }  get { return (AkAudioSessionRouteSharingPolicy)AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eRouteSharingPolicy_get(swigCPtr); } 
  }

  public AkAudioSessionSetActiveOptions eSetActivateOptions { set { AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eSetActivateOptions_set(swigCPtr, (int)value); }  get { return (AkAudioSessionSetActiveOptions)AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eSetActivateOptions_get(swigCPtr); } 
  }

  public AkAudioSessionBehaviorOptions eAudioSessionBehavior { set { AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eAudioSessionBehavior_set(swigCPtr, (int)value); }  get { return (AkAudioSessionBehaviorOptions)AkUnitySoundEnginePINVOKE.CSharp_AkAudioSessionProperties_eAudioSessionBehavior_get(swigCPtr); } 
  }

  public AkAudioSessionProperties() : this(AkUnitySoundEnginePINVOKE.CSharp_new_AkAudioSessionProperties(), true) {
  }

}
#endif // #if UNITY_IOS && ! UNITY_EDITOR