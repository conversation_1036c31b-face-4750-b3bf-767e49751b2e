Shader "Unlit/s_pickupLine"
{
	Properties
	{
		[HDR] _Color ("Color", Color) = (1,1,1,1)
		_MainTex ("Texture", 2D) = "white" {}
		_ZTest("ZTest(4=LE, 8=Al)", Int) = 4
		_ZWrite("ZWrite", Int) = 0
		_FlatLight("Flat Light", Float) = 0
	}
	SubShader
	{
		Tags { "UniversalMaterialType" = "Lit" "RenderType" = "Opaque" "Queue" = "Transparent-1" }
		LOD 100

		Pass
		{
			ZTest [_ZTest]
			ZWrite [_ZWrite]
			Cull off
			Blend SrcAlpha OneMinusSrcAlpha

			CGPROGRAM

			#pragma vertex vert
			#pragma fragment frag

			#include "UnityCG.cginc"

			struct appdata
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				float2 texcoord : TEXCOORD0;
				float4 color : COLOR;
			};

			struct v2f
			{
				float4 vertex : SV_POSITION;
				float3 worldNrm : TEXCOORD1;
				float2 uv : TEXCOORD0;
				float4 color : COLOR;
			};

			float4 _MainTex_ST;
			v2f vert (appdata v)
			{
				v2f o;
				o.vertex = UnityObjectToClipPos(v.vertex);
				o.worldNrm = normalize(mul(unity_ObjectToWorld, float4(v.normal, 0)).xyz);
				o.color = v.color;
				o.uv = TRANSFORM_TEX(v.texcoord, _MainTex);
				return o;
			}

			sampler2D _MainTex;
			float4 _Color;
			float _FlatLight;
			float4 frag (v2f i) : SV_Target
			{
				float4 col = tex2D(_MainTex, i.uv);
				col *= col; col *= col;
				col = lerp(col, col * _Color, smoothstep(.05, .1, col.r));
				float3 lightDirection = -_WorldSpaceLightPos0.xyz;
				float lighting = max(_FlatLight, min(1, dot(i.worldNrm, lightDirection)));
				col.rgb *= lighting;
				return col * i.color;
			}
			ENDCG
		}
	}
}
