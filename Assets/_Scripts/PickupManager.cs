using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PickupManager : Mono<PERSON><PERSON>leton<PickupManager> {
	public PickupGUI m_pickupGUIPrefab;
	public RectTransform m_pickupHolder;
	private PickupGUI m_pickupGUI = null;
	public List<InputUtilities.HeldObject> m_heldObjects = new List<InputUtilities.HeldObject>();
	NGCommanderBase m_lastHeldObjectTarget = null;
	public void ClearTrackedObjects() {
		if (m_pickupGUI != null) {
			Destroy(m_pickupGUI.gameObject);
			m_pickupGUI = null;
		}
		m_heldObjects.Clear();
		m_lastHeldObjectTarget = null;
	}
	void CheckGUI() {
		if (m_pickupGUI == null) {
			m_pickupGUI = Instantiate(m_pickupGUIPrefab, m_pickupHolder, false);
		}
	}
	public void TrackObject(NGMovingObject _obj, int _max) {
		if (_obj == null) return;
		foreach (var o in m_heldObjects) {
			if (o.m_object == _obj) return;
		}
		m_heldObjects.Add(new InputUtilities.HeldObject(_obj));
        if (_obj as NGPickupUpgradeCard == null)
        { 
	        //CheckGUI();
	        //m_pickupGUI.Setup(_max);
        }
		//Refresh();
	}
	void Refresh() {
		if (m_pickupGUI == null) return;
		var s = "";
		//if (m_lastHeldObjectTarget != null && m_heldObjects.Count > 0)
		//	s = m_lastHeldObjectTarget.GetHeldByPlayerText(m_heldObjects[0].m_object);
		m_pickupGUI.UpdateInfo(m_heldObjects, s);
	}
	public void TrackTarget(GameObject _target) {
		NGCommanderBase rc = null;
		if(_target != null) rc = _target.GetComponent<NGCommanderBase>();
		m_lastHeldObjectTarget = rc;
		Refresh();
	}
}
