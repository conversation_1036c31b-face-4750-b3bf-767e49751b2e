using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UIVisibilityManager : MonoBehaviour
{
    public string[] m_UIObjects;

    private List<GameObject> m_objectsToEffect;

    private void FindObjectsInScene()
    {
        m_objectsToEffect = new List<GameObject>();
        foreach (string o in m_UIObjects)
        {
            GameObject go = GameObject.Find(o);
            if (go != null)
                m_objectsToEffect.Add(go);
        }
    }

    private void OnEnable()
    {
        FindObjectsInScene();

        foreach (GameObject go in m_objectsToEffect)
        {
            go.SetActive(false);
        }
    }

    private void OnDisable()
    {
        foreach (GameObject go in m_objectsToEffect)
        {
            go.SetActive(true);
        }
    }
}
