#if UNITY_EDITOR || DEVELOPMENT_BUILD

using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public enum EClickController_Status
{
    Idle,
    MovingCamera,
    Action,
    ActionDragTo,
    Special,
}

//=======================================================================
//
public class ClickController : MonoSingleton<ClickController>
{
    private List<ClickControllerState_Base> m_allStates = new List<ClickControllerState_Base>();
    private ClickControllerState_Base m_currentState = null;
    private Transform m_canvasRoot;
    private GameObject m_tap, m_drag, m_dest, m_mouse, m_debugDisplay;
    private TMPro.TextMeshProUGUI m_debugText;
    private string m_newGameName = null;
    public string NewGameName => m_newGameName;

    public void ClearNewGameName()
    {
        m_newGameName = null;
    }

    void Start()
    {
        m_allStates.Add(new ClickControllerState_ProductLineChoice());
        m_allStates.Add(new ClickControllerState_OnBoarding());
        m_allStates.Add(new ClickControllerState_Town());
        m_allStates.Add(new ClickControllerState_ResearchLab());
        m_allStates.Add(new ClickControllerState_DesignTable());
        m_allStates.Add(new ClickControllerState_DesignCompetition());
        m_allStates.Add(new ClickControllerState_VotingScene());
        m_allStates.Add(new ClickControllerState_NetworkError());

        m_canvasRoot = (new GameObject("ClickControllerCanvas")).transform;
        m_canvasRoot.SetParent(GameManager.Me.TownCanvas.transform, false);

        m_tap = new GameObject("Tap");
        m_tap.transform.SetParent(m_canvasRoot, false);
        var img = m_tap.AddComponent<Image>();
        img.color = new Color(1, .5f, .5f);
        img.rectTransform.sizeDelta = Vector2.one * 10;
        img.sprite = DesignTableManager.BuffIcon("Tap");

        m_drag = new GameObject("Drag");
        m_drag.transform.SetParent(m_canvasRoot, false);
        img = m_drag.AddComponent<Image>();
        img.color = new Color(.5f, .5f, 1);
        img.rectTransform.sizeDelta = Vector2.one * 10;
        img.sprite = DesignTableManager.BuffIcon("Tap");

        m_dest = new GameObject("Dest");
        m_dest.transform.SetParent(m_canvasRoot, false);
        img = m_dest.AddComponent<Image>();
        img.color = new Color(.5f, 1, .5f);
        img.rectTransform.sizeDelta = Vector2.one * 10;
        img.sprite = DesignTableManager.BuffIcon("Efficiency");

        m_mouse = new GameObject("Mouse");
        m_mouse.transform.SetParent(m_canvasRoot, false);
        img = m_mouse.AddComponent<Image>();
        img.color = Color.white;
        img.rectTransform.sizeDelta = Vector2.one * 10;
        img.sprite = DesignTableManager.BuffIcon("Tap");

        m_debugDisplay = new GameObject("Debug");
        m_debugDisplay.transform.SetParent(m_canvasRoot, false);
        m_debugText = m_debugDisplay.AddComponent<TMPro.TextMeshProUGUI>();
        var rt = m_debugDisplay.GetComponent<RectTransform>();
        rt.anchorMin = rt.anchorMax = rt.pivot = new Vector2(0.5f, 1);
        rt.anchoredPosition = new Vector2(0, -1080 * 0.3f);
        m_debugText.overflowMode = TextOverflowModes.Overflow;
        m_debugText.enableWordWrapping = false;
        m_debugText.alignment = TextAlignmentOptions.Center;
        m_debugText.verticalAlignment = VerticalAlignmentOptions.Bottom;
        m_debugText.outlineWidth = 0.2f;
        m_debugText.outlineColor = Color.black;
        m_debugText.fontSize = 40;
        m_debugText.text = "Debug";

        GameSettings.SetMinimums();
        
        TouchManager.PermanentlyDisable();
    }

    private static bool s_skipFrames = false;
    private static float s_skipFrameCheckTime = 0;
    private static bool s_skipFramesOverride = false;
    private static DebugConsole.Command s_skipFramesCmd = new DebugConsole.Command("skipframes", _s => Utility.SetOrToggle(ref s_skipFramesOverride, _s));

    void CheckFrameSkip()
    {
        if (Time.unscaledTime > s_skipFrameCheckTime)
        {
            s_skipFrameCheckTime = Time.unscaledTime + 1;
            s_skipFrames = s_skipFramesOverride || System.IO.File.Exists(Application.persistentDataPath + "/skipFrames.txt");
        }
        Camera.main.enabled = !s_skipFrames || ((Time.frameCount & 15) == 0);
    }
    
    void LateUpdate()
    {
        Application.runInBackground = true;

        if (Input.GetKeyDown(KeyCode.C))
        {
            SettingsUIController.ToggleWindow();
        }
        
        CheckFrameSkip();

        //NGTutorialManager.Me.SafeEndTutorial();
        if (m_currentState == null || !m_currentState.IsViable())
            SearchForViableState();
        m_currentState?.Run();

        UpdateIndicator(m_tap);
        UpdateIndicator(m_drag);
        UpdateIndicator(m_dest);
        if (Utility.IsMouseOverriding) SetIndicator(m_mouse, Utility.InputPos);
        else UpdateIndicator(m_mouse);
        SetDebugText(m_currentState?.DebugText(), m_currentState?.DebugTextColor ?? Color.white);
    }

    void SearchForViableState()
    {
        if (m_currentState != null) m_currentState.Stop();
        m_currentState = null;
        for (int i = 0; i < m_allStates.Count; ++i)
        {
            if (m_allStates[i].IsViable())
            {
                m_currentState = m_allStates[i];
                m_currentState.Start();
                break;
            }
        }
    }

    private void SetIndicator(GameObject _obj, Vector2 _pos, float _size = 50)
    {
        var rt = _obj.GetComponent<RectTransform>();
        rt.position = _pos;
        rt.sizeDelta = Vector2.one * _size;
    }

    private void UpdateIndicator(GameObject _obj)
    {
        var rt = _obj.GetComponent<RectTransform>();
        rt.sizeDelta *= .9f;
    }

    public void PerformedTapAtPoint(Vector2 _pos)
    {
        SetIndicator(m_tap, _pos, 80);
    }

    public void SetDragAtPoint(Vector2 _pos)
    {
        SetIndicator(m_drag, _pos);
    }

    public void SetDestAtPoint(Vector2 _pos)
    {
        SetIndicator(m_dest, _pos);
    }

    public void SetDebugText(string _s, Color _clr)
    {
        if (_s == null) _s = "";
        int r = Mathf.Clamp((int) (_clr.r * 255 * .5f + 128), 0, 255);
        int g = Mathf.Clamp((int) (_clr.g * 255 * .5f + 128), 0, 255);
        int b = Mathf.Clamp((int) (_clr.b * 255 * .5f + 128), 0, 255);
        int firstSpace = _s.IndexOf(' ');
        string prefix = _s, suffix = "";
        if (firstSpace != -1)
        {
            prefix = _s.Substring(0, firstSpace);
            suffix = _s.Substring(firstSpace);
        }
        _s = $"<color=#{r:x2}{g:x2}{b:x2}>{prefix}</color><color=#ffffff>{suffix}</color>";
        m_debugText.text = _s;
    }

    public static void Create(string _name = null)
    {
        var owner = new GameObject("Clicker");
        //var owner = GameManager.Me.gameObject;
        var cc = owner.AddComponent<ClickController>();
        cc.m_newGameName = _name;
    }

    public static void Stop()
    {
        if (Me != null)
        {
            Destroy(Me);
        }
    }

    public static void Log(string _s)
    {
        Debug.LogError($"CC:{_s}");
    }
}

public abstract class ClickControllerState_Base
{
    protected EClickController_Status m_status;
    protected float m_coolOffTimer;
    protected const float c_initialCoolOffTime = .5f;
    protected const float c_actionCoolOffTime = .1f;
    abstract public string DebugText();
    abstract public Color DebugTextColor { get; }

    virtual public void Start()
    {
        m_status = EClickController_Status.Idle;
        m_coolOffTimer = c_initialCoolOffTime;
    }

    virtual public void Stop()
    {
        m_status = EClickController_Status.Idle;
    }

    abstract public void Run();
    abstract public bool IsViable();

    //

    protected void StartCoolOff()
    {
        m_coolOffTimer = c_actionCoolOffTime;
    }

    protected bool CheckCoolOff()
    {
        if (m_coolOffTimer > 0)
        {
            m_coolOffTimer -= Time.deltaTime;
            return true;
        }
        return false;
    }

    public static Vector3 GetPos(Transform _t)
    {
        var cam = Camera.main;
        var screenPos = Vector3.zero;
        if (_t == cam.transform)
        {
            screenPos = new Vector3(Screen.width * .5f, Screen.height * .5f, 0);
        }
        else if (_t is RectTransform rt)
        {
            var containingCanvas = _t.GetComponentInParent<Canvas>();
            var corners = new Vector3[4];
            rt.GetWorldCorners(corners);
            Vector3 min = corners[0], max = corners[0];
            for (int i = 1; i < 4; ++i)
            {
                min = Vector3.Min(min, corners[i]);
                max = Vector3.Max(max, corners[i]);
            }
            screenPos = new Vector3(min.x * .5f + max.x * .5f, min.y * .25f + max.y * .75f, min.z * .5f + max.z * .5f);
            if (containingCanvas.renderMode != RenderMode.ScreenSpaceOverlay)
                screenPos = cam.WorldToScreenPoint(screenPos);
        }
        else
        {
            screenPos = cam.WorldToScreenPoint(_t.position);
        }
        return screenPos;
    }

    protected bool ScrollOnScreen(Transform _t, float _threshold = .25f)
    {
        return MoveOnScreen(_t, Camera.main.transform, _threshold, false, 0);
    }

    protected bool MoveOnScreen(Transform _t, Transform _held, float _threshold, bool _moveCursor, float _edgeScale = 1)
    {
        var tPos = _t.position;
        var cam = Camera.main;
        if (true)
        {
            bool isFake = _t == GameManager.Me.transform; // special transform to get a card moving
            var destSP = cam.WorldToScreenPoint(tPos);
            var heldSP = GetPos(_held);
            var delta = destSP - heldSP;
            var deltaNrm = new Vector3(delta.x / Screen.width, delta.y / Screen.height, 0);
            if (!isFake && deltaNrm.sqrMagnitude < _threshold * _threshold)
                return true;
            Vector2 drag = Vector2.zero;
            if (destSP.z < 10)
            {
                drag.y = -1;
            }
            else
            {
                float margin = .2f * _edgeScale;
                if (destSP.x < Screen.width * (.5f - margin)) drag.x = -1;
                else if (destSP.x > Screen.width * (.5f + margin)) drag.x = 1;
                if (destSP.y < Screen.height * (.5f - margin)) drag.y = -1;
                else if (destSP.y > Screen.height * (.5f + margin * .5f)) drag.y = 1;
            }
            var rate = Screen.height * 4 * Time.deltaTime;
            if (isFake) Utility.SetInputPos(Vector3.up * rate, true);
            else if (drag.sqrMagnitude > 0) GameManager.Me.DebugCameraMove = drag * .4f;
            else Utility.SetInputPos((destSP - heldSP) * .1f, true);
            return false;
        }


        Plane basisPlane;
        Vector3 hit;
        if (_held == cam.transform)
        {
            var plane = new Plane(Vector3.up, tPos);
            var ray = cam.ScreenPointToRay(new Vector3(Screen.width * .5f, Screen.height * .5f, 0));
            float hitPoint;
            plane.Raycast(ray, out hitPoint);
            hit = ray.GetPoint(hitPoint);
            basisPlane = plane;
        }
        else if (_held is RectTransform rt)
        {
            var corners = new Vector3[4];
            rt.GetWorldCorners(corners);
            var center = (corners[0] + corners[1] + corners[2] + corners[3]) * .25f;
            var plane = new Plane(Vector3.up, tPos);
            var ray = cam.ScreenPointToRay(new Vector3(center.x, center.y, 0));
            float hitPoint;
            plane.Raycast(ray, out hitPoint);
            hit = ray.GetPoint(hitPoint);
            basisPlane = plane;
            hit = center;
        }
        else
        {
            hit = _held.position;
            var dragBase = _held.GetComponent<DragBase>();
            basisPlane = new Plane(dragBase.DragPlaneNormal, dragBase.DragPlaneOrigin);
            ;
        }

        var screenPosDest = cam.WorldToScreenPoint(tPos);
        var screenPosHeld = cam.WorldToScreenPoint(hit);

        if (_moveCursor)
        {
            ClickController.Me.SetDragAtPoint(screenPosHeld);
            ClickController.Me.SetDestAtPoint(screenPosDest);
        }

        var screenPosDelta = screenPosDest - screenPosHeld;
        screenPosDest.x = screenPosDelta.x / Screen.width;
        screenPosDest.y = screenPosDelta.y / Screen.height;
        if (screenPosDest.x * screenPosDest.x + screenPosDest.y * screenPosDest.y < _threshold * _threshold)
            return true;

        var camTransform = cam.transform;
        float rayPoint;
        Ray basisRay = cam.RayThrough(tPos);
        basisPlane.Raycast(basisRay, out rayPoint);
        var targetOnPlane = basisRay.GetPoint(rayPoint);
        basisRay = cam.RayThrough(hit);
        basisPlane.Raycast(basisRay, out rayPoint);
        var heldOnPlane = basisRay.GetPoint(rayPoint);

        var cFwd = camTransform.forward;
        var cUp = camTransform.up;
        var cRight = camTransform.right;
        var cPos = camTransform.position;
        var camToTarget = targetOnPlane - heldOnPlane;
        var fwdAmount = Vector3.Dot(camToTarget, cFwd);
        var rightAmount = Vector3.Dot(camToTarget, cRight);
        var upAmount = Vector3.Dot(camToTarget, cUp);

        if (basisPlane.normal.y * basisPlane.normal.y > basisPlane.normal.z * basisPlane.normal.z) upAmount = -fwdAmount;

        var camMove = new Vector2(rightAmount, -upAmount) * 0.1f;
        if (_t == GameManager.Me.transform) camMove = Vector2.up; // special transform to get a card moving 
        if (camMove.sqrMagnitude > 5 * 5)
            camMove = camMove.normalized * 5;
        var moveRate = Screen.height * 4 * Time.deltaTime;
        if (_moveCursor) // && _held is RectTransform)
            if ((Utility.InputPos.x < 4 && camMove.x < 0) || (Utility.InputPos.x > Screen.width - 4 && camMove.x > 0) ||
                (Utility.InputPos.y < 4 && camMove.y < 0) || (Utility.InputPos.y > Screen.height - 4 && camMove.y > 0))
                _moveCursor = false; // moving a rectTransform at edge of screen, move camera instead to avoid "over holder" move disables
        if (_moveCursor)
            Utility.SetInputPos(camMove * moveRate, true);
        else
            GameManager.Me.DebugCameraMove = camMove * .4f;
        return false;
    }

    protected bool DragOnScreen(Transform _dragOwner, Transform _originalDrag, Transform _dragTarget, float _threshold = .05f, bool _useCameraMove = true, float _edgeScale = 1)
    {
        if (_dragOwner == null) return true;
        var dragBase = _dragOwner.GetComponent<DragBase>();
        if (dragBase == null)
        {
            Debug.LogWarning($"CC attempting to drag {_dragOwner.name} with no DragBase", _dragOwner.gameObject);
            return false;
        }
        var originalBase = _originalDrag.GetComponent<DragBase>();
        if (!dragBase.IsDragging && !originalBase.IsDragging) return true;
        if (_dragTarget == null) return false;
        if (_dragTarget == _dragOwner) return true;
        bool isInPlace = MoveOnScreen(_dragTarget, _dragOwner, _threshold, true, _edgeScale);
        if (isInPlace && dragBase.CouldBeClick && originalBase.CouldBeClick) isInPlace = false;
        //dragBase.ChangeDebugDrag(move);
        return isInPlace;
    }

    private PointerEventData m_peData = new PointerEventData(null);

    protected PointerEventData CreatePointerEventDataForPoint(Transform _pos, bool _isRight = false)
    {
        var cam = Camera.main;
        var screenPos = cam.WorldToScreenPoint(_pos.position);
        return CreatePointerEventDataForPoint(screenPos, _pos.gameObject, _isRight);
    }

    protected PointerEventData CreatePointerEventDataForPoint(Vector3 _screenPos, GameObject _obj, bool _isRight = false)
    {
        m_peData.position = new Vector2(_screenPos.x, _screenPos.y);
        m_peData.pressPosition = m_peData.position;
        m_peData.button = _isRight ? PointerEventData.InputButton.Right : PointerEventData.InputButton.Left;
        var pcr = new RaycastResult();
        pcr.gameObject = _obj;
        m_peData.pointerCurrentRaycast = pcr;
        return m_peData;
    }
}

//=======================================================================
//
public class ClickControllerState_ProductLineChoice : ClickControllerState_Base
{
    override public string DebugText()
    {
        return $"ProductLineChoice";
    }

    override public Color DebugTextColor => Color.yellow;

    private bool m_haveRun;
    private float m_waitTime;

    override public void Start()
    {
        base.Start();
        m_haveRun = false;
        m_waitTime = 2;
    }

    override public void Run()
    {
        if (m_waitTime > 0)
        {
            m_waitTime -= Time.deltaTime;
            return;
        }
        if (m_haveRun) return;
        m_haveRun = true;
    }

    override public bool IsViable()
    {
        return false;
    }
}

//=======================================================================
//
public class ClickControllerState_OnBoarding : ClickControllerState_Base
{
    override public string DebugText()
    {
        return $"OnBoarding";
    }

    override public Color DebugTextColor => Color.yellow;

    private bool m_haveRun;
    private float m_waitTime;

    override public void Start()
    {
        base.Start();
        m_haveRun = false;
        m_waitTime = 2;
    }

    override public void Run()
    {
        if (m_waitTime > 0)
        {
            m_waitTime -= Time.deltaTime;
            return;
        }
        // if (NGTutorialContractMgr.Me != null && NGTutorialContractMgr.Me.isActiveAndEnabled)
        // {
        //     NGTutorialContractMgr.Me.OnPressedSignContract();
        // }
        else if (OnBoardingChoiceHolderMgr.Me != null && OnBoardingChoiceHolderMgr.Me.isActiveAndEnabled)
        {
            OnBoardingChoiceHolderMgr.Me.ChooseRandom();
        }
    }

    override public bool IsViable()
    {
        return OnBoardingManager.Me.m_vcOffice != null && OnBoardingManager.Me.m_vcOffice.activeInHierarchy;
    }
}


//=======================================================================
//
public class ClickControllerState_Town : ClickControllerState_Base
{
    float m_timeInIdle = 0;
    float m_timeTotal = 0;

    override public string DebugText()
    {
        if (m_debugOverride != null) return $"Town {m_debugOverride}";
        //if (m_coolOffTimer > 0) return $"Town cool-off {m_coolOffTimer}s";
        var targetName = m_target == null ? "-" : m_target?.name;
        var dragTargetName = m_dragTarget == null ? "-" : m_dragTarget.name;
        string res = null;
        switch (m_status)
        {
            case EClickController_Status.Idle:
                res = $"Town Idle {m_timeInIdle.ToShortTimeString()} / {m_timeTotal.ToShortTimeString()} - {(m_timeInIdle * 100 / m_timeTotal):n1}%";
                break;
            case EClickController_Status.MovingCamera:
                res = $"Town MoveCam {targetName}";
                break;
            case EClickController_Status.Action:
                res = $"Town Action {targetName} {CheckTarget(m_target, out _)}";
                break;
            case EClickController_Status.ActionDragTo:
                res = $"Town DragTo {targetName} to {dragTargetName} {m_timer:n1}";
                break;
            case EClickController_Status.Special:
                res = $"Town Special {m_specialType}";
                break;
        }
        if (res == null)
            res = $"Town unexpected status {m_status} {m_targetInteraction} {targetName} {dragTargetName} {m_specialType} {m_timer:n1}";
        if (m_coolOffTimer > 0) res += $" [{m_coolOffTimer:n1}]";
        return res;
    }

    override public Color DebugTextColor => Color.green;

    private enum EInteractionType
    {
        None,
        Tap,
        Drag,
    }
    private enum ESpecialType
    {
        None,
        WaitingForReward,
    }
    private float m_timer = 0;
    private int m_breakOutOfDrag = 0;
    private ESpecialType m_specialType;
    private Transform m_target = null;
    private Transform m_dragTarget = null;
    private EInteractionType m_targetInteraction;

    override public void Run()
    {
        if (m_status == EClickController_Status.Idle)
            m_timeInIdle += Time.deltaTime;
        m_timeTotal += Time.deltaTime;

        if (CheckCoolOff()) return;
        switch (m_status)
        {
            case EClickController_Status.Idle:
                if (SearchForTarget())
                    StartCoolOff();
                break;
            case EClickController_Status.MovingCamera:
                if (m_target == null || ScrollOnScreen(m_target))
                    m_status = EClickController_Status.Action;
                break;
            case EClickController_Status.Action:
                m_debugOverride = null;
                m_breakOutOfDrag = 0;
                if (m_target != null) // still viable?
                {
                    switch (CheckTarget(m_target, out _))
                    {
                        case EInteractionType.Tap:
                            var ped = CreatePointerEventDataForPoint(m_target);
                            ClickController.Me.PerformedTapAtPoint(ped.pressPosition);
                            StartCoolOff();
                            m_status = EClickController_Status.Idle;
                            break;
                        case EInteractionType.Drag:
                            m_dragTarget = null;
                            //var startPos = m_target.GetComponent<NGFactory>() != null ? Camera.main.WorldToScreenPoint(m_target.position) : new Vector3(Screen.width * .5f, Screen.height * .5f, 0);
                            var startPos = GetPos(m_target);
                            m_target.GetComponent<DragBase>().StartDebugDrag(CreatePointerEventDataForPoint(startPos, m_target.gameObject));
                            m_status = EClickController_Status.ActionDragTo;
                            m_timer = 0;
                            break;
                        case EInteractionType.None:
                            m_status = EClickController_Status.Idle;
                            break;
                    }
                }
                else
                    m_status = EClickController_Status.Idle;
                break;
            case EClickController_Status.ActionDragTo:
                if (m_timer > 0)
                {
                    m_timer += Time.deltaTime;
                    if (m_timer > 9.0f)
                    {
                        if (m_timer > 12f)
                        {
                            m_status = EClickController_Status.Idle;
                        }
                    }
                    else if (m_timer > .75f)
                    {
                        var db = m_target.GetComponent<DragBase>();
                        if (db != null) db.EndDebugDrag();
                        m_timer = 10.0f;
                    }
                }
                else
                {
                    if (m_dragTarget == null || m_dragTarget == GameManager.Me.transform)
                    {
                        m_dragTarget = GetBestDragTarget();
                        if (++m_breakOutOfDrag > 20)
                        {
                            m_breakOutOfDrag = 0;
                            m_timer = .01f;
                        }
                        ;
                    }
                    else
                    {
                        m_breakOutOfDrag = 0;
                    }
                    var dragee = m_target;
                    var pickup = m_target.GetComponent<Pickup>();
                    if (pickup != null && pickup.Source != null) dragee = pickup.Source.transform;
                    else if (PickupManager.Me.m_heldObjects.Count > 0) dragee = PickupManager.Me.m_heldObjects[0].m_object.transform;
                    var card = dragee.GetComponentInChildren<NGDirectionCardBase>();
                    if (card != null) dragee = card.transform;
                    if (card is NGDecorationTile)
                    {
                        var dragBase = dragee.GetComponent<DragBase>();
                        if (!dragBase.CouldBeClick)
                            m_timer = .01f;
                    }
                    if (DragOnScreen(dragee, m_target, m_dragTarget))
                    {
                        m_timer = .01f;
                    }
                }
                break;
            case EClickController_Status.Special:
                switch (m_specialType)
                {
                    case ESpecialType.WaitingForReward:
                        if (NGBusinessDecisionManager.Me.m_NGBusinessObjectiveHolder.childCount == 0)
                        {
                            m_status = EClickController_Status.Idle;
                            StartCoolOff();
                        }
                        break;
                }
                break;
        }
    }

    override public bool IsViable()
    {
        return GameManager.Me.IsCountryside;
    }

    //

    /*NGWorker FindDraggableWorker(NGFactory _where)
    {
        if (PickupManager.Me.m_heldObjects.Count > 0 && PickupManager.Me.m_heldObjects[0].m_object is NGWorker heldWorker)
            return heldWorker;
        foreach (var worker in _where.m_workers)
        {
            if (worker.MyJob == null && worker.m_state == NGMovingObject.STATE.IDLE)
            {
                var behaviour = worker.GetComponent<CharacterPickupBehaviour>();
                if (!behaviour.Falling && !behaviour.Held)
                    return worker;
            }
        }
        return null;
    }*/

    Transform GetBestDragTarget()
    {/*
        if (m_target == null) return null;
        var building = m_target.GetComponent<NGFactory>();
        if (building is NGRestPlace rest)
            return InputUtilities.NGFindRandomCollider(FindDraggableWorker(building), PickupAction.AssignJob)?.transform;
        if (building is NGDispatch)
            return m_target; // dragging from dispatch, discard (source==target)
        if (building != null)
            return building.GetBestBuildingDeliverForWorker(null, false)?.transform;

        var card = m_target.GetComponent<NGDirectionCardBase>();
        if (card != null)
        {
            if (card is NGBuildTile buildCard)
            {
                if (GameManager.Me.IsBuildingPlacement)
                {
                    if (BuildingPlacementManager.Me.m_buildingHolder.childCount > 1)
                    {
                        return BuildingPlacementManager.Me.m_buildingHolder.GetChild(Random.Range(1, BuildingPlacementManager.Me.m_buildingHolder.childCount));
                    }
                }
            }
            else if (card.PickupCard != null)
            {
                return InputUtilities.NGFindRandomCollider(card.PickupCard)?.transform;
            }
            return GameManager.Me.transform; // drag anywhere, just to start the drag motion
        }*/
        return null;
    }
    
    private HashSet<CommanderBalloons.Type> m_shownWarning = new HashSet<CommanderBalloons.Type>();

    EInteractionType CheckTarget(Transform _target, out float _priority)
    {
        _priority = 0;
        if (_target.gameObject == null) return EInteractionType.None; // been destroyed since we chose it

        var card = _target.GetComponent<DragCard>();
        if (card != null)
        {
            _priority = 1;
            return EInteractionType.Drag;
        }
        /*
        bool preferClayMine = NGManager.Me.m_NGBuildingSites.Count > 0 || NGManager.Me.m_NGRoadBuildingSites.Count > 0;
        var building = _target.GetComponent<NGFactory>();
        if (building != null)
        {
            if (building is NGDispatch dispatch)
            {
                for (int i = 0; i < dispatch.m_inputProducts.Count; ++i)
                {
                    if (ProductHelper.GetProductByID(dispatch.m_inputProducts[i].m_id, dispatch.m_inputProducts[i].m_factoryId).MarketRemaining < 1)
                    {
                        _priority = 0.9f;
                        return EInteractionType.Drag;
                    }
                }
            }
            if (m_fillMe != null)
            {
                if (m_fillMe.InputsAre.Contains(building.m_outputIs))
                {
                    _priority = .5f;
                    return building.OutputStock >= building.MaxOutputs ? EInteractionType.Drag : EInteractionType.Tap; // handle tutorial not doing what balloons say
                }
            }
            if (building.m_commanderBalloons == null) return EInteractionType.None;
            foreach (var balloon in building.m_commanderBalloons.m_balloons)
            {
                if (balloon != null && balloon.isActiveAndEnabled)
                {
                    float basePriority = (preferClayMine && building is NGClayMine) ? .5f : .2f;
                    switch (balloon.m_type)
                    {
                        case CommanderBalloons.Type.Tap:
                            // hack - deal with clay mine showing tap when full
                            if (building is NGClayMine && building.NumOutputs >= building.MaxOutputs)
                            {
                                // if this building can deliver somewhere do that
                                if (building.GetBestBuildingDeliverForWorker(null) != null)
                                {
                                    _priority = basePriority;
                                    return EInteractionType.Drag;
                                }
                                break; // otherwise ignore as tap target
                            }
                            _priority = basePriority;
                            return EInteractionType.Tap;
                        case CommanderBalloons.Type.Flick:
                        case CommanderBalloons.Type.Products:
                            var bestTarget = building.GetBestBuildingDeliverForWorker(null);
                            if (bestTarget != null && bestTarget != building)
                            {
                                bool canDrag = true;
                                // if (NGTutorialManager.Me != null)
                                //     foreach (var t in NGTutorialManager.Me.RestrictedPickupBuildings)
                                //         if (building.GetType() == t)
                                //             canDrag = false;
                                _priority = basePriority;
                                if (canDrag)
                                    return EInteractionType.Drag;
                                else if (building.NumOutputs < building.MaxOutputs && building.CanMake)
                                    return EInteractionType.Tap;
                                else
                                    m_fillMe = building;
                            }
                            break;
                        case CommanderBalloons.Type.Workers:
                            if (FindDraggableWorker(building) != null)
                            {
                                if (InputUtilities.NGFindRandomCollider(FindDraggableWorker(building), PickupAction.AssignJob) != null)
                                {
                                    _priority = .7f;
                                    return EInteractionType.Drag;
                                }
                            }
                            break;
                        case CommanderBalloons.Type.Warning:
                            if (building is NGBikeFactory)
                            {
                                _priority = 1f;
                                return EInteractionType.Tap;
                            }
                            break;
                        //case CommanderBalloons.Type.Research:
                        //_priority = 1f;
                        //return EInteractionType.Tap;
                        //case CommanderBalloons.Type.ResearchComplete:
                        //_priority = 1f;
                        //return EInteractionType.Tap;
                        default:
                            if (!m_shownWarning.Contains(balloon.m_type))
                            {
                                m_shownWarning.Add(balloon.m_type);
                                ClickController.Log($"Unsupported balloon type {balloon.m_type} on building {building.m_prefabName}");
                            }
                            break;
                    }
                }
            }
        }*/
        return EInteractionType.None;
    }

    private float m_delayDistricts = 0;
    private float m_delayOperation = 0;
    private bool m_deletingSave = false;
    private string m_debugOverride = null;
    private float m_delayStartGame = 0;

    bool SearchForTarget()
    {
        if (GameManager.Me.m_titlePage.activeInHierarchy)
        {
            if (!GameManager.Me.m_titleLoading.activeInHierarchy && !m_deletingSave && GameManager.Me.IsSaveButtonReady)
            {
                m_debugOverride = "Close TitlePage";
                if (m_delayStartGame < 3)
                {
                    m_delayStartGame += Time.deltaTime;
                    return false;
                }
                Debug.Log($"Close TitlePage");
                GameManager.Me.TitleScreenContinue();
                m_delayStartGame = 0;
                return true;
            }
            m_debugOverride = "Wait TitlePage";
            return false;
        }

        if (PlayerDetailsGUI.IsInstanceInScene)
        {
            m_debugOverride = "Close PlayerDetailsGUI";
            var name = ClickController.Me.NewGameName ?? $"{NGPlayer.Me.PlayerFirstname} {NGPlayer.Me.PlayerSurname}";
            var names = name.Split(' ');
            PlayerDetailsGUI.Instance.FillDetails(names[0], names[1], "ClickzRUs", "Keep Clicking");
            PlayerDetailsGUI.Instance.OnAccept();
            ClickController.Me.ClearNewGameName();
            return true;
        }


        // all except title screen, wait for load
        if (!GameManager.Me.LoadComplete) return false;


        // if (InteractiveLetter.ActiveLetter != null)
        // {
        //     InteractiveLetter.ActiveLetter.ClickedClose();
        //     m_debugOverride = "Close InteractiveLetter";
        //     return true;
        // }

        // if (MobileTutorial.Me != null)
        // {
        //     m_debugOverride = "MobileTutorial";
        //     MobileTutorial.Me.OnCloseButtonClicked();
        //     return true;
        // }

        /*var district = DistrictManager.Me.GetCheapestAvailableDistrict();
        if (district != null)
        {
            if (m_delayDistricts > 0)
            {
                m_delayDistricts -= Time.deltaTime;
            }
            else if (NGPlayer.Me.m_cash.Balance > district.balanceData.m_moneyCost * 2)
            {
                // can easily afford this district
                district.TryUnlockDistrict();
                m_delayDistricts = 30;
                return true;
            }
        }*/

        if (!string.IsNullOrEmpty(ClickController.Me.NewGameName))
        {
            NGPlayer.Me.PlayerFirstname = ClickController.Me.NewGameName;
            NGPlayer.Me.PlayerSurname = "Clickz";
            ClickController.Me.ClearNewGameName();
        }

        m_debugOverride = null;

#if OldBusinessFlow
        if (NGBusinessFlow.OpenChoices != null && NGBusinessFlow.OpenChoices.Count > 0)
        {
            if (NGBusinessFlow.OpenChoices[0] != null)
            {
                var r = Random.Range(0, NGBusinessFlow.OpenChoices.Count);
                NGBusinessFlow.OpenChoices[r].ClickedAccept();
                return true;
            }
        }
#endif
        // if (PDMDebugDecoration.Me != null)
        // {
        //     // for now just put it down anywhere
        //     m_debugOverride = "Handle Decoration";
        //     PDMDebugDecoration.Me.ButtonAccepted.interactable = true;
        //     PDMDebugDecoration.Me.ButtonAccepted.onClick.Invoke();
        //     return true;
        // }
#if OldBusinessFlow

        if (NGBusinessFlow.s_flows != null && NGBusinessFlow.CurrentFlow.m_state == NGBusinessFlow.State.WaitingForTappedMessage)
        {
            m_debugOverride = "Close Flow Banner";
            var banner = GameObject.FindObjectOfType<NGBusinessDirectionBanner>();
            if (banner != null) banner.ClickedMe();
            else
            {
                var msg = GameObject.FindObjectOfType<BusinessFlowTutorialMessage>();
                if (msg != null) msg.ClickedMe();
            }
            return true;
        }
        if (NGDirectionDialog.Me != null)
        {
            m_debugOverride = "Close DirectionDialog";
            NGDirectionDialog.Me.ClickedGo();
            return true;
        }
#endif
        if (BuildingPlacementManager.Me.IsConfirming)
        {
            m_debugOverride = "Confirm building placement";
            BuildingPlacementManager.Me.Confirmation(true);
            return true;
        }
        if (GameManager.ShowingTicketConversion)
        {
            GameManager.CloseTicketConversion();
            return true;
        }

        if (NGBusinessGiftsPanel.Me != null)
        {
            var cards = NGBusinessGiftsPanel.Me.m_takeAllHolder.GetComponentsInChildren<NGDirectionCardBase>();
            if (cards.Length == 0) cards = NGBusinessGiftsPanel.Me.m_chooseHolder.GetComponentsInChildren<NGDirectionCardBase>();
            if (cards.Length > 0)
            {
                var card = cards.PickRandom();
                if (card.Cost <= NGPlayer.Me.m_cash.Balance)
                {
                    m_debugOverride = "Choose reward card";
                    //NGBusinessDecisionDialog.Me.ToggleHiding(false);
                    var dragCard = card.GetComponent<DragCard>();
                    m_target = card.transform;
                    m_status = EClickController_Status.Action;
                    return true;
                }
            }
        }
#if OldBusinessFlow
        if (NGObjectiveDialog.Me != null && NGObjectiveDialog.Me.m_envelopeHolder.activeSelf)
        {
            NGObjectiveDialog.Me.ClickedEnvelope();
        }
#endif
        else if (NGBusinessDecisionManager.Me.m_NGBusinessObjectiveHolder.transform.childCount > 0)
        {
            /*var objective = NGBusinessDecisionManager.Me.m_NGBusinessObjectiveHolder.GetChild(0).GetComponent<NGBusinessObjective>();
            if (objective != null && objective.IsComplete)
            {
                if (objective.ObjectiveState)
                {
                    m_debugOverride = "Close business objective";
                    objective.OnClicked();
                    return true;
                }
            }*/
        }
        m_delayOperation *= .9999f;

        float best = 0, priority;
        m_target = null;
        /*foreach (var f in NGManager.Me.m_NGBuildingSites)
        {
            var interaction = CheckTarget(f.transform, out _);
            if (interaction != EInteractionType.None)
            {
                m_target = f.transform;
                m_targetInteraction = interaction;
                m_status = EClickController_Status.MovingCamera;
                return false;
            }
        }*/
        foreach (var b in NGManager.Me.m_NGCommanderList)
        {
            var interaction = CheckTarget(b.transform, out priority);
            if (interaction != EInteractionType.None && priority > best)
            {
                best = priority;
                m_target = b.transform;
                m_targetInteraction = interaction;
                m_status = EClickController_Status.MovingCamera;
            }
        }
        return false;
    }
}

//=======================================================================
//
public class ClickControllerState_ResearchLab : ClickControllerState_Base
{
    override public string DebugText()
    {
        return $"ResLab {m_status}";
    }

    override public Color DebugTextColor => Color.cyan;

    override public void Run()
    {
    }

    override public bool IsViable()
    {
        return true;
    }
}

//=======================================================================
//
public class ClickControllerState_VotingScene : ClickControllerState_Base
{
    override public string DebugText()
    {
        return $"VotingScene {m_status}";
    }

    override public Color DebugTextColor => Color.yellow;

    override public void Run()
    {
    }

    override public bool IsViable()
    {
        return false;
    }
}

//=======================================================================
//
public class ClickControllerState_DesignCompetition : ClickControllerState_Base
{
    override public string DebugText()
    {
        return $"DesignCompetition {m_status}";
    }

    override public Color DebugTextColor => Color.yellow;

    override public void Run()
    {
    }

    override public bool IsViable()
    {
        return false;
    }
}

//=======================================================================
//
public class ClickControllerState_NetworkError : ClickControllerState_Base
{
    override public string DebugText()
    {
        return $"NetworkError {m_status}";
    }

    override public Color DebugTextColor => Color.yellow;

    override public void Run()
    {
    }

    override public bool IsViable()
    {
        return false;
    }
}

//=======================================================================
//
public class ClickControllerState_DesignTable : ClickControllerState_Base
{
    override public string DebugText()
    {
        return $"DesTable {m_status} {m_designState} {(m_chosenBlock == null ? "-" : m_chosenBlock.name)} {(m_chosenSnap == null ? "-" : m_chosenSnap.name)}";
    }

    override public Color DebugTextColor => Color.red;

    public enum EDesignState
    {
        WaitForOperation,
        ChooseOperation,
        //
        ChooseDrawer,
        OpenDrawer,
        OpeningDrawer,
        ChooseBlock,
        //
        ChoosePaint,
        //
        StartDrag,
        DoDrag,
        //
        ChooseRemove,
        //
        InitialPause,
    }
    private EDesignState m_designState;
    private int m_drawerIndex;
    private DragBase m_chosenBlock;
    private Transform m_chosenSnap;
    private int m_blockChangeover;
    private int m_operationsRemaining;
    private float m_operationCooldown;
    private float m_initialPause;

    override public void Start()
    {
        base.Start();
        m_operationsRemaining = Random.Range(5, 8);
        m_designState = EDesignState.InitialPause; //WaitForOperation;
        m_initialPause = 1;
        m_operationCooldown = 1;
    }

    override public void Run()
    {
        if (DesignTableManager.Me.ClickerInterface()) return;

        int numPartsOnTable = DesignTableManager.Me.BlockHolder.GetComponentsInChildren<Block>().Length;
        bool forceFirstPart = false;//numPartsOnTable == 0 &&
                              //!DesignTableManager.Me.HasPreviousDesign && DesignTableManager.Me.DesignMode != DesignTableManager.DESIGN_CATEGORY.AVATAR;

        if (DesignTableManager.Me.ConfirmOrRevertUI != null)
        {
            if (m_operationsRemaining == -2)
            {
                DesignTableManager.Me.ConfirmOrRevertUI.m_confirm.GetComponent<Button>().onClick.Invoke();
                m_operationsRemaining = -3;
            }
            return;
        }
        switch (m_designState)
        {
            case EDesignState.InitialPause:
                m_initialPause -= Time.deltaTime;
                if (m_initialPause < 0) m_designState = EDesignState.WaitForOperation;
                break;
            case EDesignState.WaitForOperation:
                m_operationCooldown -= Time.deltaTime;
                if (m_operationCooldown < 0)
                {
                    m_designState = EDesignState.ChooseOperation;
                    m_operationCooldown = .5f;
                }
                break;
            case EDesignState.ChooseOperation:
                if (m_operationsRemaining < 0)
                {
                    if (numPartsOnTable > 2 && DesignTableManager.Me.IsRightButtonActive)
                    {
                        if (m_operationsRemaining > -2)
                        {
                            //DesignTableManager.Me.m_rightButton.OnPointerUp(null);
                            m_operationsRemaining = -2;
                        }
                        break;
                    }
                }
                else
                    --m_operationsRemaining;
                /*if (NGProductInfo.OwnedProductLines.Count > 1 && Random.Range(0, 100) < 30)
                {
                    var newLine = NGProductInfo.OwnedProductLines.PickRandom();
                    if (!DesignTableManager.Me.IsCurrentProductLine(newLine))
                    {
                        DesignTableManager.Me.SwitchProductLine(newLine);
                        m_designState = EDesignState.WaitForOperation;
                        m_operationCooldown = 5;
                        break;
                    }
                }*/
                if (numPartsOnTable > 1 && (NGUnlocks.Paints || DesignTableManager.Me.ForceDecorations))
                {
                    if (Random.Range(0, 100) < 40)
                    {
                        m_designState = EDesignState.ChoosePaint;
                        break;
                    }
                }
                const int c_maxPartsOnTable = 6;
                int removeChance = (numPartsOnTable) * 100 / c_maxPartsOnTable;
                removeChance = removeChance * removeChance / 100; // bias towards add
                // in case we have one top-only part on table
                if (numPartsOnTable == 1 && DesignTableManager.Me.BlockHolder.GetComponentsInChildren<Block>()[0].m_toHinges.childCount == 1)
                    removeChance = 100;
                m_designState = (Random.Range(0, 100) < removeChance) ? EDesignState.ChooseRemove : EDesignState.ChooseDrawer;
                break;
            case EDesignState.ChoosePaint:
                /* TODO   if (DesignTableManager.Me.m_decorationDrawers[0].IsClosed)
                {
                    DesignTableManager.Me.m_decorationDrawers[0].Open();
                }
                else if (DesignTableManager.Me.m_decorationDrawers[0].IsMoving)
                {
                }
                else
                {
                    var paints = DesignTableManager.Me.m_decorationDrawers[0].GetComponentsInChildren<DTDragPalette>();
                    m_chosenBlock = paints.PickRandom();
                    var blocksOnTable = DesignTableManager.Me.BlockHolder.GetComponentsInChildren<Block>();
                    m_chosenSnap = blocksOnTable.PickRandom().transform;
                    m_designState = EDesignState.StartDrag;
                }*/
                break;
            case EDesignState.ChooseDrawer:
                // skew drawer selection towards bottom drawers with low part counts
                int sub = 1 - (numPartsOnTable % 3 + 1) / 2;
                int div = 3 - numPartsOnTable / 3;
                int r = Random.Range(0, 6);
                m_drawerIndex = (r - sub) / div;
                //m_drawerIndex = Random.Range(0, 3);
                if (forceFirstPart) m_drawerIndex = 0;
                if (!DesignTableManager.Me.IsDrawerLocked(m_drawerIndex))
                    m_designState = EDesignState.OpenDrawer;
                break;
            case EDesignState.OpenDrawer:
                /* TODO   for (int i = 3; i > m_drawerIndex; --i)
                    if (!DesignTableManager.Me.m_drawers[i].IsClosed)
                        DesignTableManager.Me.m_drawers[i].Close();
                if (DesignTableManager.Me.m_drawers[m_drawerIndex].IsClosed)
                    DesignTableManager.Me.m_drawers[m_drawerIndex].Open();
                m_designState = EDesignState.OpeningDrawer;*/
                break;
            case EDesignState.OpeningDrawer:
                /* TODO   if (!DesignTableManager.Me.m_drawers[m_drawerIndex].IsMoving)
                    m_designState = EDesignState.ChooseBlock;*/
                break;
            case EDesignState.ChooseBlock:
                /* TODO   var parts = DesignTableManager.Me.m_drawers[m_drawerIndex].GetComponentsInChildren<DTDragPalette>();
                if (parts.Length == 0)
                {
                    ++m_operationsRemaining;
                    m_designState = EDesignState.ChooseDrawer;
                }
                else
                {
                    m_chosenBlock = parts.PickRandom();
                    if (forceFirstPart) m_chosenBlock = parts[0];
                    var blocksOnTable = DesignTableManager.Me.BlockHolder.GetComponentsInChildren<Block>();
                    if (blocksOnTable.Length > 0) m_chosenSnap = blocksOnTable.PickRandom().transform;
                    else m_chosenSnap = DesignTableManager.Me.TurntableVisual.transform;
                    m_designState = EDesignState.StartDrag;
                    //ClickController.Log($"Drag {m_chosenBlock.name} to {m_chosenSnap.name}");
                }*/
                break;
            case EDesignState.StartDrag:
                m_chosenBlock.StartDebugDrag(CreatePointerEventDataForPoint(m_chosenBlock.transform));
                m_blockChangeover = 0;
                m_designState = EDesignState.DoDrag;
                break;
            case EDesignState.DoDrag:
                var held = (m_chosenBlock == null) ? null : m_chosenBlock.transform;
                if (DesignTableManager.Me.GrabList != null && DesignTableManager.Me.GrabList.Count > 0 && DesignTableManager.Me.GrabList[0] != null)
                    held = DesignTableManager.Me.GrabList[0].transform;
                if (DragOnScreen(held, m_chosenBlock == null ? null : m_chosenBlock.transform, m_chosenSnap, .1f, false, 10))
                {
                    if (m_blockChangeover < 4)
                        ++m_blockChangeover;
                    else
                    {
                        m_chosenBlock.EndDebugDrag();
                        m_designState = EDesignState.WaitForOperation;
                    }
                }
                break;
            case EDesignState.ChooseRemove:
                m_chosenBlock = DesignTableManager.Me.BlockHolder.GetComponentsInChildren<Block>().PickRandom().GetComponentInChildren<DragBase>();
                //TODO   m_chosenSnap = DesignTableManager.Me.m_podiumTransforms[1].GetChild(1);
                m_designState = EDesignState.StartDrag;
                break;
        }
    }

    override public bool IsViable()
    {
        return GameManager.Me.IsDesignTable;
    }
}


//=======================================================================
//
public class TestInputModule : StandaloneInputModule
{
    public void ClickAt(float x, float y)
    {
        Input.simulateMouseWithTouches = true;
        var pointerData = GetTouchPointerEventData(new Touch()
        {
            position = new Vector2(x, y),
        }, out bool b, out bool bb);

        ProcessTouchPress(pointerData, true, true);
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.F1))
        {
            ClickAt(Screen.width / 2, Screen.height / 2);
        }
    }
}

#endif
