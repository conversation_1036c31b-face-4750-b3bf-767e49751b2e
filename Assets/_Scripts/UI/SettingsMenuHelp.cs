using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;

public class SettingsMenuHelp : MonoBeh<PERSON>our, IPointerEnterHandler, IPointerExitHandler
{
    #region private serialized members
    [SerializeField] string m_title;
    [TextArea]
    [SerializeField] string m_description;
    [SerializeField] Sprite m_enabledImage, m_disabledImage;
    [SerializeField] GameObject m_settingsMenuHelperPrefab;
    #endregion
    #region private members
    GameObject m_helperMenu;
    #endregion
    void IPointerEnterHandler.OnPointerEnter(UnityEngine.EventSystems.PointerEventData eventData)
    {
        return; // Removing until we have time to do these properly
        m_helperMenu = Instantiate(m_settingsMenuHelperPrefab, FindObjectOfType<SettingsUIController>().transform);
        Transform[] settingsInfo = m_helperMenu.transform.GetComponentsInChildren<Transform>();
        for (int i = 0; i < settingsInfo.Length; i++)
        {
            if (settingsInfo[i].name == "txt_Title") settingsInfo[i].GetComponent<TMP_Text>().text = m_title;
            if (settingsInfo[i].name == "txt_Explanation") settingsInfo[i].GetComponent<TMP_Text>().text = m_description;
            if (settingsInfo[i].name == "img_On") settingsInfo[i].GetComponent<Image>().sprite = m_enabledImage;
            if (settingsInfo[i].name == "img_Off") settingsInfo[i].GetComponent<Image>().sprite = m_disabledImage;
        }
    }


    void IPointerExitHandler.OnPointerExit(UnityEngine.EventSystems.PointerEventData eventData)
    {
        if(m_helperMenu != null) Destroy(m_helperMenu);
    }

}
