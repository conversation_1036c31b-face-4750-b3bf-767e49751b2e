using System;
using System.Collections.Generic;
using BehaviourDesignComponents;
using TMPro;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public interface IHealthBar
{
	public float MaxHealth { get; }
	public float MaxArmour { get; }
	public float NormalizedHealth { get; }
	public float NormalizedArmour { get; }
	public bool ShowHealthBar { get; }
	public int Level { get; }
	public float HealthBarHeight { get; }
}

public class HealthBar : MonoBehaviour
{
	public Canvas m_canvas;
	
	[SerializeField]
    private GameObject m_healthBarVisualHolder = null;

    [SerializeField]
    private TMPro.TextMeshProUGUI m_levelText = null;
    
    private IHealthBar m_info;
    
    private Vector3 m_targetOffset = Vector3.zero;

    public HealthBarElement m_healthBar;
	public HealthBarElement m_armourBar;
	public HealthBarElement m_manaBar;

	public RuneBarUI runeBarUI = null;

	[SerializeField]
	private GameObject healingIcon = null;

	[SerializeField]
	private bool isExternal = false;
	
    private void Awake()
    {
		if(m_info == null)
			m_info = GetComponentInParent<IHealthBar>();
		//AssertRefOrDisableSelf(m_info, "m_info");
    }
    
    private void Start()
    {
		SetupBars();
	}

	private void SetupBars()
	{
		if (m_info == null)
			return;

		//KW: set Height if it doesn't seem to have been set up manually
		if(transform.localPosition.y <= 0.0f)
        {
			Vector3 pos = transform.localPosition;
			pos.y = m_info.HealthBarHeight;
			transform.localPosition = pos;
		}
		
		if (!isExternal)
	    {
			if (AssertRefOrDisableSelf(m_canvas, "m_canvas"))
				m_canvas.worldCamera = Camera.main;

	    		m_targetOffset = transform.localPosition;
		}

	    if (AssertRefOrDisableSelf(m_healthBar, "m_healthBar")) m_healthBar.Init(m_info);
	    if (AssertRefOrDisableSelf(m_armourBar, "m_armourBar")) m_armourBar.Init(m_info);
	    if (AssertRefOrDisableSelf(m_manaBar, "m_manaBar")) m_manaBar.Init(m_info);
        
        SetBarEnabled(m_healthBar, true);
        
        UpdateShowBar();
	}

	private void Update()
	{
		if (isExternal)
		{
			var targetPos = transform.parent.position + m_targetOffset;
			var pos = transform.position;
			pos.y = Mathf.Lerp(pos.y, targetPos.y, 0.1f);
			transform.position = pos;
		}
        
        SetLevel();
        
        UpdateShowBar();
	}

	public void SetupInfo(IHealthBar info)
	{
		m_info = info;
		if (AssertRefOrDisableSelf(m_info, "m_info"))
		{
			gameObject.SetActive(true);
			SetupBars();
		}
	}

	public void SetLevel()
    {
		if (m_info == null)
			return;
		
#if UNITY_EDITOR
	    if (AssertRefOrDisableSelf(m_levelText, "m_levelText") == false) return;
#endif
	    m_levelText.text = $"{(m_info.Level < 0 ? "" : m_info.Level)}";
    }

	private void SetBarEnabled(HealthBarElement _bar, bool _value)
	{
#if UNITY_EDITOR
	    if (AssertRefOrDisableSelf(_bar, "_bar") == false) return;
#endif
		if (_bar.gameObject.activeSelf != _value)
			_bar.gameObject.SetActive(_value);
	}
	
	private void UpdateShowBar()
    {
		if (m_info == null)
		{
			if (m_healthBarVisualHolder.activeSelf)
				m_healthBarVisualHolder.SetActive(false);
			
			return;
		}
		
		bool isPossessed = GameManager.Me.IsPossessed(m_info as NGMovingObject);
	    SetBarEnabled(m_manaBar, isPossessed);
	    SetBarEnabled(m_armourBar, m_info.MaxArmour > 0);

		bool showHealingIcon = false;
		var character = m_info as MACharacterBase;
		if (isPossessed && (character != null))
		{
			float rate = character.GetHealthRecoveryRate();
			showHealingIcon = (rate > 0f) && (m_info.NormalizedHealth < 1f);
		}
		if (healingIcon != null && healingIcon.activeSelf != showHealingIcon)
			healingIcon.SetActive(showHealingIcon);
		
	    runeBarUI.ToggleUI(isPossessed && GameManager.BranchingCombosEnabled);

        var show = m_info.ShowHealthBar || (MADemoDialog.Me != null && MADemoDialog.Me.alwaysShowingHealthBars);
		if (isPossessed)
			show = isExternal;

        if (m_healthBarVisualHolder == null)
        {
	        gameObject.SetActive(false);
        }
        else
        {
	        if(m_healthBarVisualHolder.gameObject.activeSelf != show)
	        {
		        m_healthBarVisualHolder.gameObject.SetActive(show);
	        }
        }
    }

	private bool AssertRefOrDisableSelf(object _ref, string _name)
	{
		if (_ref == null)
		{
			if (!isExternal)
				Debug.LogError($"{GetType().Name} - {transform.Path()} - NUllReference exception '{_name}'");
			gameObject.SetActive(false);
			return false;
		}
		return true;
	}
}