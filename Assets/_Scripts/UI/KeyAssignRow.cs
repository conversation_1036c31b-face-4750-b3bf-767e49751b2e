using System.Collections;
using UnityEngine;
using UnityEngine.UI;

public class KeyAssignRow : MonoBehaviour
{
    public TMPro.TextMeshProUGUI m_label;
    public Button[] m_keyButtons;
    public GameObject m_setKeyObject;
    public TMPro.TextMeshProUGUI m_setKeyLabel;
    private KeyboardController.KeyAssignEntry m_entry;
    private KeyAssignController m_controller;

    public void Set(KeyboardController.KeyAssignEntry _entry, KeyAssignController _controller)
    {
        m_entry = _entry;
        m_controller = _controller;
        Refresh();
    }
    private void Refresh()
    {
        m_label.text = m_entry.m_label;
        for (int i = 0; i < m_entry.m_keys.Length; ++i)
        {
            m_keyButtons[i].GetComponentInChildren<TMPro.TextMeshProUGUI>().text = m_entry.GetString(i, true);
            m_keyButtons[i].gameObject.SetActive(true);
        }
        for (int i = m_entry.m_keys.Length; i < m_keyButtons.Length; ++i)
            m_keyButtons[i].gameObject.SetActive(false);
    }

    public void OnKeyClick(int _index)
    {
        m_setKeyObject.SetActive(true);
        m_setKeyObject.GetComponent<Image>().StartCoroutine(Co_SetKey(_index));
    }

    private IEnumerator Co_SetKey(int _index)
    {
        m_setKeyLabel.text = $"{m_entry.m_label} {m_entry.m_keyLabels[_index]}";
        while (true)
        {
            if (Input.anyKeyDown)
            {
                foreach (KeyCode kcode in System.Enum.GetValues(typeof(KeyCode)))
                {
                    if (kcode >= KeyCode.Mouse0 && kcode <= KeyCode.Mouse6) continue;
                    if (Input.GetKeyDown(kcode))
                    {
                        m_setKeyObject.SetActive(false);
                        m_entry.SetKey(_index, kcode);
                        m_controller.Refresh();
                        yield break;
                    }
                }
            }
            yield return null;
        }
    }
}
