using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LeagueIndicator : MonoBehaviour
{
    public UnityEngine.UI.Image m_fillerImage;
    public TMPro.TextMeshProUGUI m_leagueName;

    public void SetFill(float _f)
    {
        if (!m_fillerImage.fillAmount.Nearly(_f))
            m_fillerImage.fillAmount = _f;
    }

    public void SetLeague(string _name)
    {
        SetText($"{_name} League");
    }

    public void SetText(string _text)
    {
        if (m_leagueName.text != _text)
            m_leagueName.text = _text;
    }
}
