using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class DoAnimationManager : MonoBehaviour
{
    public static DoAnimationManager Me;
	public DOTweenAnimation m_flyUpAnim;
	public DOTweenAnimation m_flyToWalletAnim;
	public DOTweenAnimation m_pauseAnim;
	public DOTweenAnimation m_popupAnim;
	[Header("Too Poor Indicator")]
	public DOTweenAnimation m_flyToCentre;
	public DOTweenAnimation m_flyFromCentre;
	void Start()
	{
		Me = this;
	}
}
