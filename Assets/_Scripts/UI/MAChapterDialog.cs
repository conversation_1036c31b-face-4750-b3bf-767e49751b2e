using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;
using TMPro;
using UnityEngine;

public class MAChapterDialog : MAGUIBase
{

    public Image m_chapterImage;
    public TMP_Text m_title;
    public TMP_Text m_message;
    public void Activate(string _title, string  _meassage, string _image, bool _testIcons)
    {
        AudioClipManager.Me.PlayUISound("PlaySound_Chapter2Info");
        Activate();
        if (_testIcons)
        {
            var lines = "";
            foreach (var cr in NGCarriableResource.s_carriableResources)
            {
                var r = cr.Value;
                if (r.m_textSprite.IsNullOrWhiteSpace()) continue;
                lines+= $"{r.Name} has [{r.TextSprite}] Icon\n";
            }

            m_title.text = lines.TrimEnd('\n');
            m_chapterImage.gameObject.SetActive(false);
            m_message.gameObject.SetActive(false);
        }
        else
        {
            m_title.text = _title;
            m_message.text = _meassage;
            m_chapterImage.sprite = Resources.Load<Sprite>($"_Art/Chapters/{_image}");
        }
    } 
    
    public void ClickedClose()
    {
        AudioClipManager.Me.PlayUISound("PlaySound_Chapter2Accept");
        Destroy(gameObject);
    }
    public static MAChapterDialog Create(string _title, string  _meassage, string _image, bool _testIcons = false)
    {
        var prefab = Resources.Load<MAChapterDialog>("_Prefabs/Dialogs/MAChapterDialog");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_title, _meassage, _image, _testIcons);
        return instance;
    }
}
