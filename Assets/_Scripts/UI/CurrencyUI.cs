using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CurrencyUI : MonoBehaviour
{
    public string m_currency;
    public TMPro.TextMeshProUGUI m_currencyText;
    private CurrencyContainer m_currencyContainer;
    private int m_currencyInFlight = 0;
    private int m_currencyDisplayed = 0;
    private Sprite m_currencySprite;

    private static Dictionary<string, Sprite> s_currencySpriteLookup;

    public static Sprite GetCurrencySprite(string _currency)
    {
        if (s_currencySpriteLookup == null)
        {
            s_currencySpriteLookup = new ();
            s_currencySpriteLookup["Cash"] = GlobalData.Me.m_currencySpriteCash;
            s_currencySpriteLookup["Royal Favors"] = GlobalData.Me.m_currencySpriteRoyalFavours;
            s_currencySpriteLookup["Lords Favors"] = GlobalData.Me.m_currencySpriteLordsFavours;
            s_currencySpriteLookup["Commoners Favors"] = GlobalData.Me.m_currencySpritePeoplesFavours;
            s_currencySpriteLookup["Mystic Favors"] = GlobalData.Me.m_currencySpriteMysticFavours;
        }
        return s_currencySpriteLookup[_currency];
    }
    
    void Start()
    {
        m_currencySprite = GetCurrencySprite(m_currency);
        m_currencyContainer = CurrencyContainer.GetCurrency(m_currency);
        m_currencyContainer.SetUI(this);
        m_currencyDisplayed = (int)m_currencyContainer.Balance - 1;
    }

    void Update()
    {
        UpdateDisplay();
    }
    
    int CurrencyStep(int delta)
    {
        int mul = 1;
        if (delta < 0)
        {
            mul = -1;
            delta = -delta;
        }
        var baseStep = delta / 20.0f;
        var log10 = Mathf.Ceil(Mathf.Max(0, Mathf.Log10(baseStep)));
        var step = (int)Mathf.Pow(10, log10);
        return step * mul;
    }
    
    void UpdateDisplay()
    {
        var enable = m_currencyContainer.LifetimePeak > 0;
        var display = transform.GetChild(0);
        if (display.gameObject.activeSelf != enable)
            display.gameObject.SetActive(enable);
        var targetDisplay = (int)(m_currencyContainer.Balance - m_currencyInFlight);
        var currentDisplay = m_currencyDisplayed;
        if (targetDisplay == currentDisplay) return;
        var delta = targetDisplay - currentDisplay;
        var step = CurrencyStep(delta);
        m_currencyDisplayed += step;
        m_currencyText.text = ((float)m_currencyDisplayed).ToCurrencyString(20, "", false);
    }
    
    private int DumpMaxInFlight => NGManager.Me.m_dumpMaxInFlight;
    private float DumpDelayPerCoin => NGManager.Me.m_dumpDelayPerCoin;
    private float DumpDuration => NGManager.Me.m_dumpDuration;
    private int DumpCurrencyPerCoin => NGManager.Me.m_dumpCurrencyPerCoin;
    private float DumpXRndMin => NGManager.Me.m_dumpXRndMin;
    private float DumpXRndMax => NGManager.Me.m_dumpXRndMax;
    private float DumpYRndMin => NGManager.Me.m_dumpYRndMin;
    private float DumpYRndMax => NGManager.Me.m_dumpYRndMax;
    private int ReceiveMaxInFlight => NGManager.Me.m_receiveMaxInFlight;
    private float ReceiveDelayPerCoin => NGManager.Me.m_receiveDelayPerCoin;
    private float ReceiveDuration => NGManager.Me.m_receiveDuration;
    private float CoinBaseSize => NGManager.Me.m_coinBaseSize;

    public void DumpVisualCurrency(float _amount)
    {
        if (_amount.Nearly(0)) return;
        var amountInt = Mathf.CeilToInt(_amount);
        int inFlight = Mathf.Clamp(amountInt / DumpCurrencyPerCoin, 1, DumpMaxInFlight);
        Vector3 sourcePos2D = m_currencyText.transform.position;
        for (int i = 0; i < inFlight; ++i)
            UIManager.Me.StartCoroutine(Co_MoveCurrency(sourcePos2D, new Vector3(Screen.width * Random.Range(DumpXRndMin, DumpXRndMax), Screen.height * Random.Range(DumpYRndMin, DumpYRndMax), 0), 0, i * DumpDelayPerCoin, DumpDuration, true));

    }

    public void SendVisualCurrencyFrom(Transform _t, float _amount)
    {
        if (_amount.Nearly(0)) return;
        if (GameManager.Me.LoadComplete == false || IntroControl.IsInIntro) return;
        if (_amount < 0)
        {
            DumpVisualCurrency(-_amount);
            return;
        }

        var amountInt = Mathf.CeilToInt(_amount);
        if (amountInt == 0) return;

        Vector3 sourcePos2D;
        if (_t == null)
            sourcePos2D = Utility.mousePosition;
        else
        {
            var canvas = _t.GetComponentInParent<Canvas>();
            if (canvas == null || canvas.renderMode == RenderMode.WorldSpace)
                sourcePos2D = Camera.main.WorldToScreenPoint(_t.position); // 3D 
            else
                sourcePos2D = _t.position; // 2D
        }
        Vector3 destPos2D = m_currencyText.transform.position;

        m_currencyInFlight += amountInt;
        int inFlight = Mathf.Min(amountInt, ReceiveMaxInFlight);
        int amountPer = amountInt / inFlight;
        int firstAmountPer = amountInt - amountPer * (inFlight - 1);
        for (int i = 0; i < inFlight; ++i)
            UIManager.Me.StartCoroutine(Co_MoveCurrency(sourcePos2D, destPos2D, (i == 0) ? firstAmountPer : amountPer, i * ReceiveDelayPerCoin, ReceiveDuration, false));
    }
    
    private IEnumerator Co_MoveCurrency(Vector3 _sourcePos, Vector3 _destPos, int _amount, float _delay, float _duration, bool _isSpend)
    {
        yield return new WaitForSeconds(_delay);
        
        var canvas = UIManager.Me.GetComponent<Canvas>();
        
        m_currencyContainer.PlayAudio(_isSpend);
        
        var spriteObj = new GameObject("CurrencySprite");
        spriteObj.transform.SetParent(canvas.transform);
        var sprite = spriteObj.AddComponent<UnityEngine.UI.Image>();
        sprite.sprite = m_currencySprite;
        var spriteRT = spriteObj.transform as RectTransform;
        
        var sourceXFraction = _sourcePos.x / Screen.width;
        var destXFraction = _destPos.x / Screen.width;
        float midX;
        if (sourceXFraction.Nearly(destXFraction, .1f))
            midX = Mathf.Lerp(_sourcePos.x, Screen.width - _sourcePos.x, .25f);
        else
            midX = (_sourcePos.x + _destPos.x) * .5f;
        
        var coinBaseSize = Vector2.one * CoinBaseSize;
        var time = 0f;
        var pos = _sourcePos;
        while (time < _duration)
        {
            time += Time.deltaTime;
            var t = time / _duration;
            
            var size = Mathf.Min(t, 1 - t) * 2;
            size = size * size * (3 - size - size);
            
            var smoothT = t * t * (3 - t - t);
            var extremeT = smoothT * smoothT * smoothT;
            if (smoothT > .5f)
                pos.x = Mathf.Lerp(midX, _destPos.x, smoothT * 2 - 1);
            else
                pos.x = Mathf.Lerp(_sourcePos.x, midX, smoothT * 2);
            pos.y = Mathf.Lerp(_sourcePos.y, _destPos.y, extremeT);
            
            spriteRT.position = pos;
            spriteRT.sizeDelta = coinBaseSize * size;
            
            yield return null;
        }
        m_currencyInFlight -= _amount;
        Destroy(spriteObj);
    }
}
