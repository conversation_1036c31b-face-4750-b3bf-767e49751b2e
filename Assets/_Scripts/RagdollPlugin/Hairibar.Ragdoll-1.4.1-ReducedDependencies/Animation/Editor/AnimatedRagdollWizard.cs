using Hairibar.EngineExtensions;
using Hairibar.Ragdoll.Debug;
using UnityEditor;
using UnityEngine;
using UnityEngine.Assertions;

#pragma warning disable 649
namespace Hairibar.Ragdoll.Animation.Editor
{
    internal class AnimatedRagdollWizard
    {
        public const string WIZARD_TITLE = "Animated Ragdoll Wizard";

        internal class Data
        {
            public RagdollDefinitionBindings bindings;
            public GameObject modelGameObject;

            public RagdollDefinition definition;
            public bool definitionWasAutoGeneratedCorrectly;

            public string profileDirectory;
            public string definitionName;

            public RagdollPowerProfile powerProfile;
            public RagdollWeightDistribution weightDistribution;
            public RagdollAnimationProfile animationProfile;
        }

        [MenuItem("Tools/Hairibar.Ragdoll/Animated Ragdoll Wizard")]
        public static void CreateWizard()
        {
            RigDuplicationWizard.Display(new Data());
        }


        public class RigDuplicationWizard : ScriptableWizard
        {
            public static RigDuplicationWizard Display(Data data)
            {
                RigDuplicationWizard wizard = DisplayWizard<RigDuplicationWizard>(WIZARD_TITLE + " Step: Copy Creation", "Create Copy");
                wizard.data = data;
                return wizard;
            }

            public GameObject modelGameObject;
            internal Data data;


            void Awake()
            {
                helpString = "The wizard needs to create a copy of the GameObject that holds the model, called the Target.\n" +
                    "Assign the model GameObject. In most cases, this will be the imported object with an automatically assigned Animator.";

                modelGameObject = Selection.activeGameObject;
            }


            protected override bool DrawWizardGUI()
            {
                SerializedObject so = new SerializedObject(this);

                EditorGUI.BeginChangeCheck();
                EditorGUILayout.PropertyField(so.FindProperty("modelGameObject"), new GUIContent("Model Object"));
                bool changed = EditorGUI.EndChangeCheck();

                so.ApplyModifiedProperties();

                return changed;
            }


            void OnWizardUpdate()
            {
                ValidateAssignedGameObject();
            }

            void ValidateAssignedGameObject()
            {
                if (!modelGameObject)
                {
                    isValid = false;
                }
                else if (PrefabUtility.IsPartOfPrefabAsset(modelGameObject))
                {
                    errorString = "Tried to assign a prefab asset.";
                    modelGameObject = null;
                    isValid = false;
                }
                else
                {
                    isValid = true;
                    errorString = "";
                }
            }


            void OnWizardCreate()
            {
                int undoGroup = SetUpUndo();

                if (modelGameObject.transform.parent is null)
                {
                    CreateParent();
                }

                GameObject copy = CreateCopy();
                RemoveAllComponents(copy);
                AddBindings(copy);

                data.modelGameObject = modelGameObject;

                Undo.CollapseUndoOperations(undoGroup);

                SelectCopy(copy);

                ColliderSetupWizard.Display(data);
            }

            static void SelectCopy(GameObject copy)
            {
                Selection.activeGameObject = copy;
                EditorGUIUtility.PingObject(copy);
            }

            private void AddBindings(GameObject copy)
            {
                data.bindings = Undo.AddComponent<RagdollDefinitionBindings>(copy);
                data.bindings.hideFlags = HideFlags.NotEditable;
            }

            int SetUpUndo()
            {
                int undoGroup = Undo.GetCurrentGroup();
                Undo.SetCurrentGroupName("Create Model Copy");
                Undo.RegisterCompleteObjectUndo(modelGameObject, "");

                return undoGroup;
            }

            void CreateParent()
            {
                Transform parent = new GameObject(modelGameObject.name).transform;
                Undo.RegisterCreatedObjectUndo(parent.gameObject, "");
                Undo.SetTransformParent(modelGameObject.transform, parent, "");
            }

            GameObject CreateCopy()
            {
                modelGameObject.name = "Target";

                GameObject copy = Instantiate(modelGameObject);
                Undo.RegisterCreatedObjectUndo(copy, "");

                copy.name = "Ragdoll";
                copy.transform.SetParent(modelGameObject.transform.parent);

                return copy;
            }

            void RemoveAllComponents(GameObject copy)
            {
                foreach (Component component in copy.GetComponentsInChildren<Component>(true))
                {
                    bool isTransform = component is Transform;
                    if (!isTransform)
                    {
                        Undo.DestroyObjectImmediate(component);
                    }
                }
            }
        }

        public class ColliderSetupWizard : ScriptableWizard
        {
            public static ColliderSetupWizard Display(Data data)
            {
                ColliderSetupWizard wizard = DisplayWizard<ColliderSetupWizard>(WIZARD_TITLE + " Step: Collider Setup", "Continue");
                wizard.data = data;
                return wizard;
            }


            internal Data data;

            void Awake()
            {
                helpString = "Add Colliders to the Ragdoll. You can create children to give local offsets if necessary. Do not move the bones themselves.\n" +
                    "Press \"Continue\" when you are done.";
            }

            protected override bool DrawWizardGUI()
            {
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.ObjectField(new GUIContent("Ragdoll"), data?.bindings.gameObject, typeof(GameObject), true);
                EditorGUI.EndDisabledGroup();

                return false;
            }

            void OnWizardCreate()
            {
                JointSetupWizard.Display(data);
            }
        }

        public class JointSetupWizard : ScriptableWizard
        {
            public static JointSetupWizard Display(Data data)
            {
                var wizard = DisplayWizard<JointSetupWizard>(WIZARD_TITLE + " Step: Joint Setup", "Continue");
                wizard.data = data;
                return wizard;
            }


            public ConfigurableJoint rootBone;
            internal Data data;

            void Awake()
            {
                helpString = "Add a ConfigurableJoint to every bone that should be animated via physics, and configure their axes and limits. \n" +
                    "Bones without a ConfigurableJoint will still be animated by traditional means. " +
                    "Do not configure the Rigidbodies yet.\n" +
                    $"Assign the root bone and press \"Continue\" when you are done.";
            }

            protected override bool DrawWizardGUI()
            {
                SerializedObject so = new SerializedObject(this);

                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.ObjectField("Ragdoll", data?.bindings.gameObject, typeof(GameObject), true);
                EditorGUI.EndDisabledGroup();

                EditorGUI.BeginChangeCheck();
                EditorGUILayout.ObjectField(so.FindProperty("rootBone"));
                bool changed = EditorGUI.EndChangeCheck();

                so.ApplyModifiedProperties();

                return changed;
            }


            void OnWizardUpdate()
            {
                Validate();
            }

            void OnInspectorUpdate()
            {
                Validate();
            }

            void Validate()
            {
                if (!rootBone)
                {
                    errorString = "The root bone must be assigned.";
                    isValid = false;
                }
                else if (AreThereJointlessGaps())
                {
                    errorString = "There can't be a joint-less bone in the middle of the hierarchy.";
                    isValid = false;
                }
                else
                {
                    errorString = "";
                    isValid = true;
                }
            }

            bool AreThereJointlessGaps()
            {
                foreach (ConfigurableJoint joint in rootBone.GetComponentsInChildren<ConfigurableJoint>())
                {
                    bool parentHasJoint = joint.transform.parent.GetComponent<ConfigurableJoint>();
                    if (!parentHasJoint && joint != rootBone)
                    {
                        return true;
                    }
                }

                return false;
            }


            void OnWizardCreate()
            {
                int undoGroup = Undo.GetCurrentGroup();
                Undo.SetCurrentGroupName("Remove non-physics GameObjects from Ragdoll.");

                DestroyIrrelevantObjects();
                DetroyNonPhysicsChildrenRecursively(rootBone.transform);

                Undo.CollapseUndoOperations(undoGroup);

                DefinitionCreationWizard.Display(data);
            }

            void DestroyIrrelevantObjects()
            {
                RagdollDefinitionBindings bindings = rootBone.GetComponentInParent<RagdollDefinitionBindings>();
                Assert.IsNotNull(bindings);

                Transform transform = rootBone.transform;

                while (transform != bindings.transform)
                {
                    DestroySiblings(transform);
                    transform = transform.parent;
                }
            }

            void DestroySiblings(Transform transform)
            {
                Transform parent = transform.parent;
                for (int i = parent.childCount - 1; i >= 0; i--)
                {
                    Transform child = parent.GetChild(i);
                    if (child != transform)
                    {
                        Undo.DestroyObjectImmediate(child.gameObject);
                    }
                }
            }

            void DetroyNonPhysicsChildrenRecursively(Transform transform)
            {
                for (int i = transform.childCount - 1; i >= 0; i--)
                {
                    Transform child = transform.GetChild(i);

                    bool isPhysicsGameObject = child.GetComponent<ConfigurableJoint>() || child.GetComponentInChildren<Collider>(true) || child.GetComponentInChildren<Rigidbody>(true);

                    if (isPhysicsGameObject)
                    {
                        DetroyNonPhysicsChildrenRecursively(child);
                    }
                    else
                    {
                        Undo.DestroyObjectImmediate(child.gameObject);
                    }
                }
            }
        }

        public class DefinitionCreationWizard : ScriptableWizard
        {
            public static DefinitionCreationWizard Display(Data data)
            {
                var wizard = DisplayWizard<DefinitionCreationWizard>(WIZARD_TITLE + " Step: Definition Creation", "Create");
                wizard.Data = data;
                return wizard;
            }

            public Data Data
            {
                get => _data;
                set
                {
                    _data = value;
                    isValid = true;
                    definitionName = value.bindings.transform.parent.name;
                }
            }
            Data _data;

            public bool useExistingDefinition;
            public string definitionName;
            public RagdollDefinition existingDefinition;


            void Awake()
            {
                isValid = false;
                helpString = "Multiple RagdollProfiles will now be created.";
            }

            protected override bool DrawWizardGUI()
            {
                SerializedObject so = new SerializedObject(this);
                SerializedProperty useExisting_Property = so.FindProperty("useExistingDefinition");
                SerializedProperty existingDefinition_Prop = so.FindProperty("existingDefinition");

                EditorGUI.BeginChangeCheck();

                EditorGUILayout.PropertyField(useExisting_Property);
                if (useExisting_Property.boolValue)
                {
                    EditorGUILayout.PropertyField(existingDefinition_Prop);
                }
                else
                {
                    EditorGUILayout.PropertyField(so.FindProperty("definitionName"), new GUIContent("Definition Name"));
                }

                bool changed = EditorGUI.EndChangeCheck();

                so.ApplyModifiedProperties();

                Validate();
                createButtonName = useExistingDefinition ? "Use Existing Definition" : "Create New Definition";

                return changed;
            }

            void Validate()
            {
                if (useExistingDefinition)
                {
                    if (!existingDefinition)
                    {
                        isValid = false;
                        errorString = "No definition was provided.";
                    }
                    else if (!RagdollDefinitionValidator.Validate(existingDefinition, false))
                    {
                        isValid = false;
                        errorString = "The provided definition is invalid.";
                    }
                    else
                    {
                        isValid = true;
                        errorString = "";
                    }
                }
                else
                {
                    isValid = !string.IsNullOrEmpty(definitionName);
                    errorString = "";
                }
            }

            void OnWizardCreate()
            {
                if (useExistingDefinition)
                {
                    OnButtonUseExistingDefinition();
                }
                else
                {
                    OnButtonCreateNewDefinition();
                }

            }

            void OnButtonCreateNewDefinition()
            {
                string definitionPath = EditorUtility.SaveFilePanelInProject("Save Ragdoll Definition as...", $"RAGDEF_{definitionName}", "asset", "afdlmasdf");
                Data.profileDirectory = definitionPath.Substring(0, definitionPath.LastIndexOf("/") + 1);

                Data.definition = CreateDefinition(definitionPath);
                SetUpDefinition(Data.definition);
                Data.definitionName = definitionName;

                AssetDatabase.Refresh();

                DefinitionFixingWizard.Display(_data);
            }

            void OnButtonUseExistingDefinition()
            {
                _data.definition = existingDefinition;

                ComponentCreator.SetUpComponents(_data);
                SetFinalSelection(_data);
                Close();
            }


            static RagdollDefinition CreateDefinition(string definitionPath)
            {
                RagdollDefinition definition = CreateInstance<RagdollDefinition>();
                AssetDatabase.CreateAsset(definition, AssetDatabase.GenerateUniqueAssetPath(definitionPath));
                return definition;
            }

            void SetUpDefinition(RagdollDefinition definition)
            {
                SerializedObject serializedObject = new SerializedObject(definition);
                SerializedProperty bonesList = serializedObject.FindProperty("bones");

                int i = 0;
                foreach (ConfigurableJoint joint in Data.bindings.GetComponentsInChildren<ConfigurableJoint>())
                {
                    bonesList.InsertArrayElementAtIndex(i);
                    bonesList.GetArrayElementAtIndex(i).FindPropertyRelative("name").stringValue = joint.name;

                    i++;
                }

                serializedObject.FindProperty("_root").FindPropertyRelative("name").stringValue = bonesList.GetArrayElementAtIndex(0).FindPropertyRelative("name").stringValue;

                serializedObject.ApplyModifiedPropertiesWithoutUndo();
            }

        }

        public class DefinitionFixingWizard : ScriptableWizard
        {
            public static DefinitionFixingWizard Display(Data data)
            {
                var wizard = DisplayWizard<DefinitionFixingWizard>(WIZARD_TITLE + " Step: Definition Fixing", "Finish");
                wizard.Data = data;
                return wizard;
            }


            internal Data Data
            {
                get => _data;
                set
                {
                    _data = value;
                    if (ValidateDefinition())
                    {
                        _data.definitionWasAutoGeneratedCorrectly = true;
                        OnWizardCreate();
                    }
                    else
                    {
                        Selection.activeObject = Data.definition;
                        _data.definitionWasAutoGeneratedCorrectly = false;
                    }
                }
            }
            Data _data;

            protected override bool DrawWizardGUI()
            {
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.ObjectField("Definition", Data?.definition, typeof(RagdollDefinition), false);
                EditorGUI.EndDisabledGroup();

                return false;
            }

            void OnInspectorUpdate()
            {
                ValidateDefinition();
            }

            bool ValidateDefinition()
            {
                bool definitionIsValid = RagdollDefinitionValidator.Validate(Data.definition, false);

                SerializedObject so = new SerializedObject(Data.definition);
                so.FindProperty("_isValid").boolValue = definitionIsValid;
                so.ApplyModifiedProperties();

                this.isValid = definitionIsValid;

                if (isValid)
                {
                    errorString = "";
                }
                else
                {
                    errorString = "Could not create a valid a definition from the existing bone names. Please fix the definition manually.";
                }

                return definitionIsValid;
            }


            void OnWizardCreate()
            {
                CreateProfiles();
                ComponentCreator.SetUpComponents(_data);
                SetFinalSelection(_data);
                Close();
            }

            #region Profiles
            void CreateProfiles()
            {
                Data.powerProfile = CreatePowerProfile(Data.profileDirectory, Data.definitionName);
                SetUpPowerProfile(Data.powerProfile, Data.definition);

                Data.weightDistribution = CreateWeightDistribution(Data.profileDirectory, Data.definitionName);
                SetUpWeightDistribution(Data.weightDistribution, Data.definition);

                Data.animationProfile = CreateAnimationProfile(Data.profileDirectory, Data.definitionName);
                SetUpAnimationProfile(Data.animationProfile, Data.definition);

                AssetDatabase.Refresh();
            }

            static RagdollPowerProfile CreatePowerProfile(string profileDirectory, string definitionName)
            {
                RagdollPowerProfile powerProfile = CreateInstance<RagdollPowerProfile>();

                string path = AssetDatabase.GenerateUniqueAssetPath($"{profileDirectory}RAGPOW_{definitionName}_Default.asset");
                AssetDatabase.CreateAsset(powerProfile, path);

                return powerProfile;
            }

            static void SetUpPowerProfile(RagdollPowerProfile powerProfile, RagdollDefinition definition)
            {
                SerializedObject serializedObject = new SerializedObject(powerProfile);

                serializedObject.FindProperty("definition").objectReferenceValue = definition;

                SerializedProperty keys = serializedObject.FindProperty("settings").FindPropertyRelative("keys");
                SerializedProperty values = serializedObject.FindProperty("settings").FindPropertyRelative("values");

                int i = 0;
                foreach (BoneName bone in definition.Bones)
                {
                    keys.InsertArrayElementAtIndex(i);
                    keys.GetArrayElementAtIndex(i).FindPropertyRelative("name").stringValue = bone;

                    values.InsertArrayElementAtIndex(i);
                    values.GetArrayElementAtIndex(i).enumValueIndex = (int) PowerSetting.Powered;

                    i++;
                }

                serializedObject.FindProperty("_isValid").boolValue = true;

                serializedObject.ApplyModifiedPropertiesWithoutUndo();
            }

            static RagdollWeightDistribution CreateWeightDistribution(string profileDirectory, string definitionName)
            {
                RagdollWeightDistribution weightDistribution = CreateInstance<RagdollWeightDistribution>();

                string path = AssetDatabase.GenerateUniqueAssetPath($"{profileDirectory}RAGWGT_{definitionName}_Default.asset");
                AssetDatabase.CreateAsset(weightDistribution, path);

                return weightDistribution;
            }

            static void SetUpWeightDistribution(RagdollWeightDistribution distribution, RagdollDefinition definition)
            {
                SerializedObject serializedObject = new SerializedObject(distribution);
                serializedObject.FindProperty("definition").objectReferenceValue = definition;

                SerializedProperty keys = serializedObject.FindProperty("factors").FindPropertyRelative("keys");
                SerializedProperty values = serializedObject.FindProperty("factors").FindPropertyRelative("values");

                int i = 0;
                foreach (BoneName bone in definition.Bones)
                {
                    keys.InsertArrayElementAtIndex(i);
                    keys.GetArrayElementAtIndex(i).FindPropertyRelative("name").stringValue = bone;

                    values.InsertArrayElementAtIndex(i);
                    values.GetArrayElementAtIndex(i).floatValue = 1;

                    i++;
                }

                serializedObject.FindProperty("_isValid").boolValue = true;

                serializedObject.ApplyModifiedPropertiesWithoutUndo();
            }

            static RagdollAnimationProfile CreateAnimationProfile(string profileDirectory, string definitionName)
            {
                RagdollAnimationProfile animationProfile = CreateInstance<RagdollAnimationProfile>();

                string path = AssetDatabase.GenerateUniqueAssetPath($"{profileDirectory}RAGANIM_{definitionName}_Default.asset");
                AssetDatabase.CreateAsset(animationProfile, path);

                return animationProfile;
            }

            static void SetUpAnimationProfile(RagdollAnimationProfile animationProfile, RagdollDefinition definition)
            {
                SerializedObject serializedObject = new SerializedObject(animationProfile);

                serializedObject.FindProperty("definition").objectReferenceValue = definition;
                serializedObject.FindProperty("_isValid").boolValue = true;

                serializedObject.ApplyModifiedPropertiesWithoutUndo();
            }
            #endregion
        }

        public static class ComponentCreator
        {
            public static void SetUpComponents(Data data)
            {
                int undoGroup = Undo.GetCurrentGroup();
                Undo.SetCurrentGroupName("Add components to Ragdoll.");

                SetUpBindings(data);
                AddRagdollSettings(data);
                AddCollisionIgnorer(data);

                AddRagdollAnimator(data);
                AddCollisionReaction(data);
                AddPowerTransitioner(data);
                AddColliderVisualizer(data);

                Undo.CollapseUndoOperations(undoGroup);
            }

            static void SetUpBindings(Data data)
            {
                Undo.RecordObject(data.bindings, "");
                data.bindings.hideFlags = HideFlags.None;

                SerializedObject so = new SerializedObject(data.bindings);
                so.FindProperty("_definition").objectReferenceValue = data.definition;

                if (data.definitionWasAutoGeneratedCorrectly && data.definition)
                {
                    AutoFillBindings(so, data);
                }

                so.ApplyModifiedProperties();
            }

            static void AutoFillBindings(SerializedObject bindings, Data data)
            {
                SerializedProperty keys = bindings.FindProperty("bindings").FindPropertyRelative("keys");
                SerializedProperty values = bindings.FindProperty("bindings").FindPropertyRelative("values");

                keys.ClearArray();
                values.ClearArray();

                int i = 0;
                foreach (BoneName bone in data.definition.Bones)
                {
                    keys.InsertArrayElementAtIndex(i);
                    keys.GetArrayElementAtIndex(i).FindPropertyRelative("name").stringValue = bone;

                    ConfigurableJoint joint = data.bindings.transform.FindChildRecursively(bone)?.GetComponent<ConfigurableJoint>();

                    values.InsertArrayElementAtIndex(i);
                    values.GetArrayElementAtIndex(i).objectReferenceValue = joint;

                    i++;
                }
            }

            static void AddRagdollSettings(Data data)
            {
                var settings = Undo.AddComponent<RagdollSettings>(data.bindings.gameObject);
                SerializedObject so = new SerializedObject(settings);

                so.FindProperty("_powerProfile").objectReferenceValue = data.powerProfile;
                so.FindProperty("_weightDistribution").objectReferenceValue = data.weightDistribution;

                so.ApplyModifiedProperties();
            }

            static void AddCollisionIgnorer(Data data)
            {
                Undo.AddComponent<RagdollCollisionIgnorer>(data.bindings.gameObject);
            }

            static void AddRagdollAnimator(Data data)
            {
                RagdollAnimator animator = Undo.AddComponent<RagdollAnimator>(data.modelGameObject);
                SerializedObject so = new SerializedObject(animator);

                so.FindProperty("_ragdollBindings").objectReferenceValue = data.bindings;
                so.FindProperty("currentProfile").objectReferenceValue = data.animationProfile;

                so.ApplyModifiedProperties();
            }

            static void AddCollisionReaction(Data data)
            {
                Undo.AddComponent<RagdollCollisionReaction>(data.modelGameObject);
            }

            static void AddPowerTransitioner(Data data)
            {
                Undo.AddComponent<RagdollPowerOnTransitioner>(data.modelGameObject);
            }

            static void AddColliderVisualizer(Data data)
            {
                Undo.AddComponent<RagdollColliderVisualizer>(data.bindings.gameObject);
            }
        }

        public static void SetFinalSelection(Data data)
        {
            Selection.activeObject = data.bindings.gameObject;

            if (data.definition) EditorGUIUtility.PingObject(data.definition);
            EditorUtility.FocusProjectWindow();
        }
    }
}