using System.Collections.Generic;
using UnityEngine;

namespace Hairibar.Ragdoll.Animation
{
    internal class TargetToRagdollMapper
    {
        public IReadOnlyCollection<RagdollBoneTargetBonePair> BonePairs => _bonePairs;

        readonly RagdollBoneTargetBonePair[] _bonePairs;


        public void MapTargetToRagdoll()
        {
            foreach (RagdollBoneTargetBonePair pair in _bonePairs)
            {
                MapPair(pair);
            }
        }

        void MapPair(RagdollBoneTargetBonePair pair)
        {
            Vector3 simulatedPosition = pair.RagdollBone.Transform.position;
            Quaternion simulatedRotation = pair.RagdollBone.Transform.rotation;

            pair.TargetBone.SetPositionAndRotation(simulatedPosition, simulatedRotation);
        }

        #region Initialization
        public TargetToRagdollMapper(RagdollDefinitionBindings bindings, Transform targetParent)
        {
            if (!bindings) throw new System.ArgumentNullException("Tried to create a TargetToRagdollMapper with a null RagdollDefinitionBindings.");

            _bonePairs = CreateBonePairs(bindings, targetParent);
        }

        RagdollBoneTargetBonePair[] CreateBonePairs(RagdollDefinitionBindings bindings, Transform targetParent)
        {
            List<RagdollBoneTargetBonePair> pairs = new List<RagdollBoneTargetBonePair>();
            
            foreach (var bone in bindings.Bones)
            {
                var targetBoneTransform = targetParent.FindChildRecursiveByName(bone.Name);
                pairs.Add(new RagdollBoneTargetBonePair(bone, targetBoneTransform));
            }
            
            return pairs.ToArray();
        }
        #endregion
    }
}
