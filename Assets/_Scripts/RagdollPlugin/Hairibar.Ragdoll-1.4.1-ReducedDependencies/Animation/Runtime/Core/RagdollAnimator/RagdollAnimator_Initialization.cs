using System.Collections.Generic;

namespace Hairibar.Ragdoll.Animation
{
    //Initialization
    public partial class RagdollAnimator
    {
        void CreateTargetToRagdollMapper()
        {
            mapper = new TargetToRagdollMapper(_ragdollBindings, transform);
        }

        void CreateAnimatedPairs(IReadOnlyCollection<RagdollBoneTargetBonePair> bonePairs)
        {
            animatedPairs = new AnimatedPair[bonePairs.Count];

            int i = 0;
            foreach (RagdollBoneTargetBonePair bonePair in bonePairs)
            {
                animatedPairs[i] = new AnimatedPair(bonePair);
                i++;
            }
        }


        void InitializePreviousPosesWithCurrentPose()
        {
            foreach (AnimatedPair pair in animatedPairs)
            {
                pair.previousPose = pair.currentPose;
            }
        }
    }
}
