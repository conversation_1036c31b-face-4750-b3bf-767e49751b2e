using System.Collections.Generic;
using UnityEngine;

namespace Hairibar.Ragdoll
{
    public class RagdollBone
    {
        public PowerSetting PowerSetting
        {
            get => _powerSetting;
            set
            {
                PowerSetting oldValue = _powerSetting;
                _powerSetting = value;

                if (oldValue != value)
                {
                    OnPowerSettingChanged?.Invoke(oldValue, value);
                }
            }
        }
        public delegate void OnPowerSettingChangedHandler(PowerSetting previousSetting, PowerSetting newSetting);
        public event OnPowerSettingChangedHandler OnPowerSettingChanged;

        public BoneName Name { get; }
        public bool IsRoot { get; }

        public bool IsConfiguredInWorldSpace { get; }
        public Quaternion StartingJointRotation { get; }

        public Transform Transform { get; }
        public Rigidbody Rigidbody { get; }
        public ConfigurableJoint Joint { get; }

        public IEnumerable<Collider> Colliders { get; }

        Quaternion _currentOffset = Quaternion.identity;
        Vector3 _velocity = Vector3.zero;
        float _dampingPower = 0f;
        bool _isSleeping = true;

        PowerSetting _powerSetting = PowerSetting.Kinematic;

        #region Initialization
        internal RagdollBone(BoneName name, Transform transform, Rigidbody rigidbody, ConfigurableJoint joint, bool isRoot)
        {
            Name = name;
            Transform = transform;
            Rigidbody = rigidbody;
            Joint = joint;
            IsRoot = isRoot;
			IsConfiguredInWorldSpace = isRoot;
			var configurableJoint = Transform.GetComponent<ConfigurableJointFixer>();
			StartingJointRotation = configurableJoint.JointFixerProperties.rotation;
			Colliders = GatherColliders();
            ConfigureJoint();
        }

        Collider[] GatherColliders()
        {
            List<Collider> colliders = new List<Collider>();

            GatherCollidersAtTransform(Transform);
            VisitChildren(Transform);

            return colliders.ToArray();


            void GatherCollidersAtTransform(Transform transform)
            {
                colliders.AddRange(transform.GetComponents<Collider>());
            }

            void VisitChildren(Transform parent)
            {
                for (int i = 0; i < parent.childCount; i++)
                {
                    Transform child = parent.GetChild(i);
                    bool isItsOwnBone = child.GetComponent<ConfigurableJoint>();

                    if (!isItsOwnBone)
                    {
                        GatherCollidersAtTransform(child);
                        VisitChildren(child);
                    }
                }
            }
        }

        void ConfigureJoint()
        {
            Joint.configuredInWorldSpace = IsConfiguredInWorldSpace;
            Joint.rotationDriveMode = RotationDriveMode.Slerp;
        }
        #endregion


        /// <summary>
        /// At OnEnable(), joints do some weird re-configuring. This method, if called from OnEnable(), deals with that.
        /// </summary>
        internal void ResetJointAxisOnEnable()
        {
            // https://forum.unity.com/threads/hinge-joint-limits-resets-on-activate-object.483481/#post-5713138
            Quaternion originalRotation = Transform.localRotation;
            Transform.localRotation = StartingJointRotation;
            Joint.axis = Joint.axis;    // Yes, this is intentional. The axis setter triggers some calculations that we need.
            Transform.localRotation = originalRotation;
        }

        public void SetOffset(Quaternion currentOffset)
        {
            _currentOffset = currentOffset;
			_isSleeping = _currentOffset.AlmostEquals(Quaternion.identity) && _velocity.sqrMagnitude.IsZero();
		}

        public void ClearVelocity()
        {
            _velocity = Vector3.zero;
			_currentOffset = Quaternion.identity;
		}

        public void AddVelocity(Vector3 forceDir, float forceMag)
        {
            const float velocityMaxMagnitude = 250f;

            Vector3 oldDir = Transform.forward;
            forceDir -= oldDir * Vector3.Dot(oldDir, forceDir) / (oldDir.magnitude * forceDir.magnitude);

            Vector3 newDir = oldDir + forceDir;
            Vector3 angularImpulse = Quaternion.FromToRotation(oldDir, newDir).eulerAngles;
            if (angularImpulse.x > 180) angularImpulse.x -= 360f;
			if (angularImpulse.y > 180) angularImpulse.y -= 360f;
			if (angularImpulse.z > 180) angularImpulse.z -= 360f;

			_velocity += angularImpulse.normalized * forceMag;
            if (_velocity.x + _velocity.y + _velocity.z > velocityMaxMagnitude)
                _velocity *= velocityMaxMagnitude / (_velocity.x + _velocity.y + _velocity.z);
            if (!_velocity.sqrMagnitude.IsZero())
                _isSleeping = false;
		}

        public Quaternion UpdateVelocity(float dt)
        {
            if (_isSleeping)
            {
                _dampingPower = 0f;
                return Quaternion.identity;
            }

            _dampingPower += 1f * dt;
            _dampingPower = Mathf.Min(0.3f, _dampingPower);

            _currentOffset = Quaternion.Euler(dt * _velocity) * Quaternion.Slerp(_currentOffset, Quaternion.identity, _dampingPower);
            _velocity = Vector3.Lerp(_velocity, Vector3.zero, _dampingPower);

            _isSleeping = _currentOffset.AlmostEquals(Quaternion.identity) && _velocity.sqrMagnitude.IsZero();
            return _currentOffset;
        }

        public override string ToString()
        {
            return Name.ToString();
        }
    }
}