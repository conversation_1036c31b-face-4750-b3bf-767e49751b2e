using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AudioTransformOverride : MonoBehaviour
{
    public Vector3 m_positionAdjust = Vector3.zero;
    public Vector3 m_rotationAdjust = Vector3.zero;

    void OnEnable() => RefreshCache();
    void OnDisable() => RefreshCache();

    void RefreshCache() => AudioClipManager.Me.RefreshAudioTransformCache(gameObject);

    public (Vector3, Vector3, Vector3) GetTransform()
    {
        var pos = transform.position + m_positionAdjust;
        var rot = Quaternion.Euler(m_rotationAdjust);
        var fwd = rot * transform.forward;
        var up = rot * transform.up;
        return (pos, fwd, up);
    }

    public void ApplyAk(GameObject _o)
    {
        var (pos, fwd, up) = GetTransform();
        AkSoundEngine.SetObjectPosition(_o, pos, fwd, up);
    }
    
#if UNITY_EDITOR
    void OnDrawGizmos()
    {
        var (pos, fwd, up) = GetTransform();
        UnityEditor.Handles.color = Color.blue;
        UnityEditor.Handles.DrawLine(pos, pos + fwd * 2, 2f);
        UnityEditor.Handles.color = Color.green;
        UnityEditor.Handles.DrawLine(pos, pos + up * 2, 2f);
    }
#endif
}
