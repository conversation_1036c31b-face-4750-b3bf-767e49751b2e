using UnityEngine;

public class PlantInstance : PlantInstanceMesh
{
    public string m_plantName = "TestPlant";
    public float m_width = 4 * 1.05f, m_height = 3.5f * 1.05f;
    private GameObject m_instance;

    protected void Awake()
    {
        var prefab = Resources.Load<GameObject>("Plants/" + m_plantName);
        if (prefab == null)
        {
            Debug.LogError($"Plant prefab not found: {m_plantName}");
            Destroy(gameObject);
            return;
        }
        m_instance = Instantiate(prefab, transform);
        m_instance.transform.localScale = new Vector3(m_width, m_height, 1);
        base.Awake();
    }

#if UNITY_EDITOR
    void OnValidate()
    {
        if (m_instance == null) return;
        m_instance.transform.localScale = new Vector3(m_width, m_height, 1);
    }

    void OnDrawGizmos()
    {
        var c1 = new Vector3(m_width * -.5f, 0, 0);
        var c2 = new Vector3(m_width * .5f, 0, 0);
        var c3 = new Vector3(m_width * -.5f, m_height, 0);
        var c4 = new Vector3(m_width * .5f, m_height, 0);
        var isSelected = gameObject == UnityEditor.Selection.activeGameObject; 
        var clr = isSelected ? Color.yellow : Color.green;
        Gizmos.color = clr;
        Gizmos.matrix = transform.localToWorldMatrix;
        Gizmos.DrawLine(c1, c2);
        Gizmos.DrawLine(c2, c4);
        Gizmos.DrawLine(c4, c3);
        Gizmos.DrawLine(c3, c1);
        if (isSelected)
        {
            UnityEditor.Handles.color = clr;
            UnityEditor.Handles.matrix = transform.localToWorldMatrix;
            const float c_thickness = 3;
            UnityEditor.Handles.DrawLine(c1, c2, c_thickness);
            UnityEditor.Handles.DrawLine(c2, c4, c_thickness);
            UnityEditor.Handles.DrawLine(c4, c3, c_thickness);
            UnityEditor.Handles.DrawLine(c3, c1, c_thickness);
        }
    }
#endif
}
