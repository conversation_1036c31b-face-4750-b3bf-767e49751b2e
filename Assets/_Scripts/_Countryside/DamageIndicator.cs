using UnityEngine;

public class DamageIndicator : MonoBehaviour
{
    public TMPro.TextMeshProUGUI m_text;
    private float m_time = 0;
    private float m_damage = 0;
    private float m_duration;

    const float c_raiseBase = 3f;
    const float c_raiseSpeed = .2f;
    const float c_duration = 3;
    const float c_fadeInTime = .3f;
    const float c_fadeOutTime = 1f;

    public void Activate(Transform _target, float _damage)
    {
        m_time = 0;
        m_damage = _damage;
        m_duration = c_duration;
        transform.SetParent(_target, false);
        transform.localPosition = Vector3.up * c_raiseBase;
        RefreshDamageLabel();
    }

    public void AddDamage(float _damage)
    {
        m_damage += _damage;
        m_duration = c_duration + m_time;
        RefreshDamageLabel();
    }

    private void RefreshDamageLabel()
    {
        m_text.text = m_damage < 1 ? "" : $"{m_damage:n0}";
    }

    void LateUpdate()
    {
        m_time += Time.deltaTime;
        transform.localPosition = Vector3.up * (c_raiseBase + m_time * c_raiseSpeed);
        var alpha = m_time < c_fadeInTime ? m_time / c_fadeInTime : (m_time > m_duration - c_fadeOutTime ? 1 - (m_time - (m_duration - c_fadeOutTime)) / c_fadeOutTime : 1);
        var c = m_text.color;
        c.a = alpha;
        m_text.color = c;
        if (m_time > m_duration)
            Destroy(gameObject);
        
        transform.forward = Camera.main.transform.forward;
    }

    public static DamageIndicator Show(Transform _target, float _damage)
    {
        var existing = _target.GetComponentInChildren<DamageIndicator>();
        if (existing != null)
        {
            existing.AddDamage(_damage);
            return existing;
        }
        var prefab = Resources.Load<DamageIndicator>("_Prefabs/UI/DamageIndicator");
        var indicator = Instantiate(prefab);
        indicator.Activate(_target, _damage);
        return indicator;
    }
}
