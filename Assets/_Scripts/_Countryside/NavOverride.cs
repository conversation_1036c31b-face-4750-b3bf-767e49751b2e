using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NavOverride : MonoBehaviour
{
    public Transform m_from;
    public Transform m_to;
    public bool m_go = false;
    public bool m_reverse = false;
    public bool m_noInitialJump = false;

    void OnValidate()
    {
        if (m_reverse)
        {
            m_reverse = false;
            (m_from, m_to) = (m_to, m_from);
        }
        if (m_go)
        {
            m_go = false;
            var agent = GetComponentInChildren<NavAgent>();


            if (m_noInitialJump == false)
            {
                int startStrut = NavStrut.NavStrutAtPosition(m_from.position);
                Vector3 position = m_from.position;
                if (startStrut == 0) position = position.GroundPosition(0);
                else position = NavStrut.s_allStruts[-startStrut - 2].ClosestContainedPoint(position);
                agent.transform.position = position;
            }

            agent.NavigateTo(m_to.position);
            GetComponent<MAWorker>().enabled = false;
        }
    }
}
