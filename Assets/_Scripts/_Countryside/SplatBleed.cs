using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SplatBleed : MonoBehaviour
{
    public int m_splatIndex = 4;
    private float m_time;
    public class SplatTendril
    {
        public float m_baseTime = 0;
        public float m_speed = .05f;
        public float m_width = 1;
        public Vector2 m_pos = Vector2.zero;
        public float m_angle = 0;

        public void Draw(Vector3 _basePos, int _channel, float _dt)
        {
            const float c_steerPerSecond = .8f;
            var step = m_speed * _dt;
            m_angle += Random.Range(-1f, 1f) * c_steerPerSecond * _dt;
            var dir = new Vector2(Mathf.Cos(m_angle), Mathf.Sin(m_angle));
            m_pos += dir * step;
            var pos = _basePos;
            pos.x += m_pos.x; pos.z += m_pos.y;
            int ix = GlobalData.TerrainX(pos.x), iz = GlobalData.TerrainZ(pos.z);
            CameraRenderSettings.Me.OverrideSplatPoint(ix, iz, _channel, step * 5, true);
            CameraRenderSettings.Me.OverrideSplatPoint(ix + 1, iz, _channel, step * 5, true);
            CameraRenderSettings.Me.OverrideSplatPoint(ix, iz + 1, _channel, step * 5, true);
            CameraRenderSettings.Me.OverrideSplatPoint(ix + 1, iz + 1, _channel, step * 5, true);
        }
    }
    public SplatTendril[] m_tendrils;

    void Start()
    {
        m_tendrils = new SplatTendril[4];
        for (int i = 0; i < m_tendrils.Length; ++i)
            m_tendrils[i] = new SplatTendril() { m_angle = Random.value * Mathf.PI * 2 };
    }

    void Update()
    {
        m_time += Time.deltaTime;
        foreach (var tendril in m_tendrils)
        {
            tendril.Draw(transform.position, m_splatIndex, Time.deltaTime);
        }
    }
}
