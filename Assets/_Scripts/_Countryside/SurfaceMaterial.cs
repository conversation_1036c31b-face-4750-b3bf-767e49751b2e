using UnityEngine;

public class SurfaceMaterial : MonoBehaviour
{
    public enum ESurfaceType
    {
        Mud,
        Grass,
        Dirt,
        Wood,
        WaterShallow,
        WaterDeep,
        Stone,
        Snow,
        Slab,
    }
    public ESurfaceType m_surfaceType = ESurfaceType.Mud;
    public string SurfaceAudioSwitch => $"Surface_{m_surfaceType}";

    public static string GetSurfaceAudioSwitchFromObject(GameObject _o, string _default)
    {
        var sm = _o.GetComponentInParent<SurfaceMaterial>();
        if (sm != null) return sm.SurfaceAudioSwitch;
        return _default;
    }
}
