using MACharacterStates;
using UnityEngine;

public class HeadTracker : MonoBehaviour
{
	public const float c_headTurnLimit = 0f; //cos(90)
	public const float c_headTurnAnimSensitivity = 20f;

	public const float c_minD2 = 2f * 2f;
	public const float c_maxD2 = 30f * 30f;
	public const float c_maxPossessedD2 = 10f * 10f;

	public const float c_destDistMult = 1.6f;
	public const float c_obstructedDistMult = 1.3f;
	public const float c_touristDistMult = 1.1f;
	public const float c_creatureDistMult = 0.7f;
	public const float c_heroDistMult = 0.7f;
	public const float c_targetDistMult = 0.25f;

	public const float c_updateTargetPollDelayMin = 0.5f;
	public const float c_updateTargetPollDelayMax = 1f;

	public enum TargetType
	{
		Animation,
		Position,
		Transform,
		Forced
	}

	public TargetType m_targetType;
	public RagdollController m_controller;
	public MACharacterBase m_character;
	public Transform m_head;
	public Vector3 m_headPos;
	public float m_eyeOffset;

	public Transform m_lastTarget;
	public Vector3 m_lastPosition;
	public Transform m_forcedTarget = null;
	private Vector3 m_smoothDir;
	private int m_disableCount = 0;
	private float m_nextUpdateTargetTime = 0f;
	private float m_currentmaxD2;
	
	public void Stop() => ++m_disableCount;
	public void Resume() => --m_disableCount;

	public static HeadTracker Create(MACharacterBase _character, Transform _head)
	{
		var ht = _character.gameObject.AddComponent<HeadTracker>();
		ht.Init(_character, _head);
		return ht;
	}

	public void Init(MACharacterBase _character, Transform _head)
	{
		m_character = _character;
		m_controller = m_character.GetComponentInChildren<RagdollController>();
		m_head = _head;
	}
	
	public Transform m_debugOverrideLookAt
	{
		get { return m_debugOverrideLookAt; }
		set { Override(value); m_debugOverrideLookAt = value; }
	}

	public void Override(Transform _t) => m_forcedTarget = _t;

	bool CanLookAt(Vector3 dir)
	{
		return Vector3.Dot(m_head.parent.forward, dir.GetXZNorm()) >= c_headTurnLimit;
	}

	public TargetType GetWhatToLookAt()
	{
		if (m_forcedTarget != null)
			return TargetType.Forced;

		var bestTargetType = TargetType.Animation;
		float bestD2 = m_currentmaxD2;

		bool CheckTarget(Transform _potentialTarget, float _typeMult = 1f)
		{
			if (CheckDist(_potentialTarget.position, _typeMult, false))
			{
				m_lastTarget = _potentialTarget;
				bestTargetType = TargetType.Transform;
				return true;
			}
			return false;
		}

		bool CheckDist(Vector3 _potentialPoint, float _typeMult, bool _setPosition = true)
		{
			var dir = _potentialPoint - m_headPos;
			if (!CanLookAt(_potentialPoint - m_headPos))
				return false;
			var d2 = dir.xzSqrMagnitude() * (_typeMult * _typeMult);
			if (d2 <= c_minD2 || d2 >= bestD2)
				return false;
			if (!m_character.IsTargetVisible(_potentialPoint, out bool _closeEnough))
			{
				if (!_closeEnough)
					return false;
				d2 *= c_obstructedDistMult * c_obstructedDistMult;
				if (d2 >= bestD2)
					return false;
			}

			m_lastTarget = null;
			m_lastPosition = _potentialPoint.GroundPosition(m_eyeOffset);
			if (_setPosition)
				bestTargetType = TargetType.Position;
			bestD2 = d2;
			return true;
		}

		if (m_character.TargetObject != null)
		{
			CheckTarget(m_character.TargetObject.transform, c_targetDistMult);
		}
		else
		{
			foreach (var c in NGManager.Me.m_MACharacterList)
			{
				if (c == null || c == m_character || !c.isActiveAndEnabled)
					continue;
				float typeMult = c switch
				{
					MATourist => c_touristDistMult,
					MAHeroBase => c_heroDistMult,
					MACreatureBase => c_creatureDistMult,
					_ => 1f
				};
				CheckTarget(c.m_transform, typeMult);
			}
		}
		var navAgent = m_character.m_nav;
		if (navAgent != null && navAgent.IsNavigating)
			CheckDist(navAgent.OriginalTargetPosition, c_destDistMult * c_destDistMult);

		return bestTargetType;
	}

	public void LateUpdateMe()
	{
		if (m_disableCount > 0) 
			return;
		
		if (!GameManager.Me || !GameManager.Me.LoadComplete)
			return;
		
		if (!m_character.isActiveAndEnabled || m_controller.IsRagdolled || m_character.m_state == NGMovingObject.STATE.HELD_BY_PLAYER || m_character.InState(CharacterStates.HeldByPlayer))
			return;

		bool isPossessed = GameManager.Me.IsPossessed(m_character);
		m_currentmaxD2 = isPossessed ? c_maxPossessedD2 : c_maxD2;
		m_headPos = m_head.position;
		m_eyeOffset = m_headPos.y - m_character.m_transform.position.y;
		if (Time.time > m_nextUpdateTargetTime)
		{
			m_nextUpdateTargetTime = Time.time + Random.Range(c_updateTargetPollDelayMin, c_updateTargetPollDelayMax);
			m_targetType = GetWhatToLookAt();
		}

		Vector3 lookDir = m_head.forward;
		if (isPossessed)
			lookDir = Camera.main.transform.forward.GetXZNorm();
		switch (m_targetType)
		{
			case TargetType.Forced:
				{
					if (m_forcedTarget == null)
					{
						m_nextUpdateTargetTime = Time.time;
						break;
					}
					var dir = m_forcedTarget.position - m_headPos;
					if (CanLookAt(dir))
						lookDir = dir.normalized;
					break;
				}
			case TargetType.Transform:
				{
					if (m_lastTarget == null || !m_lastTarget.gameObject.activeSelf)
					{
						m_lastTarget = null;
						m_nextUpdateTargetTime = Time.time;
						break;
					}
					var dir = m_lastTarget.position - m_headPos;
					dir.y += m_eyeOffset;
					if (CanLookAt(dir))
						lookDir = dir.normalized;
					break;
				}

			case TargetType.Position:
				{
					var dir = m_lastPosition - m_headPos;
					if (dir.sqrMagnitude >= c_minD2 * c_minD2)
						lookDir = dir;
					break;
				}

			case TargetType.Animation:
				{
					break; //Just leave lookDir default value as is
				}
		}
		m_head.localEulerAngles = SmoothAndClampDir(lookDir);
	}

	public Vector3 SmoothAndClampDir(Vector3 dir)
	{
		dir.Normalize();

		var neckRot = m_head.parent.rotation;
		var invNeckRot = Quaternion.Inverse(neckRot);

		var lerpAmount = Mathf.Atan2(Quaternion.Angle(m_head.localRotation, Quaternion.identity), Vector3.Angle(dir, Vector3.forward)) / (Mathf.PI * 0.5f);
		var unsmoothedDir = invNeckRot * Utility.SlerpFixedY(dir, m_head.forward, SmoothStep(lerpAmount));

		if (m_smoothDir.sqrMagnitude < .1f * .1f)
			m_smoothDir = dir;
		else
			m_smoothDir = Vector3.Lerp(m_smoothDir, unsmoothedDir, 0.1f);

		var unclampedRot = Quaternion.LookRotation(m_smoothDir, Vector3.up);
		Vector3 target = unclampedRot.eulerAngles;
		var clampedRot = new Vector3(Utility.ClampAngleBetween(target.x, -30f, 30f), Utility.ClampAngleBetween(target.y, -50f, 50f), Utility.ClampAngleBetween(target.z, -3f, 3f));

		/*GameManager.Me.ClearGizmos($"ppop{m_character.m_ID}");
		var col = (m_targetType == TargetType.Animation) ? Color.green : ((m_targetType == TargetType.Position) ? Color.blue : Color.red);
		GameManager.Me.AddGizmoLine($"ppop{m_character.m_ID}", m_headPos, m_headPos + Quaternion.LookRotation(clampedRot, Vector3.up) * Vector3.forward, col);
		GameManager.Me.AddGizmoLine($"ppop{m_character.m_ID}", m_headPos, m_headPos + dir, Color.white);
		GameManager.Me.AddGizmoLine($"ppop{m_character.m_ID}", m_headPos, m_headPos + unsmoothedDir, Color.grey);
		GameManager.Me.AddGizmoLine($"ppop{m_character.m_ID}", m_headPos, m_headPos + m_head.forward, Color.black);*/

		return clampedRot;
	}

	public float SmoothStep(float t)
	{
		return t * t * t * (10 + t * (t * 6 - 15));
	}
}
