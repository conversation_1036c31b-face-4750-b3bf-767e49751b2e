using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;

public class CameraControlTransitionAsset : PlayableAsset
{
	public CameraControlTransitionBehaviour template;

	public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
	{
		var playable = ScriptPlayable<CameraControlTransitionBehaviour>.Create(graph, template);
       	return playable;   
	}
}