using UnityEngine;
using UnityEngine.Timeline;

[TrackClipType(typeof(CameraControlOffsetAsset))]
[TrackClipType(typeof(CameraControlTransitionAsset))]
[TrackClipType(typeof(CameraControlResetAsset))]
[TrackBindingType(typeof(CameraTransitionReference))]
public class CameraControlTrack : TrackAsset {}

public class TimelineCameraTransition : CameraTransition
{
	// Position Transition
	private Vector3 tweenNormal = Vector3.up;
	private float tweenDistance = 5f;

	// Rotation Transition
	Quaternion rotationStart = Quaternion.identity;
	Quaternion rotationEnd = Quaternion.identity;
	Quaternion rotationSlerp = Quaternion.identity;

	// Position Offset
	Vector3 offset_camera_position = Vector3.zero;

	// Rotation Offset Camera
	Vector3 offset_camera_rotation = Vector3.zero;

	// Rotation Offset Pivot
	float offset_pivot_rotation = 0f;
	



	public TimelineCameraTransition(Vector3 _focusPos, System.Action _onFinishedCallback, float _distToFocus = 50f)
									: base( _focusPos, 0f, false, _onFinishedCallback, _distToFocus ) {}

	public override void Start( out Vector3 _targetPosition, out Vector3 _targetRotation )
	{
		m_startPos = GameManager.Me.CamPos;
		m_endPos = m_focusPos + ((m_startPos-m_focusPos).NewY(0).normalized * m_distanceToFocus) + Vector3.up * (m_distanceToFocus * 0.5f);

		tweenNormal = (m_endPos - m_startPos);
		tweenDistance = tweenNormal.magnitude;
		tweenNormal.Normalize();

		rotationStart = rotationSlerp = GameManager.Me.m_camera.transform.rotation;
		rotationEnd = Quaternion.LookRotation( (m_focusPos-m_endPos).normalized, Vector3.up );
		
		//CameraMovement.Me.CancelCameraTransition();
		m_state = EState.Running;
		
		_targetPosition = GameManager.Me.CamPos;
		_targetRotation = GameManager.Me.CamRot;
	}

	public override void Update(out Vector3 _targetPosition, out Vector3 _targetRotation)
	{
		_targetPosition = m_startPos + cameraTween + offset_camera_position;

		var f = m_focusPos - _targetPosition;
		f = Quaternion.Euler(0, offset_pivot_rotation, 0) * f;
		_targetPosition = m_focusPos - f;

		_targetRotation = rotationSlerp.eulerAngles + offset_camera_rotation + Vector3.up * offset_pivot_rotation;
	}

	public void Reset()
	{
		offset_camera_position = Vector3.zero;
		offset_camera_rotation = Vector3.zero;
		offset_pivot_rotation = 0f;

		cameraTween = Vector3.zero;
		rotationSlerp = rotationStart;
	}

	public void ApplyTransitionBehaviour( CameraControlTransitionBehaviour transition )
	{
		cameraTween = (tweenNormal * tweenDistance * transition.normalized_position);
		rotationSlerp = Quaternion.SlerpUnclamped( rotationStart, rotationEnd, transition.normalized_rotation );
	}

	public void ApplyOffsetBehaviour( CameraControlOffsetBehaviour offset )
	{
		offset_camera_position = offset.offset_camera_position;
		offset_camera_rotation = offset.offset_camera_rotation;
		offset_pivot_rotation = offset.offset_pivot_rotation;
	}

	public void ForceFinish()
	{
		m_state = EState.Finished;
	}
}