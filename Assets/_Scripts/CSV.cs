using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

public class CSV
{
    private List<List<string>> m_content;
    public List<List<string>> Content => m_content;
    
    private static string Fixup(string _s)
    {
        return _s.Replace("\\n", "\n");
    }
    
    public CSV(string _name, bool _inhibitComments)
    {
        string content;
        content = Resources.Load<TextAsset>(_name).text;
        var res = new List<List<string>>();
        m_content = res;
        var line = new List<string>();
        bool inQuote = false;
        var str = new StringBuilder();
        for (int i = 0; i < content.Length; ++i)
        {
            bool finishLine = false;
            if (content[i] == '"')
            {
                if (i > 0 && content[i - 1] == '"') str.Append('"');
                inQuote = !inQuote;
            }
            else if (content[i] == ',' && !inQuote)
            {
                line.Add(Fixup(str.ToString()));
                str.Clear();
            }
            else if (content[i] == '\n' && !inQuote)
            {
                finishLine = true;
            }
            else if (content[i] != '\r')
            {
                str.Append(content[i]);
            }
            if (finishLine || i == content.Length - 1)
            {
                line.Add(Fixup(str.ToString()));
                str.Clear();
                if (line.Count > 0 && (!_inhibitComments || !line[0].StartsWith("#")))
                    res.Add(line);
                line = new List<string>();
            }
        }
        if (line.Count > 0)
            res.Add(line);
    }
    
    public static List<List<string>> Read(string _s, bool _inhibitComments = true)
    {
        return (new CSV(_s, _inhibitComments)).Content;
    }
}
