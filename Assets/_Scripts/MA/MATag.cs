using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class MATag : MonoBehaviour, IPointerClickHandler
{
    [SerializeField]
    private Transform m_panel = null;
    
    [SerializeField]
    private Image m_tagIcon = null;
    public Image TagIcon => m_tagIcon;
    
    [SerializeField]
    private TextMeshProUGUI m_tagCount = null;    
    
    [SerializeField]
    private string m_triggerCloseAnimation = "close", m_triggerWiggleAnimation = "isupdated";

    private Animator m_animator = null;

    private string m_tagType = "";
    private Queue<Transform> m_tagTargetQueue = new Queue<Transform>();
    private string m_currentIcon = "";
    private bool m_closing = false;

    private string m_rightClickInfoPanelUI = "";
    
    private Transform LastFocussedObject => m_tagTargetQueue.Peek();

    public class TagInfo
    {
        public string m_tagType = "";//MATagManager.TagTypes.None;
        public string m_icon = "";
        public Transform[] m_targetObjects = null;
        public string m_rightClickInfoPanelUI;
    }
    
    private void Awake()
    {
        m_animator = GetComponent<Animator>();
        gameObject.SetActive(false);
    }
    
    public void Init(TagInfo _tagInfo)
    {
        if (_tagInfo.m_targetObjects == null || _tagInfo.m_targetObjects.Length == 0)
        {
            m_tagTargetQueue.Clear();
            if (m_closing == false)
            {
                m_closing = true;
                m_animator.SetTrigger(m_triggerCloseAnimation);
            }
            return;
        }
        
        gameObject.SetActive(true);
        m_closing = false;
        
        m_tagType = _tagInfo.m_tagType;
        m_rightClickInfoPanelUI = _tagInfo.m_rightClickInfoPanelUI;
        
        if(m_currentIcon != _tagInfo.m_icon)
        {
            m_currentIcon = _tagInfo.m_icon;
            m_tagIcon.sprite = Resources.Load<Sprite>(_tagInfo.m_icon);
        }
        
        m_tagCount.text = _tagInfo.m_targetObjects.Length.ToString();

        Queue<Transform> newQueue = new Queue<Transform>();
        foreach (Transform oldTargetTransform in m_tagTargetQueue)
        {
            if (oldTargetTransform != null && _tagInfo.m_targetObjects.Contains(oldTargetTransform)) //carefully filter out nulls and chars not on new list anymore
            {
                newQueue.Enqueue(oldTargetTransform);
            }
        }
        
        bool newTargetFound = false;
        foreach (Transform newTargetTransform in _tagInfo.m_targetObjects)
        {
            if(newQueue.Contains(newTargetTransform) == false)
            {
                newTargetFound = true;
                newQueue.Enqueue(newTargetTransform);
            }
        }

        if (newTargetFound || newQueue.Count != m_tagTargetQueue.Count)
        {
            m_animator.SetTrigger(m_triggerWiggleAnimation);
        }

        m_tagTargetQueue = newQueue;
    }
    
    public void OnPointerClick(PointerEventData eventData)
    {
        if (m_tagTargetQueue.Count == 0) return;
        if (eventData.button == PointerEventData.InputButton.Left || eventData.button == PointerEventData.InputButton.Right)
        {
            Transform focussedObject = m_tagTargetQueue.Dequeue();
            m_tagTargetQueue.Enqueue(focussedObject);

            //if open -> switch to this target

            GameManager.Me.CameraTransition(
                focussedObject.transform.position,
                MATagManager.Me.CameraTransitionTime,
                MATagManager.Me.CameraDistanceFromFocussedObject,
                OnCameraTransitionComplete);

            if (m_rightClickInfoPanelUI.IsNullOrWhiteSpace() == false)
            {
                if (eventData.button == PointerEventData.InputButton.Right)
                {
                    UIManager.Me.m_centralInfoPanelManager.ToggleInfoPanel(m_rightClickInfoPanelUI, focussedObject);
                }
                else
                {
                    UIManager.Me.m_centralInfoPanelManager.UpdateInfoPanelIfOpen(m_rightClickInfoPanelUI, focussedObject);
                }
            }
        }
    }

    private void OnCameraTransitionComplete()
    {
        //Debug.Log($"{GetType().Name} - {name} - finished camera transition");
    }

    public void OnCloseAnimFinished() //callback from animation event ('close' anim)
    {
        gameObject.SetActive(false);
    }
}
