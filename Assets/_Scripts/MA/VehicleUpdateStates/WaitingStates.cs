using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace VehicleStates
{
    [System.Serializable]
    public class WaitingForCargo : Waiting
    {
        public WaitingForCargo(MAVehicle _vehicle) : base(_vehicle)
        {
            State = VehicleStateFactory.VehicleState.kWaitingForCargo;

            m_maDeliveryCart = _vehicle as MADeliveryCart;

            if(m_maDeliveryCart == null)
            {
                Debug.LogError(
                    $"WaitingForCargo - assigned vehicle is not a MADeliveryCart -> {_vehicle.GetType().Name}");
            }
        }

        private MADeliveryCart m_maDeliveryCart = null;

        public override void OnEnter()
        {
            base.OnEnter();

            var dispatch = m_stateControlledVehicle.Home as BCActionDispatch;
            
            if(dispatch != null && dispatch.Building != null)
            {
                MADeliveryCart cart = m_stateControlledVehicle as MADeliveryCart;
                int required = cart.FreeCargoSpace;
                for(int i = 0; i < required; ++i)
                {   
                    var resource = dispatch.ConsumeStockForCart();
                    if(resource == null)
                        break;

                    cart.AddOrderItemToVanCargo(resource);
                }
            }
        }
        
        //remove update, only for debug force
        public override void OnUpdate()
        {
            base.OnUpdate();

            if(m_maDeliveryCart.IsFull)
            {
                VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kMovingOffMap, m_stateControlledVehicle);
            }
        }
        
        public override void OnExit()
        {
            base.OnExit();
        }
    }

    [System.Serializable]
    public class WaitingOffMap : Waiting
    {
        public WaitingOffMap(MAVehicle _vehicle) : base(_vehicle)
        {
            State = VehicleStateFactory.VehicleState.kWaitingOffMap;
        }

        public override void OnEnter()
        {
            m_stateControlledVehicle.m_waitFor = m_stateControlledVehicle.VehicleMetaData.m_waitingTimeForDeliveriesOffMap;
			m_stateControlledVehicle.m_afterWait = VehicleStateFactory.VehicleState.kInitialSpawn;

            base.OnEnter();
        }

        public override void OnExit()
        {
            base.OnExit();
        }
    }

    [System.Serializable]
    public class Waiting : VehicleBaseState
    {
        public Waiting(MAVehicle _vehicle)
        {
            State = VehicleStateFactory.VehicleState.kWaiting;
            m_stateControlledVehicle = _vehicle;
        }

        //protected float m_desiredWaitingTime = 0f; // 0 waits forever or until state is changed
        protected VehicleStateFactory.VehicleState m_postWaitingState = VehicleStateFactory.VehicleState.kNone;

        protected Coroutine m_waitingCoroutine = null;
        protected float m_dragBackup = 0f;
        protected float m_startTime = 0f;
        protected float m_waitingTime = 0f;

        public override float WaitingTimeLeft => m_startTime > 0f ? m_startTime + m_waitingTime - Time.time : 0f;

        public override void OnEnter()
        {
            base.OnEnter();

            m_waitingTime = m_stateControlledVehicle.m_waitFor;
                //> 0f
            //     ? m_stateControlledVehicle.m_waitFor
            //     : m_desiredWaitingTime;
            m_postWaitingState = m_stateControlledVehicle.m_afterWait;

            m_dragBackup = m_stateControlledVehicle.FrontAxleRigidBody.linearDamping;
            m_stateControlledVehicle.FrontAxleRigidBody.linearDamping = 1f;
            m_stateControlledVehicle.StopTravelSound();
            m_stateControlledVehicle.SetState(NGMovingObject.STATE.IDLE);
            m_stateControlledVehicle.m_nav.Pause(true, true);

            if(m_stateControlledVehicle.VehicleData.m_timeWaited > 0)
            {
                m_waitingTime = Mathf.Clamp(m_waitingTime - m_stateControlledVehicle.VehicleData.m_timeWaited, 0f, m_waitingTime);
            }

            Wait(m_waitingTime);
        }

        public override void OnUpdate()
        {
            base.OnUpdate();
            
            if(m_startTime > 0f && Time.time > m_startTime + m_waitingTime)
            {
                if(m_postWaitingState != VehicleStateFactory.VehicleState.kNone)
                {
                    VehicleStateFactory.ApplyState(m_postWaitingState, m_stateControlledVehicle);
                }
                else
                {
                    Debug.LogError("Waiting - OnUpdate - Post waiting state is not set");
                }
            }
        }

        public override void OnExit()
        {
            base.OnExit();

            m_startTime = 0f;
            m_waitingTime = 0f;
            m_stateControlledVehicle.FrontAxleRigidBody.linearDamping = m_dragBackup;
            m_stateControlledVehicle.VehicleData.m_timeWaited = 0;
            m_stateControlledVehicle.m_waitFor = 0f;
            m_stateControlledVehicle.m_afterWait = VehicleStateFactory.VehicleState.kNone;
            if(m_waitingCoroutine != null)
            {
                m_stateControlledVehicle.StopCoroutine(m_waitingCoroutine);
                m_waitingCoroutine = null;
            }
        }

        private void Wait(float _durationSecs)
        {
            m_startTime = _durationSecs > 0 ? Time.time : 0f;
        }
    }
}