using UnityEngine;

namespace VehicleStates
{
    [System.Serializable]
    public class InitialSpawnState : VehicleBaseState
    {
        public InitialSpawnState(MAVehicle _vehicle)
        {
            State = VehicleStateFactory.VehicleState.kInitialSpawn;
            m_stateControlledVehicle = _vehicle;
        }

        public override void OnEnter()
        {
            m_stateControlledVehicle.gameObject.SetActive(true);

            Transform tr = m_stateControlledVehicle.transform;
            
            var searchFromPos = m_stateControlledVehicle.Home ? m_stateControlledVehicle.Home.transform.position : tr.position;
            Vector3 pos = NGManager.Me.GetRoadStart(searchFromPos).transform.position;
            pos = pos.SetYToHeight();
            tr.position = pos + Vector3.up;

            m_stateControlledVehicle.Init();
        }

        public override void OnUpdate()
        {
            VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kMovingToHome, m_stateControlledVehicle);
        }

        public override void OnExit()
        {
            base.OnExit();
        }
    }
}