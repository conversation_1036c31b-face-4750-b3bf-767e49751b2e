using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(menuName = "Scriptable Objects/Animation Set")]
public class MAAnimationSet : ScriptableObject
{
	[SerializeField]
	private List<AnimationParams> m_animations = new List<AnimationParams>();

	public (AnimationParams animParams, string clipName) Play(string _animationEntryName, MonoBehaviour _toAnimate, Animator _alternateAnimator = null, Action<AnimationParams, bool> _onFinished = null, float _customSpeedFactor = 1f)
	{
		//Debug.Log($"MAAnimationSet - Play '{_animationEntryName}'");
		int iAnim = GetAnimIndex(_animationEntryName);
		if(iAnim == -1)
		{
			Debug.LogWarning($"MAAnimationSet - Cannot Play Animation Entry '{_animationEntryName}', not found in Scriptable object '{name}'");
			return (null, "");
		}
		
		AnimationParams entry = m_animations[iAnim];
		void OnFinished(bool x) { if (_onFinished != null) _onFinished(entry, x); };
		string clipName = entry.Play(_toAnimate, _alternateAnimator, OnFinished, _customSpeedFactor);
		return (entry, clipName);
	}

	public int GetAnimIndex(string _animationEntryName)
	{
		return m_animations.FindIndex(x => x.name.ToLower() == _animationEntryName.Trim().ToLower());
	}
	
	[Serializable]
	public class AnimationParams
	{
		public string name;
		public List<string> clipNames;
		public float speed = 1f;
		public float delay = 0f;
		public bool useRootMotion = false;
		
        public string Play(MonoBehaviour _toAnimate, Animator _alternateAnimator = null, Action<bool> _onComplete = null, float _customSpeedFactor = 1f)
        {
	        if(delay > 0)
	        {
		        _toAnimate.StartCoroutine(DelayedPlay(_toAnimate.gameObject, _alternateAnimator, _onComplete, _customSpeedFactor));
	        }
	        else
	        {
		        return Play_Internal(_toAnimate == null ? null : _toAnimate.gameObject, _alternateAnimator, _onComplete, _customSpeedFactor);
	        }

	        return "";
        }

        private string Play_Internal(GameObject _toAnimate, Animator _alternateAnimator = null, Action<bool> _onComplete = null, float _customSpeedFactor = 1f)
        {
	        string clip = "";
	        if(clipNames.Count > 0)
	        {
		        clip = clipNames[UnityEngine.Random.Range(0, clipNames.Count)];
	        }
	        
	        AnimationOverride animOverride =
		        _toAnimate != null ? AnimationOverride.PlayClip(_toAnimate, clip, _onComplete, useRootMotion) : null;
	        
	        float speedFactor = speed * _customSpeedFactor;
	        if(animOverride != null)
	        {
		        if(Mathf.Approximately(speedFactor, 1f) == false) animOverride.SetSpeed(speedFactor); //why approx 1?
	        }
	        else if(_alternateAnimator != null)
	        {
		        _alternateAnimator.speed = speedFactor;
		        _alternateAnimator.Play(clip);
	        }

	        return clip;
        }

        private IEnumerator DelayedPlay(GameObject _toAnimate, Animator _alternateAnimator = null, Action<bool> _onComplete = null, float _customSpeedFactor = 1f)
        {
	        yield return new WaitForSeconds(delay);
	        if(_toAnimate == null) yield break;
	        Play_Internal(_toAnimate, _alternateAnimator, _onComplete, _customSpeedFactor);
        }
	}
}
