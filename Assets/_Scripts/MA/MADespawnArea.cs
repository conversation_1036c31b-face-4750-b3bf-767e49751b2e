using System;
using System.Collections.Generic;
using UnityEngine;

public class MADespawnArea : MonoBehaviour
{
    [SerializeField]
    private bool m_freezePausedCharacters = false;
    
    private SphereCollider m_deSpawnArea = null;
    private MACharacterBase m_characterInArea = null;
    private HashSet<MACharacterBase> m_pausedCharacters = new();

    private void Awake()
    {
        if (MASpawnArea.DestroyIfInvalid(gameObject) == false)
        {
            m_deSpawnArea = gameObject.GetComponent<SphereCollider>();
        }
    }

    private void Start()
    {
        MASpawnArea.DestroyIfInvalid(gameObject);
    }
    
    protected virtual void OnTriggerEnter(Collider other)
    {
        MACharacterBase newCharacter = other.gameObject.GetComponentInParent<MACharacterBase>();

        if(newCharacter == null ||
           (newCharacter.CharacterUpdateState.State != CharacterStates.GoingHome &&
           newCharacter.CharacterUpdateState.State is not CharacterStates.Despawn or CharacterStates.Dying or CharacterStates.Dead) || 
           (newCharacter.m_nav.OriginalTargetPosition - transform.position).xzMagnitude() > NavAgent.c_distanceToFinalPositionThreshold)
            return;
        
        if(m_characterInArea == null)
        {
            m_characterInArea = newCharacter;
            newCharacter.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(m_characterInArea);
        }
        else
        {
            newCharacter.m_nav.PushPause("MADespawnArea", false, m_freezePausedCharacters);
            if (m_freezePausedCharacters)
            {
                newCharacter.m_nav.ZeroVelocity();
            }
            m_pausedCharacters.Add(newCharacter);
        }
    }
    
    protected virtual void OnTriggerStay(Collider other)
    {       
        MACharacterBase newCharacter = other.gameObject.GetComponentInParent<MACharacterBase>();

        if(newCharacter == null ||
           (newCharacter.CharacterUpdateState.State != CharacterStates.GoingHome &&
            newCharacter.CharacterUpdateState.State is not CharacterStates.Despawn or CharacterStates.Dying or CharacterStates.Dead) || 
           (newCharacter.m_nav.OriginalTargetPosition - transform.position).xzMagnitude() > NavAgent.c_distanceToFinalPositionThreshold)
            return;
        
        if(m_characterInArea == null)
        {
            m_characterInArea = newCharacter;
            m_characterInArea.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(m_characterInArea);
        }
        else if(m_characterInArea != newCharacter)
        {
            newCharacter.m_nav.PushPause("MADespawnArea", false, m_freezePausedCharacters);
            m_pausedCharacters.Add(newCharacter);
        }
        else
        {
            m_characterInArea.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(m_characterInArea);
        }
    }
    
    protected virtual void OnTriggerExit(Collider other)
    {
        MACharacterBase newCharacter = other.gameObject.GetComponentInParent<MACharacterBase>();

        if(newCharacter == null)
            return;

        if(m_characterInArea == newCharacter)
        {
            m_pausedCharacters.Remove(m_characterInArea);
            m_characterInArea.m_nav.PopPause("MADespawnArea");
            m_characterInArea = null;
        }
        else
        {
            newCharacter.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(newCharacter);
        }
    }
}
