using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAToggleGUI : MonoBehaviour
{
    public TMP_Text m_label;
    private Toggle m_toggle;
    private Action<MAToggleGUI> m_clickedAction;
    public bool IsOn {
        get => m_toggle.isOn;
        set => m_toggle.isOn = value;
    }
    public void Activate(string _label, Action<MAToggleGUI> _clickedAction, bool _isOn)
    {
        m_toggle = GetComponentInChildren<Toggle>();
        m_label.text = _label;
        m_clickedAction = _clickedAction;
        IsOn=_isOn;
    }
    
    public void ClickedMe()
    {
        m_clickedAction(this);
    }

    public static MAToggleGUI Create(Transform _holder, string _label, Action<MAToggleGUI> _clickedAction, bool _isOn)
    {
        var prefab = Resources.Load<MAToggleGUI>("_Prefabs/Dialogs/MAToggleGUI");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_label, _clickedAction, _isOn);
        return instance;
    }
}
