using System;
using System.Collections;
using System.Collections.Generic;
using Hairibar.Ragdoll;
using Unity.Mathematics;
using UnityEngine;
using Quaternion = UnityEngine.Quaternion;
using Random = UnityEngine.Random;
using Vector2 = UnityEngine.Vector2;
using Vector3 = UnityEngine.Vector3;
using System.Linq;
using MACharacterStates;


#if UNITY_EDITOR
using UnityEditor.SceneManagement;
using UnityEditor;
#endif

public abstract class MACharacterBase : NGMovingObject, IOnClick, IHealthBar
{
	[Header("MACharacterBase")]
	[SerializeField] protected MACharacterSettings m_settings;	
	public MACharacterSettings CharacterSettings => m_settings;

	public abstract Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary(); //consider allowing dynamic state sets for any character for pure data driven-ness
	public List<string> GetStateNames() => StateLibrary().Keys.ToList();
	public float m_power = 2500f;

	[ReadOnlyInspector][SerializeField] protected MACreatureInfo m_creatureInfo = null;
	
	[SerializeField] protected Rigidbody m_rigidBody = null;
	[SerializeField] protected List<BodyTypeInfo> m_bodyTypes = new List<BodyTypeInfo>();
	[SerializeField] private bool m_editorShowNextWayPointDummy = false;
	
	[ReadOnlyInspector][SerializeField] protected Transform m_contentRoot = null;
	private CharacterBaseState m_updateState = null;
	[ReadOnlyInspector][SerializeField] private string m_previousCharacterUpdateState = "";
	private CharacterBaseState m_scheduledState = null;
	
	override public KeyboardShortcutManager.EShortcutType KeyboardShortcut => m_updateState != null ? m_updateState.KeyboardShortcut : KeyboardShortcutManager.EShortcutType.HeldObjects;

	[SerializeField]
	protected TargetObject m_targetObject = null;
	protected HealthBar m_healthBar = null;
	protected float m_overrideHealthRecoveryRate = 0f;

	public RagdollController m_ragdollController = null;

	
	public const string c_combatTarget_Armour = "Armour";
	public const string c_combatTarget_Club = "Club";
	public const string c_combatTarget_Fence = "Fence";
	public const string c_combatTarget_Flesh = "Flesh";
	public const string c_combatTarget_Foliage = "Foliage";
	public const string c_combatTarget_Shield = "Shield";
	public const string c_combatTarget_Sword = "Sword";
	public const string c_combatTarget_Tree = "Tree";
	public const string c_combatTarget_Wall = "Wall";

	public const string c_weaponType_Axe = "AXE";
	public const string c_weaponType_Club = "CLUB";
	public const string c_weaponType_Fist = "FIST";
	public const string c_weaponType_Mace = "MACE";
	public const string c_weaponType_Sword = "SWORD";
	public const string c_weaponType_Food = "FOOD";
	
	#region CREATURE_IGNORE_STATE_PRESETS
	private List<STATE> m_ignorePetrifiedStatesPreset = new()
	{
		//STATE.MA_MOVE_TO_INSIDE_BUILDING,
		STATE.MA_DEAD,
		STATE.WORKING,
		STATE.RESTING,
		STATE.HELD_BY_PLAYER,
		STATE.THROWN_BY_PLAYER,
		STATE.DROPPED_BY_PLAYER,
		STATE.MA_MOVE_TO_INSIDE_BUILDING,
		STATE.MA_MOVE_TO_OUTSIDE_BUILDING,
		STATE.MOVE_WITHIN_COMMANDER,
		STATE.IN_COMMANDER_AVAILABLE,
		STATE.IN_COMMANDER_UNAVAILABLE,
		STATE.DESTROY_ME
	};
    
	private List<STATE> m_ignoreTargetStatesPreset = new()
	{
		STATE.MA_DEAD,
		STATE.WORKING,
		STATE.RESTING,
		STATE.HELD_BY_PLAYER,
		STATE.THROWN_BY_PLAYER,
		STATE.DROPPED_BY_PLAYER,
		STATE.MA_MOVE_TO_INSIDE_BUILDING,
		STATE.MA_MOVE_TO_OUTSIDE_BUILDING,
		STATE.MOVE_WITHIN_COMMANDER,
		STATE.IN_COMMANDER_AVAILABLE,
		STATE.IN_COMMANDER_UNAVAILABLE,
		STATE.DESTROY_ME
	};
	#endregion
    
	public HashSet<STATE> m_ignorePetrifiedInStates = new();
	public HashSet<STATE> m_ignoreCreatureTargetStates = new();
	
	private HashSet<Collider> m_wallsInProximity = new();

	public HashSet<Collider> WallsInProximity
	{
		get
		{
			m_wallsInProximity.RemoveWhere(x => x == null);
			return m_wallsInProximity;
		}
		set => m_wallsInProximity = value;
	}
	
	public TargetObject GetSelfAsTarget() => GetComponent<TargetObject>();
	virtual protected string m_defaultWeaponStrikeType => c_weaponType_Fist;
	private string m_weaponStrikeType, m_weaponStrikeAudio;
	virtual protected string m_defaultArmourHitType => c_combatTarget_Flesh;
	private string m_armourHitType;
	
	virtual protected string m_defaultWeaponDesign => null;
	
	public virtual bool IsAllowedToGuardOutsideDistrict => true;
	
	protected bool StaggerFromEveryHit => m_settings.m_staggerFromEveryHit;
	
	public float NormalizedHealth => Health / MaxHealth;
	public float NormalizedArmour => Armour == null ? 0 : Armour.ArmourPoints / Armour.MaxArmourPoints;
	public float MaxArmour => Armour == null ? 0 : Armour.MaxArmourPoints;
	public float DefenseValue => Armour?.DefenseValue ?? 0;
	public bool IsPossessed => GameManager.Me.PossessedCharacter == this;

	public override string Name 
	{ 
		get => (CharacterGameState == null || CharacterGameState.m_name.IsNullOrWhiteSpace()) ? GetDefaultDisplayName() : CharacterGameState.m_name;
		set { if(CharacterGameState != null) CharacterGameState.m_name = value; base.Name = value; } 
	}
	
	public float MaxHealth
	{
		get
		{
		// Level 1 is the default level, shouldn't offer any boost.
			float multiplier = 1 + (Level-1)*HealthBoostPerLevel;
			return m_creatureInfo.m_health * multiplier;
		}
	}
	
	// RW-20-MAR-25: Creatures don't collect experience or level up. This is overridden for 
	// the hero. I imagine that at some point, we might well want to 
	public virtual float Experience => 0;
	public virtual int Level => m_creatureInfo.m_initialLevel;
	public virtual float HealthBoostPerLevel => 0f;
	public virtual float DamageBoostPerLevel => 0f;
	public virtual bool HasBlockAbility => false;
	protected virtual bool GetsDisabledBasedOnProximity => false;
	
	public float ReincarnationHealth => MaxHealth * 0.2f;
	
	public override MAMovingInfoBase GetMovingInfo() { return m_creatureInfo; }

	[ReadOnlyInspector]
	public Vector3 m_attackJumpTarget;
	
	public List<CharacterStateAudioEvent> m_audioStateEvents = new List<CharacterStateAudioEvent>();

	protected HashSet<CapsuleCollider> m_isTouchingCapsule = new();
	protected HashSet<string> m_isTouchingMeshColliders = new();
	
	// public float m_lastHitTime = 0f;
	public float m_neckHeight = 2.0f;
	public float m_headHeight = 2.0f;
	
	private Coroutine m_isForcingThrough = null;
	private float m_gravityFactor = 0f;
	
	private long timestampToNextCartHit = 0;

	[NonSerialized]
	public string m_scheduledAnimSetKey = "";
	
	public Action m_onCollisionWithTarget = null;
	public Action m_onRangeAttackFired = null;
	public Action m_onShowProjectile = null;
	/// <param name="old, new"></param>
	public Action<MACharacterBase, string, string> m_onPreStateChange = null;
	public Action m_comboStateUpdate = null;
	public Func<bool> IsTurnFinished = null;

	private GameObject m_objWayPointDummy = null;
	
	/***** protected/private properties ******/
	protected bool IsForcingThroughObstacle => m_isForcingThrough != null;
	
	/***** public properties ******/
	public GameState_Character CharacterGameState => m_gameState;
	public override GameState_MovingObject GameState => m_gameState;
	[SerializeReference]
	protected GameState_Character m_gameState = null;
	public virtual bool IsHuman => true; //knack?
	public virtual bool IgnoreCharacterSize => false;

	public MAAttackComboState m_meleeComboState;
	public MAAttackComboState m_runeComboState = null;
	public MAAttackComboState m_rangedComboState = null;
	public MAAttackComboState m_parryComboState = null;
	public MAAttackComboState CurrentComboState = null;

	public MAAttackInstance CurrentAttack => CurrentComboState.GetCurrentAttack();
	
	private const float characterBaseRadius = 0.65f;

	public bool IsHarvestingCreataure
	{
		get { return CreatureInfo.m_behaviour == "Harvest"; }
	}
	public GameObject m_harvestHotspot = null;
	
	private Renderer m_mainRenderer;
	public Renderer MainRenderer => m_mainRenderer;

	private SkinnedMeshRenderer m_skinnedMeshRenderer;
	public SkinnedMeshRenderer SkinnedMeshRenderer() => m_skinnedMeshRenderer;

	List<MARangeAttackProjectile> m_projectilesStuckInMe = new List<MARangeAttackProjectile>();

	public virtual string GetDefaultDisplayName()
	{
		if(m_creatureInfo == null) return "";
		return m_creatureInfo.m_displayName;
	}
	
	public override string GetTypeInfo()
	{
		return m_creatureInfo.m_name;
	}

	public override string GetStateInfo()
	{
		if(CharacterUpdateState != null)
			return $"State[{CharacterUpdateState.State}] Target[{(m_targetObject==null ? "" : m_targetObject.name)}]. MoveState[{m_state.ToString()}]";
		return base.GetStateInfo();
	}
	
	public bool HasCombo()
	{
		return CurrentComboState != null;
	}
	public bool QueueNextAttack(bool _useBranch)
	{
		if (CurrentAttack == null)
		{
			for (int i=0; i<m_activeWeaponTrails.Count; i++)
			{
				m_activeWeaponTrails[i].ClearAllPoints();
			}
			m_activeWeaponTrails.Clear();
		}

		return CurrentComboState?.QueueNextAttack(_useBranch) ?? false;
	}

	public MAAttackInstance GetNextAttackAvailableInCombo()
	{
		return CurrentComboState?.GetNextAttack();
	}

	public bool IsComboInCooldown()
	{
		return CurrentComboState?.IsInCooldown() ?? false;
	}

	public bool IsComboReady()
	{
		return CurrentComboState?.IsReady() ?? false;
	}

	// RW-17-APR-25: Only disable the characters in certain states to ensure nothing weird happens.
	// There's a bit of a risk here if some members of a group are disabled and some aren't, that they
	// could become distant from each other, but for now, let's assume it won't be too long until characters are
	// in one of the below states. Otherwise we could just disable the entire group at once when they're all in a suitable state.
	public bool CanBeDisabledBasedOnProximity()
	{
		string state = CharacterUpdateState.State;
		return GetsDisabledBasedOnProximity && (state == CharacterStates.GuardLocation || state == CharacterStates.RoamForTarget);
	}

	WeaponTrail m_weaponTrail = null;

	// RW-22-JAN-25: Keeping track of these so if the character gets interrupted and the anim events 
	// which stop the trails don't get executed, we can stop them manually. In the future, we'd like to 
	// do this by having the option to execute remaining anim events when an anim is interrupted.
	List<WeaponTrail> m_activeWeaponTrails = new List<WeaponTrail>();
	List<GameObject> m_activeWeaponGlows = new List<GameObject>();
	public GameObject m_weaponTrailPrefab;
	public GameObject m_weaponGlowPrefab;
	public GameObject m_weaponGlowRunePrefab;

	public GameObject m_blockFXPrefab;

	public GameObject m_healingVFXPrefab;
	ParticleSystem m_healingVFXInstance;

	private bool isBlocking = false;
	public bool IsBlocking => isBlocking;
	private float blockElapsedTime = float.MaxValue;
	[SerializeField]
	private float parryDuration = 0.25f;
	[SerializeField]
	private float blockAngle = 90f;
	[SerializeField]
	private float blockFactor = 0f;
	[SerializeField]
	public float blockManaPerSecond = 15f;
	[SerializeField]
	private float blockHitMana = 5f;

	public virtual bool IsInAttackState
	{
		get
		{
			CharacterBaseState updateState = CharacterUpdateState;
			return updateState != null && (updateState.State == CharacterStates.ChaseTarget ||
			                               updateState.State == CharacterStates.AttackFar ||
			                               updateState.State == CharacterStates.AttackClose ||
			                               updateState.State == CharacterStates.AttackCooldown);
		}
	}

	public virtual bool IsFollowing => m_updateState.State == CharacterStates.Follow;
	public virtual bool IsNotBusy => (IsOnPatrol || IsRoamingFreely || IsFollowing) && m_targetObject == null && NGManager.Me.IsUnderAttack() == false;

	public virtual bool IsInMovementState
	{
		get
		{
			if (m_state == STATE.MA_MOVE_TO_POSITION || m_state == STATE.MA_MOVE_TO_BUILDING ||
			    m_state == STATE.MA_MOVE_TO_OBJECT ||
			    m_state == STATE.MA_MOVE_TO_INSIDE_BUILDING ||
			    m_state == STATE.MA_MOVE_TO_OUTSIDE_BUILDING) return true;
			CharacterBaseState updateState = CharacterUpdateState;
			return updateState != null && (updateState.State == CharacterStates.ChaseTarget ||
			                               updateState.State == CharacterStates.LookForTarget ||
			                               updateState.State == CharacterStates.RoamForTarget ||
			                               updateState.State == CharacterStates.GoingHome ||
			                               updateState.State == CharacterStates.PatrolToWaypoint ||
			                               updateState.State == CharacterStates.PatrolTownWalls ||
			                               updateState.State == CharacterStates.ReturnToPatrol ||
			                               updateState.State == CharacterStates.GuardLocation ||
			                               updateState.State == CharacterStates.ReturnToGuardLocation ||
			                               updateState.State == CharacterStates.GoToWaypointIgnoringTargets ||
			                               updateState.State == CharacterStates.ClimbBuilding);
		}
	}

	private MACharacterGroupBehaviour groupBehaviour = null;
	public MACharacterGroupBehaviour GroupBehaviour
	{
		get { return groupBehaviour; }
		set { groupBehaviour = value; }
	}
	public Action<MACharacterBase> CharacterDead;
	public Action<MACharacterBase> CharacterDespawned;
	public Action<MACharacterBase> CharacterDestroyed;
	public Action<MACharacterBase, MACharacterBase> CharacterGotHit;

	public override void DeallocateJob() 
	{
		if(GameState.m_job) GameState.m_job.Deallocate(this);
		base.DeallocateJob();
	}
	
	public override void DeallocateHome() 
	{
		if(GameState.m_home) GameState.m_home.Deallocate(this);
		base.DeallocateHome();
	}

	public virtual bool IsActivelyHealing => false;
	public virtual bool IsFleeing => (CharacterUpdateState?.State ?? "") == CharacterStates.Flee || (ScheduledState?.State ?? "") == CharacterStates.Flee ;
	public virtual bool IsResting =>  m_state == STATE.MA_HANGOUT || m_state == STATE.RESTING || CharacterUpdateState.State == CharacterStates.Interaction;
	public virtual bool IsInCombat
	{
		get
		{
			if (IsInAttackState) return true;
			CharacterBaseState updateState = CharacterUpdateState;
			if (updateState != null)
			{
				if (updateState.State == CharacterStates.Possessed) return true;
				if (updateState.State == CharacterStates.KnockedDown && 
				    m_targetObject != null && m_targetObject.TargetObjectType != TargetObject.TargetType.InteractionPointType) return true;
			}
			if (m_state == STATE.MA_PETRIFIED) return true;
			return false;
		}
	}

	public virtual bool IsWorking => m_state == STATE.WORKING;
	
	public virtual Consciousness IsUnconscious()
	{
		return (Consciousness)m_gameState.m_consciousness;
	}

	public virtual bool IsIncapacitated
	{
		get
		{
			CharacterBaseState updateState = CharacterUpdateState;
			return (m_gameState.m_consciousness > 0 ||
			       updateState.State == CharacterStates.UnconsciousDead ||
			       updateState.State == CharacterStates.Unconscious) ||
			       updateState.State == CharacterStates.Dead;
		}
	}

	public virtual bool IsBeingTargeted
	{
		get
		{
			TargetObject selfAsTarget = GetComponent<TargetObject>();
			if (selfAsTarget == null) return false;
			return selfAsTarget.IsBeingTargeted;
		}
	}

		public virtual bool IsBeingTargetedByEnemies
	{
		get
		{
			TargetObject selfAsTarget = GetComponent<TargetObject>();
			if (selfAsTarget == null) return false;
			return selfAsTarget.IsBeingTargetedByEnemies;
		}
	}
	
	public virtual bool ShowHealthBar => IsInCombat || IsClosestAttackerCloserThan(CharacterSettings.m_godModeHealthBarVisionRadius);
    
	private HealthBar externalHealtBar = null;
	public HealthBar HealthBar
	{
		get
		{
			if (m_healthBar == null)
				m_healthBar = GetComponentInChildren<HealthBar>(true);
			
			return (externalHealtBar != null) ? externalHealtBar : m_healthBar;
		}
	}

	private const float HEALTHBAR_HEIGHT_SCALE = 1.4f;
	public virtual float HealthBarHeight => m_neckHeight * HEALTHBAR_HEIGHT_SCALE;

	public virtual string InitialState
	{
		get => string.IsNullOrWhiteSpace(m_gameState?.m_initialState) ? CharacterStates.Spawn : m_gameState.m_initialState;
		set => m_gameState.m_initialState = value;
	}
	
	public virtual string DefaultState
	{
		get => string.IsNullOrWhiteSpace(m_gameState?.m_defaultState) ? CharacterStates.RoamForTarget : m_gameState.m_defaultState;
		set => m_gameState.m_defaultState = value;
	}
	
	public CharacterBaseState CharacterUpdateState => m_updateState ?? m_scheduledState;
	public CharacterBaseState ScheduledState => m_scheduledState;
	public CharacterBaseState ActualUpdateState => m_updateState;

	public bool IsOrWillBeInState(string _state) => string.IsNullOrEmpty(_state) == false && (m_updateState?.State ?? m_scheduledState?.State) == _state;

	public void SetCharacterUpdateState(CharacterBaseState _toState)
	{
		if (GameManager.Me.LoadComplete == false) return;
		m_previousCharacterUpdateState = m_updateState?.State;
		m_updateState = _toState;
		CharacterGameState.m_savedUpdateState = _toState.State;
		m_scheduledState = null;
		if(m_previousCharacterUpdateState != m_updateState.State)
		{
			m_onPreStateChange?.Invoke(this, m_previousCharacterUpdateState, m_updateState.State);
			m_updateState.SetupCharacterState();			
		}
		name = GetGameObjectName();
		m_updateState.OnEnter();
	}

	public void SetScheduledState(CharacterBaseState _scheduleState)
	{
		m_scheduledState = _scheduleState;
		m_scheduledState?.OnPreEnter();
		if(m_updateState == null)
		{
			SetCharacterUpdateState(_scheduleState);
		}
	}
	
	public MACreatureInfo CreatureInfo => m_creatureInfo;
	public override Rigidbody RigidBody => m_rigidBody;
	public TargetObject TargetObject => m_targetObject;

	public bool IsAlive => Health > 0f;

	public float AttackRange => CurrentComboState is var cs ? (cs.GetCurrentAttack() ?? cs.GetNextAttack()).AttackRadius : 0f;

	//public float AttackRadiusClose => Range // m_creatureInfo.m_attackRadiusClose;
	public float VisionRadiusSq => m_creatureInfo.m_visionRadius * m_creatureInfo.m_visionRadius;
	public float ProximityRadiusSq => m_creatureInfo.m_proximityRadius * m_creatureInfo.m_proximityRadius;
	//public float PatrolAttackRadius => CharacterSettings.m_patrolAttackRadius;
	//public float WeaponRangeClose => Weapon != null ? Weapon.Length : 0f;

	public float AliveDuration
	{
		get => CharacterGameState.m_aliveDuration;
		set => CharacterGameState.m_aliveDuration = value;
	}
	// public float MinimumAttackDistance
	// {
	// 	get
	// 	{
	// 		float attackRadiusClose = AttackRadiusClose;
	// 		return attackRadiusClose * attackRadiusClose;
	// 	}
	// }
	
	public virtual bool IsTimeToHangout => false;

	public virtual bool IsTimeToGoHome
	{
		get
		{
			if(MACreatureControl.Me.IsValidTimeOfDay(this) == false)
				return true;

			if (NormalizedHealth < CharacterSettings.m_healAtHomeHealthThreshold && IsNotBusy) //need to check targetobject == null?
			{
				if (IsReadyToHealAtHome())
				{
					return true;
				}
			}

			float despawnTime = m_creatureInfo.m_despawnTime;
			if(despawnTime <= 0) return false;
			return CharacterGameState.m_aliveDuration >= despawnTime;
		}
	}

	protected bool IsReadyToHealAtHome()
	{
		float _travelTimeLimit = 45f;
		if (Home && Home.Building)
		{
			if (Home.Building.HasSpecialHandling(this, SpecialHandlingAction.GuildHealing) !=
			    SpecialHandlingAction.GuildHealing)
				return false;

			float newDist = (transform.position - Home.Building.transform.position).xzMagnitude();
			float travelTime = _travelTimeLimit <= 0f ? 0f : newDist / GetDesiredSpeed();
			if (travelTime <= _travelTimeLimit)
			{
				return true;
			}
		}
		return false;
	}

	private Vector3 m_lastDistanceTravelledPosition = Vector3.zero;
	private float m_lastDistanceTravelledTime = 0f;
	private const float DISTANCE_TRAVELLED_INTERVAL = 5.0f;

	protected void UpdateDistanceTravelled()
	{
		if (m_lastDistanceTravelledTime == 0f)
		{
			m_lastDistanceTravelledTime = Time.time;
			m_lastDistanceTravelledPosition = transform.position;
			return;
		}

		if (Time.time - m_lastDistanceTravelledTime >= DISTANCE_TRAVELLED_INTERVAL)
		{
			m_lastDistanceTravelledTime = Time.time;
			float distance = (transform.position - m_lastDistanceTravelledPosition).magnitude;
			m_gameState.m_distanceTravelled += distance;
			m_lastDistanceTravelledPosition = transform.position;
		}
	}

	public void AddItemsCollected(int _amount = 1)
	{
		m_gameState.m_itemsCollected += _amount;
	}

	[SerializeField] private GameObject defaultWeapon = null;
	[SerializeField] private GameObject defaultArmour = null;
	[SerializeField] private GameObject defaultProjectile = null;
	[SerializeField] private string weaponAttachName = "";
		
	// IDamageReceiver
	public override IDamageReceiver IsTargetting => m_targetObject?.DamageReceiver;
	// IDamager
	public override void ClearTarget()
	{
		SetTargetObj(null);
	}

	public float GetTravelTime(Vector3 _dest)
	{
		float newDist = (transform.position - _dest).xzMagnitude();
		return newDist / GetDesiredSpeed();		
	}
	private Transform weaponAttach = null;
	private GameObject weaponObject = null;
	
	public MAWeapon Weapon = null;
	public MAArmour Armour = null;

	private float m_powerAvailable = 0f;
	public float PowerAvailable => m_powerAvailable;
	
	
	public bool IsLowEnergy() => Energy <= 0.001f;
	public bool IsMaxEnergy() => Energy >= MaxEnergy;
	
	public virtual float ProductionPower => 0f;
	public virtual float MaxEnergy => 0f;
	
	public void ConsumePower(float _amount)
	{
		_amount = MathF.Round(_amount, 3);
		m_powerAvailable -= _amount;
		Energy = Mathf.Clamp(Energy - _amount, 0f, MaxEnergy);
	}
	
	public void SetPowerAvailable(float _value)
	{
		m_powerAvailable = _value;
	}
	
	public bool IsClosestAttackerCloserThan(float _distance = 10)
	{
		TargetObject selfAsTarget = GetComponent<TargetObject>();
		if (selfAsTarget == null) return false;
		TargetObject.Attacker attacker = selfAsTarget.GetClosestAttacker();
		return attacker != null && attacker.m_attacker != null && attacker.m_attacker.Transform.position.DistanceSq(selfAsTarget.transform.position) < _distance * _distance;
	}

	private void OnWeaponDesignChanged(string design, GameObject weaponPrefab, float weaponUnscaledLength)
	{
		if (weaponAttach == null) return;
		if (weaponObject != null)
		{
			Destroy(weaponObject);
			weaponObject = null;
		}

		bool useDefault = weaponPrefab == null;
		GameObject prefab = useDefault ? defaultWeapon : weaponPrefab;
		if (prefab == null) return;
		
		var holdPoint = HoldPoint.HoldOffset(prefab);
		
		Vector3 scale = useDefault ? Vector3.one : Vector3.one * GlobalData.Me.m_heldItemScale;
		weaponObject = Instantiate(prefab, weaponAttach);

		GenerateWeaponRuneList(weaponObject, weaponUnscaledLength);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		SetupRuneComboState();
#endif
		
		weaponObject.transform.localPosition = holdPoint * GlobalData.Me.m_heldItemScale;
		weaponObject.transform.localRotation = Quaternion.identity;
		weaponObject.transform.localScale = scale;
		if (useDefault)
		{
			Weapon = weaponObject.GetComponent<MAWeapon>();
		}
		else
		{
			Weapon = weaponObject.AddComponent<MAWeapon>();
			Weapon.Length = weaponUnscaledLength * weaponObject.transform.lossyScale.y;
		}

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		Weapon.SetDamageMultiplier(NGDesignInterface.Get(design).TotalAttack);
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)

		// RW-12-FEB-25: Find where we should put the action cam's tracking object and the 
		// weapon trail component.
		var actionCamObj = new GameObject("ActionCamTrackingObject");
		actionCamObj.transform.SetParent(weaponObject.transform);
		actionCamObj.transform.localPosition = new Vector3(0, weaponUnscaledLength, 0);

		if (m_weaponTrailPrefab != null)
		{
			var weaponTrailObj = GameObject.Instantiate(m_weaponTrailPrefab);
			weaponTrailObj.transform.SetParent(weaponObject.transform);
			weaponTrailObj.transform.localPosition = new Vector3(0, weaponUnscaledLength, 0);
			m_weaponTrail = weaponTrailObj.GetComponentInChildren<WeaponTrail>();
		}

		weaponObject.SetActive(true);
		
		foreach (var collider in weaponObject.GetComponentsInChildren<Collider>())
			collider.isTrigger = true;
	}

	protected static string s_overrideRuneVFX = null;
	private static DebugConsole.Command s_overrideRuneVFXCmd = new("debugrune", _s => { s_overrideRuneVFX = _s; });
	
	public class WeaponRune
	{
		public int stickerDataId = -1;
		public string runeName = "";
		public float fraction = 0f;
		public Transform owner = null;
		public Vector3 localPos = Vector3.zero;
	};
	public List<WeaponRune> m_weaponRunes = new List<WeaponRune>();
	public bool WeaponHasPowers() => m_weaponRunes.Count > 0 || string.IsNullOrEmpty(s_overrideRuneVFX) == false;
	
	void GenerateWeaponRuneList(GameObject prefab, float weaponUnscaledLength)
	{
		m_weaponRunes.Clear();
		var stickers = prefab.GetComponentsInChildren<StickerInstance>();
		foreach (var s in stickers)
		{
			foreach (var p in s.m_stickers)
			{
				var weaponRune = new WeaponRune();
				weaponRune.stickerDataId = p.m_id;
				weaponRune.runeName = StickerData.s_entries[p.m_id].m_name;
				weaponRune.fraction = s.transform.TransformPoint(p.m_position).y / weaponUnscaledLength;
				weaponRune.owner = s.transform;
				weaponRune.localPos = p.m_position;
				m_weaponRunes.Add(weaponRune);
			}
		}

		if (!string.IsNullOrEmpty(s_overrideRuneVFX))
		{
			var split = s_overrideRuneVFX.Split(';');
			for (int i=0; i<split.Length; i++)
			{
				int id = StickerData.s_entries.FindIndex(o => o.m_name == split[i]);
				if (id >= 0)
				{
					var weaponRune = new WeaponRune();
					weaponRune.stickerDataId = id;
					weaponRune.runeName = split[i];
					weaponRune.fraction = 0f;
					weaponRune.owner = prefab.transform;
					weaponRune.localPos = Vector3.zero;
					m_weaponRunes.Add(weaponRune);
				}
			}
		}
	}

	protected void SetupRuneComboState()
	{
		bool shouldResetCurrentCombo = (CurrentComboState != null) && (CurrentComboState == m_runeComboState);
		if (m_weaponRunes.Count <= 0)
		{
			m_runeComboState = null;
		}
		else
		{
			m_runeComboState = new MAAttackComboState(this, GetComboFromWeaponRune(m_weaponRunes[0]));
		}

		if (shouldResetCurrentCombo)
		{
			if (m_runeComboState == null)
				CurrentComboState = m_meleeComboState;
			else
				CurrentComboState = m_runeComboState;
		}
	}
	
	private void TriggerRuneVFX(string _powerType, float _powerLevel, float _start, Transform _transform, Vector3 _localPos)
	{
		var dir = Camera.main.transform.forward.GetXZNorm();
		RuneVFX.Create(_powerType, transform, dir, _start, _powerLevel);
		RuneCastVFX(_powerType, _transform, _localPos);
	}
	private void RuneCastVFX(string _powerType, Transform _owner, Vector3 _localPos)
	{
		var prefab = Resources.Load<GameObject>($"Runes/Rune_{_powerType}_CastVFX");
		if (prefab == null) return;
		var inst = Instantiate(prefab, _owner);
		inst.transform.localPosition = _localPos;
	}


	public class BodyArmourTypes
	{
		public enum EBodyParts { None = -1, Head, Chest, Hips, LegLeft, LegRight, ArmLeft, AmrRight, HandLeft, HandRight, FootLeft, FootRight, Belt, Count }
		public string[] m_armourTypes = new string[(int)EBodyParts.Count];

		public BodyArmourTypes(MACharacterBase _chr)
		{
			var defaultType = _chr.m_defaultArmourHitType;
			for (int i = 0; i < (int)EBodyParts.Count; i++)
				m_armourTypes[i] = defaultType;
			foreach (var sh in _chr.GetComponentsInChildren<SnapHinge>())
			{
				var type = EBodyParts.None;
				var hasSide = false;
				switch (sh.HingeCategory)
				{
					case SnapHinge.ECategory.ClothesSourceHead: type = EBodyParts.Head; break;
					case SnapHinge.ECategory.ClothesSourceChest: type = EBodyParts.Chest; break;
					case SnapHinge.ECategory.ClothesSourceHips: type = EBodyParts.Hips; break;
					case SnapHinge.ECategory.ClothesSourceLegs: type = EBodyParts.LegLeft; hasSide = true; break;
					case SnapHinge.ECategory.ClothesSourceArms: type = EBodyParts.ArmLeft; hasSide = true; break;
					case SnapHinge.ECategory.ClothesSourceHands: type = EBodyParts.HandLeft; hasSide = true; break;
					case SnapHinge.ECategory.ClothesSourceFeet: type = EBodyParts.FootLeft; hasSide = true; break;
					case SnapHinge.ECategory.ClothesSourceBelt: type = EBodyParts.Belt; break;
					default:
						break;
				}
				if (type != EBodyParts.None)
				{
					var block = sh.gameObject.GetComponentInParent<Block>(true);
					if (block == null) continue;
					if (hasSide && block.IsFlipped) type = (EBodyParts)((int)type + 1);
					m_armourTypes[(int)type] = c_combatTarget_Armour; // could look up tags from block to be more specific?
				}
			}
			
			var hasShoes = m_armourTypes[(int)EBodyParts.FootLeft] != c_combatTarget_Flesh || m_armourTypes[(int)EBodyParts.FootRight] != c_combatTarget_Flesh;
			/*for (int i = 0; i < (int)EBodyParts.Count; i++)
				Debug.LogError($"Character {_chr.Name} has armour type {m_armourTypes[i]} on body part {(EBodyParts)i}");
			Debug.LogError($"Character {_chr.Name} has shoes: {hasShoes}");*/
		}
	}
	
	public BodyArmourTypes m_bodyArmourTypes = null;

	private void UpdateArmourTypes()
	{
		m_bodyArmourTypes = new BodyArmourTypes(this);
	}

	private void OnArmourDesignChanged(string _armourDesign, float armourPartCount)
	{
		if (m_animHandler == null) return;
		var obj = m_animHandler.AddClothing(_armourDesign, GlobalData.Me.m_heldItemScale, () => UpdateArmourTypes());
		Armour = obj.AddComponent<MAArmour>();
		Armour.MaxArmourPoints = armourPartCount * 10f;
	}

	public bool IsTouchingMeshCollider(string _colliderName) => m_isTouchingMeshColliders.Contains(_colliderName.ToLower());
	
	private static DebugConsole.Command s_setClothesCmd = new ("setclothes", _s =>
	{
		var split = _s.Split(':');
		if (split.Length != 2) return;
		var id = int.Parse(split[0]);
		var character = NGManager.Me.FindCharacterByID(id);
		if (character == null) return;
		character.SetArmourDesign(new GameState_Design(split[1]));
	});
	
	public void SetWeaponDesign(GameState_Design _design)
	{
		CharacterGameState.m_weaponDesign = _design;
		UpdateWeapon();
	}

	public void SetArmourDesign(GameState_Design _design)
	{
		CharacterGameState.m_armourDesign = _design;
		UpdateArmour();
	}
	
	//==
	void SetDefaultWeaponStrikeType()
	{
		m_weaponStrikeType = m_defaultWeaponStrikeType;
		SetWeaponStrikeAudio();
	}

	void SetWeaponStrikeType(string _type)
	{
		m_weaponStrikeType = _type;
		SetWeaponStrikeAudio();
	}
	public string WeaponTargetType()
	{
		switch(m_weaponStrikeType)
		{
			case c_weaponType_Sword:
				return c_combatTarget_Sword;
		}
		return c_combatTarget_Club;
	}
	void SetWeaponStrikeAudio()
	{
		m_weaponStrikeAudio = $"PlaySound_CombatWeaponImpact_{m_weaponStrikeType}";
		//Debug.Log($"Weapon assigned with audio {m_weaponStrikeAudio}");
	}

	void SetDefaultArmourHitType()
	{
		m_armourHitType = m_defaultArmourHitType;
	}

	void SetArmourHitType(string _type)
	{
		m_armourHitType = _type;
	}

	public string GetArmourHitType()
	{
		return m_armourHitType;
	}

	void SetWeaponStrikeTypeFromBlocks(GameObject _weapon)
	{
		var blocks = _weapon.GetComponentsInChildren<Block>();
		var lookup = new Dictionary<string, int>();
		foreach (var block in blocks)
		{
			var info = NGBlockInfo.GetInfo(block.BlockID);
			if (info == null) continue;
			var drawers = MADrawerInfo.GetDrawerNames(info.m_mADrawerInfos);
			foreach (var drawer in drawers)
			{
				string res = null;
				switch (drawer.Item1)
				{
					case "Food":
						res = c_weaponType_Food;
						break;
					case "Weapons":
						switch (drawer.Item2)
						{
							case "Blades":
								res = c_weaponType_Sword;
								break;
							case "Axes":
								res = c_weaponType_Axe;
								break;
							case "Maces":
								res = c_weaponType_Mace;
								break;
						}
						break;
				}
				if (res != null) lookup[res] = lookup.ContainsKey(res) ? lookup[res] + 1 : 1;
			}
		}
		int bestCount = 0;
		string best = null;
		foreach (var kvp in lookup)
		{
			if (kvp.Value > bestCount)
			{
				bestCount = kvp.Value;
				best = kvp.Key;
			}
		}
		if (best != null) SetWeaponStrikeType(best);
		else SetDefaultWeaponStrikeType();
	}

	void PlayStrikeAudio(string _targetType)
	{
		AudioClipManager.Me.SetSoundSwitch("CombatTarget", $"CombatTarget_{_targetType}", gameObject);
		AudioClipManager.Me.PlaySound(m_weaponStrikeAudio, gameObject);
	}
	//==

	protected void UpdateWeapon()
	{
		if (CharacterGameState.m_weaponDesign == null && m_defaultWeaponDesign == null)
		{
			SetDefaultWeaponStrikeType();
			OnWeaponDesignChanged(null, null, 0);
			return;
		}
		var design = CharacterGameState.m_weaponDesign == null ? m_defaultWeaponDesign : CharacterGameState.m_weaponDesign.m_design;
		var weapon = new GameObject("Weapon");
		DesignTableManager.Me.RestoreDesign(DesignTableManager.RestoreType.Product, design, weapon.transform, (_o,componentsChanged) =>
		{
			SetWeaponStrikeTypeFromBlocks(_o);
			var bounds = ManagedBlock.GetTotalVisualBounds(_o);
			var length = bounds.size.y;
			OnWeaponDesignChanged(design, weapon, length);
			Destroy(weapon);
		});
	}

	private void UpdateArmour()
	{
		if (CharacterGameState.m_armourDesign == null)
		{
			SetDefaultArmourHitType();
			OnArmourDesignChanged(null, 0);
			return;
		}
		var design = CharacterGameState.m_armourDesign.m_design;
		int partCount = int.Parse(design.Split('|')[0]);
		OnArmourDesignChanged(design, partCount);
		SetArmourHitType(c_combatTarget_Armour);
	}

	public void SetExternalHealthBar(HealthBar bar)
	{
		if (bar != null)
		{
			externalHealtBar = bar;
			externalHealtBar.SetupInfo(this);
		}
		else
		{
			if (externalHealtBar != null)
				externalHealtBar.SetupInfo(null);
			externalHealtBar = null;
		}
	}
	
	/***** override properties ******/
	public override NGMovingObject Leader
	{
		get => base.Leader;
		set 
		{
#if UNITY_EDITOR
			if (value != null && StateLibrary().ContainsKey(CharacterStates.Follow) == false)
			{
				Debug.Log($"{GetType().Name} Leader ref set but character cannot {CharacterStates.Follow.ToString()}. Character: {name}, Leader: {value.name}");
			}
#endif
			base.Leader = value;
		}
	}
	
	public bool IsLeaderAvailable => Leader != null && Leader.m_insideMABuilding == null && Leader.gameObject.activeSelf;
	
	public override float Health
	{
		get => CharacterGameState?.m_health ?? 0;
		set => CharacterGameState.m_health = CharacterGameState.m_immortal ? MaxHealth : Mathf.Clamp(value, 0, MaxHealth);
	}
	
	public override float Energy { get => m_gameState.m_energy; set => m_gameState.m_energy = value; }
	
	public Vector3? ObjectiveWaypoint
	{
		get
		{
			GameState_Character gameState = CharacterGameState;
			Vector3? v = gameState.m_objectiveWaypoint == Vector3.zero ? null : gameState.m_objectiveWaypoint;
			#if UNITY_EDITOR
			if(m_editorShowNextWayPointDummy && v != null)
			{
				if(m_objWayPointDummy == null) m_objWayPointDummy = MACreatureControl.Me.CreateFakeCreature(m_creatureInfo, CharacterGameState.m_objectiveWaypoint.GroundPosition(), null);
				m_objWayPointDummy.transform.position = v.Value.GroundPosition();
			}
			#endif
			return v;
		}
		set
		{
			CharacterGameState.m_objectiveWaypoint = value ?? Vector3.zero;
			if (LastPatrolPath != null) LastPatrolPath = null;
			#if UNITY_EDITOR
			if(m_editorShowNextWayPointDummy && CharacterGameState.m_objectiveWaypoint != null)
			{
				if(m_objWayPointDummy == null) m_objWayPointDummy = MACreatureControl.Me.CreateFakeCreature(m_creatureInfo, CharacterGameState.m_objectiveWaypoint.GroundPosition(), null);
				m_objWayPointDummy.transform.position = CharacterGameState.m_objectiveWaypoint.GroundPosition();
			}
			else if(m_objWayPointDummy != null) Destroy(m_objWayPointDummy); 
			#endif
		}
	}
	
	
	public List<float3> LastPatrolPath
	{
		get
		{
			GameState_Character gameState = CharacterGameState;
			if ((gameState.m_patrolPath == null || gameState.m_patrolPath.Count == 0))
			{
				return null;
			}
			
			if (ObjectiveWaypoint != null)
			{
				if (((Vector3)gameState.m_patrolPath[^1] - m_gameState.m_objectiveWaypoint).xzSqrMagnitude() < 1f)
				{
					return gameState.m_patrolPath;
				}
				else
				{
					gameState.m_patrolPath = m_nav.CopyTenthOfPath();
					return gameState.m_patrolPath;
				}
			}
			gameState.m_patrolPath = null;
			return gameState.m_patrolPath;
		}
		set
		{
			CharacterGameState.m_patrolPath = (value?.Count ?? 0) > 0 ? value : null;
		}
	}

	public bool IsOnPatrol
	{
		get
		{
			CharacterBaseState updateState = CharacterUpdateState;
			return updateState != null && (updateState.State == CharacterStates.GuardLocation ||//CharacterStates.GoToGuardLocation
			                               updateState.State == CharacterStates.PatrolToWaypoint ||
			                               updateState.State == CharacterStates.PatrolTownWalls ||
			                               updateState.State == CharacterStates.ReturnToPatrol);
		}
	}
	
	public bool IsRoamingFreely
	{
		get
		{
			CharacterBaseState updateState = CharacterUpdateState;
			return updateState != null && updateState.State == CharacterStates.RoamForTarget;
		}
	}
	
	protected override bool CanCancelRagdoll()
	{
		return base.CanCancelRagdoll() && !IsIncapacitated;
	}

	protected bool CanAttackBuildingComponents => m_creatureInfo.m_componentTargets.Length > 0;
	
	protected void SetCharacterSize(float _headScale = 1f, bool _ignoreHeadScale = false)
	{
		Transform tr = transform;
		var info = GetMovingInfo();
		if (info is MAWorkerInfo workerInfo)
		{
			float overallScale = UnityEngine.Random.Range(workerInfo.m_lowOverallScale, workerInfo.m_highOverallScale);
			float heightScale = UnityEngine.Random.Range(workerInfo.m_lowHeightScale, workerInfo.m_highHeightScale);
			float fatScale = UnityEngine.Random.Range(workerInfo.m_lowFatScale, workerInfo.m_highFatScale);
			Vector3 localScale = new Vector3(fatScale, heightScale, fatScale) * overallScale;
			tr.localScale = localScale;
			float baseHeadScale = _headScale * ((workerInfo.m_lowOverallScale + workerInfo.m_highOverallScale) * .5f);
			Transform root = tr.FindChildRecursiveByName(m_contentRootName) ?? transform;
			var head = root?.FindChildRecursiveByName(m_headMainBoneName);
			if (head != null && !_ignoreHeadScale)
				head.localScale = new Vector3(baseHeadScale / head.lossyScale.x, baseHeadScale / head.lossyScale.y, baseHeadScale / head.lossyScale.z);
		}
		else if (info is MACreatureInfo creatureInfo)
		{
			var scale = creatureInfo.m_scale;
			if (scale < .1f) scale = 1f;
			tr.localScale = Vector3.one * scale;
		}
		Transform neckTr = tr.FindChildRecursiveByName(m_neckBoneName);
		if(neckTr != null) m_neckHeight = neckTr.position.y - tr.position.y;
		Transform headTr = tr.FindChildRecursiveByName(m_headBoneName);
		if(headTr != null) m_headHeight = headTr.position.y - tr.position.y;
	}

	/***** Getters ******/
	public const int c_objectiveTargetPriority = 2;
	public const int c_normalTargetPriority = 1;

	public virtual void GetCharacterTargetPriority(TargetResult _bestTarget, MACharacterBase _receiver, int _priority, float _persistence, MAMovingInfoBase _myInfo, Vector3 _myPos, Vector3 _dir, bool _ignoreTargetPresets = false, Vector3 _posNearestOriginalPath = default)
	{
		if(_receiver == null || _receiver == this || _receiver.CanBeTargeted == false || _receiver.m_updateState == null) return;
		if(IsTargetAliveAndValid(_receiver.transform) == false) return;
		MAMovingInfoBase receiverInfo = _receiver.GetMovingInfo();
		if(receiverInfo == null) return;

		var pos = _myPos;
		// Check this is an allowed target
		if (_ignoreTargetPresets == false)
		{
			if (m_creatureInfo.WorkerTargets.Contains(receiverInfo as MAWorkerInfo) == false &&
			         m_creatureInfo.CreatureTargets.Contains(receiverInfo as MACreatureInfo) == false)
			{
				return;
			}
		}
		
		Transform receiverTransform = _receiver.Transform;
		Vector3 receiverPos = receiverTransform.position;

		if(ObjectiveWaypoint != null)
		{
			if((_posNearestOriginalPath - receiverPos).xzSqrMagnitude() > VisionRadiusSq)
				return;
		}

		//var sqDist = (recieverPos - _myPos).xzSqrMagnitude();
		var dot = Vector3.Dot(_dir, receiverPos - pos) / (receiverPos - pos).magnitude;
		var weight = 1f / (0.4f * dot + 1f);
				
		if (receiverInfo.GetMovingInfoTargets().Contains(_myInfo))
			weight *= 0.5f;
		if (TargetObject != null && receiverTransform == TargetObject.transform)
			weight /= _persistence;
		if (_receiver.m_updateState.State == CharacterStates.KnockedDown)
			weight *= 10f;
		if (!_receiver.HasCombo() || ((_receiver.CurrentAttack ?? _receiver.GetNextAttackAvailableInCombo()) == null))
			weight *= 5f;

		var dist = GetDistanceScore(pos, receiverPos, weight * weight);

		dist.Item1 *= _priority;
		if (dist.Item1 <= _bestTarget.m_distanceScore) return;

		float reachabilityScore = 1f;
		var targetState = TargetResult.TargetState.WithinOpenWalls;
		if (MACreatureControl.Me.TryProcessBoundaries())
		{
			foreach (PathManager.Path wall in GameManager.Me.m_state.m_paths)
			{
				if ((wall.Set.m_isPerimeterType && wall.IsCycle /* && wall.ContainsAnyGates*/) == false) continue;
				if (wall.IsInRange(receiverPos, 0) == false) continue;
				if (MACreatureControl.Me.m_enclosures.TryGetValue(wall, out var col) == false) continue;
				if (col.EnclosedArea.OverlapPoint(receiverPos.GetXZVector2()) == false) continue;

				Vector2 selfPos = _myPos.GetXZVector2();
				if (wall.ContainsAnyOpenGates)
				{
					targetState = TargetResult.TargetState.WithinOpenWalls;
					reachabilityScore = 1f;
					break;
				}

				if (MACreatureControl.Me.m_enclosures[wall].EnclosedArea.OverlapPoint(selfPos))
				{
					targetState = TargetResult.TargetState.WithinClosedWalls;
					reachabilityScore = 0.25f;
					break;
				}

				if (m_creatureInfo.m_canSmashWallTypes.Contains(wall.m_roadType))
				{
					targetState = TargetResult.TargetState.WithinSmashableWalls;
					reachabilityScore = 0.75f;
					break;
				}
			}
		}

		dist.Item1 *= reachabilityScore;
		if (dist.Item1 > _bestTarget.m_distanceScore)
		{
			_bestTarget.Set(dist, receiverTransform, TargetObject.TargetType.CharacterType, targetState);
		}
	}
	
	protected virtual void GetBuildingTargetPriority(TargetResult _bestTarget, MABuilding _building, int _priority)
	{
		if(_building == null) return;
		if(IsTargetAliveAndValid(_building.transform) == false) return;

		if(_building.ContainsDamageInteractionArea(this, out MADamageArea damageAreaOut) && damageAreaOut == null) return;
		_building.TargetPriority = _priority;

		float reachabilityScore = 0.25f;
		var targetState = TargetResult.TargetState.WithinOpenWalls;
		foreach (var enclosurePair in MACreatureControl.Me.m_enclosures)
		{
			if (enclosurePair.Value.EnclosedArea.OverlapPoint(_building.DoorPosOuter) == false) continue;
			targetState = TargetResult.TargetState.WithinClosedWalls;
			if (enclosurePair.Key.ContainsAnyOpenGates)
			{
				reachabilityScore = 1f;
				targetState = TargetResult.TargetState.WithinOpenWalls;
				break;
			}

			if (this.CreatureInfo.m_canSmashWallTypes.Contains(enclosurePair.Key.m_roadType))
			{
				reachabilityScore = 0.75f;
				targetState = TargetResult.TargetState.WithinSmashableWalls;
				break;
			}
		}
		
		var attackAreas = _building.GetDamageInteractionAreas(this);
		Vector3 attackPos = (attackAreas == null || attackAreas.Count == 0) ? _building.DoorPosOuter : attackAreas[0].closestPos;
		var dist = GetDistanceScore(transform.position, attackPos);
		dist.Item1 = dist.Item1 * reachabilityScore * _building.TargetPriority;
		
		if(dist.Item1 > _bestTarget.m_distanceScore)
		{
			_bestTarget.Set(dist, _building.transform, TargetObject.TargetType.BuildingType, targetState);
		}
	}

	public virtual void GetExplodeInteractionTargetPriority(TargetResult _bestTarget, ExplodeInteraction _explodeInteraction, int _priority, float _persistence, MAMovingInfoBase _myInfo, Vector3 _myPos, Vector3 _dir)
	{
		if (_explodeInteraction == null || _explodeInteraction.isActiveAndEnabled == false) return;

		var pos = _myPos;

		Transform receiverTransform = _explodeInteraction.transform;
		Vector3 receiverPos = receiverTransform.position;

		var dot = Vector3.Dot(_dir, receiverPos - pos) / (receiverPos - pos).magnitude;
		var weight = 1f / (0.4f * dot + 1f);

		if (TargetObject != null && receiverTransform == TargetObject.transform)
			weight /= _persistence;

		var dist = GetDistanceScore(pos, receiverPos, weight * weight);

		dist.Item1 *= _priority;
		
		if (dist.Item1 > _bestTarget.m_distanceScore)
		{
			_bestTarget.Set(dist, receiverTransform, TargetObject.TargetType.ExplodeInteraction, TargetResult.TargetState.WithinOpenWalls);
		}
	}

	protected virtual void GetInteractionPoint(TargetResult _bestTarget) { }

	private float m_timeStampLastTargetRefresh = -1f;
	public float TimeSinceLastTargetRefresh => m_timeStampLastTargetRefresh > 0f ? Time.time - m_timeStampLastTargetRefresh : Single.MaxValue;
	
	public virtual TargetResult GetBestTarget(float _persistence = 1f)
	{
		m_timeStampLastTargetRefresh = Time.time;
		
		if (groupBehaviour != null)
			return groupBehaviour.GetBestTarget(this, _persistence);
		
		return CalculateBestTarget(_persistence);
	}

	public TargetResult CalculateBestTarget(float _persistence = 1f)
	{
		TargetResult bestTarget = new();
		Transform tr = Transform;
		Vector3 positionSelf = tr.position;
		Vector3 facing = tr.forward;
		var myInfo = GetMovingInfo();

		Vector3 posNearestOriginalPath = default;
		if (ObjectiveWaypoint != null)
			posNearestOriginalPath = NavAgent.GetPathPosition(LastPatrolPath, positionSelf);
		
		// Check Existing Target
		IDamageReceiver objectiveTarget = ObjectiveTarget;
		if(objectiveTarget as MABuilding)
		{
			GetBuildingTargetPriority(bestTarget, objectiveTarget as MABuilding, c_objectiveTargetPriority);
		}
		else if(objectiveTarget as MACharacterBase)
		{
			GetCharacterTargetPriority(bestTarget, objectiveTarget as MACharacterBase, c_objectiveTargetPriority, _persistence, myInfo, positionSelf, facing, _posNearestOriginalPath: posNearestOriginalPath);
		}

		// Check Characters
		foreach (var character in NGManager.Me.m_MACharacterList)
		{
			GetCharacterTargetPriority(bestTarget, character, c_normalTargetPriority, _persistence, myInfo, positionSelf, facing, _posNearestOriginalPath: posNearestOriginalPath);
		}

		// Check Buildings
		if (CanAttackBuildingComponents)
		{
			var validTargetBuildings = MABuildingSupport.GetBuildingsWithComponents(false, CreatureInfo.ComponentTargets.ToArray());
			foreach (var building in validTargetBuildings)
			{
				if(bestTarget.m_object == building.transform) continue;
				GetBuildingTargetPriority(bestTarget, building, c_normalTargetPriority);
			}
		}
		
		// Check Characters
		foreach (var character in NGManager.Me.m_MACharacterList)
		{
			GetCharacterTargetPriority(bestTarget, character, c_normalTargetPriority, _persistence, myInfo, positionSelf, facing, _posNearestOriginalPath: posNearestOriginalPath);
		}

		// Check Explode Interactions
		if (GameManager.Me.PossessedCharacter == this)
		{
			List<ExplodeInteraction> attackableExplodeInteractions = ExplodeInteraction.GetAttackableExplodeInteractions();

			foreach (var explodeInteraction in attackableExplodeInteractions)
			{
				GetExplodeInteractionTargetPriority(bestTarget, explodeInteraction, c_normalTargetPriority, _persistence, myInfo, positionSelf, facing);
			}
		}

		if (CharacterSettings.m_isBodyGuard && Leader != null)
		{
			var leaderAsTarget = Leader.GetComponent<TargetObject>();
			TargetObject.Attacker attackingLeader = leaderAsTarget?.GetClosestAttacker();
			if (attackingLeader != null)
			{
				(Leader as MACharacterBase)?.GetCharacterTargetPriority(bestTarget, attackingLeader.m_attacker as MACharacterBase,
					c_objectiveTargetPriority, _persistence, myInfo, positionSelf, facing, true, posNearestOriginalPath); //note the last param
			}
		}
		
		bool isNotBusy = IsNotBusy;
		// Check Interaction Points
// #if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
// 		if (bestTarget.HasTarget == false && isNotBusy)
// 		{
// 			GetInteractionPoint(bestTarget);
// 		}
// #endif
		
		if (bestTarget.HasTarget)
		{
			bestTarget.CreateObject();
			return bestTarget;
		}
		
		return null;
	}
	
	public Vector3 GetAveragePos(HashSet<IDamageReceiver> _characters, Vector3 _from)
	{
		if (_characters == null || _characters.Count == 0) return Vector3.zero;
		
		const float checkExtraDist = 8f;
		const int maxCheck = 12;
		int characterCount = 0;

		float bestSqrDist = float.PositiveInfinity;
		foreach (var character in _characters)
		{
			if (characterCount >= maxCheck)
			{
				break;
			}
			if (character == null) continue;
			var dist = (character.Transform.position - _from).sqrMagnitude;
			if (dist < bestSqrDist)
				bestSqrDist = dist;
			characterCount++;
		}
		
		var sqDistThreshold = bestSqrDist + checkExtraDist * checkExtraDist;
		Vector3 totPos = Vector3.zero;
		int iCharacter = 0;
		foreach (var character in _characters)
		{
			if (iCharacter >= maxCheck)
			{
				break;
			}
			if (character == null) continue;
			var dir = character.Transform.position - _from;
			var dist = dir.xzSqrMagnitude();
			if (dist < sqDistThreshold)
				totPos += dir;
			iCharacter++;
		}
		return totPos / characterCount;
	}

	const RigidbodyConstraints FreezeXZ = (RigidbodyConstraints)(2 | 8);
	public void SetCanBePushed(bool _pushable)
	{
		if (_pushable)
			RigidBody.constraints &= ~FreezeXZ;
		else
			RigidBody.constraints |= FreezeXZ;
	}

	/***** Init ******/
	protected virtual void InitialiseGameState()	{ if(m_ID <= 0) AllocateID(); }
	public override void SetGameStateSaveData(GameState_MovingObject _gameState)
	{
		base.SetGameStateSaveData(_gameState);
		m_gameState = _gameState as GameState_Character;
	}
	
	protected virtual void ApplyInitialCharacterState() { }
	
	public CapsuleCollider m_bodyToBodyCollider = null;
	private float bodyToBodyColliderScaleElapsedTime = 0f;
	private Vector3 bodyToBodyColliderMinScale = Vector3.one;
	private Vector3 bodyToBodyColliderMaxScale = Vector3.one;

	/***** Unity ******/
	protected override void Awake()
	{
		base.Awake();
		
		if (m_nav == null)
			m_nav = GetComponent<NavAgent>();
		
		if (m_rigidBody == null)
			m_rigidBody = GetComponent<Rigidbody>();
		
		List<CapsuleCollider> caps = new();
		GetComponentsInChildren(caps);
		m_bodyToBodyCollider = caps.Find(x => x.name == m_bodyToBodyColliderName);
		bodyToBodyColliderScaleElapsedTime = float.MaxValue;
		if (m_bodyToBodyCollider != null)
		{
			bodyToBodyColliderScaleElapsedTime = 0f;
			bodyToBodyColliderMinScale = Vector3.one * 0.1f;
			bodyToBodyColliderMaxScale = m_bodyToBodyCollider.transform.localScale;
			m_bodyToBodyCollider.transform.localScale = bodyToBodyColliderMinScale;
		}
		
		m_nav.m_onStuckTimeExceeded -= OnStuckTimeExceeded;
		m_nav.m_onStuckTimeExceeded += OnStuckTimeExceeded;
		
		Transform attackFarCollider = Array.Find(transform.GetComponentsInChildren<Transform>(true), x => x.name == "WorkerToCreatureAttackFarCollider");
		if(attackFarCollider != null)
			attackFarCollider.gameObject.SetActive(false);

		m_ignorePetrifiedInStates = new(m_ignorePetrifiedStatesPreset);
		m_ignoreCreatureTargetStates = new(m_ignoreTargetStatesPreset);
	}

	private bool m_scheduleSetupCarrying = false;
	protected override void OnEnable()
	{
		base.OnEnable();
		if (m_anim != null)
		{
			if (m_scheduleSetupCarrying)
			{
				BlendAnimatorLayerWeight("Carry", m_carrying == null ? 0f : 1f);
				m_scheduleSetupCarrying = false;
			}
		}
	}
	
	private void UpdateBoredeom()
	{
		if (IsResting == false && IsActivelyHealing == false && 
		    (IsInMovementState || IsWorking || IsInCombat || IsFleeing || IsOnPatrol || IsRoamingFreely))//TS - unify hang-out state with resting/interact states
		{
			m_gameState.m_wantsToPlay += m_raiseWantsToPlayPerSecond * Time.deltaTime;
			m_gameState.m_wantsToPlay = Mathf.Clamp01(m_gameState.m_wantsToPlay);
		}
	}

	public bool UpdateHealthRecovery()
	{
		float rate = GetHealthRecoveryRate();

		if (rate > 0 && !IsAnyDeadState)
		{
			RemoveStuckProjectileIfPossible();
		}

		if (m_healingVFXPrefab != null)
		{
			if (m_healingVFXInstance == null)
			{
				m_healingVFXInstance = GameObject.Instantiate(m_healingVFXPrefab, transform).GetComponent<ParticleSystem>();
			}
			bool shouldPlay = rate > 0 && Health < MaxHealth;
			if (m_healingVFXInstance.isPlaying != shouldPlay)
			{
				if (shouldPlay)
				{
					m_healingVFXInstance.Play();
				}
				else
				{
					m_healingVFXInstance.Stop();
				}
			}
		}
		
		if (Health < MaxHealth)
		{
			Health += Time.deltaTime * rate;	
			return true;
		}
		
		if(Armour != null && Armour.ArmourPoints < Armour.MaxArmourPoints)
		{
			Armour.ArmourPoints += Time.deltaTime * rate;
			return true;
		}
		
		return false;
	}

	public float GetHealthRecoveryRate()
	{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
			float timePassingSpeedMultiplier = DayNight.Me.TimePassingSpeedMultiplier;
#else
			float timePassingSpeedMultiplier = 1f;
#endif
		if (m_overrideHealthRecoveryRate > 0)
		{
			return m_overrideHealthRecoveryRate * timePassingSpeedMultiplier;
		}
		else
		{
			if (CreatureInfo.IsDayWalker
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
				&& DayNight.Me.m_isFullDay
#else
				&& false
#endif
				&& CreatureInfo.m_healthDayRegeneration > 0f
				&& TargetObject == null && !IsBeingTargetedByEnemies)
			{
				// RW-02-APR-25: Peter's asked for health to recover at an accelerated rate when time is sped up
				// e.g. by Advance to Night.
				return CreatureInfo.m_healthDayRegeneration * timePassingSpeedMultiplier;
			}
			else// if (CreatureInfo.IsNightWalker && DayNight.Me.m_isFullNight && CreatureInfo.m_healthNightRegeneration > 0f)
			{
				//need night-time recover rate, too
			}
		}

		return 0f;
	}

	protected float m_raiseWantsToPlayPerSecond = 0.0005f;
	
	protected override void Update()
	{
		base.Update();
		
#if UNITY_EDITOR
		UpdateDebugHelperMessageIfPossible();
#endif

		if ((m_meleeComboState != null || m_rangedComboState != null) && GameManager.Me.PossessedCharacter != this && IsComboReady())
		{
			SetCurrentComboBasedOnReachability();
		}

		UpdateComboState();
	 
		if (m_scheduledState != null)
		{
			SetCharacterUpdateState(m_scheduledState);
		}
		else if(m_updateState != null)
		{
			m_updateState.OnUpdate();
		}
		
		bool isPossessed = GameManager.Me.IsPossessed(this);
		 
		if(isPossessed)
			CharacterGameState.m_timePossessed += Time.deltaTime;
		else
			CharacterGameState.m_timeUnpossessed += Time.deltaTime;
		 
		if (isPossessed && IsBlocking)
		{
			blockElapsedTime += Time.deltaTime;

			ReduceManaByAmount(blockManaPerSecond * Time.deltaTime);
			if (GameManager.Me.m_state.m_powerMana <= 0f)
			{
				UndoAttackBlock();
			}
		}

		if (m_updateState != null)
		{
			UpdateHealthRecovery();
			UpdateBoredeom();
		}

		UpdateTurning();

		UpdateBodyToBodyCollider();
	}

	private void UpdateBodyToBodyCollider()
	{
		if (m_bodyToBodyCollider == null)
			return;
		
		float bodyToBodyColliderScaleDuration = 1f;
		float t = Mathf.Clamp01(bodyToBodyColliderScaleElapsedTime / bodyToBodyColliderScaleDuration);
		var scale = Vector3.Lerp(bodyToBodyColliderMinScale, bodyToBodyColliderMaxScale, t);
		m_bodyToBodyCollider.transform.localScale = scale;
		if (bodyToBodyColliderScaleElapsedTime < bodyToBodyColliderScaleDuration)
			bodyToBodyColliderScaleElapsedTime += Time.deltaTime;
	}
	
	private void UpdateComboState()
	{
		if (HasCombo() && CurrentComboState.CheckNewState())
		{
			m_comboStateUpdate?.Invoke();
			if (CurrentComboState.IsInCooldown())
			{
				if (!InState(CharacterStates.AttackCooldown) && !InState(CharacterStates.KnockedDown))
				{
					// RW-10-JAN-25: If the combo is ended prematurely (e.g. by the player not continuing it),
					// we may have an anim associated with that attack, to return the character to idle pose.
					if (CurrentComboState.ComboEndingAttack() != null)
					{
						string recoveryAnimName = CurrentComboState.ComboEndingAttack().VisualsUsed + "_Recovery";
						if (AnimExists(recoveryAnimName))
						{
							PlayAnim(recoveryAnimName);
						}
						else if (CurrentComboState == m_parryComboState)
						{
							OnBlockIntoFinished(null, false);
						}
					}

					if (GameManager.Me.PossessedCharacter == this)
					{
						GameManager.Me.ToggleActionCamTracking(false);
					}

					if (!InState(CharacterStates.Possessed))
					{
						CharacterUpdateState.ApplyState(CharacterStates.AttackCooldown);
					}
				}

				if (GameManager.Me.PossessedCharacter == this)
					HealthBar.runeBarUI.ResetRuneUIs();
			}
			else if (CurrentAttack != null)
			{
				PlayAnim(CurrentAttack.VisualsUsed, (a, b) => { AttackAnimCallback(a, b); });
				
				if (CurrentAttack.IsProjectile)
				{
					Animator weaponAnimator = Weapon.transform.GetComponentInChildren<Animator>();
					if (weaponAnimator != null)
					{
						weaponAnimator.SetTrigger("FireWeapon");
					}
				}

				// RW-05-FEB-25: Don't let the action cam track the weapon for rune attacks.
				if (GameManager.Me.PossessedCharacter == this)
				{
					if (CurrentAttack.RuneEffect.IsNullOrWhiteSpace())
					{
						GameManager.Me.ToggleActionCamTracking(true);
					}
					
					var runeType = GetRuneTypeFromBranchAttackIfExists();
					if (!string.IsNullOrEmpty(runeType))
					{
						HealthBar.runeBarUI.ActivateRuneUI(runeType);
						PlayRuneHintEffect(runeType);
					}
					else
					{
						HealthBar.runeBarUI.ResetRuneUIs();
					}
					
					GameManager.Me.ScheduleInstaTurn();
				}

				if (CurrentAttack.UsesWeaponGlow)
				{
					var weaponBlocks = Weapon.transform.GetComponentsInChildren<Block>();
					for (int i=0; i<weaponBlocks.Length; i++)
					{
						var meshesInBlock = weaponBlocks[i].GetComponentsInChildren<MeshFilter>();
						for (int j=0; j<meshesInBlock.Length; j++)
						{
							var go = GameObject.Instantiate(m_weaponGlowPrefab, meshesInBlock[j].transform);
							m_activeWeaponGlows.Add(go);
							var particleSystems = go.transform.GetComponentsInChildren<ParticleSystem>();
							for (int k=0; k<particleSystems.Length; k++)
							{
								var ps = particleSystems[k];
								var sh = ps.shape;
								sh.enabled = true;
								sh.shapeType = ParticleSystemShapeType.Mesh;
								sh.mesh = meshesInBlock[j].sharedMesh;

								ps.Play();
							}
						}
					}
				}
			}
		}
	}

	public string GetRuneTypeFromBranchAttackIfExists()
	{
		var attack = CurrentComboState.GetNextAttack();
		var weaponRune = GetWeaponRuneFromAttack(attack);
		if (!attack.IsBranch || weaponRune == null)
			return null;

		var data = StickerData.s_entries[weaponRune.stickerDataId];
		if (!GameManager.BranchingCombosEnabled)
		{
			var manaCost = data.m_runeManaCost;
			if (GameManager.Me.m_state.m_powerMana < manaCost)
				return null;
		}

		return data.m_runePowerType;
	}
	
	private void PlayRuneHintEffect(string runeType)
	{
		GameManager.Me.PlayRuneAvailableSlowMotion();
		var weaponBlocks = Weapon.transform.GetComponentsInChildren<Block>();
		for (int i=0; i<weaponBlocks.Length; i++)
		{
			var meshesInBlock = weaponBlocks[i].GetComponentsInChildren<MeshFilter>();
			for (int j=0; j<meshesInBlock.Length; j++)
			{
				var go = GameObject.Instantiate(m_weaponGlowRunePrefab, meshesInBlock[j].transform);
				m_activeWeaponGlows.Add(go);
				var particleSystems = go.transform.GetComponentsInChildren<ParticleSystem>();
				for (int k=0; k<particleSystems.Length; k++)
				{
					var ps = particleSystems[k];
					var sh = ps.shape;
					sh.enabled = true;
					sh.shapeType = ParticleSystemShapeType.Mesh;
					sh.mesh = meshesInBlock[j].sharedMesh;

					var psr = ps.GetComponent<ParticleSystemRenderer>();
					var main = ps.main;
					if (runeType == "Flame")
					{
						main.startColor = new Color(255,0,0);
						psr.material.color = new Color(255,0,0);
					}
					else
					{
						main.startColor = new Color(0,0,255);
						psr.material.color = new Color(0,0,255);
					}

					ps.Play();
				}
			}
		}
	}

	protected virtual void AttackAnimCallback(MAAnimationSet.AnimationParams _animInfo, bool _interrupted)
	{
		// See comment on the definition of m_activeWeaponTrails for info about this.
		for (int i=0; i<m_activeWeaponTrails.Count; i++)
		{
			m_activeWeaponTrails[i].SetRecording(false);
		}

		CurrentComboState.AttackCompleted(_interrupted);
		UpdateComboState();
	}

	public override void SetupPossessed(bool _possessedOn)
	{
		AudioClipManager.Me.SetPossessedSwitch(gameObject, _possessedOn);
		if (_possessedOn)
		{
			if (CharacterUpdateState.State != CharacterStates.Possessed &&
			    ScheduledState is not { State: CharacterStates.Possessed })
			{
				CharacterUpdateState.ApplyState(CharacterStates.Possessed);
			}
		}
		// RW-03-APR-25: When unpossessing the character, we don't want to change their state if they're unconscious.
		else if (!IsIncapacitated)
			CharacterUpdateState.ApplyState(DefaultState);
	}

	public override void SetCarriedObject(ReactPickup _obj)
	{
		base.SetCarriedObject(_obj);
		if (m_anim != null)
		{
			if (gameObject.activeSelf)
			{
				m_scheduleSetupCarrying = false;
				BlendAnimatorLayerWeight("Carry", m_carrying == null ? 0f : 1f);
			}
			else m_scheduleSetupCarrying = true;
		}
	}
	
	public void StartWeaponTrail(string _boneName)
	{
		// This instances refers to the character's weapon.
		WeaponTrail wt = m_weaponTrail;
		if (!string.IsNullOrEmpty(_boneName))
		{
			Transform t = transform.FindChildRecursiveByName(_boneName);
			wt = t.GetComponentInChildren<WeaponTrail>();
			if (wt == null)
			{
				var go = GameObject.Instantiate(m_weaponTrailPrefab, t);
				wt = go.GetComponent<WeaponTrail>();
			}
		}

		wt?.SetRecording(true);
		m_activeWeaponTrails.Add(wt);
	}

	public void StopWeaponTrail(string _boneName)
	{
		// This instances refers to the character's weapon.
		WeaponTrail wt = m_weaponTrail;
		if (!string.IsNullOrEmpty(_boneName))
		{
			Transform t = transform.FindChildRecursiveByName(_boneName);
			wt = t.GetComponentInChildren<WeaponTrail>();
			if (wt == null)
			{
				var go = GameObject.Instantiate(m_weaponTrailPrefab, t);
				wt = go.GetComponent<WeaponTrail>();
			}
		}

		wt?.SetRecording(false);
	}	

	public void StopWeaponGlow()
	{
		for (int i=0; i<m_activeWeaponGlows.Count; i++)
		{
			GameObject.Destroy(m_activeWeaponGlows[i]);
		}
		m_activeWeaponGlows.Clear();
	}

	public void PlayAnimVFX(string _boneName, GameObject _go)
	{
		Transform vfxTrans = transform;
		if (!String.IsNullOrEmpty(_boneName))
		{
			vfxTrans = transform.FindChildRecursiveByName(_boneName);
		}
		Transform t = GameObject.Instantiate(_go).transform;
		t.position = vfxTrans.TransformPoint(t.position);
	}

	public void FireRuneEffectIfExists()
	{
		var weaponRune = GetWeaponRuneFromAttack(CurrentAttack);
		if (weaponRune != null)
		{
			var data = StickerData.s_entries[weaponRune.stickerDataId];
			var manaCost = data.m_runeManaCost;
			var powerType = data.m_runePowerType;
			var powerLevel = data.m_runePower;
			
			if (!GameManager.BranchingCombosEnabled)
			{
				GameManager.Me.m_state.m_powerMana -= manaCost;
			}
			TriggerRuneVFX(powerType, powerLevel, weaponRune.fraction, weaponRune.owner, weaponRune.localPos);
		}
	}

	public WeaponRune GetWeaponRuneFromAttack(MAAttackInstance attack)
	{
		if (attack == null)
			return null;
		
		var runeName = attack.RuneEffect;
		if (string.IsNullOrEmpty(runeName))
			return null;
		
		for (int i = 0; i < m_weaponRunes.Count; ++i)
		{
			var weaponRune = m_weaponRunes[i];
			if (weaponRune.runeName == runeName)
			{
				return weaponRune;
			}
		}

		return null;
	}

	private MAAttackCombo GetComboFromWeaponRune(WeaponRune weaponRune)
	{
		foreach (var combo in MAAttackCombo.s_combos)
		{
			if (combo.AttackSkills[0].RuneEffect == weaponRune.runeName)
			{
				return combo;
			}
		}

		return null;
	}

	public void SetHealthRecoverRateOverride(float _valuePerSecond)
	{
		m_overrideHealthRecoveryRate = _valuePerSecond;
	}

	public void ResetHealthRecoveryRate()
	{
		m_overrideHealthRecoveryRate = -1;
	}
	
	protected void OnLevelUp()
	{
		// RW-31-MAR-25: Peter's asked for the hero's health to be refilled on levelup.
		Health = MaxHealth;
	}

	override protected void LateUpdate()
	{
		m_townProximityState = null;
		base.LateUpdate();
	}

	public bool InState(string _toCheckFor)
	{
		if (ScheduledState != null)
			return ScheduledState.State == _toCheckFor;
		if (CharacterUpdateState != null)
			return CharacterUpdateState.State == _toCheckFor;
		return false;
	}
	
	private void ClearAllTargets()
	{
		// if (m_targetObject != null)
		// {
		// 	m_targetObject.RemoveTargeter(this);
		// 	// MAWorker currentTargetWorker = m_targetObject.GetComponent<MAWorker>();
		// 	// if (currentTargetWorker != null)
		// 	// {
		// 	// 	currentTargetWorker.m_targetedBy.Remove(this);
		// 	// }
		// }
		
		SetTargetObj(null);
	}

	public bool RemoveAttacker(IDamager attacker)
	{
		bool removed = TargettedBy.Remove(attacker);
		TargetedByEnemies.Remove(attacker);
		if (TargettedBy.Count <= 0)
		{
			m_nav.PopPause("ReceivedDamage");
		}

		return removed;
	}

	private void RemoveFromSpawnPoint()
	{
		if(GlobalData.Me != null)
		{
			MASpawnPoint[] maSpawnPoints = GlobalData.Me.m_spawnPointHolder.GetComponentsInChildren<MASpawnPoint>();
			foreach(var spawnPoint in maSpawnPoints)
			{
				if(spawnPoint.TryRemoveCharacter(m_ID))
					break;
			}
		}
	}

	protected override void OnDestroy()
	{
		base.OnDestroy();

		CharacterDestroyed?.Invoke(this);
		
		AliveDuration = -1f;
		
		ClearAllTargets();
		
		TargetObject selfAsTarget = GetSelfAsTarget();
		if(selfAsTarget != null) selfAsTarget.ClearAllAttackers();
		
		m_isTouchingCapsule.Clear();
		m_isTouchingMeshColliders.Clear();
		
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		RemoveFromSpawnPoint();
#endif

		DeallocateJobAndHome();

		if (NGManager.Me != null)
		{
			NGManager.Me.m_MAHumanList.Remove(this);
			NGManager.Me.m_MACharacterList.Remove(this);
		}

		if(m_objWayPointDummy != null)
		{
			Destroy(m_objWayPointDummy);
		}

		if (m_updateState != null)
		{
			m_updateState.OnExit();
			m_updateState = null;
		}
		m_scheduledState = null;
	}

	override public void DestroyMe()
	{
		base.DestroyMe();
		
		AliveDuration = -1f;
		if(NGManager.Me != null)
			NGManager.Me.m_MACharacterList.Remove(this);
	}
	
	/***** run-time ******/
	public virtual void DoAttackOnTarget()
	{
		if (m_targetObject == null)
		{
#if UNITY_EDITOR
			Debug.LogWarning($"{GetType().Name} - DoAttackOnTarget - m_targetObject is null");
#endif
			return;
		}

		var attack = CurrentAttack;
		if (attack == null)
		{
#if UNITY_EDITOR
			Debug.LogWarning($"{GetType().Name} - DoAttackOnTarget - CurrentAttack is null");
#endif
			return;
		}
		
		switch(m_targetObject.TargetObjectType)
		{
			case TargetObject.TargetType.CharacterType:
				AttackOnTargetMovingObject(m_targetObject.GetComponent<MACharacterBase>(), attack);
				break;
			case TargetObject.TargetType.BuildingType:
			case TargetObject.TargetType.WallType:
				AttackOnTargetStructure(attack.Damage);
				break;
			case TargetObject.TargetType.ExplodeInteraction:
				AttackOnTargetExplodeInteraction(attack.Damage, attack);
				break;
			default:
				Debug.LogError($"{GetType().Name} - {name} - DoAttackOnTarget - Unknown target type for object {m_targetObject.name}");
				break;
		}
	}

	public virtual float GetDamageMultiplier()
	{
		// Level 1 is the default level, shouldn't offer any boost.
		return 1 + (Level-1)*DamageBoostPerLevel;
	}

	public virtual void DoRangeAttackOnTarget()
	{
		if (m_targetObject == null)
		{
#if UNITY_EDITOR
			Debug.LogError($"{GetType().Name} - DoRangeAttackOnTarget - m_targetObject is null");
#endif
			return;
		}

		var attack = CurrentAttack;
		if (attack == null)
		{
#if UNITY_EDITOR
			Debug.LogError($"{GetType().Name} - DoRangeAttackOnTarget - CurrentAttack is null");
#endif
			return;
		}
		
		switch(m_targetObject.TargetObjectType)
		{
			case TargetObject.TargetType.CharacterType:
				RangeAttackOnTargetMovingObject(m_targetObject.GetComponent<MACharacterBase>(), attack);
				break;
			case TargetObject.TargetType.BuildingType:
			case TargetObject.TargetType.WallType:
			case TargetObject.TargetType.ExplodeInteraction:
				RangeAttackOnTargetStructure(attack);
				break;
			default:
				Debug.LogError($"{GetType().Name} - {name} - DoRangeAttackOnTarget - Unknown target type for object {m_targetObject.name}");
				break;
		}
	}

	public virtual void AddExperience(float _xpToAdd, string _reason)
	{

	}

	public void AttackOnTargetMovingObject(MACharacterBase _targetCharacter, MAAttackInstance attack)
	{
		if (_targetCharacter == null)
		{
			return;
		}

		var hits = GetCharactersInAttackArea(_targetCharacter, attack);
		// var hits = ApplyHitForceToCharacters(hits, attack);
		foreach (var hit in hits)
		{
			var hitCharacter = hit.character;
			if (hitCharacter == null)
				continue;

			if (hitCharacter.CanBlockAttack(transform.position))
			{
				hitCharacter.PlayBlockFX(transform.forward);
				hitCharacter.ReduceManaByHit();
				if (hitCharacter.CanParryAttack())
				{
					hitCharacter.DoParry();
					
					break;
				}
				else
				{
					PlayStrikeAudio(hitCharacter.WeaponTargetType());

					hitCharacter.ApplyBlockDamage(IsPossessed ? IDamageReceiver.DamageSource.Possession : IDamageReceiver.DamageSource.AI, attack.Damage);
				}
			}
			else
			{
				hitCharacter.ApplyDamageEffect(IsPossessed ? IDamageReceiver.DamageSource.Possession : IDamageReceiver.DamageSource.AI, 0f, transform.position, attack);
				hitCharacter.CharacterGotHit?.Invoke(hitCharacter, this);
				PlayStrikeAudio(hitCharacter.m_armourHitType);

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
				if (hit.character.Health <= 0)
				{
					m_gameState.m_kills++;
					AddExperience(CreatureInfo.m_killExp * hit.character.Level, "Kill");
				}
#endif
				hitCharacter.SpawnBloodFromHit();
			}
		}
	}

	public void SpawnBloodFromHit()
	{
		var rc = m_ragdollController;
		if (rc == null)
			return;
		
		var hitBone = rc.BoneHipsTarget;
		SpawnBloodFromHit(hitBone);
	}
	
	public void SpawnBloodFromHit(Transform hitBone)
	{
		int emitCount = m_settings.m_bloodEmitCountMax;
		var normal = (transform.position.GetXZ() - hitBone.position.GetXZ()).normalized;
		normal = Vector3.Lerp(normal, Vector3.up, 0.5f);

		//MAFightVisualsControl.Me.ShowDecalAtHit(this, hitBone.position, normal, transform.forward, hitCharacter.transform, 1);
		if (m_settings.m_canBleed && m_settings.m_bloodEmitCountMax > 0)
		{
			MABloodControl.Me.EmitBloodAtHit(hitBone.position, normal, hitBone, emitCount);
			BloodEmitter bloodEmit = GetComponentInChildren<BloodEmitter>();
			if (bloodEmit == null)
			{
				var bloodEmitter = new GameObject("BloodEmitter", typeof(BloodEmitter));
				bloodEmit = bloodEmitter.GetComponent<BloodEmitter>();
				bloodEmit.transform.SetParent(hitBone, false);
				bloodEmit.transform.position = hitBone.position;
			}
			bloodEmit.m_bloodRunningTimeLeft = 0f;// bloodEmit.m_bloodRunningAfterAttackSeconds;
		}
	}

	public void AttackOnTargetStructure(float _potentialDamage)
	{
		if (m_targetObject == null)
			return;
		
		m_targetObject.DoDamage(IsPossessed ? IDamageReceiver.DamageSource.Possession : IDamageReceiver.DamageSource.AI, ref _potentialDamage, transform.position);

		// RW-14-APR-25: Some characters may take damage when attacking walls.
		if (m_targetObject.TargetObjectType == TargetObject.TargetType.WallType)
		{
			ApplyDamageEffect(IDamageReceiver.DamageSource.AI, _potentialDamage*m_creatureInfo.m_takesWallDamage, transform.position, null);
		}
	}

	public void AttackOnTargetExplodeInteraction(float _potentialDamage, MAAttackInstance _attack)
	{
		if (m_targetObject == null)
			return;

		if(CanReachTargetWithAttack(_attack, out var targetPos) && IsCharacterInAngle(targetPos.Value, _attack.AttackWidth))
		{
			m_targetObject.DoDamage(IsPossessed ? IDamageReceiver.DamageSource.Possession : IDamageReceiver.DamageSource.AI, ref _potentialDamage, transform.position);
		}	
	}

	private void RangeAttackOnTargetMovingObject(MACharacterBase _targetCharacter, MAAttackInstance attack)
	{
		if (_targetCharacter == null)
		{
			return;
		}

		if (CanHitMainTarget(_targetCharacter, attack))
		{
			var rc = _targetCharacter.m_ragdollController;
			var targetHitPos = rc.BoneLeftShoulderTarget.position;
			var rot = Quaternion.LookRotation(targetHitPos - weaponAttach.position);
			var projectileObject = Instantiate<GameObject>(defaultProjectile, weaponAttach.position, rot);
			var projectile = projectileObject.GetComponent<MARangeAttackProjectile>();
			float chance = attack.HitChance * 1000.0f;
			int chanceInt = (int)chance;
			int random = UnityEngine.Random.Range(0, 1000);
			projectile.SetupWithCharacter(_targetCharacter, 100f, (random < chanceInt) ? attack : null);
		}
	}

	public void RangeAttackOnTargetStructure(MAAttackInstance attack)
	{
		if (m_targetObject == null)
			return;
		
		var targetHitPos = m_targetObject.transform.position;
		var rot = Quaternion.LookRotation(targetHitPos - weaponAttach.position);
		var projectileObject = Instantiate<GameObject>(defaultProjectile, weaponAttach.position, rot);
		var projectile = projectileObject.GetComponent<MARangeAttackProjectile>();
		bool direct = CanTargetBeHit(m_targetObject, attack);
		projectile.SetupWithStructure(m_targetObject, 100f, attack, direct);
	}

	public class CharacterHit
	{
		public MACharacterBase character;
		// public Transform bestBoneTransform;
		public float sqrDist;
	}
	
	// public List<CharacterHit> ApplyHitForceToCharacters(List<CharacterHit> hits, MAAttackSkill attack)
	// {
	// 	for (int i = hits.Count - 1; i >= 0; --i)
	// 	{
	// 		var hitCharacter = hits[i];
	// 		var bestBone = ApplyRagdollBonesEffect(hitCharacter.character, attack.KnockbackPower);
	// 		if (bestBone != null) 
	// 		{
	// 			hitCharacter.bestBoneTransform = bestBone.Transform;
	// 			hitCharacter.sqrDist = (bestBone.Transform.position - transform.position).sqrMagnitude;
				
	// 			PlayStrikeAudio(hitCharacter.character.m_armourHitType);
	// 		}
	// 		else
	// 		{
	// 			hits.RemoveAt(i);
	// 		}
	// 	}

	// 	return hits;
	// }

	public List<CharacterHit> GetCharactersInAttackArea(MACharacterBase mainTarget, MAAttackInstance attack)
	{
		var hitCharacters = new List<CharacterHit>();
		if (CanHitMainTarget(mainTarget, attack))
			hitCharacters.Add(new CharacterHit() { character = mainTarget });
		
		var hitColliders = Physics.OverlapSphere(transform.position, attack.AttackRadius, LayerMask.GetMask(new string[] { "MovingObject" }), QueryTriggerInteraction.Ignore);
		HashSet<MAWorkerInfo> workerInfoTargets = m_creatureInfo.WorkerTargets;
		HashSet<MACreatureInfo> creatureInfoTargets = m_creatureInfo.CreatureTargets;
		foreach (var hitCollider in hitColliders)
		{
			var character = hitCollider.gameObject.GetComponent<MACharacterBase>();

			if (character == this)
				continue;
			
			if ((character == null) || (character == mainTarget))
				continue;

			if (!IsTargetAliveAndValid(character.transform))
				continue;

			if (character.InState(CharacterStates.KnockedDown))
				continue;
			
			if (!CanReachCharacterWithAttack(attack, character, out var targetPos))
				continue;

			MAMovingInfoBase infoBase = character.GetMovingInfo();
			if (infoBase == null)
				continue;

			if (workerInfoTargets.Contains(infoBase as MAWorkerInfo) == false &&
				creatureInfoTargets.Contains(infoBase as MACreatureInfo) == false)
				continue;

			if (!IsCharacterInAngle(character.transform.position, attack.AttackWidth))
				continue;

			hitCharacters.Add(new CharacterHit() { character = character });
		}

		return hitCharacters;
	}

	private bool CanHitMainTarget(MACharacterBase _mainTarget, MAAttackInstance _attack)
	{
		if (_mainTarget == null)
			return false;

		bool canHitMainTarget = IsTargetAliveAndValid(_mainTarget.transform)
		                        && !_mainTarget.InState(CharacterStates.KnockedDown)
		                        && CanReachTargetWithAttack(_attack, out var targetPos)
		                        && IsCharacterInAngle(targetPos.Value, _attack.AttackWidth)
		                        && CanCharacterBeHit(_mainTarget, _attack);
		return canHitMainTarget;
	}

	private bool CanCharacterBeHit(MACharacterBase character, MAAttackInstance attack)
 	{
 		var targetPos = character.transform.position;
 		
 		var offsetUp = Vector3.up * m_neckHeight;
 		var offsetTargetPos = targetPos + offsetUp;
 		var pos = transform.position + offsetUp;
 		var dir = offsetTargetPos - pos;
 
 		int layerMask = LayerMask.GetMask(string.IsNullOrEmpty(attack.RuneEffect) ? new string[] { "Default", "Terrain", "Roads" } : new string[] { "Default" });
 		if (Physics.Raycast(pos, dir.normalized, dir.magnitude, layerMask, QueryTriggerInteraction.Ignore))
 		{
 			return false;
 		}
 
 		return true;
 	}
 
 	public bool CanTargetBeHit(TargetObject target, MAAttackInstance attack)
 	{
 		var targetPos = target.transform.position;
 
 		var offsetUp = Vector3.up * m_neckHeight;
 		var offsetTargetPos = targetPos + offsetUp;
 		var pos = transform.position + offsetUp;
 		var dir = offsetTargetPos - pos;
 
 		int layerMask = LayerMask.GetMask(string.IsNullOrEmpty(attack.RuneEffect) ? new string[] { "Default", "Terrain", "Roads" } : new string[] { "Default" });
 		RaycastHit hitInfo;
 		if (Physics.Raycast(pos, dir.normalized, out hitInfo, dir.magnitude, layerMask, QueryTriggerInteraction.Ignore))
 		{
 			// If the raycast's hit the target building, that's great. Otherwise, we're obstructed.
 			MABuilding hitBuilding = hitInfo.transform.GetComponentInParent<MABuilding>();
 			if (target.TargetObjectType == TargetObject.TargetType.BuildingType && hitBuilding == target.BuildingSelf)
 			{
 				return true;
 			}
 			return false;
 		}
 
 		return true;
 	}

	public bool IsCharacterInAngle(Vector3 characterPos, float angle)
	{
		var toChar = (characterPos - transform.position).GetXZ();
		float angleToChar = Vector3.Angle(transform.forward.GetXZNorm(), toChar);
		var baseAngle = Mathf.Asin(characterBaseRadius / toChar.magnitude) * Mathf.Rad2Deg;
		if ((Mathf.Abs(angleToChar) - baseAngle) > (angle * 0.5f))
			return false;
		
		return true;
	}

	// public RagdollBone ApplyRagdollBonesEffect(MACharacterBase _character, float _power)
	// {
	// 	var rC = _character.m_ragdollController;
	// 	var bones = new List<RagdollBone>(rC.RagdollBones);
	// 	RagdollBone bestBone = null;
	// 	float bestDist = float.MaxValue;
	// 	foreach (var bone in bones)
	// 	{
	// 		var dist = (bone.Transform.position - transform.position).sqrMagnitude;
	// 		if (dist < bestDist)
	// 		{
	// 			bestDist = dist;
	// 			bestBone = bone;
	// 		}
	// 	}

	// 	if (bestBone == null)
	// 		return null;
		
	// 	if (_power > 0f)
	// 	{
	// 		var bodyCentre = Vector3.MoveTowards(bestBone.Transform.position, transform.position, 0.5f);
	// 		foreach (var bone in bones)
	// 		{
	// 			if (bone.IsRoot)
	// 				continue;
				
	// 			var dir = bone.Transform.position - bodyCentre;
	// 			dir += UnityEngine.Random.insideUnitSphere * 0.2f;
	// 			bone.AddVelocity(dir.normalized, _power);
	// 		}
	// 	}

	// 	return bestBone;
	// }

	public MASpawnPoint FindOriginSpawnPoint()
	{
		MASpawnPoint[] spawnPoints = GlobalData.Me.m_spawnPointHolder.GetComponentsInChildren<MASpawnPoint>();
		foreach(var spawnPoint in spawnPoints)
		{
			if(spawnPoint.IsSpawnPointOfCharacter(m_ID)) return spawnPoint;
		}
		return null;
	}
	
	public override void ApplyDamageEffect(IDamageReceiver.DamageSource _source, float _damageDone, Vector3 _sourceOfDamage, MAAttackInstance attack = null, MAHandPowerInfo handPower = null)
	{
		float healthBefore = Health;
		base.ApplyDamageEffect(_source, _damageDone, _sourceOfDamage, attack, handPower);
        float healthAfter = Health;
		
		bool isExplosion = false;
		float power = _damageDone;
		if (handPower != null)
		{
			power = handPower.m_knockbackPower;
		}
		else if (attack != null)
		{
			isExplosion = attack.ExplosionPower > 0f;
			power = isExplosion ? attack.ExplosionPower : attack.KnockbackPower;
		}
		//
		else if (_source == IDamageReceiver.DamageSource.ThrownByHand)
		{
			//RW-28-MAY-25: If damage has been caused by being thrown, we don't want to stagger or enter ragdoll,
			// since that's already been taken care of.
			power = 0;
		}
		const float impulseBaseMass = 50f;
		float scaledPower = power * (impulseBaseMass / m_rigidBody.mass);
		float impulse = impulseBaseMass * scaledPower;
		var attackDir = transform.position - _sourceOfDamage;
		var forceDir = attackDir.normalized;

		var forceDirXZ = forceDir.GetXZNorm();
		float absDirX = Mathf.Abs(forceDirXZ.x);
		float absDirZ = Mathf.Abs(forceDirXZ.z);
		float forceDirY = (absDirX > absDirZ) ? absDirX : absDirZ;
		const float attackForceDirYFactor = 3f;
		var forceDirXZY = (forceDirXZ + (Vector3.up * forceDirY * attackForceDirYFactor)).normalized;
		
        if(healthAfter <= 0)
        {
            if(healthBefore > 0)
            {
				var dir = isExplosion ? forceDirXZY : forceDirXZ;
				ActivateRagDoll(dir * impulse);
				SetDead();
				AlignmentManager.Me.CharacterDied(_source, this);
            }
			return;
        }
        
		if (power <= 0f)
		{
			if (((attack != null) && attack.CanInterrupt) || StaggerFromEveryHit)
			{
				// RW-04-MAR-25: If the character has a StaggerBack anim set, then playing this will
				// break the combo. If not, we need to break the combo explicitly.
				if (AnimExists("StaggerBack"))
				{
					SetupStaggerBackAnim(null);
				}
				// RW-21-MAR-25: Only break the combo if the hero is currently attacking.
				// This should help prevent the hero going into AttackCooldown when Held, 
				// for example.
				else if (CurrentAttack != null)
				{
					CurrentComboState.QueueComboBreak(false);
				}
			}

			return;
		}

		//if (canAttack)
			//CurrentComboState.QueueComboBreak();

		float propagationSpeed = 60f;
		float propagationDelay = attackDir.magnitude / propagationSpeed;
		if (isExplosion)
		{
			if (this is MACreatureBase || StaggerFromEveryHit)
			{
				StartCoroutine(Utility.Co_After(propagationDelay,
					() => {
						ApplyAttackExplosionEffect(forceDirXZY * impulse);
					}));
			}
		}
		else
		{
			// m_lastHitTime = Time.time;
			
			StartCoroutine(Utility.Co_After(propagationDelay,
				() => {
					SetupStaggerBackAnim(forceDirXZ * scaledPower);
				}));

		/*var damageFelt = (Random.Range(1.5f, 3f) * _damageDone) / m_creatureInfo.m_health;
		FeelDamage(damageFelt, _sourceOfDamage);*/
		}
	}

	private void ApplyAttackExplosionEffect(Vector3 ragdollForce)
	{
		StopCurrentAnimation(true);

		ActivateRagDoll(ragdollForce, (_interrupted) => { });
		MACharacterStateFactory.ApplyCharacterState(CharacterStates.KnockedDown, this);
	}

	// public bool m_canBeKnockedDownByDamage;
	// public bool m_canBeStaggeredByDamage;

	// protected virtual bool FeelDamage(float _damageFelt, Vector3 _sourceOfDamage)
	// {
	// 	if (_damageFelt < m_settings.m_damageFeltStep)
	// 	{
	// 		return false;
	// 	}

	// 	// m_lastHitTime = Time.time;

	// 	if (StateLibrary().ContainsKey(CharacterStates.KnockedDown) &&
	// 	    m_settings.m_damageFeltRagdoll >= 0f && _damageFelt > m_settings.m_damageFeltRagdoll)
	// 	{
	// 		StopCurrentAnimation(true);
	// 		MACharacterStateFactory.ApplyCharacterState(CharacterStates.KnockedDown, this);
	// 		return true;
	// 	}
		
	// 	if (InState(CharacterStates.Stagger) == false &&
	// 	    StateLibrary().ContainsKey(CharacterStates.Stagger))
	// 	{
	// 		AnimateStagger(_sourceOfDamage, _damageFelt);
	// 		MACharacterStateFactory.ApplyCharacterState(CharacterStates.Stagger, this);
	// 	}

	// 	return true;
	// }

	// public void AnimateStagger(Vector3 _sourceOfDamage, float _damageFelt)
	// {
	// 	if (!m_inRootMotionAnim && Vector3.Dot(_sourceOfDamage - transform.position, transform.forward) > 0)
	// 	{
	// 		StopCurrentAnimation(true);
	// 		var rA = GetComponentInChildren<RagdollAnimator>();
	// 		rA.MasterAlpha = 0.25f;
	// 		var clip = (_damageFelt > m_settings.m_damageFeltStagger) ? "StaggerBack" : "StepBack";
	// 		PlayAnim(clip, (x, y) => { rA.MasterAlpha = 1f; });
	// 		// AddAnimListener();
	// 	}
	// }

	public void SetupStaggerBackAnim(Vector3? shoveVelocity)
	{
		var pauseLabel = "KnockedBack"+Time.frameCount;
		bool canPlay = PlayAnim("StaggerBack", (a, b) =>
			{
				m_nav.PopPause(pauseLabel);

				var motionExtractor = gameObject.GetComponentInChildren<SendPosToParent>();
				if(motionExtractor != null)
					motionExtractor.ToggleForceDisable(false);
			});
		if (canPlay)
		{
			m_nav.PushPause(pauseLabel, false, true);
			
			if (shoveVelocity.HasValue)
			{
				m_nav.SetShoveVelocity(shoveVelocity.Value);
					
				var motionExtractor = gameObject.GetComponentInChildren<SendPosToParent>();
				motionExtractor.ToggleForceDisable(true);
			}
		}
	}

	public void ReduceManaByHit()
	{
		ReduceManaByAmount(blockHitMana);
	}

	private void ReduceManaByAmount(float amount)
	{
		if (!GameManager.Me.IsPossessed(this))
			return;
		
		var mana = GameManager.Me.m_state.m_powerMana;
		mana -= amount;
		if (mana <= 0f)
			mana = 0f;
		GameManager.Me.m_state.m_powerMana = mana;
	}

	public void DoAttackBlock()
	{
		PlayAnim("BlockInto", (a, b) => { OnBlockIntoFinished(a, b); });

		blockElapsedTime = 0f;
		isBlocking = true;

		GameManager.Me.ScheduleInstaTurn();
	}

	private void OnBlockIntoFinished(MAAnimationSet.AnimationParams _animInfo, bool _interrupted)
	{
		if (_interrupted)
			return;
			
		if (isBlocking && Utility.GetKey(KeyCode.Space))
			PlayAnim("BlockLoop");
	}

	public void UndoAttackBlock()
	{
		if (isBlocking)
			PlayAnim("BlockOut");

		isBlocking = false;
		blockElapsedTime = float.MaxValue;
	}

	public bool CanBlockAttack(Vector3 attackerPosition)
	{
		if (!IsBlocking)
			return false;
		
		return IsCharacterInAngle(attackerPosition, blockAngle);
	}

	public void ApplyBlockDamage(IDamageReceiver.DamageSource source, float attackDamage)
	{
		float damageDone = attackDamage * blockFactor;
		base.ApplyDamageEffect(source, damageDone, transform.position, null);
	}

	public bool CanParryAttack()
	{
		return blockElapsedTime <= parryDuration;
	}

	public void DoParry()
	{
		CurrentComboState = m_parryComboState;
		CurrentComboState.QueueNextAttack(false);
	}

	public override void DoPossessedAction()
	{
		// RWTODO - I've taken this out so the player can queue the next attack
		// in the combo while the previous one is playing. Need to double-check
		// the implications of this.
		//if (GetComponentInChildren<AnimationOverride>() != null)
		//	return;
		
		var possessedState = CharacterUpdateState as MACharacterStates.Possessed;
		if (possessedState != null)
		{
			possessedState.PerformAttack();
		}
	}

    public override void DoPossessedSecondaryAction()
    {
        var possessedState = CharacterUpdateState as MACharacterStates.Possessed;
		if (possessedState != null)
		{
			possessedState.PerformSecondaryAttack();
		}
    }

	public override void DoPossessedBlockAction()
    {
        var possessedState = CharacterUpdateState as MACharacterStates.Possessed;
		if (possessedState != null)
		{
			possessedState.StartBlock();
		}
    }

	public override void UndoPossessedBlockAction()
    {
        var possessedState = CharacterUpdateState as MACharacterStates.Possessed;
		if (possessedState != null)
		{
			possessedState.StopBlock();
		}
    }

    public virtual void SetToGuard(Vector3 _pos, float _radius)
	{
		ObjectiveWaypoint = _pos;
		m_gameState.m_guardRadius = _radius;
		m_gameState.m_guardObjectiveWaypoint = true;
	}
	
	public virtual void SetToGuard(string _objective, float _radius)
	{
		m_gameState.m_objectiveTarget = _objective;
		
		bool isValidObjective = false;
		if (string.IsNullOrEmpty(_objective) == false)
		{
			if (InputUtilities.GetObjectFromLocationString(_objective, out var obj))
			{
				IDamageReceiver target = obj as IDamageReceiver;
				if (target == null)
				{
					Vector3? wayPoint = obj as Vector3?;
					if (wayPoint != null)
					{
						SetToGuard((Vector3)wayPoint, _radius);
						return;
					}
					else
					{
						m_gameState.m_objectiveTarget = "";
						m_gameState.m_guardRadius = m_settings.m_guardModeOverrideRadius;
	                    m_gameState.m_guardObjectiveWaypoint = false;
					}
				}
				else
				{
					SetToGuard(target.Transform.position, _radius);
					return;
				}
			}
		}
	}
	
	public bool TryGetGuardArea(out (Vector3 pos, float radius) outArea)
	{
		outArea.Item1 = Vector3.zero;
		outArea.Item2 = 0f;

		IDamageReceiver objectiveTarget = ObjectiveTarget;
		Vector3? wayPoint = null;
		if (objectiveTarget != null)
		{
			wayPoint = objectiveTarget.Transform.position;
		}
		else
		{
			wayPoint = ObjectiveWaypoint;
		}
		
		if (wayPoint == null) 
			return false;
		
		outArea.Item1 = wayPoint.Value;
		
		if(m_gameState.m_guardRadius <= 0f) return false;
		outArea.Item2 = m_gameState.m_guardRadius;
        return true;
	}
	
	public virtual void DisableObjective()
	{
		m_gameState.m_guardObjectiveWaypoint = false;
		m_gameState.m_guardRadius = 0f;
	}

	public virtual void Save() 
	{
		Transform tr = transform;
		GameState_Character gameState = CharacterGameState;
		gameState.m_pos = tr.position;
		gameState.m_rotation = tr.rotation.eulerAngles;
		gameState.m_savedUpdateState = CharacterUpdateState.State;
		
        if(m_destinationMABuilding != null)
            gameState.m_destBuilding = m_destinationMABuilding.m_linkUID;
        else gameState.m_destBuilding = 0;
        if(m_insideMABuilding != null)
            gameState.m_inside = m_insideMABuilding.m_linkUID;
        else gameState.m_inside = 0;
		
		bool isGroupValid = GroupBehaviour != null;
		bool canGroupPatrol = isGroupValid && GroupBehaviour.CanPatrol;
		gameState.m_groupId = isGroupValid ? GroupBehaviour.ID : -1;
		gameState.m_groupPatrolPosition = canGroupPatrol ? GroupBehaviour.PatrollingInfo.centerPos : Vector3.zero;
		gameState.m_groupPatrolRadius = canGroupPatrol ? GroupBehaviour.PatrollingInfo.radius : 0f;
		gameState.m_groupStationaryPosition = canGroupPatrol ? GroupBehaviour.GetStationaryPosition(this) : Vector3.zero;
	}
	
	public enum EDeathType
	{
		None,
		Worker,
		Enemy,
		Hero,
	}

	public enum Consciousness
	{
		Conscious,
		Unconscious,
		UnconsciousDead,
		Dead
	}

	public virtual EDeathType DeathCountType => EDeathType.None;
	
	public virtual void SetUnconscious(float _duration)
	{
		m_gameState.m_unconsciousTimeLeft = _duration;
		m_gameState.m_consciousness = (int)MAHeroBase.Consciousness.Unconscious;
		m_updateState.ApplyState(CharacterStates.Unconscious);
	}
	
	public override bool SetDead()
	{
		bool stateChanged = base.SetDead();
		
		if (stateChanged)
		{
			CharacterGameState.m_timesKnockedDown++;
			
			switch (DeathCountType)
			{
				case EDeathType.Worker:
					GameManager.Me.m_state.m_gameStats.m_deaths.Increment(1);
					break;
				case EDeathType.Hero:
					GameManager.Me.m_state.m_gameInfo.m_heroKnockedDownToday = true;
					GameManager.Me.m_state.m_gameStats.m_heroDeaths.Increment(1);
					break;
				case EDeathType.Enemy:
					GameManager.Me.m_state.m_gameStats.m_enemyDeaths.Increment(1);
					break;
			}

			if (m_previousCharacterUpdateState.IsNullOrWhiteSpace() == false && this is MACreatureBase)
			{
				GameManager.Me.m_state.m_creaturesDefeated++;
			}
		}
			
		ClearAllTargets();
	
		TargetObject selfAsTarget = GetSelfAsTarget();
		if(selfAsTarget != null) selfAsTarget.ClearAllAttackers();
		
		if(m_updateState != null && m_updateState.State != CharacterStates.Dying)// && StateLibrary().ContainsKey(CharacterStates.Dying))
		{
			m_updateState.ApplyState(CharacterStates.Dying);
		}

		return stateChanged;
	}
	
	public virtual void DestroyedTarget()
	{
		Debug.Log($"{GetType().Name} - DestroyedTarget");
	}

	virtual protected void OnDeath()
	{
		DestroyMe();
	}

	virtual public bool SetMoveToObject(GameObject _gameObject, PeepActions? _action = null, Vector3 _offsetPos = default, float _arrivalRad = 0) // This should be the ONLY way the MA code should send workers to position
    {
        if (m_nav == null)
        {
            Debug.LogError($"MAWorker - MoveToObject m_agent == null with action {PeepAction.ToString()} ");
            return false;
        }

        m_destinationObject = _gameObject.transform;
        m_destinationMABuilding = null;
  
        if(_action != null) PeepAction = (PeepActions)_action;
        
        bool isActive = gameObject.activeSelf;
        gameObject.SetActive(true);
        if (m_insideMABuilding)
        {
            SetMovingObjectToLeaveBuilding(isActive);
            return true;
        }
        m_nav.Speed = GetDesiredSpeed();

        Vector3 targetPos = m_destinationObject.position + _offsetPos;
        targetPos = GlobalData.Me.GetClosestLegalCharacterDropPoint(targetPos);

        SetNavTarget(targetPos, false, null, _arrivalRad);
        SetState(STATE.MA_MOVE_TO_OBJECT);
        return true;
    }

	public override float GetDesiredSpeed()
	{
		return m_gameState.m_speed;
	}
	
	protected virtual void StateMoveToOutsideBuildingComplete()
	{
		m_nav.Speed = GetDesiredSpeed();

		if(m_destinationMABuilding != null)
		{
			SetMoveToBuilding(m_destinationMABuilding as MABuilding, PeepAction);
			return;
		}

		if(m_destinationObject != null)
		{
			SetMoveToObject(m_destinationObject.gameObject);
			return;
		}
		
		SetState(STATE.MA_MOVE_TO_POSITION);
		MoveToPos(DestinationPosition, false, OnPathToPositionReady);
	}
	
	protected override bool HasAgentArrived()
	{		
		if (m_nav.PathPending)
			return false;
		
		// if(m_nav.TargetReached)
		// {
		// 	if(m_state == STATE.MA_MOVE_TO_OUTSIDE_BUILDING)
		// 	{
		// 		//SetState(STATE.MA_MOVING_OUTDOORS);
		// 	}
		// 	else if(m_state == STATE.MA_MOVE_TO_INSIDE_BUILDING)
		// 	{
		// 		m_insideMABuilding.ObjectArrivedInside(this);
		// 		SetState(STATE.NONE);
		// 	}
		// 	return true;
		// }
		
		m_destinationRemainingDistance = m_nav.DistanceToTarget;
		return m_nav.TargetReached;
	}

	static DebugConsole.Command s_debugColourClipCmd = new ("debugcolourclip", _s => {
		var chr = GameManager.Me.PossessedCharacter?.GetComponentInChildren<CharacterVisuals>();
		if (chr == null)
		{
			Debug.LogError($"Possess a character to debug colour clip");
			return;
		}
		if (string.IsNullOrEmpty(_s))
		{
			chr.ClearColourClip();
		}
		else
		{
			UInt32 clip = 0;
			if (_s.Length >= 16)
			{
				for (int i = 0; i < 16; ++i)
				{
					clip <<= 1;
					clip |= _s[i] == '1' ? 1U : 0U;
				}
			}
			else if (int.TryParse(_s, out int i))
			{
				clip = (uint)i;
			}
			chr.SetColourClip(clip);
		}
	});

	public virtual void Activate(MACreatureInfo _info, MABuilding _optionalOwner, bool _init, Vector3 _position,
		Vector3 _rotation, bool _addToCharacterLists = true)
	{
		m_creatureInfo = _info;

		if (_info.m_attackComboObj != null)
		{
			m_meleeComboState = new MAAttackComboState(this, _info.m_attackComboObj);
			CurrentComboState = m_meleeComboState;
		}
		if (_info.m_runeComboObj != null)
		{
			m_runeComboState = new MAAttackComboState(this, _info.m_runeComboObj);
			if (CurrentComboState == null)
				CurrentComboState = m_runeComboState;
		}
		if (_info.m_rangedComboObj != null)
		{
			m_rangedComboState = new MAAttackComboState(this, _info.m_rangedComboObj);
		}

		m_parryComboState = new MAAttackComboState(this, MAAttackCombo.GetByID("ParryCombo"));

		Transform tr = transform;

		if(m_insideMABuilding != null)
		{
			tr.position = GetSpawnHeight(_position, m_insideMABuilding);
		}
		else if(_optionalOwner != null)
		{
			tr.position = GetSpawnHeight(_position, _optionalOwner);
		}
		else
		{
			tr.position = _position;
		}

		tr.eulerAngles = _rotation;

		if(_init)
		{
			if(_optionalOwner != null && _optionalOwner.DoorPosInner.Approximately(_position))
			{
				if(m_insideMABuilding == null)
				{
					m_insideMABuilding = _optionalOwner;
					tr.position = GetSpawnHeight(_position, m_insideMABuilding);
				}
			}

			InitialiseGameState();
			m_gameState.m_armourDesign = _info.PickRandomArmourDesign();
			
			// Doing this until a better solution
			if(_optionalOwner != null && _optionalOwner.ActionComponents.Count > 0)
			{
				Home = _optionalOwner.ActionComponents[0];	
			}
		}
		
		enabled = false;

		if (string.IsNullOrWhiteSpace(CharacterGameState.m_bodyType))
		{
			CharacterGameState.m_bodyType = FindBodyType();
		}
		AttachBodyType(CharacterGameState.m_bodyType);

		if(NGManager.Me.m_MACharacterList.Contains(this) == false)
			NGManager.Me.m_MACharacterList.Add(this);
		
		ApplyInitialCharacterState();
	}
	
	/// <summary>
	/// Vector3.zero means m_optionalDespawnPosition is ignored
	/// </summary>
	public void SetDespawnPosition(Vector3 _despawnPosition)
	{
		m_gameState.m_optionalDespawnPosition = _despawnPosition;
    }
	
	public Vector3 GetDespawnPosition()
	{
		if (m_gameState.m_optionalDespawnPosition.Approximately(Vector3.zero) == false)
		{
			return m_gameState.m_optionalDespawnPosition;
		}

		int iSpawnPoint = NGManager.Me.m_MACreatureSpawnPoints.FindIndex(x => x.IsSpawnPointOfCharacter(m_ID));
		if (iSpawnPoint != -1)
		{
			return NGManager.Me.m_MACreatureSpawnPoints[iSpawnPoint].transform.position;
		}

		if (Home && Home.Building)
		{
			return Home.Building.DoorPosOuter;
		}

		return transform.position;
	}

	public void ActivateWithSaveData(MACreatureInfo _info, GameState_Character _state)
	{
		SetGameStateSaveData(_state);
		SetID(m_gameState.m_id);
		SetState(STATE.MA_LOADING);

		GameState_Character gameState = CharacterGameState;

		GameManager gm = GameManager.Me;
        m_insideMABuilding = gameState.m_inside <= 0 ? null : gm.GetMACommander<MABuilding>(gameState.m_inside);
        m_destinationMABuilding = gameState.m_destBuilding <= 0 ? null : gm.GetMACommander<MABuilding>(gameState.m_destBuilding);
        
		Activate(_info, null, false, gameState.m_pos, gameState.m_rotation);
	}

	[SerializeField]
	private float m_turnSpeed = 90f; //Deg per sec of turn anim at speed 1 (90 deg over 22 frames)
	[SerializeField]
	private float m_turnAcceleration = 720f; // Max increase in angular vel per sec

	float m_targetTurnVel;
	float m_turnVel;

	public void SetTargetTurnVelocity(float _turnVelocity)
	{
		m_targetTurnVel = _turnVelocity;
		m_anim.SetBool("Turn", !_turnVelocity.IsZero());
	}

	private void UpdateTurning()
	{
		if (!(m_turnVel - m_targetTurnVel).IsZero())
		{
			m_turnVel = Mathf.MoveTowards(m_turnVel, m_targetTurnVel, m_turnAcceleration * Time.deltaTime);
			SetAnimatorFloat("TurnSpeed", m_turnVel / m_turnSpeed);
		}

		if (!m_turnVel.IsZero())
		{
			var euler = transform.eulerAngles;
			euler.y += m_turnVel * Time.deltaTime;
			transform.eulerAngles = euler;
		}
	}

	public void OnTurnFinished()
	{
		if (IsTurnFinished == null || !IsTurnFinished())
			return;
		
		IsTurnFinished = null;
		SetTargetTurnVelocity(0f);
	}

	protected virtual bool StateMoveToInsideBuilding()
	{ 
		(m_insideMABuilding as MABuilding)?.SetEntranceCollidersDisabled(true, this);
		
		if(HasAgentArrived() == false)
		{
			return false;
		}
		
		m_insideMABuilding.ObjectArrivedInside(this);
		return true;
	}

	public bool IsWithinGuardRadius()
	{
		return IsWithinGuardRadius(transform.position, m_bodyToBodyCollider.radius);
	}

	public bool IsWithinGuardRadius(Vector3 _posToCheck, float _extraRadius = 0f)
	{
		Vector3? objWayPoint = ObjectiveWaypoint;
		if(objWayPoint != null)
		{
			var toFinalDest = (Vector3)objWayPoint - _posToCheck; toFinalDest.y = 0;
			var toFinalDestDist = toFinalDest.xzMagnitude();
			float guardRadius = GuardRadius;
			if(toFinalDestDist <= _extraRadius + guardRadius)
			{
				return true;
			}
		}
		return false;
	}
	
	public bool IsAtObjectiveWaypoint()
	{
		if(m_gameState.m_guardObjectiveWaypoint)
		{
			return IsWithinGuardRadius(transform.position);
		}
		const float c_objectiveReachedRadiusMultiplier = 2.5f;
		Vector3? objWayPoint = ObjectiveWaypoint;
		if(objWayPoint != null)
		{
			var toFinalDest = (Vector3)objWayPoint - transform.position; toFinalDest.y = 0;
			var toFinalDestDist = toFinalDest.xzMagnitude();
			if(toFinalDestDist < (m_bodyToBodyCollider.radius * c_objectiveReachedRadiusMultiplier))
			{
				return true;
			}
		}
		return false;
	}
	
	public virtual bool MAReturnToHome()
	{
		return false;
	}
	
	public bool SendToHangOut()
	{
		var tavernBuildings = MABuildingSupport.GetBuildingsWithComponent<BCActionTavern>(true);
		const float maxDist = 200f;
		
		var origin = Home ? Home.transform.position : transform.position;
		
		foreach (var tavernBuilding in tavernBuildings)
		{
			var taverns = tavernBuilding.BuildingComponents<BCActionTavern>(true);
			foreach (var tavern in taverns)
			{
				var d2 = (origin - tavern.transform.position).GetXZ().sqrMagnitude;
				if(d2 > (maxDist*maxDist))
					continue;
				
				SetMoveToPosition(tavern.GetRandomPosInHangOutArea(), false, PeepActions.HangOut);
				return true;
			}
		}
		
		// Hang out around home
		if(Home != null)
		{
			SetMoveToPosition(Home.GetRandomPosInHangOutArea(), false, PeepActions.HangOut);
			return true;
		}
		return false;
	}
	
	public void StopForGestureAndContinue(string _playAnimSetKey)
	{
		if (StateLibrary().ContainsKey(CharacterStates.Waiting))
		{
			m_gameState.m_backupState = m_state.ToString();
			m_scheduledAnimSetKey = _playAnimSetKey;
			CharacterUpdateState.ApplyState(CharacterStates.Waiting);
		}
		else
		{
			m_nav.Pause(false, true);
			if (PlayAnim(_playAnimSetKey, (x, y) =>
			    {
				    m_nav.Unpause();
			    }) == false)
			{
				m_nav.Unpause();
				Debug.LogError($"{GetType().Name} - {gameObject.name} - StopGestureAndContinue - Failed to play anim {_playAnimSetKey}");
			}
		}
	}
	
	public void CancelGestureAndContinue()
	{
		if (m_gameState.m_backupState.IsNullOrWhiteSpace() == false && m_scheduledAnimSetKey.IsNullOrWhiteSpace() == false)
		{
			CharacterUpdateState.ApplyState(m_gameState.m_backupState);
		}
		m_gameState.m_backupState = "";
		m_scheduledAnimSetKey = "";
		m_nav.Unpause();	
	}

	virtual protected bool StateMoveToBuilding()
	{
		if(HasAgentArrived() == false)
		{
			if(m_destinationMABuilding != null && m_nav.PathExists)
			{ 
				float speed = m_nav.m_finalSpeed * Time.deltaTime;
				float doorwayDist2 = (m_destinationMABuilding.DoorPosOuter - m_destinationMABuilding.DoorPosInner).xzSqrMagnitude();
				float checkDist2 = Mathf.Max(speed*speed, doorwayDist2);
				float targetDist = m_nav.DistanceToOriginalTarget; 
				if(targetDist*targetDist < checkDist2)
				{
					MASetStateMoveIntoBuilding();
				}
			}
			
			if(m_destinationMABuilding != null && NGCommanderBase.s_heldBuilding == m_destinationMABuilding)
			{
				if(m_stoppedForBuildingPlacement == false)
				{
					m_nav.Pause(false);
					m_stoppedForBuildingPlacement = true;
				}
			}
			else
			{
				if(m_stoppedForBuildingPlacement)
				{
					m_nav.Unpause();
					m_stoppedForBuildingPlacement = false;
				}
			}
			return false;
		}
		
		if(m_destinationMABuilding != null)
		{
			// If the building moved, try and get position again, otherwise move into building
			if(DestinationPosition.Approximately(m_destinationMABuilding.DoorPosInner) == false)
			{
				SetMoveToBuilding(m_destinationMABuilding, PeepAction);
			}
			else
			{
				MASetStateMoveIntoBuilding();
			}
		} 
		return true;
	}
	
	protected virtual bool StateMoveToOutsideBuilding()
	{
		(m_insideMABuilding as MABuilding)?.SetEntranceCollidersDisabled(true, this);
		if (HasAgentArrived() == false) return false;
		
		(m_insideMABuilding as MABuilding)?.SetEntranceCollidersDisabled(false, this);
		
		SetCustomGravity(true);
		m_insideMABuilding = null;
		m_mainColliders.FindAll(x => x.gameObject.layer == LayerMask.NameToLayer(c_bodyToBodyCollisionLayer)).ForEach(x => x.enabled = true);
		StateMoveToOutsideBuildingComplete();
		return true;
	}

	public override bool SetMoveToPosition(Vector3 _position, bool _direct = false, PeepActions _action = PeepActions.None, float _destinationRadius = 0f)
	{
		m_destinationMABuilding = null;
		DestinationPosition = _position;		
		
		GameManager.Me.ClearGizmos("Destination");
		GameManager.Me.AddGizmoPoint("Destination", _position.GroundPosition(1f), 0.3f, Color.yellow);
		
		bool isActive = gameObject.activeSelf;
		gameObject.SetActive(true);
		
		if (m_insideMABuilding)
		{
			SetMovingObjectToLeaveBuilding(isActive);
			return true;
		}
		
		return NavigateToPosition(_position, _direct, 0f, _destinationRadius);
	}
	
	public override bool SetMoveToBuilding(NGCommanderBase _building, PeepActions _action = PeepActions.None)
	{
		if (_building == null)
		{
			Debug.LogError($"{GetType().Name} - MoveToBuilding _destinationBuilding == null {m_insideMABuilding} with action {_action.ToString()} ");
			return false;
		}
		
        if (m_nav == null)
        {
            Debug.LogError($"MAWorker - MoveToBuilding m_agent == null with action {_action.ToString()} ");
            return false;
        }

		if(m_nav.PathPending)
			return false;
		
		m_stoppedForBuildingPlacement = false;
		
		bool wasActive = gameObject.activeSelf;
		var previousPeepAction = PeepAction;
		
		m_destinationObject = null;
		m_destinationMABuilding = _building;
		PeepAction = _action;

		// Handles case where a worker will get stuck in the building because it can't complete the current action and is instructed to
		// move into the same building but using a different action
		if(previousPeepAction != PeepAction && m_insideMABuilding == _building)
			return true;
		
		gameObject.SetActive(true);
		
		// Move out of building, if destintion building is different
		if (m_insideMABuilding != null && m_insideMABuilding != _building)
		{
			SetMovingObjectToLeaveBuilding(wasActive);
			return true;
		}

		float destRadius = 0f;
		var buildingNav = m_destinationMABuilding.GetComponent<BuildingNav>();
		if ((buildingNav != null) && buildingNav.IsFakeBuilding)
		{
			var resourceNeededRepair = m_destinationMABuilding.GetComponentInChildren<BCResourceNeededRepair>();
			if (resourceNeededRepair != null)
				destRadius = 2f;
		}
		if(NavigateToPosition(m_destinationMABuilding.DoorPosInner, false, destRadius))
		{
			SetState(STATE.MA_MOVE_TO_BUILDING);
			return true;
		}
		return false;
	}

	public bool IsAnyDeadState => (CharacterUpdateState.State == CharacterStates.Dying ||
	                               CharacterUpdateState.State == CharacterStates.Dead ||
	                               CharacterUpdateState.State == CharacterStates.UnconsciousDead);

	override public bool CanEnterPossessionMode => base.CanEnterPossessionMode && Health > 0 &&
	                                               (CharacterUpdateState == null || IsAnyDeadState == false) &&
	                                               GameManager.Me.CanPossessAtPosition(this);
	
	override protected void CommonUpdateState()
	{
		AliveDuration += Time.deltaTime;
		if(Health <= 0 && m_gameState.m_immortal == false
		               && CharacterUpdateState != null
		               && IsAnyDeadState == false)
		{
			SetDead();
		}
	}
	
	private void OnPathToBuildingReturned(TargetReachability _targetReachability)
	{
		switch(_targetReachability)
		{
			case MACreatureBase.TargetReachability.IsReachable:
				if(m_destinationMABuilding != null) 
				{
					m_nav.AddLastPos(m_destinationMABuilding.DoorPosInner);
					DestinationPosition = m_destinationMABuilding.DoorPosInner;
				}
				break;
			default:
				break;
		}

		//m_onPathProcessed -= OnPathToBuildingReturned;
	}

	public GameObject EditorGetRandomSubTypePrefab()
	{
		return m_bodyTypes.Find(x => x.m_bodyTypeName == GetRandomBodyType())?.m_prefabRef;
	}

	public void ApplySubType()
	{
		CharacterVisuals visuals = GetComponentInChildren<CharacterVisuals>();
		if (visuals != null)
		{
			visuals.m_optionalLevel = m_gameState.CharacterLevel;
			if (string.IsNullOrWhiteSpace(m_gameState.m_subtype))
			{
				visuals.SetVisuals();
				m_gameState.m_subtype = visuals.m_optionalSubVariant;
			}
			else
			{
				visuals.m_optionalSubVariant = m_gameState.m_subtype;
				visuals.SetVisuals();
			}
		}
	}
	
	protected virtual void AttachBodyType(string _bodyType)
	{
		m_contentRoot = m_contentRoot == null ? transform : m_contentRoot;

		GameObject prefabRef = null;
		
		if (_bodyType.IsNullOrWhiteSpace() == false)
		{
			int iBodyType = m_bodyTypes.FindIndex(x => x.m_bodyTypeName == _bodyType);
			if (iBodyType == -1)
			{
				//Debug.LogError($"{GetType().Name} - AttachBodyType - Subtype {_subType} not found in m_subTypes");
			}
			else
			{
				prefabRef = m_bodyTypes[iBodyType].m_prefabRef;
			}
		}

		if (prefabRef != null)
		{
			GameObject bodytype = Instantiate(prefabRef, m_contentRoot);

			ApplySubType();
			OnVisualReady(bodytype);
		}
		
		if (m_anim == null)
		{
			var characterAnim = GetComponentInChildren<Animator>(true);
			SetupWithAnimator(characterAnim);
		}
		
		m_nav.Initialise(this);
		
		if (m_ragdollController == null)
		{
			m_ragdollController = GetComponentInChildren<RagdollController>();
		}

		if (m_mainRenderer == null)
		{
			m_mainRenderer = GetComponentInChildren<Renderer>(true);
		}
	}

	protected string FindBodyType()
	{//just random at the moment.
		return GetRandomBodyType();
	}

	protected virtual void OnVisualReady(GameObject visual)
	{
		Transform visuals = visual.transform;
		weaponAttach = visuals.FindChildRecursiveByName(weaponAttachName);
		//RegisterToDispatcherEvents(visual);
	}

	// protected void RegisterToDispatcherEvents(GameObject visual)
	// {
	// 	var ragdollCollisionReaction = visual.GetComponentInChildren<RagdollCollisionReaction>(true);
	// 	if (ragdollCollisionReaction == null)
	// 	{
	// 		return;
	// 	}

	// 	if (ragdollCollisionReaction.CollisionEventDispatcher != null)
	// 	{
	// 		OnDispatcherCreated(ragdollCollisionReaction.CollisionEventDispatcher);
	// 	}
	// 	else
	// 	{
	// 		ragdollCollisionReaction.OnDispatcherCreated -= OnDispatcherCreated;
	// 		ragdollCollisionReaction.OnDispatcherCreated += OnDispatcherCreated;
	// 	}
	// }

	public static IDamageReceiver GetObjectiveTarget(string _target)
	{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (string.IsNullOrEmpty(_target) == false)
			if (InputUtilities.GetObjectFromLocationString(_target, out var obj))
				return obj as IDamageReceiver;
		return null;
#else
		var structure = GameObject.Find("TargetStructure");
		if (structure != null)
			return structure.GetComponent<TestingTargetStructure>();
			
		return null;
#endif
	}

	public IDamageReceiver ObjectiveTarget => GetObjectiveTarget(m_gameState.m_objectiveTarget);

	public virtual void SetTargetObj(TargetResult _obj) 
	{
		var newTarget = _obj?.m_targetObject;
		
		if (m_targetObject != null)
		{
			m_targetObject.RemoveTargeter(this as IDamager);
		}

		m_targetObject = _obj?.m_targetObject;
		
		if (m_targetObject != null)
		{
			m_targetObject.AddTargeter(this as IDamager);
		}
		
		if (newTarget == m_targetObject)
			return;

		// var newChar = newTarget == null ? null : newTarget.GetComponent<MACharacterBase>();
		// if (newChar != null)
		// 	Physics.IgnoreCollision(m_bodyToBodyCollider, newChar.m_bodyToBodyCollider, true);
	} 

	public virtual bool IsTargetAliveAndValid(Transform _target) { return _target != null; }

	public static Vector3? ScanForNavigableRandomPos(Vector3 _position, float _innerRadius, float _outerRadius,
		bool isAllowedOutsideDistrict = true, GlobalData.AllowNavType _allowNavType = GlobalData.AllowNavType.AnyLowNav)
	{
		float dist = _outerRadius * Random.Range(0f, 1f);

		Vector3 newDir = Vector3.forward;
		Vector3 dir = newDir.RotateAbout(Vector3.up, Mathf.PI * 2 * Random.Range(0f, 1f));

		int tryTimes = 4;
		float byRad = (Mathf.PI * 2) / (tryTimes+1);
		while(tryTimes-- > 0)
		{
			Vector3 newPos = _position + dir * (dist + _innerRadius);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
			if (isAllowedOutsideDistrict || DistrictManager.Me.IsWithinDistrictBounds(newPos))
#endif
			{
				if (IsTargetVisible(newPos, _position, 1, _allowNavType))
				{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
					GlobalData.NavCostTypes navCostType = GlobalData.Me.GetNavAtPoint(newPos);
					switch (navCostType)
					{
						case GlobalData.NavCostTypes.Road:
						case GlobalData.NavCostTypes.OffRoad:
						case GlobalData.NavCostTypes.Pavement:
							return newPos;
					}
#endif

					return newPos;
				}
			}

			dir = dir.RotateAbout(Vector3.up, byRad);
		}
		return null;
	}	
	
	public bool IsTargetVisible(Vector3 _targetPos, out bool isWithinVisionRadius, int _width = 1, float _customRadius = 0f)
	{
		float visionRadiusSquared = _customRadius <= 0f ? VisionRadiusSq : (_customRadius * _customRadius);
		
		Vector3 creatPos = transform.position.GetXZ();
		Vector3 destPos = _targetPos.GetXZ();
		Vector3 dirToDest = destPos - creatPos;
		if(dirToDest.xzSqrMagnitude() > visionRadiusSquared)
		{
			isWithinVisionRadius = false;
			return false;
		}
		
		isWithinVisionRadius = true;
		return IsTargetVisible(_targetPos, creatPos, _width);
	}
	
	public static bool IsTargetVisible(Vector3 _destPos, Vector3 _originPosition, int _width = 1, GlobalData.AllowNavType _allowNavType = GlobalData.AllowNavType.AnyLowNav)
	{
		_originPosition = _originPosition.GetXZ();
		_destPos = _destPos.GetXZ();
		Vector3 dirToDest = _destPos - _originPosition;
		Vector3 dirNorm = dirToDest.normalized;
		Vector3 centrePos = _originPosition + dirToDest * 0.5f;
		float d = dirToDest.xzMagnitude();
		int depth = Mathf.RoundToInt(d);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		bool isPlotValid = PathManager.Path.IsPlotValid(centrePos, dirNorm, dirNorm.PerpendicularXZ(), _width, depth, true, _allowNavType);
		GameManager.Me.ClearGizmos("IsTargetVisible");
		GameManager.Me.AddGizmoLine("IsTargetVisible", centrePos.GroundPosition(1f), (_originPosition + dirToDest).GroundPosition(1f), isPlotValid ? Color.blue : Color.red, 0.2f);
		return isPlotValid;
#else
		return true;
#endif
	}
	
	public bool IsTargetVisible(Vector3 _targetPos, float _customRadius = 0f)
	{
		return IsTargetVisible(_targetPos, out bool isWithinVisionRadius, 1, _customRadius);
	}

	public bool IsTargetNavBlocker(Vector3 _targetPos, int _width = 1)
	{
		return PathManager.Path.IsPlotValid(_targetPos, Vector3.forward, Vector3.right, _width, _width) == false;
	}

	public float GuardRadius
	{
		get
		{
			if ((GroupBehaviour != null) && GroupBehaviour.CanPatrol)
				return GroupBehaviour.ActualGuardRadius;
			
			return m_gameState.m_guardRadius <= 0
				? CharacterSettings.m_guardModeOverrideRadius
				: m_gameState.m_guardRadius;
		}
	}
	
	public bool IsTargetWithinRadius(Vector3 _targetPos, float _radius)
	{
		Transform trCreat = transform;
		Vector3 creatPos = trCreat.position.GetXZ();
		Vector3 destPos = _targetPos.GetXZ();
		Vector3 dirToDest = destPos - creatPos;
		return dirToDest.xzSqrMagnitude() < (_radius * _radius);
	}
	
	public bool IsTargetWithinCircle(Vector3 _targetPos, Vector3 _centre, float _radius)
	{
		Vector3 pos = _centre.GetXZ();
		Vector3 destPos = _targetPos.GetXZ();
		Vector3 dirToDest = destPos - pos;
		return dirToDest.xzSqrMagnitude() < (_radius * _radius);
	}
	
	public bool IsTargetWithinGuardRadius(float _extraRadius = 0f)
	{
		if (ObjectiveWaypoint == null) return false;
		return IsTargetWithinCircle(m_targetObject.GetDestinationPosition(this, 0f).destinationPos.GetXZ(),  CharacterGameState.m_objectiveWaypoint, GuardRadius + TargetObject.Radius + _extraRadius);
	}
	
	public bool IsTargetWithinVisionRadius(Vector3 _targetPos)
	{
		return IsTargetWithinRadius(_targetPos, m_creatureInfo.m_visionRadius);
	}

	public bool IsTargetAudible(Vector3 _targetPos)
	{	
		Vector3 creatPos;
		Transform trCreat = transform;
		creatPos = trCreat.position.GetXZ();
		Vector3 destPos = _targetPos.GetXZ();
		Vector3 dirToDest = destPos - creatPos;
		return dirToDest.sqrMagnitude <= ProximityRadiusSq;
	}

	public void SetCurrentComboBasedOnReachability()
	{
		if (m_meleeComboState != null)
		{
			MAAttackInstance atk = m_meleeComboState.GetAttackInstanceFromIndex(0);
			if (CanReachTargetWithAttack(atk, out var targetPos))
			{
				CurrentComboState = m_meleeComboState;
				return;
			}
		}
		
		if (m_rangedComboState != null)
		{
			MAAttackInstance atk = m_rangedComboState.GetAttackInstanceFromIndex(0);
			if (CanReachTargetWithAttack(atk, out var targetPos))
			{
				CurrentComboState = m_rangedComboState;
				return;
			}
		}
	}

	public virtual bool CanReachCharacterWithAttack(MAAttackInstance _attack, MACharacterBase _targetCharacter, out Vector3? targetPos)
	{
		targetPos = null;

		if (_targetCharacter == null) return false;
		if (_attack == null) return false;

		TargetObject targetObject = TargetObject.Create(_targetCharacter);
		return CanReachTargetWithAttack(_attack, targetObject, out targetPos);
	}	
	
	public virtual bool CanReachTargetWithAttack(MAAttackInstance attack, out Vector3? targetPos)
	{
		return CanReachTargetWithAttack(attack, TargetObject, out targetPos);
	}

	public virtual bool CanReachTargetWithAttack(MAAttackInstance _attack, TargetObject _targetObject, out Vector3? targetPos)
	{
		targetPos = null;

		if (_targetObject == null) return false;
		if (_attack == null) return false;

		TargetObject targetObject = TargetObject.Create(_targetObject);
		
		var ctp = targetObject.transform.position;
		if (string.IsNullOrEmpty(_attack.RuneEffect) && !_attack.IsProjectile)
			ctp = targetObject.ClosestTargetPoint(this, _attack.AttackRadius, m_neckHeight);
		if (ctp.Approximately(Vector3.zero))
			return false;
		
		var dist = (ctp - transform.position).GetXZ();
		float distWithoutRadius = dist.sqrMagnitude - (characterBaseRadius * characterBaseRadius);
		if (distWithoutRadius > (_attack.AttackRadius * _attack.AttackRadius))
			return false;
		
		targetPos = ctp;

		return true;
	}
	
	override public void SetCollisionStyle(COLLISIONSTYLE _style)
	{
		if (IsForcingThroughObstacle)
		{
			Debug.Log($"{GetType().Name} - {gameObject.name} - SetCollisionStyle - deferred {_style.ToString()}") ;
			m_collisionStyle = _style;
			return;
		}
		base.SetCollisionStyle(_style);
	}
	
	public override void ActivateRagDoll(Vector3? _force = null, Action<bool> _onComplete = null)
	{
		// RW-14-MAR-25: Always reset the combo when entering ragdoll. This prevents issues with
		// things like the cooldown attempting to change state while ragdolled.
		if (HasCombo())
		{
			CurrentComboState.ResetCombo();
		}

		base.ActivateRagDoll(_force, _onComplete);
	}

	public override void UpdateGameState()
	{
		if (m_state == STATE.MA_LOADING) return;

		base.UpdateGameState();

		GameState_Character gameState = GameState as GameState_Character;

		if (gameState == null)
		{
			Debug.LogError($"{GetType().Name} - UpdateGameState - gameState save data == null");
			return;
		}

		gameState.m_rotation = transform.eulerAngles;
		bool isGroupValid = GroupBehaviour != null;
		bool canGroupPatrol = isGroupValid && GroupBehaviour.CanPatrol;
		gameState.m_groupId = isGroupValid ? GroupBehaviour.ID : -1;
		gameState.m_groupPatrolPosition = canGroupPatrol ? GroupBehaviour.PatrollingInfo.centerPos : Vector3.zero;
		gameState.m_groupPatrolRadius = canGroupPatrol ? GroupBehaviour.PatrollingInfo.radius : 0f;
		gameState.m_groupStationaryPosition = canGroupPatrol ? GroupBehaviour.GetStationaryPosition(this) : Vector3.zero;
	}


	protected override void UpdateState()
	{
		switch (m_state)
		{
            case STATE.MA_MOVE_TO_OBJECT:
                StateMoveToObject();
                break;
			case STATE.MA_MOVE_TO_POSITION:
				StateMoveToPosition();
				break;
			case STATE.MA_MOVE_TO_BUILDING:
				StateMoveToBuilding();
				break;
			case STATE.MA_MOVE_TO_INSIDE_BUILDING:
				StateMoveToInsideBuilding();
				break;
			case STATE.MA_MOVE_TO_OUTSIDE_BUILDING:
				StateMoveToOutsideBuilding();
				break;
			case STATE.MA_WAITING_FOR_ANIMATION:
				break;
			case STATE.MA_CHASE_OBJECT:
				StateChaseObject();
				break;
			default:
				base.UpdateState();
				break;
		}
	}
	
	public void ApplyAudioState()
	{
		if (m_audioStateCoroutine != null)
		{
			StopCoroutine(m_audioStateCoroutine);
		}

		if (m_audioStateEvents != null && m_audioStateEvents.Count > 0)
		{
			CharacterStateAudioEvent audioStateEvent =
				m_audioStateEvents.Find(x => m_updateState.State == x.m_characterState);
			#if UNITY_EDITOR
			if (audioStateEvent == null) 
				Debug.LogWarning($"{GetType()} - An Entry for AudioStateEvent is null. State: '{m_updateState?.State ?? ".."}' Character: '{name}'");
			#endif
			PlayStateAudio(audioStateEvent);
		}
	}

	protected override void SetObjectHeld(bool _willBeHeld)
	{
		// RW-14-MAR-25: Always reset the combo when being picked up. This prevents issues with
		// things like the cooldown attempting to change state while ragdolled.
		if (_willBeHeld && HasCombo())
		{
			CurrentComboState.ResetCombo();
		}

		base.SetObjectHeld(_willBeHeld);
	}

	Coroutine m_audioStateCoroutine = null;
	private void PlayStateAudio(CharacterStateAudioEvent _audioStateEvent)
	{
        if (_audioStateEvent != null)
        {
	        if (_audioStateEvent.m_eventHolder != null)
	        {
		        _audioStateEvent.m_eventHolder?.Play(gameObject);
	        }
	        else
	        {
		        Debug.LogWarning($"Entry for AudioStateEvent for state {_audioStateEvent.m_characterState} is missing an event holder.");
	        }
	        
	        if (_audioStateEvent.m_refreshAndRepeatAfter > 0)
	        {
		        m_audioStateCoroutine = StartCoroutine(Co_PlayRepeatAudioEvent(_audioStateEvent));
	        }
        }
        else
        {
	        Debug.LogWarning($"An Entry for AudioStateEvent is null. Character: '{name}'");
        }
	}
	
	private IEnumerator Co_PlayRepeatAudioEvent(CharacterStateAudioEvent _audioStateEvent)
    {
     	yield return new WaitForSeconds(_audioStateEvent.m_refreshAndRepeatAfter);
        PlayStateAudio(_audioStateEvent);
    }

	public void PrepareToTakeDamage()
	{
	}
	
	public void ResetTakeDamage()
	{
	}
	
	public void PrepareToDoDamage()
	{
        m_nav.Pause(true, true);
    }
	
	public void ResetDoDamage()
	{
	}
	
	public (float, float) GetDistanceScore(Vector3 _from, Vector3 _to, float _weight = 1f)
	{
		var sqMag = (_to - _from).xzSqrMagnitude();

		sqMag *= _weight;
		if(sqMag > VisionRadiusSq && sqMag > ProximityRadiusSq)
		{
			return (0f, sqMag);
		}
		
		//var v3Distance = Vector3.Distance(_to, _from);
		//var sqMagScore = (2f - score) * sqMag;
		float log2Dist = Mathf.Log(sqMag, 2);
		if (log2Dist > 0) return (1f / log2Dist, sqMag);
		return (1f, sqMag);
	}
	
	public override void SetCustomGravity(bool _active)
	{
		m_gravityFactor = _active ? m_settings.m_addGravityFactor : 0f;
	}

	protected string GetRandomBodyType()
	{
		List<BodyTypeInfo> validBodyType = m_bodyTypes.FindAll(x => string.IsNullOrWhiteSpace(x.m_bodyTypeName) == false);
		if (validBodyType == null || validBodyType.Count == 0)
		{
			return null;
		}
		BodyTypeInfo random = validBodyType.PickRandom();
		if (random == null)
		{
			Debug.LogError($"{GetType().Name} - Warning - No Sub Type Found in SubType List to act as model. Needs valid name and reference.");
			return null;
		}
		return random.m_bodyTypeName;
	}
	
	protected string GetSubTypeByLevel(int _level)
	{
		return "";
	}

	protected void OnStuckTimeExceeded(float _stuckFactor)
	{
		if (m_isForcingThrough == null)
		{
			m_isForcingThrough = StartCoroutine(SetKinematicForDuration(3));
		}
	}
	
	protected IEnumerator SetKinematicForDuration(float _secs)
	{
		var oldStyle = m_collisionStyle;
		SetCollisionStyle(COLLISIONSTYLE.KINEMATIC);
		//Debug.Log($"{GetType().Name} - {gameObject.name} - SetKinematicForDuration - activate for {_secs} seconds") ;
		//m_rigidBody.isKinematic = true;
		yield return new WaitForSeconds(_secs);
		//Debug.Log($"{GetType().Name} - {gameObject.name} - SetKinematicForDuration - deactivate after {_secs} seconds") ;
		//m_rigidBody.isKinematic = false;
		m_isForcingThrough = null;
		SetCollisionStyle(oldStyle);
	}

	public static MACharacterBase Create(MACreatureInfo _creatureInfo, Vector3 _pos, bool _addToCharacterLists = true)
	{
		return MACreatureControl.Me.GetAndSetupNewCreature(_creatureInfo, _pos, _addToCharacterLists);
	}

	// private void OnDispatcherCreated(RagdollCollisionEventDispatcher dispatcher)
	// {
	// 	dispatcher.OnCollisionEnter -= OnDispatcherCollisionEnter;
	// 	dispatcher.OnCollisionEnter += OnDispatcherCollisionEnter;
	// }

	// private void OnDispatcherCollisionEnter(Collision collision, RagdollBone bone)
	// {
	// 	if (ReactToPickupIfPossible(collision, bone))
	// 	{
	// 		return;
	// 	}
		
	// 	OnCollisionEnter(collision);
	// }

	private bool ReactToPickupIfPossible(Collision collision, RagdollBone bone)
	{
		var rpp = collision.collider.GetComponentInParent<ReactPickupPersistent>();
		if (rpp == null)
			return false;

		if (m_ragdollController == null)
			return false;

		bone?.AddVelocity(collision.relativeVelocity, 150f);
		return true;
	}
	

	protected virtual void OnCollisionExit(Collision collision)
	{		
		if(collision.collider.name == m_bodyToBodyColliderName)
		{
			CapsuleCollider capsuleCollider = collision.collider as CapsuleCollider;
			if(capsuleCollider != null)
			{
				m_isTouchingCapsule.Remove(capsuleCollider);
			}
		}
		else
		{
			MeshCollider meshCollider = collision.collider.gameObject.GetComponent<MeshCollider>();
			if(meshCollider != null) m_isTouchingMeshColliders.Remove(meshCollider.gameObject.name.ToLower());
		}
			
		if(collision.collider.name == m_bodyToBodyColliderName)
		{
			CapsuleCollider capsuleCollider = collision.collider as CapsuleCollider;
			if(capsuleCollider != null)
			{
				m_isTouchingCapsule.Remove(capsuleCollider);
			}
		}
		
		WallsInProximity.Remove(collision.collider);
	}

	public string m_bodyToBodyColliderName = "BodyCollider";
	protected virtual void OnCollisionEnter(Collision collision)
	{
		if (ReactToPickupIfPossible(collision, null))
			return;

		if (collision.collider.GetComponent<RoadSetLink>()?.m_set.m_creatureBreakable ?? false)
		{
			WallsInProximity.Add(collision.collider);
		}
		
		if(collision.collider.name == m_bodyToBodyColliderName)
		{
			CapsuleCollider capsuleCollider = collision.collider as CapsuleCollider;
			if(capsuleCollider != null)
			{
				m_isTouchingCapsule.Add(capsuleCollider);
			}
		}
		else
		{
			MeshCollider meshCollider = collision.collider.gameObject.GetComponent<MeshCollider>();
			if(meshCollider != null) m_isTouchingMeshColliders.Add(meshCollider.gameObject.name.ToLower());
		}
		
		var cartBody = collision.rigidbody;
		var deliveryCart = cartBody?.transform.FindComponentInParents<MADeliveryCart>();
		if (deliveryCart != null)
		{
			HitByCart(collision);
		}
	}
	
	protected override bool OnTrigger(Collider other) //Enter or Stay
	{
		bool newOverlap = base.OnTrigger(other);
		if (other.GetComponent<RoadSetLink>()?.m_set.m_creatureBreakable ?? false)
		{
			WallsInProximity.Add(other);
		}
		else if ((!other.GetComponentInParent<PathBreak>()?.IsOpen) ?? false)
		{
			WallsInProximity.Add(other);
		}
		return newOverlap;
	}

	protected override void OnTriggerEnter(Collider other)
	{
		base.OnTriggerEnter(other);
	}
	
	protected override void OnTriggerStay(Collider other)
	{
		base.OnTriggerStay(other);
	}

	protected override void OnTriggerExit(Collider other)
	{
		base.OnTriggerExit(other);
		WallsInProximity.Remove(other);
	}

	public virtual bool HitByCart(Collision _collision)
	{
		long time = DateTime.UtcNow.Ticks / TimeSpan.TicksPerMillisecond;
		if (time < timestampToNextCartHit)
			return false;

		foreach (var contact in _collision.contacts)
		{
			if (contact.thisCollider.attachedRigidbody == m_rigidBody)
			{
				var cart = _collision.rigidbody;
				var workerDir = m_rigidBody.transform.position - cart.transform.position;
				var dir = cart.transform.right;
				if (Vector3.Dot(workerDir, cart.transform.right) < 0f)
					dir = -cart.transform.right;
				dir = dir + cart.transform.forward;
				var velocity = cart.linearVelocity.sqrMagnitude;
				if (velocity > (1f * 1f))
				{
					timestampToNextCartHit = time + (2 * 1000); // 2 secs cooldown before responding to next hit
					m_nav.PushPause($"CartHit{time}", false, true);
					ActivateRagDoll(dir * velocity * 5f, (_interrupted) => { m_nav.PopPause($"CartHit{time}"); });

					return true;
				}

				break;
			}
		}

		return false;
	}

	public void PlayBlockFX(Vector3 _attackDir)
	{
		if (m_blockFXPrefab != null)
		{
			Transform tr = GameObject.Instantiate(m_blockFXPrefab).transform;
			// Offset the FX a little bit up the length of the blocking weapon.
			tr.position = Weapon.transform.position + Weapon.transform.up;
			// Orient the particles towards where the attack's come from.
			tr.up = -_attackDir;
		}
	}

	public void StickProjectile(MARangeAttackProjectile _proj, Transform _bodyPartTransform)
	{
		_proj.transform.SetParent(_bodyPartTransform, true);
		m_projectilesStuckInMe.Add(_proj);

		// Set the last removal time to now so the projectile doesn't get removed immediately.
		m_lastProjectileRemovalTime = Time.time;
	}

	float m_lastProjectileRemovalTime = 0f;
	float m_projectileRemovalFrequency = 5f;
	void RemoveStuckProjectileIfPossible()
	{
		if (m_projectilesStuckInMe.Count > 0 && Time.time - m_lastProjectileRemovalTime >= m_projectileRemovalFrequency)
		{
			GameObject.Destroy(m_projectilesStuckInMe[0].gameObject);
			m_projectilesStuckInMe.RemoveAt(0);
			m_lastProjectileRemovalTime = Time.time;
		}
	}

	void DestroyAllStuckProjectiles()
	{
		for (int i=0; i<m_projectilesStuckInMe.Count; i++)
		{
			GameObject.Destroy(m_projectilesStuckInMe[i].gameObject);
		}
		m_projectilesStuckInMe.Clear();
	}

	public void SetIsUnderground(bool _isUnderground)
	{
		GameState.m_isUnderground = _isUnderground;
		m_nav.SetIsUnderground(_isUnderground);
	}
	
	public override void PostLoad()
	{
		// Updating the Name in the GameObject for unity debug visibility
		base.Name = Name;
		
		base.PostLoad();

		// RW-19-FEB-25: Caching this here before the weapon gets brought in,
		// since that could have another SMR on it and this would be ambiguous.
		m_skinnedMeshRenderer = GetComponentInChildren<SkinnedMeshRenderer>();

		if (m_state == STATE.MA_LOADING)
		{
			SetState(STATE.IDLE);
		}
		
		if(CharacterGameState.m_customStartingPosition != Vector3.zero)
		{
			CustomStop = true;
			transform.position = CharacterGameState.m_customStartingPosition;
		}
		
		GenericSubScene.ProcessCharacter(this);

		// This should already be done for MAWorkers in InitialiseVisuals
		if (!IgnoreCharacterSize)
		{
			SetCharacterSize();
		}
		
		UpdateWeapon();
		UpdateArmour();
		
		//m_rigidBody.isKinematic = false;
		m_rigidBody.useGravity = true;
		if (m_nav)
		{
			if (m_nav.OriginalTargetPosition == Vector3.zero)
				m_nav.OriginalTargetPosition = transform.position;
		}

		enabled = true;
		// if(CharacterGameState.IsTargeting)
		// 	m_targetObject = CharacterGameState.GetTargetTransform();
		//
		// if(MACreatureControl.Me.IsValidTimeOfDay(this) == false)
		// {
		// 	if (StateLibrary().ContainsKey(CharacterStates.GoingHome))
		// 	{
		// 		if (CharacterUpdateState.State != CharacterStates.GoingHome)
		// 			CharacterUpdateState.ApplyState(CharacterStates.GoingHome);
		// 	}
		// }
	}


	public void OnClick(int _inputId, bool _longClick)
	{
		if(_inputId == 1)
		{
			if (transform != UIManager.Me.m_centralInfoPanelManager.CurrentTransformFocus)
			{
				UIManager.Me.m_centralInfoPanelManager.OpenInfoPanel("Character", transform);
			}
			else
			{
				UIManager.Me.m_centralInfoPanelManager.ToggleInfoPanel("Character", transform);
			}
		}

		if (Input.GetKey(KeyCode.N))
		{
			DestroyMe();
		}
	}

	public bool PlayEatPickupAnim(string animClipName)
	{
		return PlayAnim(animClipName, (a, b) => { OnEatPickupAnimFinished(a, b); });
	}

	private void OnEatPickupAnimFinished(MAAnimationSet.AnimationParams _animationParams, bool _interrupted)
	{
		var state = CharacterUpdateState as EatPickup;
		if (state == null)
			return;
		
		state.ApplyState(DefaultState);
	}

	public bool PlayInteractAtPositionAnim(string animClipName)
	{
		return PlayAnim(animClipName, (a, b) => { OnInteractAtPositionAnimFinished(a, b); });
	}

	private void OnInteractAtPositionAnimFinished(MAAnimationSet.AnimationParams _animationParams, bool _interrupted)
	{
		var state = CharacterUpdateState as InteractAtPosition;
		if (state == null)
			return;
		
		state.Finish(state.m_delayAfterInteractComplete);
	}

	public bool PlayInteractWithDecorationAnim(string animClipName)
	{
		return PlayAnim(animClipName, (a, b) => { OnInteractWithDecorationAnimFinished(a, b); });
	}

	private void OnInteractWithDecorationAnimFinished(MAAnimationSet.AnimationParams _animationParams, bool _interrupted)
	{
		var state = CharacterUpdateState as InteractWithDecoration;
		if (state == null)
			return;
		
		state.Finish(state.m_delayAfterInteractComplete);
	}

	public bool PlaySpawnAnim(string animClipName)
	{
		return PlayAnim(animClipName, (a, b) => { OnSpawnAnimFinished(a, b); });
	}

	private void OnSpawnAnimFinished(MAAnimationSet.AnimationParams _animInfo, bool wasInterrupted)
	{
		var state = CharacterUpdateState as Spawn;
		if (state == null)
			return;
		
		state.PlayingAnim = false;
		state.ApplyState(CharacterStates.RoamForTarget);
	}

	public bool PlayDespawnAnim(string animClipName)
	{
		return PlayAnim(animClipName, (a, b) => { OnDespawnAnimFinished(a, b); });
	}
    
	private void OnDespawnAnimFinished(MAAnimationSet.AnimationParams _animInfo, bool _wasInterrupted)
	{
		var state = CharacterUpdateState as Despawn;
		if (state == null)
			return;
		
		state.DespawnTime = 0f;
	}

	public bool PlayWaitForAnimationEndAnim(string animClipName)
	{
		return PlayAnim(animClipName, (a, b) => { OnWaitForAnimationEndAnimFinished(a, b); });
	}

	private void OnWaitForAnimationEndAnimFinished(MAAnimationSet.AnimationParams _info, bool _interrupted)
	{
		var state = CharacterUpdateState as WaitForAnimationEnd;
		if (state == null)
			return;
		
		state.ApplyState(state.GameStateData.m_backupState);
	}
	
	public bool IsInTown()
	{		
		List<Enclosure> enclosures = new();
		foreach(var enclosure in MACreatureControl.Me.m_enclosures)
		{
			if(enclosure.Value.EnclosedArea.OverlapPoint(transform.position))
			{
				return true;
			}
		}
		return false;
	}

	public enum TownProximity
	{
		InsideTown,
		OutsideNearTown,
		FarOutsideTown,
	}

	public class TownProximityState
	{
		public TownProximity m_townProximity;
		public PathManager.Path m_enclosurePath;
		
		public TownProximityState(TownProximity _townProximity, PathManager.Path _enclosurePath)
		{
			m_townProximity = _townProximity;
			m_enclosurePath = _enclosurePath;
		}
	}


	protected TownProximityState m_townProximityState = null;
	public TownProximityState GetTownProximityState() //TS - this is 'any' town boundary. in the future we should allow for a position parameter.
	{
		if (m_townProximityState != null) return m_townProximityState;
		
		if (MACreatureControl.Me.TryProcessBoundaries() == false)
			return m_townProximityState = null;
		
		var enclosures = MACreatureControl.Me.m_enclosures.Keys;
		Vector2 pos = transform.position.GetXZVector2();
		foreach(var enclosurePath in enclosures)
		{
			Enclosure enclosure = MACreatureControl.Me.m_enclosures[enclosurePath];
			Vector2 closestPointOnWall = enclosure.EnclosedArea.ClosestPoint(pos);
			float distSq = (closestPointOnWall - pos).SqrMagnitude();
			
			if (Mathf.Approximately(distSq, 0f))
			{
				return m_townProximityState = new TownProximityState(TownProximity.InsideTown, enclosurePath);
			}
			
			if(distSq < VisionRadiusSq)//TODO: TS - replace with wall-based vision radius
			{
				return m_townProximityState = new TownProximityState(TownProximity.OutsideNearTown, enclosurePath);
			}
			return m_townProximityState = new TownProximityState(TownProximity.FarOutsideTown, enclosurePath);
		}
		return m_townProximityState = null;
	}

	[System.Serializable]
	public class TargetResult
	{
		public bool HasTarget => m_object != null;
		public Transform m_object = null;
		public TargetObject.TargetType m_targetType;
		public TargetObject m_targetObject = null;
		public float m_distanceScore = 0f;
		public float m_distanceXZSq = 0f;
		public TargetState m_targetLocationState = TargetState.OutsideWalls;
		
		public TargetResult Set((float,float) _dist, Transform _object, TargetObject.TargetType _type, TargetState _state = TargetState.OutsideWalls) { return Set(_dist.Item1, _dist.Item2, _object, _type, _state); }
		
		public TargetResult Set(float _distScore, float _distXZSq, Transform _object, TargetObject.TargetType _type, TargetState _state)
		{
			m_object = _object;
			m_distanceScore = _distScore;
			m_distanceXZSq = _distXZSq;
			m_targetType = _type;
			m_targetLocationState = _state;
			return this;
		}
		
		public TargetResult CreateObject()
		{
			m_targetObject = TargetObject.Create(m_object, m_targetType);
			return this;
		}

		public enum TargetState
		{
			OutsideWalls,
			WithinOpenWalls,
			WithinSmashableWalls,
			WithinClosedWalls,
			IsWall,
		}
	}

	[Serializable]
	protected class BodyTypeInfo
	{
		public int m_level = 0;
		public string m_bodyTypeName = "";
		public GameObject m_prefabRef = null;
	}
	
	[Serializable]
	public class CharacterStateAudioEvent
	{
		[Dropdown("GetStateNames()")]
		public string m_characterState;
		public AkEventHolder m_eventHolder;
		public float m_refreshAndRepeatAfter;
	}

#if UNITY_EDITOR
	public bool enableVisionRadiusDebug = false;
	public bool enableProximityRadiusDebug = false;
	public bool enablePatrolDebug = false;
	//public bool enablePatrolAttackRadiusDebug = false;
	public bool enableAttackAreaDebug = true;
	protected void OnDrawGizmos()
	{
		Vector3 origin = transform.position;
		origin = origin.GroundPosition();

		if (enableVisionRadiusDebug)
		{
			if(this is MACreatureBase)
			{
				Handles.color = new Color(Color.green.r, Color.green.g, Color.green.b, 0.1f);
			}
			else
			{
				Handles.color = new Color(Color.red.r, Color.red.g, Color.red.b, 0.1f);
			}

			Handles.DrawSolidDisc(origin, new Vector3(0, 1, 0), this.m_creatureInfo.m_visionRadius);
		}
		
		if (enableProximityRadiusDebug)
		{
			if(this is MACreatureBase)
			{
				Handles.color = new Color(Color.blue.r * 0.5f, Color.blue.g * 0.5f, Color.blue.b * 0.5f, 0.15f);
			}
			else
			{
				Handles.color = new Color(Color.red.r * 0.5f, Color.red.g * 0.5f, Color.red.b * 0.5f, 0.15f);
			}
			Handles.DrawSolidDisc(origin, new Vector3(0, 1, 0), CreatureInfo.m_proximityRadius);
		}

		if (enablePatrolDebug)
		{
			DrawPatrolPath(Color.white);
		}

		#if UNITY_EDITOR
		if (GameManager.Me.LoadComplete && enableAttackAreaDebug && HasCombo())
		{
			var attack = CurrentAttack;
			if (attack == null)
				attack = GetNextAttackAvailableInCombo();
			DrawAttackArea(attack);
			DrawAttackTargets(attack);
		}
		#endif
	}
	
	private void DrawAttackArea(MAAttackInstance attack)
	{
		if (attack == null)
			return;
		
		float halfWidth = attack.AttackWidth * 0.5f;
		var attackAreaStart = Quaternion.Euler(0f, -halfWidth, 0f) * transform.forward;
		Handles.color = new Color(Color.blue.r, Color.blue.g, Color.blue.b, 0.2f);
		Handles.DrawSolidArc(transform.position, Vector3.up, attackAreaStart, attack.AttackWidth, attack.AttackRadius);
	}

	private void DrawAttackTargets(MAAttackInstance attack)
	{
		if (m_updateState == null) return;
		var target = GetBestTarget();
		SetTargetObj(target);
		if (m_targetObject == null)
			return;
		
		Handles.color = new Color(Color.black.r, Color.black.g, Color.black.b, 0.2f);
		var hitCharacters = GetCharactersInAttackArea(m_targetObject.GetComponent<MACharacterBase>(), attack);
		foreach (var hitCharacter in hitCharacters)
		{
			Handles.DrawSolidDisc(hitCharacter.character.transform.position, Vector3.up, characterBaseRadius);
		}
	}

	private void DrawPatrolPath(Color? customColour)
	{
		if (LastPatrolPath != null)
		{
			Handles.color = customColour ?? Color.white;
			GlobalData.DrawPathGizmo(LastPatrolPath.ConvertAll(x => (Vector3)x));
			Handles.color = Color.cyan;
		}
	}

#endif
	
	public bool CustomStop
	{
		get => m_nav.enabled == false;
		set
		{
			if(value) m_nav.SetAnimatorSpeed(Vector3.zero);
			m_nav.enabled = !value;
			m_bodyToBodyCollider.isTrigger = m_nav.enabled == false;
		}
	}
	
	public void SaveCustomStartingPosition()
	{
		if(transform.position != Vector3.zero)
		{
			CustomStop = true;
			CharacterGameState.m_customStartingPosition = transform.position; //.GroundPosition();
		}
		else
		{
			Debug.LogError("Does not have a valid custom starting position yet (0,0,0)");
		}
	}
	
	public void ApplyCustomStartingPosition()
	{
		if(CharacterGameState.m_customStartingPosition != Vector3.zero)
		{
			transform.position = CharacterGameState.m_customStartingPosition;
			CustomStop = true;
		}
		else
		{
			Debug.LogError("Does not have a valid custom starting position yet (0,0,0)");
		}
	}
	
	public void ClearCustomStartingPosition()
	{
		CharacterGameState.m_customStartingPosition = Vector3.zero;//.GroundPosition();
	}

#if UNITY_EDITOR
	private MAHelper debugHelper = null;
	public void CreateDebugHelperIfNeeded()
	{
		if (debugHelper != null)
			return;
		
		debugHelper = MAHelper.Create("CharacterDebugHelper", gameObject, "PossessDog", GetDebugHelperMessage(), 0f, 30f, 7f);
	}
	private string GetDebugHelperMessage()
	{
		string stateName = CharacterUpdateState.State;
		if (CharacterUpdateState.State == "WorkerState")
			stateName = $"{m_state}";
		
		return $"State=[{stateName}] - Creture=[{m_creatureInfo.m_name}]";
	}
	private void UpdateDebugHelperMessageIfPossible()
	{
		if (debugHelper == null)
			return;
		
		debugHelper.m_text.text = GetDebugHelperMessage();
	}
#endif
}


#if UNITY_EDITOR
public class MACharacterBaseEditor : Editor
{
	protected string numHealth = "<Enter Health>";
	protected string homeId = "-1";
	protected string animName = "Wave";

	protected virtual List<string> GetStates()
	{
		MACharacterBase hb = target as MACharacterBase;
		var stateLib = hb.StateLibrary();
		if (stateLib.Count > 0)
		{
			return new List<string>(hb.StateLibrary().Keys.ToArray());
		}
		return new();
	}

	protected virtual void ApplyState(string _state, MACharacterBase _character)
	{
		MACharacterStateFactory.ApplyCharacterState(_state, _character);
	}

	private MonoBehaviour m_targetObjectCurrent = null;
	private string m_objectivePos = "";
	public override void OnInspectorGUI()
	{
		MACharacterBase character = (MACharacterBase)target;
		
		if(Application.isPlaying)
		{
			PrefabStage prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
			if(prefabStage == null || prefabStage.IsPartOfPrefabContents(character.gameObject) == false)
			{
				// if(character.m_nav != null)
				// {
				// 	if(GUILayout.Button(character.m_nav.enabled
				// 		   ? "STOP (not saved, use button below)"
				// 		   : "Stopped. Press to START"))
				// 	{
				// 		character.CustomStop = !character.CustomStop;
				// 	}
				// }
				//
				// if (character.CharacterGameState != null)
				// {
				// 	if (GUILayout.Button(
				// 		    $"Save Starting Pos {(character.transform.position.Approximately(character.CharacterGameState.m_customStartingPosition) == false ? "(Changed)" : "")}"))
				// 	{
				// 		character.SaveCustomStartingPosition();
				// 	}
				//
				// 	if (GUILayout.Button(
				// 		    $"Revert to Saved Starting Pos ({(character.CharacterGameState.m_customStartingPosition != Vector3.zero ? $"{(character.CharacterGameState.m_customStartingPosition)}" : "n/a")})"))
				// 	{
				// 		character.ApplyCustomStartingPosition();
				// 	}
				//
				// 	if (GUILayout.Button($"Set To Ground Level"))
				// 	{
				// 		character.transform.position = character.transform.position.GroundPosition();
				// 	}
				//
				// 	if (GUILayout.Button(
				// 		    $"Clear Saved Starting Pos ({(character.CharacterGameState.m_customStartingPosition != Vector3.zero ? $"{(character.CharacterGameState.m_customStartingPosition)}" : "n/a")})"))
				// 	{
				// 		character.ClearCustomStartingPosition();
				// 	}
				// }
			}
		}

		base.OnInspectorGUI();

		if(Application.isPlaying)
		{
			if (character.TargetObject != m_targetObjectCurrent)
			{
				if (character.TargetObject != null && character.TargetObject.TargetedBy.Count == 0)
				{
					var targetObject = TargetObject.Create(character.TargetObject);
					character.SetTargetObj(
						new MACharacterBase.TargetResult()
						{
							m_distanceScore = 1f,
							m_distanceXZSq = 1f,
							m_targetLocationState = MACharacterBase.TargetResult.TargetState.WithinOpenWalls,
							m_targetType = targetObject.TargetObjectType,
							m_targetObject = targetObject
						});
				}

				m_targetObjectCurrent = character.TargetObject;
			}

			List<string> states = GetStates();

			int rowCount = 3;
			GUILayout.BeginHorizontal();
			foreach(string state in states)
			{
				if(rowCount-- == 0)
				{
					rowCount = 3;
					GUILayout.EndHorizontal();
					GUILayout.BeginHorizontal();
				}
				if(GUILayout.Button($"{state}"))
				{
					ApplyState(state, character);
				}
			}
			GUILayout.EndHorizontal();
			
			if (character.m_anim != null)
			{
				GUILayout.Label("Play Custom Anim Clip:");
				string animName2 = GUILayout.TextField(animName);
				if(animName2 != null && animName != animName2)
				{
					animName = animName2;
				}
					
				if (GUILayout.Button($"Play Anim: '{animName}'"))
				{
					if (character.PlayAnim(animName) == false)
					{
						character.PlaySingleAnimation(animName, null);
					}
				}
			}

			GUILayout.Label("Set Custom Waypoint (e.g. 50;50 )");
			m_objectivePos = GUILayout.TextField(m_objectivePos);
			if(GUILayout.Button($"Apply Objective Waypoint '{m_objectivePos}'"))
            {
	            if (m_objectivePos == null || m_objectivePos.IsNullOrWhiteSpace())
	            {
		            character.ObjectiveWaypoint = null;

	            }
	            else
	            {
		            Vector3 p = (Vector3)MAParserSupport.m_specialConverters[typeof(Vector3)].m_converter( new string[2] { "", m_objectivePos } );
		            character.ObjectiveWaypoint = p;
	            }
            }

			string numHealth2 = GUILayout.TextField(numHealth);
			if(numHealth2 != null && numHealth != numHealth2)
			{
				numHealth = numHealth2;
			}
			if(GUILayout.Button($"Set Health As = {numHealth}"))
			{
				if(float.TryParse(numHealth, out float numHealthf))
				{
					character.Health = numHealthf;
				}
			}
			if(GUILayout.Button($"Level Up"))
			{
				character.AddExperience(1f, "debug");
			}
			if(GUILayout.Button($"Clear Target"))
			{
				character.SetTargetObj(null);
			}			
			if(character.GameState != null && character.Home != null && GUILayout.Button($"Select Home Building"))
			{
				Selection.objects = new[] { character.Home.Building as UnityEngine.Object };
			}		
			string homeId2 = GUILayout.TextField(homeId.ToString());
			if(homeId2 != null && homeId != homeId2)
			{
				homeId = homeId2;
			}
			if(GUILayout.Button($"Set Home Id As: {homeId}"))
			{
				if(int.TryParse(homeId, out int homeIdi))
				{
					character.GameState.m_home = null;
					character.GameState.m_homeComponentId = (long)homeIdi;
					character.UpdateComponentReferences();
				}
			}
		}
	}
}
#endif
