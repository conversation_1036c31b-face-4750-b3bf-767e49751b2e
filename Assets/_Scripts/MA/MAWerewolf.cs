using System;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
[CanEditMultipleObjects]
#endif
public class MAWerewolf : MACreatureBase
{
	public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() { return MAWerewolfStateLibrary.StateLibrary; }
	override public string HumanoidType => "Werewolf";
	public override EDeathType DeathCountType => EDeathType.Enemy;
	
	[Header("MAWerewolf")]
	[Range(2f, 10f)]
	public float m_stayOnBuildingDuration = 4f;
	
	[Range(0, 1f)]
	public float m_chanceToClimbBuilding = 0.2f; //when doing 'StandAround'
	
	protected override void Awake()
	{
		base.Awake();

		//m_attackRadius = 1.3f;
		
		IgnoreAllCollisionsExcept ignoreCollisionsExcept = GetComponentInChildren<IgnoreAllCollisionsExcept>(true);
		if(ignoreCollisionsExcept != null) ignoreCollisionsExcept.enabled = false;

		// m_animationStates = new()
		// {
		// 	{ CharacterStates.Spawn, () => "walk" },
		// 	{ CharacterStates.AttackCooldown, () => "idleBreathe" },
		// 	{ CharacterStates.Idle, () => "idleBreathe" },
		// 	{ CharacterStates.GoingHome, () => $"walk" },
		// 	{ CharacterStates.AttackFar, () => "walk" },
		// 	{ CharacterStates.AttackClose, () => $"clawsAttackRight" },
		// 	{ CharacterStates.Dying, () => $"death" },
		// };
		
		// m_animationStateReference = new Dictionary<CreatureMoveAnimations, Func<string>>()
		// {
		// 	{ CreatureMoveAnimations.Walk, () => "walk" },
		// 	{ CreatureMoveAnimations.Run, () => "run" },
		// 	{ CreatureMoveAnimations.Stop, () => "idleBreathe" },
		// 	{ CreatureMoveAnimations.StopLook, () => "idleBreathe" },
		// 	{ CreatureMoveAnimations.JumpToTarget, () => "jumpBiteAttack" },
		// 	{ CreatureMoveAnimations.Death, () => "death" },
		// 	{ CreatureMoveAnimations.AttackClose, () => "clawsAttackRight" },
		// 	
		// };///TODO: not finished, we dont just have 1 anim state per creature state.

	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
		
		//TODO: TS - howl here or in state?
	}

	/*public override bool CanReachFarAttack()
	{
		var destPos = TargetObject.GetDestinationPosition(this, m_settings.m_jumpDuration - m_settings.m_jumpDelay);
		m_attackJumpTarget = destPos.destinationPos;
		var jumpDistSqr = (transform.position - m_attackJumpTarget).xzSqrMagnitude();
		var jumpRadius = CreatureInfo.m_attackRadiusFar;
		var jumpFrac = jumpDistSqr / (jumpRadius * jumpRadius);
		return jumpFrac < 1 && jumpFrac > 1 - JumpTolerance;
	}*/
}


#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(MAWerewolf))]
public class MAWerewolfEditor : MACreatureBaseEditor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
	}
}
#endif