using System.Collections.Generic;
using UnityEngine;

public class BCOrderInfo : BCBase
{
	private MAOrderCardDisplay m_orderCardDisplay = null;
	private MABuildingInfoBoard m_infoBoard = null;
	private bool m_hasFactory = false;
	private float m_nextUpdateTime = 0;
	
	private void RefreshOnChange()
	{
		m_hasFactory = Building.HasBuildingComponent<BCFactory>();
	}
	
	public override void OnBuildingDesignChanged()
	{
		RefreshOnChange();
		base.OnBuildingDesignChanged();
	}

	public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
	{
		base.Activate(_indexOfComponentType, _quantityInBuilding);
		if(m_orderCardDisplay == null) 
			m_orderCardDisplay = GetComponent<MAOrderCardDisplay>();
			
		if(m_infoBoard == null) 
			m_infoBoard = GetComponent<MABuildingInfoBoard>();
			
		RefreshOnChange();
	}

	protected override void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
	{
		if(m_orderCardDisplay)
		{
			m_orderCardDisplay.ClearAll();
		}
		base.Deactivate(_previousOwner, _quantityRemainingInBuilding, _action);
	}
	
	public override void UpdateInternal()
	{
		if(m_nextUpdateTime < Time.time)
		{
			UpdateInfoBoardDisplay();
			UpdateOrderCardDisplay();
			m_nextUpdateTime = Time.time + 0.5f;
		}
		
		base.UpdateInternal();
	}
	
	private void UpdateInfoBoardDisplay()
	{
		if(m_infoBoard == null) return;
		
		/*string neededText = "";
		foreach(var requirement in Building.m_externalStockRequired)
		{
			neededText += $"{requirement.TextSprite}";
		}
		m_infoBoard.SetText(neededText);*/
		
		NGStock stock = new();
		foreach(var action in Building.ActionComponents)
		{
			var input = action.GetInputStock();
			if(input == null) continue;
			
			foreach(var item in input.Items)
			{
				if(Building.m_externalStockRequired.Contains(item.Resource) == false)
					continue;
					
				stock.AddOrCreateStock(item.Resource, 0, item.Needed);
			}
		}
		
		var needed = "";
		foreach(var item in stock.Items)
		{
			needed += $" {item.m_neededToProduce}{item.Resource.TextSprite}";
		}
		m_infoBoard.SetText(needed.TrimStart());	
		
	}
	
	private void UpdateOrderCardDisplay()
	{
		if(m_orderCardDisplay == null) return;
		
		if(m_hasFactory == false)
		{
			m_orderCardDisplay.ClearCard();
			m_orderCardDisplay.SetInfoText(LocalizeKnack.TranslateLocalisedString("Need Factory"));
			return;
		}
        
		MAOrder order = Building.Order;
		if(order.IsNullOrEmpty() || order.IsValid == false)
		{
			m_orderCardDisplay.ClearCard();
			m_orderCardDisplay.SetInfoText(LocalizeKnack.TranslateLocalisedString("Need Order"));
			return;
		}
        
		if(m_orderCardDisplay.IsDisplayingOrder(order) == false)
		{
			m_orderCardDisplay.SetupOrderCard(order);
			return;
		}

		/*GameState_Product productMade = Building.GetProduct();
		if(productMade == null || productMade.HasDesign == false)
		{
			m_orderCardDisplay.SetInfoText(LocalizeKnack.TranslateLocalisedString("Need Design"));
			return;
		}*/
		
		m_orderCardDisplay.SetDefaultText();
	}
}
