using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
public class BCGUIStock : BCBase
{
	private MAGUIStockInfo m_GUIStockInfo;
	private MaterialPropertyBlock m_propBlockInText;
	private MaterialPropertyBlock m_propBlockOutText;
    private bool m_isRealFactory = false;
    private bool m_inDesignTable = false;
    private NGCommanderBase m_designTableFactory;
    
    
   /* override public void Activate()
    {
        m_GUIStockInfo = GetComponent<MAGUIStockInfo>();
		SetupPropertyBlocks();
    }

    public void ActivateFromDesignTable(bool _activate, NGCommanderBase _factory)
    {
	    gameObject.SetActive(_activate);
	    if (_activate == false) return;
	    m_inDesignTable = true;
	    m_designTableFactory = _factory;
    }
	private void SetupPropertyBlocks()
	{
		if (m_GUIStockInfo==null || m_GUIStockInfo.m_inStockTextRenderer == null)
			return;
		m_propBlockInText = new MaterialPropertyBlock();
		m_propBlockOutText = new MaterialPropertyBlock();
		m_GUIStockInfo.m_inStockTextRenderer.GetPropertyBlock(m_propBlockInText);
		m_GUIStockInfo.m_outStockTextRenderer.GetPropertyBlock(m_propBlockOutText);
		m_propBlockInText.SetColor("_NumbersCol", Color.black);
		m_propBlockOutText.SetColor("_NumbersCol", Color.black);
		SetInStockText(0);
		SetOutStockText(0);
		m_GUIStockInfo.m_roofStockCubeHolder.DestroyChildren();
		if (m_building != null)
		{
			m_isRealFactory = m_building.HasBuildingComponent(typeof(BCFactory));
			var mat = new Material[m_GUIStockInfo.m_inStockMesh.materials.Length];
			for (int i = 0; i < mat.Length; i++)
			{
				mat[i] = m_GUIStockInfo.m_factoryMaterial;
			}
			//m_GUIStockInfo.m_inStockMesh.materials = mat;
		}
		else
		{
			m_GUIStockInfo.m_roofStockBackground.gameObject.SetActive(false);
		}

		var inputStock = m_building.GetInputStock();
		if (m_GUIStockInfo.m_inStockMesh != null && inputStock.GetPrimaryStock != null && inputStock.GetPrimaryStock.IsNone == false)
		{
			Extensions.SetTintWindowColour(m_GUIStockInfo.m_inStockMesh, 0,inputStock.GetPrimaryStock.m_color , false, false, false);

		}
		var outputStock = m_building.GetOutputStock();
		if (m_GUIStockInfo.m_outStockMesh != null && outputStock.GetPrimaryStock != null && outputStock.GetPrimaryStock.IsNone == false)
		{
			Extensions.SetTintWindowColour(m_GUIStockInfo.m_outStockMesh, 0,outputStock.GetPrimaryStock.m_color , false, false, false);
		}
	}

	private float oldMaxInputs;
	private List<NGStock.NGStockItem> oldNGStock = new List<NGStock.NGStockItem>();
	private int oldItemsCount;
	
	void 	UpdateRoofStockCubes(NGStock _inputsAre, float _maxInputs)
	{
		if (oldMaxInputs == _maxInputs && _inputsAre.Items.Count == oldNGStock.Count)
		{
			bool same = true;
			for (int i = 0; i < _inputsAre.Items.Count;i++)
			{
				var old = oldNGStock[i];
				var current = _inputsAre.Items[i];
				if (old.m_stock != current.m_stock || old.m_resource != current.m_resource || old.m_neededToProduce != current.m_neededToProduce)
				{
					same = false;
					break;
				} 
			}
			if (same)
				return;
		}
		int createPos = 0;

		var totalPerSection = _inputsAre.GetTotalNeededToProduce();
		var sectionSize = _maxInputs;
		m_GUIStockInfo.m_roofStockCubeHolder.DestroyChildren();
		oldNGStock.Clear();
		if (totalPerSection <= 0f) return;
		var sections = _maxInputs / _inputsAre.GetTotalNeededToProduce();
		var stockItemIndex = 0;
		var sectionY = Mathf.Ceil(totalPerSection / m_GUIStockInfo.m_maxStockWidth) * m_GUIStockInfo.m_cubePrefab.m_mesh.bounds.size.y + m_GUIStockInfo.m_sectionPadY;
	
		//Backup data
		oldMaxInputs = _maxInputs;
		foreach (var item in _inputsAre.Items)
		{
			oldNGStock.Add(new NGStock.NGStockItem()
				{
					m_resource = item.m_resource,
					m_stock = item.m_stock,
					m_neededToProduce = item.m_neededToProduce
				}
			);
		}

		//Create sections
		var roofStockCubes = new List<RoofStockCube>();
		var numSections = Mathf.Ceil(sections);
		for (int section = 0; section < numSections; section++)
		{
			createPos = 0;
			stockItemIndex = 0;
			for (int j = 0; j < sectionSize && stockItemIndex < _inputsAre.Items.Count; j++)
			{
				var stockItem = _inputsAre.Items[stockItemIndex];
				for (int i = 0; i < stockItem.m_neededToProduce && createPos < sectionSize && roofStockCubes.Count < _maxInputs; i++)
				{
					roofStockCubes.Add(RoofStockCube.Create(m_GUIStockInfo.m_cubePrefab, m_GUIStockInfo.m_roofStockCubeHolder, m_inDesignTable, m_GUIStockInfo.m_blockPad, stockItemIndex, sectionY*(float)section, createPos++, m_GUIStockInfo.m_maxStockWidth, true, stockItem.m_resource));
				}
				stockItemIndex++;
			}
		}
		
		//Fulfil Stock
		var stocks = new int[_inputsAre.Items.Count];
		var maxY = float.MinValue;
		foreach (var stockCube in roofStockCubes)
		{
			var sIndex = stockCube.m_stockItemIndex;
			if (stocks[sIndex] < _inputsAre.Items[sIndex].m_stock)
			{
				stocks[sIndex]++;
				stockCube.SetTransparent(false);
			}

			if (stockCube.GetHeightPos() > maxY)
			{
				maxY = stockCube.GetHeightPos();
			}
		}

		var backgroundSize = sectionY * (float) numSections+m_GUIStockInfo.m_sectionPadY;
		
		m_GUIStockInfo.m_roofStockBackground.localScale = new Vector3(m_GUIStockInfo.m_roofStockBackground.localScale.x, (maxY+m_GUIStockInfo.m_sectionPadY)*100f, m_GUIStockInfo.m_roofStockBackground.localScale.z);
		SetInStockText((float)_inputsAre.GetTotalPossibleToProduce());
	}

	static void SetPropertyBlockValue(MaterialPropertyBlock _block, float _v)
	{
		_block.SetFloat("_Value", _v);
		float scale = 1.8f;
		if (_v > 999) scale = 3.6f;
		else if (_v > 99) scale = 2.7f;
		_block.SetVector("_NumbersTex_ST", new Vector4(scale,.8f, 0,0));
		_block.SetFloat("_NumberSpacing", .04f);
	}

	protected void SetInStockText(float _in)
	{
		if (m_propBlockInText != null)
		{
			SetPropertyBlockValue(m_propBlockInText, _in);
			m_GUIStockInfo.m_inStockTextRenderer.SetPropertyBlock(m_propBlockInText);
		}
	}

	protected void SetOutStockText(float _out) {
		SetPropertyBlockValue(m_propBlockOutText, _out);
		m_GUIStockInfo.m_outStockTextRenderer.SetPropertyBlock(m_propBlockOutText);
	}

    // Update is called once per frame
    override public void UpdateInternal()
    {
	    if (m_GUIStockInfo == null || m_building == null) return;
	    if (m_inDesignTable)
	    {
		    UpdateFromDesignTable();
		    return;
	    }
        if(m_building.MaxInputs > 0)
        {
	        var inputStock = m_building.GetInputStock();
	        var canMake = inputStock.GetPrimaryStockCount;
	        if (m_isRealFactory)
	        {
		        //UpdateRoofStockCubes(m_building.InputsAre, m_building.MaxInputs);
		        m_GUIStockInfo.m_inStockMesh.gameObject.SetActive(false);

	        }
	        else
	        {
	            var canMakeFraction = (float)canMake / (float)m_building.MaxInputs;// factory.CanMakeMaximumQuantity;
		        if(canMake == 0)
			    {
				    m_GUIStockInfo.m_inStockMesh.gameObject.SetActive(false);
				}
				else
				{
					m_GUIStockInfo.m_inStockMesh.gameObject.SetActive(true);
					m_GUIStockInfo.m_inStockMesh.transform.localScale = new Vector3(m_GUIStockInfo.m_inStockMesh.transform.localScale.x, canMakeFraction, m_GUIStockInfo.m_inStockMesh.transform.localScale.z);
				}
		        SetInStockText((float)canMake);
	        }
        }
        if (m_building.MaxOutputs > 0)
        {
	        var outputStock = m_building.GetOutputStock();
	        ;
            var outStock = outputStock.GetTotalStock() / m_building.MaxOutputs;
            if (outStock.IsZero())
            {
	            m_GUIStockInfo.m_outStockMesh.gameObject.SetActive(false);
            }
            else
            {
	            m_GUIStockInfo.m_outStockMesh.gameObject.SetActive(true);
	            m_GUIStockInfo.m_outStockMesh.transform.localScale = new Vector3(m_GUIStockInfo.m_outStockMesh.transform.localScale.x, outStock, m_GUIStockInfo.m_outStockMesh.transform.localScale.z);
            }
			SetOutStockText(m_building.NumOutputs);
        }

        var progress = m_building.GetProductScore();
        m_GUIStockInfo.m_progressBar.fillAmount = progress;

    }

    void UpdateFromDesignTable()
    {
	    var design = NGDesignInterface.Get();
	    var inputStock = new NGStock();

	    foreach (var mats in design.MaterialsRequired)
	    {
		    inputStock.Items.Add(new NGStock.NGStockItem(mats.Key, mats.Value));
	    }
	    UpdateRoofStockCubes(inputStock, m_designTableFactory.MaxInputs);
    }*/
}

