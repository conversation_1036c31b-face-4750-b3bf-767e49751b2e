using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;

[CustomEditor(typeof(MARectRegions))]
public class MARectRegionsInspector : MonoEditorDebug.MonoBehaviourEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var myScript = (MARectRegions) target;


        if (GUILayout.Button("Clear Saved Rects"))
        {
            myScript.ClearRects();
        }
    }
}
#endif

public class MARectRegions : MonoBehaviour
{
    const string PrefsKey = "MARectRegions";
    public List<Rect> m_placmentRects = new List<Rect>();
    public bool m_showRects;
    public bool m_showTutorialRects;
    public float  m_thickness = 0.1f;
    public float m_height = 110f;
    void Start()
    {
/*        m_placmentRects = new List<Rect>();
        var savedKeys = PlayerPrefs.GetString(PrefsKey);
        foreach (var s in savedKeys.Split('\n'))
        {
            if(Utility.TryParseRect(s, out var r))
                m_placmentRects.Add(r);
        }*/ 
        UpdateRects();
    }

    void Update()
    {
        GameManager.Me.ClearGizmos(PrefsKey);

        UpdateRects();
        UpdateShowTutorialRects();
    }

#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if(m_showRects == false && m_showTutorialRects == false) return;
        var count = 0;
        if (m_showRects)
        {
            foreach(var r in m_placmentRects)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(new Vector3(r.x + r.width / 2, m_height, r.y + r.height / 2), new Vector3(r.width, 0, r.height));
                Handles.Label(new Vector3(r.center.x, m_height, r.center.y), $"Rect({count})");
                count++;
            }

        }
    }
#endif
    
    void UpdateShowTutorialRects()
    {
        if(m_showTutorialRects == false) return;
 
        /*foreach (var d in NGTutorial.s_dialogDict)
        {
            foreach(var l in d.Value.m_lines)
            {
                if(l.m_reward.Contains("rect", StringComparison.OrdinalIgnoreCase))
                {
                    var parseString = l.m_reward.Split('(',')');
                    var r = new Rect();
                    if (Utility.TryParseRect(parseString[1], out r))
                    {
                        GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x, m_height, r.y), new Vector3(r.x + r.width, m_height, r.y), Color.blue, m_thickness);
                        GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x + r.width, m_height, r.y), new Vector3(r.x + r.width, m_height, r.y + r.height), Color.blue, m_thickness);
                        GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x + r.width, m_height, r.y + r.height), new Vector3(r.x, m_height, r.y + r.height), Color.blue, m_thickness);
                        GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x, m_height, r.y + r.height), new Vector3(r.x, m_height, r.y), Color.blue, m_thickness);
                     
                    }
                }
            }
        }*/
    }
    void UpdateRects()
    {
        if(m_showRects == false) return;
        var saveString = "";
        int count = 0;
        foreach(var r in m_placmentRects)
        {
            if(r.width == 0 || r.height == 0) continue;
            Debug.DrawLine(new Vector3(r.x, m_height, r.y), new Vector3(r.x + r.width, m_height, r.y), Color.red);
            Debug.DrawLine(new Vector3(r.x + r.width, m_height, r.y), new Vector3(r.x + r.width, m_height, r.y + r.height), Color.red);
            Debug.DrawLine(new Vector3(r.x + r.width, m_height, r.y + r.height), new Vector3(r.x, m_height, r.y + r.height), Color.red);
            Debug.DrawLine(new Vector3(r.x, m_height, r.y + r.height), new Vector3(r.x, m_height, r.y), Color.red);

            GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x, m_height, r.y), new Vector3(r.x + r.width, m_height, r.y), Color.red, m_thickness);
            GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x + r.width, m_height, r.y), new Vector3(r.x + r.width, m_height, r.y + r.height), Color.red, m_thickness);
            GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x + r.width, m_height, r.y + r.height), new Vector3(r.x, m_height, r.y + r.height), Color.red, m_thickness);
            GameManager.Me.AddGizmoLine(PrefsKey,new Vector3(r.x, m_height, r.y + r.height), new Vector3(r.x, m_height, r.y), Color.red, m_thickness);
            saveString += $"{r.x},{r.y},{r.width},{r.height}\n";
            count++;
        }
        PlayerPrefs.SetString(PrefsKey, saveString.TrimEnd('\n'));
    }
    
    public void ClearRects()
    {
        m_placmentRects.Clear();
        PlayerPrefs.DeleteKey(PrefsKey);
    }
    
    
}
