using UnityEngine;

public class NCLookForHomeComponent : NCCharacterComponentBase
{
    public MABuilding HomeBuilding;
    override public bool UpdateMe()
    {
        if (State == ComponentState.Processing) return false;
        base.UpdateMe();
        
        if(HomeBuilding == null)
        {
            if (AcquireTarget())
            {
                State = ComponentState.Processing;
                return true;
            }
        }

        return false;
    }
    override public void SetTarget(object target)
    {
        var building = target as MABuilding;
        if(building == null)
        {
            Debug.LogError("Invalid target for NCWorkingCharacterComponent: " + target);
            return;
        }
        HomeBuilding = building;
    }
    override public bool AcquireTarget()
    {
        if (NGManager.Me == null) return false;
        foreach(var building in NGManager.Me.m_maBuildings)
        {
            if(building.GetFreeWorkerBedrooms() > 0)
            {
                SetTarget(building);
                return true;
            }
        }
        return false;
    }
}
