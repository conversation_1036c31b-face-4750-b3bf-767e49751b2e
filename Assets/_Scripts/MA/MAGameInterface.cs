using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAGameInterface : MonoSingleton<MAGameInterface>
{
    /*public static float GetHotspotStock(NGCarriableResource _type) 
    {
        var total = 0f;
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            var hotspotList = b.BuildingComponents<BCStockHotspot>();
            foreach (var hl in hotspotList)
            {
                var hotspot = hl as BCStockHotspot;
                var stock = hotspot.GetStockIn();
                if(stock.GetPrimaryStock == _type || _type == null)
                    total+= stock.GetPrimaryStockCount;
            }
        }
        return total;
    }*/

    public static bool CanLeaveFocusDesignMode()
    {
        return CheckIsDesignLegal();
    }
    
    private static void LogParseError(NGBusinessFlow _flow, string _msg)
    {
        Debug.LogError($"{_msg}: {_flow.m_enterWaitForTrigger} in {_flow.m_section}[{_flow.m_blockIndex}");
    }
    
    public static bool CheckIsDesignLegal()
    {
        var conditions = MAParserManager.GetCurrentFeedbackConditions();
        if(conditions == null)
            return true;
        return conditions.IsDesignAllowed();
    }
 
    
    public static bool CheckSpecialPickupBlock(Block _block)
    {
        if(_block.BlockInfo.m_isInvisible) 
            return true;
        var conditions = MAParserManager.GetCurrentFeedbackConditions();
        if(conditions == null)
            return false;
        return conditions.IsBlockAllowed(_block) == false;
    }
    public static MAFeedbackCondition.Result CheckSpecialBuildPlacement(Vector3 _plot)
    {
        var conditions = MAParserManager.GetCurrentFeedbackConditions();
        if(conditions == null)
            return MAFeedbackCondition.Result.None;
        return conditions.IsBuildPlacementAllowed(_plot);
    }
    public static void Save(ref string _s)
    {
        _s = ReactReflection.MakePropertyString<MAGameInterface>();
    }    
    public static void Load(string _l)
    {
        ReactReflection.DecodePropertyStrings<MAGameInterface>(_l);
    }
}
