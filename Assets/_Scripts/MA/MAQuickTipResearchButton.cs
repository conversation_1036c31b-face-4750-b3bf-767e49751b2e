using System;
using UnityEngine;

public class MAQuickTipResearchButton : MAQuickTipButton
{
    [SerializeField] 
    private MAIconAnimated m_affordableIconAnimated = null;

    private int m_count = Int32.MinValue; //don't remove this, it's used to check and debug if the count

    private void Awake()
    {
        if (m_affordableIconAnimated == null)
        {
            Debug.LogError($"{GetType()} - m_affordableIconAnimated is missing at {transform.Path()}", gameObject);
            enabled = false;
            return;
        }
        m_affordableIconAnimated.gameObject.SetActive(true); //ensure it's on for Awake. just in case it isn't in the prefab.
    }
    
    private void Start()
    {
        m_affordableIconAnimated.gameObject.SetActive(false); //turn off for Start, we'll turn it on in Update if we can afford things
        Activate(Name);
    }
    
    private void Update()
    {
        int newCount = MAResearchInfo.GetPlayerCanAffordCount();
#if !UNITY_EDITOR // in the editor we can adjust 'm_affordableIconAnimated' scales and other values as we play.
        if (newCount != m_count)
#endif
        {
            m_affordableIconAnimated.Set(newCount, 0, 4);
            m_count = newCount;
        }
    }
}
