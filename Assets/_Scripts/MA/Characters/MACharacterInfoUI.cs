using System;
using TMPro;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.UI;

public class MACharacterInfoUI : MAInfoPanelUI
{
    public GameObject m_possessButton;
    public GameObject m_recallButton;
    public GameObject m_editNameButton;
    
    public TMP_Text m_recallText;
    public Transform m_infoPanel;
    public Transform m_combatPanel;
    public Transform m_historyPanel;
    public TMP_Text m_title = null;
    
    [SerializeField]
    private float m_refreshTime = 0.5f;

    protected MATimer m_timer = null;
    protected MACharacterBase m_character = null;

    private List<MACharacterInfoElement> m_elements = new();
    protected override void Awake()
    {
        base.Awake();
        m_timer = new(m_refreshTime);
    }

    private void Update()
    {
        if (m_timer.IsFinished)
        {
            UpdateInfo();
        }
    }
    
    private void DestroyElements()
    {
        foreach(var field in m_elements)
        {
            Destroy(field.gameObject);
        }
        m_elements.Clear();
    }
    
    public void OnZoom()
    {
        if(m_character == null) return;
        
        MAParser.MoveCamera(m_character.transform.position, 20f);
        Close();
    }
    
    private NGRename m_rename;
    public void OnEditName()
    {
        if(m_character == null) return;
        
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        m_rename = NGRename.Create(m_character.Name, GotNewName);
    }
    
    public void GotNewName(string _newName)
    {
        if(m_character == null) return;
        
        m_character.Name = _newName;
    }
    
    public void OnRecall()
    {
        var action = MABuildingWorkerPanelLine.GetWorkerActionButton(m_character);
        
        if(action.action == null) return;
        
        action.action();
    }
    
    public void OnPossess()
    {
        if(GameManager.Me.CanPossess(m_character) == false) return;
        
        GameManager.Me.PossessObject(m_character);
        Close();
    }
        
    public override void Open(Transform _object)
    {
        MACharacterBase charBase = _object.GetComponent<MACharacterBase>();
        if (charBase != null)
        {
            base.Open(_object);
            
            DestroyElements();
            
            m_character = charBase;
            
            Setup();
                        
            UpdateInfo();
        }
    }
    
    private void AddInfo(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_infoPanel, _name, _value, _onClick)); }
    private void AddCombat(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_combatPanel, _name, _value, _onClick)); }
    private void AddHistory(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_historyPanel, _name, _value, _onClick)); }
    
    private string GetAllocatedBuilding(BCBase _base)
    {
        if(_base == null)
            return "None";
        if(_base.Building) return _base.Building.GetBuildingTitle();
        return _base.Title;
    }
    
    private Action GetBuildingZoomAction(BCBase _base)
    {
        if(_base == null) return null;
        return () => { MAParser.MoveCamera(_base.transform.position, 30f); this.Close(); };
    }
    
    private void Setup()
    {
        string characterType = "";
        string speed = "";
        
        if(m_character is MAWorker worker)
        {
            characterType = worker.m_workerInfo.m_displayName;
            speed = NGCardInfoGUI.GetSpeed(worker.m_workerInfo.m_highWalkSpeed);
        }
        else if(m_character.CreatureInfo != null)
        {
            characterType = m_character.CreatureInfo.m_displayName;
            speed = NGCardInfoGUI.GetSpeed(m_character.CreatureInfo.m_highWalkSpeed);
        }
        
        var gameState = m_character.CharacterGameState;

        bool isDog = m_character is MADog;

        if (isDog)
        {
            AddInfo("Type", () => /*characterType*/"Husky Dog");
            AddInfo("State", () => /*m_character.PeepAction.ToString()*/"Panting");
            AddInfo("Speed", () => speed);
            AddHistory("Possessed Time", () => gameState.m_timePossessed.FormatTime());
            AddHistory("Distance Travelled", () => $"{gameState.m_distanceTravelled:F2}m");
            AddHistory("Items Found", () => gameState.m_itemsCollected.ToString());
        }
        else
        {
            AddInfo("Type", () => characterType);
            AddInfo("State", () => m_character.PeepAction.ToString());
            AddInfo("Level", () => gameState.m_characterExperience.GetCharacterLevelName());
            AddInfo("Speed", () => speed);
            AddInfo("Experience", () => $"{m_character.Experience:F0}/{gameState.m_characterExperience.ExperienceRequiredForNextLevel():F0}");
            AddInfo("Health", () => $"{m_character.Health:F1}/{m_character.MaxHealth:F1}");
            AddInfo("Energy", () => $"{m_character.Energy:F1}/{m_character.MaxEnergy:F1}");

            AddInfo("Home", () => GetAllocatedBuilding(m_character.Home), GetBuildingZoomAction(m_character.Home));
            AddInfo("Job", () => GetAllocatedBuilding(m_character.Job), GetBuildingZoomAction(m_character.Job));

            AddCombat("Weapon", () => (gameState.m_weaponDesign != null && gameState.m_weaponDesign.HasDesign) ? gameState.m_weaponDesign.GetDominantMaterial() : "None");
            AddCombat("Armour", () => (gameState.m_armourDesign != null && gameState.m_armourDesign.HasDesign) ? gameState.m_armourDesign.GetDominantMaterial() : "None");
            AddCombat("Defense", () => $"{m_character.Armour.ArmourPoints:F0}/{m_character.MaxArmour:F0}");
            AddCombat("Attack", () => ((gameState.m_weaponDesign != null && gameState.m_weaponDesign.HasDesign) ? gameState.m_weaponDesign.m_attackScore : 0).ToString());

            AddHistory("Kills", () => gameState.m_kills.ToString());
            AddHistory("Knocked Down", () => gameState.m_timesKnockedDown.ToString());

            // Only show if we hired this character
            if (gameState.m_dayHired >= 0)
                AddHistory("Hired", () => $"Day { gameState.m_dayHired }");
            AddHistory("Possessed Time", () => gameState.m_timePossessed.FormatTime());
            AddHistory("Freewill Time", () => gameState.m_timeUnpossessed.FormatTime());
        }
    }
    
    private void UpdateInfo()
    {
        bool canPossess = GameManager.Me.CanPossess(m_character);
        m_possessButton.SetActive(canPossess);
        
        
        if (m_character == null)
        {
            Close();
            return;
        }

        bool isDog = m_character is MADog;
        bool isQuestDog = isDog && m_character.Name == "QuestDog";

        // We should move this to either knack or have a virtual function in MACharacterBase
        bool canRecall = m_character is MAWorker && m_character is not MAQuestGiver && m_character is not MATourist;
        bool canRename = m_character is not MACreatureBase && m_character is not MAQuestGiver && m_character is not MATourist && !isQuestDog;
        
        var action = MABuildingWorkerPanelLine.GetWorkerActionButton(m_character);
        
        if(action.description.IsNullOrWhiteSpace() == false)
        {
            m_recallText.text = action.description;
        }
        m_recallButton.SetActive(action.description.IsNullOrWhiteSpace() == false);
        m_editNameButton.SetActive(canRename);
        m_combatPanel.gameObject.SetActive(!isDog);
        
        foreach(var field in m_elements)
        {
            field.UpdateValue();
        }
        
        m_title.text = isQuestDog ? "Old Bane" : m_character.Name;
        
        m_timer.Set(m_refreshTime);
    }
}