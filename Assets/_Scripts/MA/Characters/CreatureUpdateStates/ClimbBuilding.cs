using System;
using UnityEngine;
using Random = UnityEngine.Random;

namespace MACharacterStates
{
	public class ClimbBuilding : CommonState
	{
		public ClimbBuilding(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		private const float c_stopDrag = 5f;

		[ReadOnlyInspector] private MABuilding m_building = null;

		[ReadOnlyInspector] private Tuple<Vector3, Block> m_jumpTarget;

		[ReadOnlyInspector] private Vector3 m_jumpPos = Vector3.zero;

		[ReadOnlyInspector] private float m_jumpDistance = 150;
		[ReadOnlyInspector] private float m_stayOnBuildingTimer = -1f;
		[ReadOnlyInspector] private float m_backupMass = 0;
		[ReadOnlyInspector] private float m_backupDrag = 0;

		[ReadOnlyInspector] private bool m_isJumping = false;
		[ReadOnlyInspector] private bool m_isArrived = false;

		private IgnoreAllCollisionsExcept m_ignoreCollisionsExcept = null;

		private MAWerewolf m_werewolf = null;

		public override void OnEnter()
		{
			base.OnEnter();

			m_werewolf = m_character as <PERSON>Werewolf;

			IgnoreAllCollisionsExcept ignoreCollisionsExcept =
				m_character.GetComponentInChildren<IgnoreAllCollisionsExcept>(true);
			ignoreCollisionsExcept.enabled = false;

			float bestDistance = float.MaxValue;
			MABuilding bestBuilding = null;
			Vector3 vectorFromBuilding = Vector2.zero;

			foreach(var building in NGManager.Me.m_maBuildings)
			{
				vectorFromBuilding = (m_character.transform.position.GetXZ() - building.transform.position.GetXZ());
				float distance = vectorFromBuilding.xzSqrMagnitude();
				if(distance < bestDistance)
				{
					bestDistance = distance;
					bestBuilding = building;
				}
			}

			if(bestBuilding == null)
			{
				ApplyState(CharacterStates.LookForTarget);
				return;
			}

			m_building = bestBuilding;
			m_jumpDistance = m_jumpDistance > bestDistance ? bestDistance : m_jumpDistance;
			vectorFromBuilding = vectorFromBuilding.normalized * Mathf.Sqrt(m_jumpDistance);
			m_jumpPos = bestBuilding.transform.position + vectorFromBuilding;
			m_gameStateData.m_speed = m_gameStateData.m_attackSpeed;
			m_character.SetMoveToPosition(m_jumpPos);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if(m_isJumping)
			{
				// float distLeft = (m_creature.transform.position - m_jumpTarget.Item1).sqrMagnitude;
				// if(distLeft < 0.3f && m_jumpTarget.Item2 != null)
				//{//we dont use distance because the bounds of a building are not accurate enough -> buildings have protruding details on their roofs and creature would mostly float.
				//OnLandedOnBuilding();
				//m_creature.transform.position = m_jumpTarget.Item1;
				//}
				return;
			}
			else if(m_stayOnBuildingTimer > -1)
			{
				if(m_stayOnBuildingTimer <= m_gameStateData.m_timeInState)
				{
					if(m_jumpTarget.Item2)
					{
						MABuilding building = m_jumpTarget.Item2.GetComponentInParent<MABuilding>();
						m_jumpTarget = new Tuple<Vector3, Block>(building.DoorPosOuter, null);
					}
					else
					{
						Vector3 randomAreaPos = new Vector3(Random.Range(-1, 1), 0, Random.Range(-1, 1)) *
						                        Random.Range(17, 20);
						m_jumpTarget = new Tuple<Vector3, Block>(m_werewolf.transform.position + randomAreaPos,
							m_jumpTarget.Item2);
					}

					m_werewolf.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
					Jump();
					return;
				}
				else
				{
					//m_stayOnBuildingTimer -= Time.deltaTime;
					return;
				}
			}
			else if(m_isArrived)
			{
				return;
			}
			else if(HasAgentArrived())
			{
				m_jumpTarget = MABuildingSupport.FindBuildingHighestPoint(m_building);
				m_backupMass = m_werewolf.RigidBody.mass;
				m_backupDrag = m_werewolf.RigidBody.linearDamping;
				Jump();
				OnLiftOffGround();
				return;
			}
		}

		public override void OnExit()
		{
			base.OnExit();
			m_werewolf.RigidBody.linearDamping = m_backupDrag;
			m_werewolf.RigidBody.mass = m_backupMass;
			m_werewolf.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
			m_werewolf.SetCustomGravity(true);
			m_werewolf.m_nav.Unpause();
			m_character.m_nav.Unpause();
		}

		private void Jump()
		{
			m_werewolf.LookAt(m_jumpTarget.Item1);
			m_werewolf.m_nav.Pause(true, true);
			m_werewolf.RigidBody.mass = 0;
			m_werewolf.RigidBody.linearDamping = 0;

			GameManager.Me.ClearGizmos("jumpTarget");
			GameManager.Me.AddGizmoPoint("jumpTarget", m_jumpTarget.Item1, 2, Color.blue);
			Vector3 targetVel = GetThrowVelocityAccurate(m_jumpTarget.Item1);
			if(targetVel.Approximately(Vector3.zero))
			{
				ApplyState(CharacterStates.LookForTarget);
				return;
			}

			m_werewolf.SetCustomGravity(false); // = targetVel;
			m_werewolf.RigidBody.linearVelocity = targetVel;

			Block blockTarget = m_jumpTarget.Item2;
			Collider mainCollider = m_werewolf.GetComponent<Collider>();
			Collider[] allColliders = m_werewolf.GetComponentsInChildren<Collider>(true);

			foreach(var collider in allColliders)
			{
				if(mainCollider != collider && collider is not MeshCollider) collider.gameObject.SetActive(false);
			}

			if(m_ignoreCollisionsExcept == null)
				m_ignoreCollisionsExcept = m_werewolf.GetComponentInChildren<IgnoreAllCollisionsExcept>(true);

			m_ignoreCollisionsExcept.enabled = true;

			if(blockTarget != null)
			{
				Collider[] buildingColliders = m_building.GetComponentsInChildren<Collider>();
				m_ignoreCollisionsExcept.ExceptObjectsWithComponents(buildingColliders, OnExpectedCollision);
				m_ignoreCollisionsExcept.m_onTerrainCollisionEnd -= OnLiftOffGround;
				m_ignoreCollisionsExcept.m_onTerrainCollisionEnd += OnLiftOffGround;
			}

			// m_werewolf.PlayAnim("JumpToTarget");//MACreatureBase.CreatureMoveAnimations.JumpToTarget);

			m_isArrived = false;
			m_isJumping = true;
			m_stayOnBuildingTimer = -1f;
		}

		private Vector3 GetThrowVelocity(Vector3 _targetPosition)
		{
			var throwDir =
				(_targetPosition - m_werewolf.transform.position /*.GroundPosition()*/).normalized; //GetXZNorm();
			throwDir.y = 1.3f;
			throwDir *= NGManager.Me.m_throwVelocityScaler;
			return throwDir;
		}

		private Vector3 GetThrowVelocityAccurate(Vector3 _targetPos)
		{
			const float c_throwSpeed = 6f;
			Vector3 pos = m_werewolf.transform.position;
			float time = (pos - _targetPos).magnitude / c_throwSpeed;
			var throwDir = Global3D.GetVelocityRequiredForPointToPointOverTime(pos, _targetPos, time);
			return throwDir;
		}

		private void OnLiftOffGround()
		{
			Debug.LogError("ClimbBuilding - OnLiftOffGround");
			m_werewolf.m_nav.Unpause();

			m_ignoreCollisionsExcept.m_onTerrainCollisionEnd -= OnLiftOffGround;
			m_ignoreCollisionsExcept.m_onTerrainCollisionStart -= OnReachGround;
			m_ignoreCollisionsExcept.m_onTerrainCollisionStart += OnReachGround;

			m_werewolf.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
		}

		private void OnReachGround()
		{
			m_werewolf.SetCustomGravity(true);
			m_werewolf.m_nav.Unpause();
			m_ignoreCollisionsExcept.enabled = false;
			m_ignoreCollisionsExcept.m_onTerrainCollisionStart -= OnReachGround;
			m_ignoreCollisionsExcept.m_onTerrainCollisionEnd -= OnLiftOffGround;

			m_werewolf.RigidBody.mass = m_backupMass;
			Collider col = m_werewolf.GetComponent<Collider>();
			Collider[] cols = m_werewolf.GetComponentsInChildren<Collider>(true);
			foreach(var collider in cols)
			{
				if(col != collider && collider is not MeshCollider)
				{
					collider.gameObject.SetActive(true);
				}
			}

			m_isJumping = false;
			m_isArrived = false;

			m_werewolf.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);

			ApplyState(CharacterStates.LookForTarget);
		}

		private void OnLandedOnBuilding()
		{
			m_werewolf.SetCustomGravity(true);
			Debug.Log($"{GetType().Name} - OnLandedOnBuilding");

			m_werewolf.RigidBody.linearVelocity = Vector3.zero;
			m_werewolf.RigidBody.mass = m_backupMass;

			m_backupDrag = m_werewolf.RigidBody.linearDamping;
			m_werewolf.RigidBody.linearDamping = c_stopDrag;

			m_werewolf.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.KINEMATIC);

			Collider col = m_werewolf.GetComponent<Collider>();
			Collider[] cols = m_werewolf.GetComponentsInChildren<Collider>(true);
			foreach(var collider in cols)
			{
				if(col != collider && collider is not MeshCollider)
				{
					collider.gameObject.SetActive(true);
				}
			}

			m_isJumping = false;
			m_isArrived = true;

			// m_werewolf.PlayAnim("howl");

			IgnoreAllCollisionsExcept ignoreCollisionsExcept =
				m_werewolf.GetComponentInChildren<IgnoreAllCollisionsExcept>(true);
			ignoreCollisionsExcept.enabled = false;
			m_stayOnBuildingTimer = m_werewolf.m_stayOnBuildingDuration;
		}

		private void OnExpectedCollision(Collider _collider)
		{
			OnLandedOnBuilding();
		}
	}
}
