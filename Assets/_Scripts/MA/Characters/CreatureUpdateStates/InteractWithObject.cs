using System;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	[Serializable]
	public class InteractWithDecoration : CommonState
	{
		public InteractWithDecoration(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		private const float c_maxInteractTime = 8f;
		private float m_interactDuration = -1f;
		public float m_maxDistanceBetween = 1f;

		public float m_delayAfterInteractComplete = 1f;
		private MATimer m_endDelayTimer = null;
		public string m_animClip = "Pee";
		public float m_speed = 1f;
		private bool hasFinished = false;
		public Action<MACharacterBase, TargetObject, InteractWithDecoration> m_interact = null;
		public Action<MACharacterBase, TargetObject, InteractWithDecoration> m_onFinished = null;

		protected (bool isValid, NGDecoration obj) IsValidDecoration(TargetObject _decoration)
		{
			if (_decoration == null)
				return (false, null);

			if (_decoration.TargetObjectType != TargetObject.TargetType.InteractionPointType)
				return (false, null);

			if (_decoration.gameObject.activeSelf == false)
				return (false, null);

			NGDecoration obj = _decoration.GetComponent<NGDecoration>();
			if (obj == null)
				return (false, null);

			if (obj.transform.parent != NGDecorationInfoManager.Me.m_decorationHolder)
				return (false, obj);

			return (true, obj);
		}

		public override void OnEnter()
		{
			base.OnEnter();

			hasFinished = false;
			m_interactDuration = -1f;
			TargetObject target = m_character.TargetObject;
			var isValid = IsValidDecoration(target);
			if (isValid.isValid)
			{
				m_character.m_nav.StopNavigation();
				m_character.m_onPathProcessed -= OnPathReturned;
				m_character.m_onPathProcessed += OnPathReturned;
				m_character.SetMoveToObject(target.gameObject, PeepActions.None);
				m_character.SetSpeed(m_speed);
			}
			else
			{
				Finish(0f);
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
			
			if(m_endDelayTimer != null)
			{
				if (m_endDelayTimer.IsFinished)
				{
					ApplyState(m_character.DefaultState);
				}
				return;
			}

			if (m_interactDuration >= 0f)
			{
				m_interactDuration += Time.deltaTime;
				if (m_interactDuration > c_maxInteractTime)
				{
					Finish(m_delayAfterInteractComplete);
				}

				return;
			}

			TargetObject target = m_character.TargetObject;
			Transform targetTr = target.transform;
			var validDecoration = IsValidDecoration(target);
			if (validDecoration.isValid == false)
			{
				Finish(m_delayAfterInteractComplete);
				return;
			}

			var targetPos = targetTr.position;
			var toTarget = (targetPos - m_character.transform.position);
			float dist = toTarget.xzMagnitude();
			var bounds = targetTr.GetComponentsInChildren<Collider>()
				.Find(x => x.enabled && x.isTrigger == false)?.bounds ?? new Bounds();

			float radSelf = m_character.GetComponent<Collider>()?.bounds.size.x ?? 0f;

			if (dist < m_maxDistanceBetween + radSelf + bounds.extents.GetXZVector2().magnitude)
			{
				m_interact?.Invoke(m_character, target, this);
				m_interactDuration = 0f;
			}
			else if (HasAgentArrived())
			{
				m_character.SetMoveToObject(target.gameObject, PeepActions.None);
				m_character.SetSpeed(m_speed);
			}
		}

		public override void OnExit()
		{
			base.OnExit();

			hasFinished = true;
			m_character.m_onPathProcessed -= OnPathReturned;
			m_character.StopCurrentAnimation();
		}

		public void LookAt(Vector3 _at, float _speed)
		{
			m_character.LookAt(_at, _speed, () =>
			{
				if (!m_character.PlayInteractWithDecorationAnim(m_animClip))
				{
					Finish(0f);
				}
			});
		}

		public void Finish(float _delay)
		{
			if (hasFinished)
				return;

			hasFinished = true;
			m_interactDuration = -1f;
			
			TargetObject target = m_character.TargetObject;
			m_onFinished?.Invoke(m_character, target, this);
			m_onFinished = null;
			if(_delay <= 0f)
				ApplyState(m_character.DefaultState);
			else
			{
				m_endDelayTimer = new MATimer(_delay);
			}
		}

		protected void OnPathReturned(NGMovingObject.TargetReachability _targetReachability)
		{
			switch (_targetReachability)
			{
				case NGMovingObject.TargetReachability.IsReachable:
					break;
				case NGMovingObject.TargetReachability.IsNotReachable:
				case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
				case NGMovingObject.TargetReachability.PathFindingAlreadyInProgress:
				case NGMovingObject.TargetReachability.PathFailure:
					Finish(0f);
					break;
			}
		}
	}
}