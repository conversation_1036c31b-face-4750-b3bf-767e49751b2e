using System;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	[Serializable]
	public class InteractAtPosition : CommonState
	{
		public InteractAtPosition(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		private const float c_maxInteractTime = 8f;
		private float m_interactDuration = -1f;
		private float m_maxDistanceBetween = 2f;

		public float m_delayAfterInteractComplete = 2f;

		private MATimer m_endDelayTimer = null;
		public string m_animClip = "Poo";
		public float m_speed = 1f;
		public Vector3 m_position = Vector3.zero;
		public float m_range = 0f;
		private bool hasFinished = false;
		public Action<MACharacterBase, InteractAtPosition> m_interact = null;
		public Action<MACharacterBase, InteractAtPosition> m_onFinished = null;

		protected virtual Vector3? ScanForValidSpot()
		{
			if(m_position != Vector3.zero)
			{
				return m_position;
			}
			return MACharacterBase.ScanForNavigableRandomPos(
				m_character.transform.position, 0f ,
				m_range > 0f ? m_range : m_character.CreatureInfo.m_visionRadius,
				true, GlobalData.AllowNavType.AnyPerfectNav);
		}

		public override void OnEnter()
		{
			base.OnEnter();

			hasFinished = false;
			m_interactDuration = -1f;
			Vector3? spot = ScanForValidSpot();
			if(spot == null)
				Finish(0f);
			m_character.m_nav.StopNavigation();
			m_character.m_onPathProcessed -= OnPathReturned;
			m_character.m_onPathProcessed += OnPathReturned;
			m_character.SetMoveToPosition((Vector3)spot);
			m_character.SetSpeed(m_speed);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
			
			if(m_endDelayTimer != null)
			{
				if (m_endDelayTimer.IsFinished)
				{
					ApplyState(m_character.DefaultState);
				}
				return;
			}

			if (m_interactDuration >= 0f)
			{
				m_interactDuration += Time.deltaTime;
				if(m_interactDuration > c_maxInteractTime)
				{
					Finish(m_delayAfterInteractComplete);
				}
				return;
			}

			if (HasAgentArrived())
			{
				m_interact?.Invoke(m_character, this);
				m_interactDuration = 0f;
				m_interact = null;
			}
		}
		
		public override void OnExit()
		{
			base.OnExit();

			hasFinished = true;
			m_character.m_onPathProcessed -= OnPathReturned;
			// m_character.RemoveAnimListener(OnAnimFinished);
		}

		public void LookAt(Vector3 _at, float _speed)
		{
			m_character.LookAt(_at, _speed, () =>
			{
				if (!m_character.PlayInteractAtPositionAnim(m_animClip))
				{
					Finish(0f);
				}
			});
		}

		public void Finish(float _delay)
		{
			if (hasFinished)
				return;

			hasFinished = true;
			m_interactDuration = -1;

			// m_character.RemoveAnimListener(OnAnimFinished);
			
			m_onFinished?.Invoke(m_character, this);
			m_onFinished = null;
			if(_delay <= 0f)
				ApplyState(m_character.DefaultState);
			else
			{
				m_endDelayTimer = new MATimer(_delay);
			}
		}
		
		protected void OnPathReturned(NGMovingObject.TargetReachability _targetReachability)
		{
			switch(_targetReachability)
			{
				case NGMovingObject.TargetReachability.IsReachable:
					break;
				case NGMovingObject.TargetReachability.IsNotReachable:
				case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
				case NGMovingObject.TargetReachability.PathFindingAlreadyInProgress:
				case NGMovingObject.TargetReachability.PathFailure:
					Finish(0f);
					break;
			}
		}
	}
}