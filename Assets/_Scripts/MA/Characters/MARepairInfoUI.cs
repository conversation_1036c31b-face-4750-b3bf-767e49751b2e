using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MAR<PERSON>airInfoUI : MAInfoPanelUI
{
    [SerializeField]
    protected TextMeshProUGUI m_titleText = null;
    
    [System.Serializable]
    public class WallSectionInfo
    {
        public TextMeshProUGUI m_repair = null;
    }
    
    [SerializeField]
    protected List<WallSectionInfo> m_sectionInfos = new();

    public override void Open(Transform _object)
    {
        //PathBreak pathBreak = _object.parent.GetComponent<PathBreak>();
       // if (pathBreak != null)
       {
           List<PathBreak> pathBreaksAvailable = new List<PathBreak>();
            for (int i = 0; i < PathBreak.s_allBreaks.Count; i++)
            {
                PathBreak pb = PathBreak.s_allBreaks[i];
                if (pb.RepairLevel < 1f)
                {
                    (string, bool) districtAtPoint = DistrictManager.Me.GetDistrictAtPoint(pb.transform.position);
                    if (districtAtPoint.Item2)
                    {
                        pathBreaksAvailable.Add(pb);
                    }
                }
            }

            for (int i = 0; i < pathBreaksAvailable.Count; i++)
            {
                PathBreak pb = pathBreaksAvailable[i];
                if (i >= m_sectionInfos.Count)
                {
                    m_sectionInfos.Add(new WallSectionInfo()
                    {
                        m_repair = Instantiate(m_sectionInfos[0].m_repair, m_sectionInfos[0].m_repair.transform.parent)
                    });
                }
                
                m_sectionInfos[i].m_repair.text = (pb.RepairLevel * 100).ToString("0.00") + " %";
            }

            base.Open(_object);
        }
    }
}
