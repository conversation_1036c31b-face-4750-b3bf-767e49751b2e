using UnityEngine;

public class MAInfoPanelUI : MonoBehaviour
{
    public string m_name;
    [SerializeField]
    protected string m_closeAnimationTrigger = "Close";
    
    protected Animator m_animator = null;
    private bool m_isOpen = false;
    
    public bool IsOpen => m_isOpen;
    
    protected virtual void Awake()
    {
        if(m_isOpen == false)
            gameObject.SetActive(false);
        m_animator = GetComponent<Animator>();
    }
    
    public virtual void Open(Transform _object)
    {
        m_isOpen = true;
        gameObject.SetActive(true);
    }

    public virtual void Close()
    {
        m_isOpen = false;
        if (m_animator == null)
        {
            OnCloseAnimationFinished();
            return;
        }
        m_animator.SetTrigger(m_closeAnimationTrigger);
    }

    public virtual void OnCloseAnimationFinished()
    {
        gameObject.SetActive(m_isOpen);
    }
}