using System.Collections;
using UnityEngine;

public class MAQuestDog : MAQuestBase
{
    public Transform m_dogHolder;
    public MAScentTrail m_scentTrail;

    protected MAAnimal m_dog;
    private int m_currentScentTrail = -1;
    private bool m_isShowingTrail = false;
    private float m_loadScentTrailProgress = -1.0f;
    
    private bool m_isPossessed = false;
    private bool? m_isRunning = null;
    private bool m_possessionAudioPlayed = false;
    public AkEventHolder m_onPossessAudio = new();
    public AkEventHolder m_onUnpossessAudio = new();
    public AkSwitchHolder m_onPossessMusic = new();
    public AkSwitchHolder m_onUnpossessMusic = new();
    public AkSwitchHolder m_onRunMusic = new();
    public AkSwitchHolder m_onWalkMusic = new();
    
    public bool m_disableRunningMusic = false;

    private void Update()
    {
        UpdateScentTrail();
        CheckRunningState();
        CheckPossessionState();
    }

    protected void UpdateScentTrail()
    {
        if (m_status == QuestStatus.InProgress && m_dog != null)
        {
            var possessed = GameManager.Me.PossessedObject;

            if (possessed == m_dog.gameObject && m_currentScentTrail >= 0)
            {
                if (!m_isShowingTrail)
                {
                    if (m_loadScentTrailProgress >= 0.0f)
                    {
                        m_scentTrail.Show(m_dog.gameObject, m_currentScentTrail, true, m_loadScentTrailProgress);
                        m_loadScentTrailProgress = -1.0f;
                    }
                    else
                    {
                        m_scentTrail.Show(m_dog.gameObject, m_currentScentTrail);
                    }
                    m_isShowingTrail = true;
                }

                if (m_scentTrail.GetProgress() >= 1.0f)
                {
                    m_currentScentTrail++;

                    if (m_currentScentTrail >= m_scentTrail.m_splineContainer.Splines.Count)
                    {
                        m_currentScentTrail = -1;
                        m_scentTrail.Show(null);
                    }
                    else
                    {
                        m_scentTrail.Show(m_dog.gameObject, m_currentScentTrail, true);
                    }
                }
            }
            else
            {
                if (m_isShowingTrail)
                {
                    m_scentTrail.Show(null);
                    m_isShowingTrail = false;
                }
            }
        }
    }

    protected void ShowScentTrail(int _scentTrailIndex)
    {
        m_currentScentTrail = _scentTrailIndex;
    }

    protected override void OnPostLoad()
    {
        var possessed = GameManager.Me.PossessedObject;
        if (m_dog != null)
        {       
            bool isPossessed = possessed == m_dog.gameObject;
            PlayPossessionMusic(isPossessed);

            m_isPossessed = isPossessed;
            m_isRunning = null;
        }
    }
    
    override protected void SetQuestGiverParent()
    {
        base.SetQuestGiverParent();

        if (m_dog != null)
        {
            m_dog.transform.SetParent(m_dogHolder);
            m_dog.transform.eulerAngles = m_dogHolder.eulerAngles;
        }
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        if (m_status == QuestStatus.InProgress)
        {
            m_dog = NGManager.Me.m_MAAnimalList.Find(x => x.GetTypeInfo() == "QuestDog");
        }
        else
        {
            m_scentTrail.Show(null);
            PlayRunningMusic(false);
            PlayPossessionMusic(false);
        }
    }

    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("ShowScentTrail"))
        {
            int trailIndex = -1;

            if (int.TryParse(split[1], out trailIndex))
            {
                ShowScentTrail(trailIndex);
            }
        }
        else if (split[0].Contains("DisableRunningMusic"))
        {
            SetRunningMusicDisabled(true);
        }
    }

    public class ScentTrailSaveState
    {
        [Save] public int m_currentScentTrail;
        [Save] public float m_currentScentTrailProgress;
    }

    protected ScentTrailSaveState GetScentTrailSaveState()
    {
        ScentTrailSaveState saveState = new ScentTrailSaveState();
        saveState.m_currentScentTrail = m_currentScentTrail;
        saveState.m_currentScentTrailProgress = m_currentScentTrail < 0 ? -1.0f : m_scentTrail.GetProgress();
        return saveState;
    }

    protected bool IsRunningMusicDisabled()
    {
        return m_disableRunningMusic == false;
    }
    
    protected void SetRunningMusicDisabled(bool disabled)
    {
        m_disableRunningMusic = disabled;
    }

    protected void SetScentTrailSaveState(ScentTrailSaveState _saveState)
    {
        if (_saveState != null)
        {
            m_currentScentTrail = _saveState.m_currentScentTrail;
            m_loadScentTrailProgress = _saveState.m_currentScentTrailProgress;
        }
    }

    private void PlayRunningMusic(bool _isRunning)
    {
        if (m_dog == null) return;
        if (_isRunning)
        {
            m_onRunMusic.SetAsOverride();// Play(m_dog.gameObject, AkEventHolder.EBus.Music);
        }
        else
        {
            m_onWalkMusic.SetAsOverride();//.Play(m_dog.gameObject, AkEventHolder.EBus.Music);
        }
    }

    private void PlayPossessionSound(bool _isPossessed)
    {
        if (_isPossessed)
        {
            m_onPossessAudio.Play(m_dog.gameObject);
        }
        else
        {
            m_onUnpossessAudio.Play(m_dog.gameObject);
        }
    }
    
    private void PlayPossessionMusic(bool _isPossessed)
    {
        if (m_dog == null) return;
        if (_isPossessed)
        {
            m_onPossessMusic.SetAsOverride();// Play(m_dog.gameObject, AkEventHolder.EBus.Music);
        }
        else
        {
            m_onUnpossessMusic.SetAsOverride();//Play(m_dog.gameObject, AkEventHolder.EBus.Music);
        }
    }

    private void CheckRunningState()
    {
        if (m_isPossessed && !m_disableRunningMusic)
        {
            bool isRunning = (m_dog.m_nav.TargetSpeed) >= m_dog.m_possessionFwdWalkSpeed + (m_dog.m_possessionFwdRunSpeed - m_dog.m_possessionFwdWalkSpeed) * 0.5f;
            if (m_isRunning == null || (isRunning != (bool)m_isRunning))
            {
                PlayRunningMusic(isRunning);
                m_isRunning = isRunning;
            }
        }
    }

    private void CheckPossessionState()
    {
        var possessed = GameManager.Me.PossessedObject;
        if (m_dog != null)
        {
            bool isPossessed = possessed == m_dog.gameObject;

            if (isPossessed != m_isPossessed)
            {
                PlayPossessionSound(isPossessed);
                PlayPossessionMusic(isPossessed);
                m_isPossessed = isPossessed;
            }
        }
    }
}
