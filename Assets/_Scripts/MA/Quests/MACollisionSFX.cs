using UnityEngine;

public class MACollisionSFX : MonoBehaviour
{
    public enum CollisionSFXType
    {
        Everything,
        Terrain,
        Character,
        Hero
    }

    [System.Serializable]
    public class CollisionSFXData
    {
        public CollisionSFXType m_type = CollisionSFXType.Everything;
        public int m_maxTriggers = -1;
        public float m_minRelativeSpeed = -1.0f;
        public AkEventHolder m_audio;

        private int m_triggerCount = 0;

        public bool Play(GameObject _go, Collision _collision)
        {
            if (m_audio != null)
            {
                if (m_maxTriggers < 0 || m_triggerCount < m_maxTriggers)
                {
                    if (IsValidCollider(_collision))
                    {
                        if(m_minRelativeSpeed < 0.0f || _collision.relativeVelocity.sqrMagnitude > (m_minRelativeSpeed * m_minRelativeSpeed))
                        {
                            m_audio.Play(_go);
                            m_triggerCount++;
                            return true;
                        } 
                    }
                }
            }

            return false;
        }

        private bool IsValidCollider(Collision _collision)
        {
            switch(m_type)
            {
                case CollisionSFXType.Terrain:
                    return _collision.gameObject == GlobalData.Me.m_moaTerrain.gameObject;
                case CollisionSFXType.Character:
                    return _collision.gameObject.GetComponent<MACharacterBase>() != null;
                case CollisionSFXType.Hero:
                    return _collision.gameObject.GetComponent<MAHeroBase>() != null;
                default:
                    return true;
            }
        }
    }

    public float m_minRepeat = 0.2f;
    public CollisionSFXData[] m_audioList = new CollisionSFXData[1];

    private float m_lastPlayTime = -1.0f;

    // Old references, remove once updated
    public AkEventHolder m_collisionAudio;
    public AkEventHolder m_secondaryCollisionAudio;
    
    void OnCollisionEnter(Collision _collision)
    {
        if(Time.time > m_lastPlayTime + m_minRepeat)
        {
            foreach(var sfx in m_audioList)
            {
                if(sfx.Play(gameObject, _collision))
                {
                    m_lastPlayTime = Time.time;
                    return;
                }
            }
        }
    }
}
