using UnityEngine;
using UnityEngine.Splines;

public class MAScentParticle : MonoBehaviour
{
    [Header("Deformation Settings")]
    public float m_noiseAmplitude = 2.0f; // Amplitude of noise for deformation
    public float m_noiseFrequency = 0.15f; // Frequency of noise
    public float m_deformationSpeed = 0.15f; // Speed of deformation evolution

    private ParticleSystem m_particleSystem;

    private float m_progress;
    private float m_timeOffset;

    private Vector3 m_splinePosition;
    private Vector3 m_finalPosition;

    public float Progress { get => m_progress; }

    private void Awake()
    {
        m_particleSystem = GetComponent<ParticleSystem>();
    }

    private void Update()
    {
        UpdatePerlin();
        transform.localPosition = m_finalPosition;
    }

    public void Init(Spline _spline, float _progress)
    {
        m_timeOffset = 0.0f;// UnityEngine.Random.Range(0f, 100f); // Randomize time offset for unique effects

        m_progress = _progress;
        m_splinePosition = _spline.EvaluatePosition(_progress);

        Update();
        gameObject.SetActive(true);
        m_particleSystem.Play(true);
    }

    public void Hide()
    {
        m_particleSystem.Stop(true);
    }

    private void UpdatePerlin()
    {
        float time = Time.time * m_deformationSpeed + m_timeOffset;

        float noise = Mathf.PerlinNoise(m_splinePosition.x * m_noiseFrequency + time, m_splinePosition.z * m_noiseFrequency + time);
        Vector3 deformation = new Vector3(noise - 0.5f, noise - 0.5f, noise - 0.5f) * m_noiseAmplitude;
        m_finalPosition = m_splinePosition + deformation;
    }
}
