using UnityEngine;
using UnityEngine.Splines;

public class MATrailSection : MonoBehaviour
{
    public LineRenderer m_lineRenderer;
    public float m_startProgress;
    public float m_endProgress;

    [Header("Deformation Settings")]
    public float m_noiseAmplitude = 2.0f; // Amplitude of noise for deformation
    public float m_noiseFrequency = 0.15f; // Frequency of noise
    public float m_deformationSpeed = 0.15f; // Speed of deformation evolution

    private float m_timeOffset;
    private float m_showValue = 0.0f;
    private float m_showRate = 0.0f;

    private Vector3[] m_splinePositions;
    private Vector3[] m_finalPositions;

    public void Init(Spline _spline, float _startProgress, float _endProgress)
    {
        m_startProgress = _startProgress;
        m_endProgress = _endProgress;
        m_timeOffset = 0.0f;// UnityEngine.Random.Range(0f, 100f); // Randomize time offset for unique effects
        m_showValue = 0.0f;
        m_showRate = 0.5f;

        int posCount = m_lineRenderer.positionCount;
        float progress = m_startProgress;
        float deltaProgress = (m_endProgress - m_startProgress) / posCount;

        m_splinePositions = new Vector3[posCount];
        m_finalPositions = new Vector3[posCount];

        for (int i = 0; i < posCount; i++)
        {
            m_splinePositions[i] = _spline.EvaluatePosition(progress);
            progress += deltaProgress;
        }

        Update();
        gameObject.SetActive(true);
    }

    private void Update()
    {
        UpdatePerlin();
        UpdateShowValue();
        m_lineRenderer.SetPositions(m_finalPositions);
    }

    private void UpdatePerlin()
    {
        float time = Time.time * m_deformationSpeed + m_timeOffset;

        for (int i = 0; i < m_splinePositions.Length; i++)
        {
            float noise = Mathf.PerlinNoise(m_splinePositions[i].x * m_noiseFrequency + time, m_splinePositions[i].z * m_noiseFrequency + time);
            Vector3 deformation = new Vector3(noise - 0.5f, noise - 0.5f, noise - 0.5f) * m_noiseAmplitude;
            m_finalPositions[i] = m_splinePositions[i] + deformation;
        }
    }

    private void UpdateShowValue()
    {
        if (m_showValue < 1.0f)
        {
            float indexValue = (1.0f - m_showValue) * (m_finalPositions.Length - 1);
            int index = Mathf.FloorToInt(indexValue);
            Vector3 showPos = m_finalPositions[index];

            if (index < m_finalPositions.Length - 1)
            {
                float lerpValue = indexValue - index;
                showPos = Vector3.Lerp(showPos, m_finalPositions[index + 1], lerpValue);
            }

            for (int i = 0; i <= index; i++)
            {
                m_finalPositions[i] = showPos;
            }
        }

        m_showValue += m_showRate * Time.deltaTime;

        if (m_showValue >= 1.0f)
        {
            m_showValue = 1.0f;
            m_showRate = 0.0f;
        }
        else if (m_showValue <= 0.0f)
        {
            m_showValue = 0.0f;
            m_showRate = 0.0f;
            gameObject.SetActive(false);
        }
    }

    public void Hide()
    {
        m_showRate = -0.5f;
    }
}
