using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif


public class MAQuestPirateSmugglers : MAQuestBase
{
    [Header(nameof(MAQuestPirateSmugglers))]
    [SerializeField]
    private string m_shipResourcePath = "_Prefabs/Quests/MAShipControlled";
    [SerializeField] 
    private Transform m_pirateShipStartingPos = null, m_smugglerBoatStartingPos = null;
    [SerializeField]
    private bool m_replaceOtherPirateShip = false;
    [SerializeField][ReadOnlyInspector]
    private MAShip m_ship = null;

    public override List<NGMovingObject> GetPossessableMovers()
    {
        return new List<NGMovingObject>() { m_ship.GetComponent<NGMovingObject>() };
    }

    public class SaveLoadQuestPirateSmugglers : SaveLoadQuestBaseContainer
    {
        public MAShip.Save_Ship m_pirateShipSaveData = null;
        
        public SaveLoadQuestPirateSmugglers() : base() { }
        public SaveLoadQuestPirateSmugglers(MAQuestBase _base) : base(_base) { }
        [Save] public List<MAQuestInteraction.SaveState> m_interactionSaveStates;
    }
    
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestPirateSmugglers(this);
        if (m_ship != null)
        {
            saveContainer.m_pirateShipSaveData = m_ship.Save();
        }
        else
        {
            saveContainer.m_pirateShipSaveData = null;
        }

        return saveContainer;
    }

    private MAShip.Save_Ship m_pirateShipSaveData = null;
    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestPirateSmugglers;
        if (saveContainer != null)
        {
            m_pirateShipSaveData = saveContainer.m_pirateShipSaveData;
        }
    }

    private void LoadPirateShip()
    {
        if (m_status == QuestStatus.Active || m_status == QuestStatus.Invalid) return;
        GameObject shipObj = Resources.Load<GameObject>(m_shipResourcePath);
        m_ship = Instantiate(shipObj, m_pirateShipStartingPos, false).GetComponent<MAShip>();

        MAQuestPirate[] existingPirateQuest =
            GameObject.FindObjectsByType<MAQuestPirate>(FindObjectsSortMode.None);
        if (existingPirateQuest.Length > 0)
        {
            Transform existingPirateShipTr = existingPirateQuest[0].transform
                .Find("PirateShip/PirateShipMovement/SK_PirateShip");
            if (existingPirateShipTr != null)
            {
                if (m_replaceOtherPirateShip)
                {
                    Transform pTr = m_ship.transform;
                    pTr.position = existingPirateShipTr.position;
                    pTr.rotation = existingPirateShipTr.rotation;
                }

                existingPirateShipTr.gameObject.SetActive(false);
                //Destroy(existingPirateShipTr.gameObject);
            }
        }
        
        GameManager.Me.m_onPossess -= OnPossess;
        GameManager.Me.m_onPossess += OnPossess;

        if (m_pirateShipSaveData == null)
        {
            m_pirateShipSaveData = m_ship.Save();
        }
        else
        {
            m_ship.Init(m_pirateShipSaveData);
        }
    }

    protected void OnPossess(bool _enabled, NGLegacyBase _obj)
    {
        if (_enabled == false)
        {
            GameManager.Me.m_onPossess -= OnPossess;
        }
    }
    
    override public void SetQuestStatus(QuestStatus _status)
    {
        bool changed = _status != m_status;
        base.SetQuestStatus(_status);
        
            //if (changed)
        {
            switch (_status)
            {
                case QuestStatus.InProgress:
                    if (m_pirateShipSaveData == null)
                    {
                        LoadPirateShip();
                    }
                    break;
            }
        }
    }
    
    protected override void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        
        LoadPirateShip();
    }

    public void Reset()
    {
        if (m_ship)
        {
            Destroy(m_ship.gameObject);
        }
        m_pirateShipSaveData = null;
        SetQuestStatus(QuestStatus.Active);
    }
    
}


#if UNITY_EDITOR
[CustomEditor(typeof(MAQuestPirateSmugglers))]
public class MAQuestPirateSmugglersEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        var quest = target as MAQuestPirateSmugglers;
        if (Application.isPlaying && GameManager.Me != null && GameManager.Me.LoadComplete && GUILayout.Button("Simulate Start of Quest"))
        {
            quest.SetQuestStatus(MAQuestBase.QuestStatus.InProgress);
        }
        if (Application.isPlaying && GameManager.Me != null && GameManager.Me.LoadComplete && GUILayout.Button("Reset Quest"))
        {
            quest.SetQuestStatus(MAQuestBase.QuestStatus.Active);
            quest.Reset();
        }
    }
}
#endif
