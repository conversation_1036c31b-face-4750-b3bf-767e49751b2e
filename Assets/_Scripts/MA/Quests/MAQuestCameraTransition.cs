using UnityEngine;
using UnityEngine.Events;

public class MAQuestCameraTransition : MonoBehaviour
{
    public Transform m_focus;
    public Transform m_position;
    public float m_time = 1.0f;
    public UnityEvent m_onComplete;

    public void Transition()
    {
        GameManager.Me.CameraTransition(m_focus.position, m_position.position, m_time, OnComplete);
    }

    public void OnComplete()
    {
        m_onComplete?.Invoke();
    }
}
