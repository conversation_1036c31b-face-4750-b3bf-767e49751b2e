using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Input = UnityEngine.Input;

public class ResearchManagerScrollRect : ScrollRect
{
    public override void OnDrag(PointerEventData eventData)
    {
        if(MAResearchManagerUI.Me.m_editMode == false || MAResearchManagerUI.Me.NodeDragMode == false)
        {
            if (eventData.button == PointerEventData.InputButton.Right)
                eventData.button = PointerEventData.InputButton.Left;
            base.OnDrag(eventData);
        }
    }

    public override void OnBeginDrag(PointerEventData eventData)
    {
        if (eventData.button == PointerEventData.InputButton.Right)
            eventData.button = PointerEventData.InputButton.Left;
            
        base.OnBeginDrag(eventData);
    }

    public override void OnEndDrag(PointerEventData eventData)
    {
        if (eventData.button == PointerEventData.InputButton.Right)
            eventData.button = PointerEventData.InputButton.Left;
        
        base.OnEndDrag(eventData);
    }
    
    public void SnapTo(RectTransform target)
    {
        var pos = transform.InverseTransformPoint(content.position) - transform.InverseTransformPoint(target.position);
        SetContentAnchoredPosition(pos);
    }
}
