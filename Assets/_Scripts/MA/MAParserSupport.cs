using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using TMPro;
using UnityEngine;

public class MAParserSupport 
{
    static string m_callingStep = "";
    public class ConverterInfo
    {
        public ConverterInfo(string _name, Func<string[], object> _converter, string _example)
        {
            m_name = _name;
            m_converter = _converter;
            m_example = _example;
        }
        public string m_name;
        public Func<string[], object> m_converter;
        public string m_example;
    }
    public static Dictionary<Type, ConverterInfo> m_specialConverters = new Dictionary<Type, ConverterInfo>()
    {
        {typeof(MAOrderInfo), new ConverterInfo("OrderInfo", ConvertOrderInfo, "OrderInfo[22]")},
        {typeof(MAMessageType), new ConverterInfo("Message", ConvertMessageType, "Message[22]")},
        {typeof(MAGUIBase), new ConverterInfo("GUI", ConvertGUI, "GUI[MessageDialog]")},
        {typeof(ReactPickup), new ConverterInfo("Pickup", ConvertPickup, "Pickup[Wood]")},
        {typeof(MAWorker), new ConverterInfo("Worker", ConvertWorker, "Worker[22]")},
        {typeof(BCBase), new ConverterInfo("BuildingComponent", ConvertBuildingComponent, "BuildingComponent[ActionFactory]")},
        {typeof(Block), new ConverterInfo("Block", ConvertBuildingBlock,"BuildingBlock[22].FactoryDoor")},
        {typeof(MABuilding), new ConverterInfo("Building", ConvertBuilding, "Building[22] or Building[MetalMine]")},
        {typeof(NGDecoration), new ConverterInfo("Decoration", ConvertDecoration, "Decoration[MA_Tardis_Crypt]")},
        {typeof(HashSet<MABuilding>), new ConverterInfo("ComponentBuilding", ConvertComponentBuilding, "Components[ActionFactory]")},
        {typeof(Vector3), new ConverterInfo("Pos", ConvertPos, "Pos[1;2;3] or Pos[1;2]")},
        {typeof(ReactDistrictTable), new ConverterInfo("Region", (passSplit) => { return ReactDistrictTable.GetInfo(passSplit[1]); }, "District[Iron]")},
        {typeof(NGBlockInfo), new ConverterInfo("BuildingInfo", (passSplit) => { return NGBlockInfo.GetInfo(passSplit[1]); }, "BuildingInfo[22]")},
        {typeof(MAComponentInfo), new ConverterInfo("ComponentInfo", (passSplit) => { return MAComponentInfo.GetInfo(passSplit[1]); }, "ComponentInfo[ActionClayMine]")},
        {typeof(MACreatureInfo), new ConverterInfo("CreatureInfo", (passSplit) => { return MACreatureInfo.GetInfo(passSplit[1]); }, "CreatureInfo[Zombie]")},
        {typeof(MACreatureSpawnInfo), new ConverterInfo("SpawnPoint", (passSplit) => { return MACreatureSpawnInfo.GetInfo(passSplit[1]); }, "SpawnPoint[DayNightTestOneGiant]")},
        {typeof(MASpawnByDayInfo), new ConverterInfo("SpawnPoint", (passSplit) => { return MASpawnByDayInfo.GetInfo(int.Parse(passSplit[1])); }, "SpawnPoint[1]")},
        {typeof(MAQuestInfo), new ConverterInfo("Quest", (passSplit) => { return MAQuestInfo.GetInfo(passSplit[1]); }, "Quest[PayTheMoney]")},
        {typeof(NGMovingObject), new ConverterInfo("Character", ConvertCharacter, "Character[22]")},
        {typeof(NGCarriableResource), new ConverterInfo("Resource", (passSplit) => { return NGCarriableResource.GetInfo(passSplit[1]); }, "Resource[PeoplesFavor]")},
        {typeof(NGBusinessGift), new ConverterInfo("Gift", (passSplit) => { return NGBusinessGift.GetInfo(passSplit[1]); }, "Gift[PeoplesFavor]")},
        {typeof(GameObject), new ConverterInfo("GameObject", ConvertGameObject,"GameObject[Factory_Output>StockOut] ")},
        {typeof(MACharacterBase), new ConverterInfo("Character", ConvertCharacter, "Character[22]")},
        {typeof(MAFlowCharacter), new ConverterInfo("FlowCharacter", (passSplit) => { return MAFlowCharacter.FindCharacter(passSplit[1]); }, "FlowCharacter[22]")},
        {typeof(MAWorkerInfo), new ConverterInfo("WorkerInfo", (passSplit) => { return MAWorkerInfo.GetInfo(passSplit[1]); }, "Creature[Zombie]")},
        {typeof(MAResearchInfo), new ConverterInfo("ResearchInfo", (passSplit) => { return MAResearchInfo.GetInfo(passSplit[1].Trim()); }, "ResearchInfo[22]")},
        {typeof(PathManager.Path), new ConverterInfo("Path", ConvertPath, "Path[1;3]")},
        {typeof(MAAnimal), new ConverterInfo("Animal", ConvertAnimal, "Animal[22]")},
        {typeof(Quaternion), new ConverterInfo("Rot", ConvertRot, "Rot[1;2;3] or Rot[1]")},
    };
    private static Dictionary<Type, (string, Func<string[], object>)> m_baseConverters = new Dictionary<Type, (string, Func<string[], object>)>()
    {
        {typeof(int), ("", (argStr) =>
        {
            if (int.TryParse(argStr[0], out var value)) 
                return value;
            return null;
        })},
        {typeof(float), ("", (argStr) =>
        {
            if (floatinv.TryParse(argStr[0], out var value)) 
                return value;
            return null;
        })},
        {typeof(bool), ("", (argStr) =>
        {
            if (bool.TryParse(argStr[0], out var value)) 
                return value;
            return null;
        })},
        {typeof(string), ("", (argStr) =>
        {
            if(argStr[0] != null && argStr[0].Trim() == "Null")
                return "";
            return argStr[0];
        })}
    };
    static string m_currentLine = "";
    public static bool TryParse(string _what, out bool _result, string _callingStep = "", string _class = "MAParser")
    {
        m_currentLine = _what;
        _result = true;
        if(_what == null)
            return false;
        object result;
        var lines = ParseString(_what);
        if (lines == null)
        {
            return false;
        }
        foreach (var line in lines)
        {
            var isError = false;
            if(line.m_args.Count == 0)
            {
                result = TryParse(line.m_functionName, null, out isError, _callingStep, _class);
            }
            else
            {
                result = TryParse(line.m_functionName, line.m_args, out isError, _callingStep, _class);
            }

            if (isError)
                return false;
            if (result is bool)
                _result = (bool) result & _result;
            /*if(result == null)
                return false;*/
        }
        return true;
    }
    public static bool TryParseGetValue(string _what, out object _result, string _callingStep = "", string _class = "MAParser")
    {
        m_currentLine = _what;
        _result = true;
        if(_what == null)
            return false;
        object result;
        var lines = ParseString(_what);
        foreach (var line in lines)
        {
            var isError = false;
            if(line.m_args.Count == 0)
            {
                _result = TryParse(line.m_functionName, null, out isError, _callingStep, _class);
            }
            else
            {
                _result = TryParse(line.m_functionName, line.m_args, out isError, _callingStep, _class);
            }
        }
        return true;
    }
    public static object TryParse(string functionName, List<string> args, out bool _isError, string _callingStep = "", string _class = "MAParser")
    {
        try
        {
            m_callingStep = _callingStep;
            _isError = false;
            // Get all methods with the given name
            var methods = Type.GetType(_class).GetMethodsCached().Where(m => m.Name == functionName).ToArray();
            if(methods == null || methods.Length == 0)
            {
                MAParser.ParserError($"Function '{functionName}' not found @ {_callingStep}");
                _isError = true;
                return null;
            }
            // Find the method with matching parameter types
            if(args == null || args.Count == 0)
            {
                var noArgsMethod = methods.FirstOrDefault(m => m.GetParameters().Length == 0);
                if (noArgsMethod != null)
                {
                    var result = noArgsMethod.Invoke(null, null);
                    if(result != null)
                    {
                        return result;
                    }
                    return true;
                }
            }
            MethodInfo bestMatchingMethod = null;
            object[] bestPassObjs = new object[args.Count];
            
            foreach (MethodInfo method in methods)
            {
                var prams = method.GetParameters();
                if (prams.Length == args.Count)
                {
                    var passObjs = new object[args.Count];
                    bool allParamsMatch = true;
                    bool noBaseType = true;
                    for (int i = 0; i < args.Count; i++)
                    {
                        if (ConvertArgument(args[i], prams[i], out var tObject, out var baseType) == false)
                        {
                            allParamsMatch = false;
                            break;
                        }

                        if (baseType)
                            noBaseType = false;
                        passObjs[i] = tObject;
                    }

                    if (allParamsMatch)
                    {
                        if(bestMatchingMethod == null)
                        {
                            bestMatchingMethod = method;
                            bestPassObjs = passObjs;
                        }
                        else if(noBaseType)
                        {
                            bestMatchingMethod = method;
                            bestPassObjs = passObjs;
                        }
                    }
                }
            }
            if (bestMatchingMethod == null)
            {
                MAParser.ParserError($"Function '{functionName}' not found with matching parameter count or types. @ {_callingStep}");
                return null;
            }
            //Check to See if method has a [ParserSave] attribute if so add matchingMethord & bestPassObjs to a list
            var ret = bestMatchingMethod.Invoke(null, bestPassObjs);
            return ret;
        }
        catch (Exception ex)
        {
            MAParser.ParserError($"Function '{functionName}' failed to execute: {ex.Message} @ {_callingStep}\n{ex.StackTrace}");
            _isError = true;
            return null;
        }
    }
    public static bool ConvertArgument(string argStr, ParameterInfo parameter, out object _object, out bool _base)
    {
        _object = null;
        if (m_baseConverters.TryGetValue(parameter.ParameterType, out var baseConverter))
        {
            _base = true;
            _object =baseConverter.Item2(new string[] { argStr });
            if (_object == null)
                return false;
            return true;
        }

        _base = false;
        if (m_specialConverters.TryGetValue(parameter.ParameterType, out var converter) == false)
            return false;
        var passSplit = argStr.Split('[', ']');
        if (passSplit.Length != 3 || passSplit[0].Trim().Equals(converter.m_name, StringComparison.OrdinalIgnoreCase) == false)
            return false;
        _object = converter.m_converter(passSplit);
        return true;
    }
    static public object ConvertOrderInfo(string[] passSplit)
    {
        var orderInfo = MAOrderInfo.GetInfo(passSplit[1]);
        if (orderInfo == null)
        {
            MAParser.ParserError($"No order info with ID '{passSplit[1]}' @ {m_callingStep}");
            return false;
        }
        return orderInfo;
    }
    static object ConvertMessageType(string[] passSplit)
    {
        var messageType = MAMessageType.GetInfo(passSplit[1]);
        if(messageType == null)
        {
            MAParser.ParserError($"No message type with ID '{passSplit[1]}' @ {m_callingStep}");
        }
        return messageType;
    }
    static public object ConvertGUI(string[] passSplit)
    {
        var gui = MAGUIBase.Find(passSplit[1]);
        if (gui == null)
        {
            MAParser.ParserError($"No GUI with ID '{passSplit[1]}' @ {m_callingStep}");
        }
        return gui;
    }
    static object ConvertPickup(string[] passSplit)
    {
        var pickups = GlobalData.Me.m_pickupsHolder.GetComponentsInChildren<ReactPickup>();
        int lookForCount = 0;
        if (passSplit[3].IsNullOrWhiteSpace() == false)
        {
            int.TryParse(passSplit[2], out lookForCount);
        }
        var count = 0;
        foreach (var p in pickups)
        {
            if (p.Name.Equals(passSplit[1]))
            {
                if (count++ >= lookForCount)
                {
                    return p;
                }
            }
        }
        MAParser.ParserError($" No carryable [{passSplit[1]}] with ID '{passSplit[2]}' modifier @ {m_callingStep}");
        return null;
    }
    static object ConvertWorker(string[] passSplit)
    {
        if(int.TryParse(passSplit[1], out var id))
        {
            var worker1 = NGManager.Me.m_MAWorkerList.Find(o => o.m_ID.Equals(id));
            if (worker1 == null)
            { 
                MAParser.ParserError($"No worker with ID '{passSplit[1]}' @ {m_callingStep}"); 
                return null;
            } 
            return worker1;
        }
        var worker = NGManager.Me.m_MAWorkerList.Find(o => o.GetTypeInfo().Equals(passSplit[1]));
        if (worker == null)
        {
            worker = NGManager.Me.m_MAWorkerList.Find(o => o.name.Equals(passSplit[1]));

            if (worker == null)
            {
                MAParser.ParserError($"No worker with ID '{passSplit[1]}' @ {m_callingStep}"); 
                return null;
            }
        }
        return worker;
    }

    static object ConvertPath(string[] passSplit)
    {
        var fakePassString = new string[passSplit.Length];
        fakePassString[1] = passSplit[1];
        var result = ConvertPos(fakePassString);
        if (result is Vector3 == false)
        {
            MAParser.ParserError($"{passSplit[1]} is not a valid position @ {m_callingStep}");
            return null;
        }

        var pos = (Vector3) result;
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(pos);
        return path;
    }
    static object ConvertCharacter(string[] passSplit)
    {
        var reference = passSplit[1];
        if(int.TryParse(reference, out var id))
        {
            foreach(var character in NGManager.Me.m_MACharacterList)
            {
                if(character.m_ID.Equals(id))
                    return character;
            }
        }
        else
        {
            // Name first
            foreach(var character in NGManager.Me.m_MACharacterList)
            {
                if(character.Name.Equals(reference))
                    return character;
            }
            // Type as backup
            foreach(var character in NGManager.Me.m_MACharacterList)
            {
                if(character.GetTypeInfo().Equals(reference))
                    return character;
            }
        }
        MAParser.ParserError($"No character with ID '{passSplit[1]}' @ {m_callingStep}");
        return null;
    }
    static object ConvertAnimal(string[] passSplit)
    {
        var reference = passSplit[1];
        if (int.TryParse(reference, out var id))
        {
            foreach (var animal in NGManager.Me.m_MAAnimalList)
            {
                if (animal.m_ID.Equals(id))
                    return animal;
            }
        }
        else
        {
            // Name first
            foreach (var animal in NGManager.Me.m_MAAnimalList)
            {
                if (animal.Name.Equals(reference))
                    return animal;
            }
            // Type as backup
            foreach (var animal in NGManager.Me.m_MAAnimalList)
            {
                if (animal.GetTypeInfo().Equals(reference))
                    return animal;
            }
        }
        MAParser.ParserError($"No animal with ID '{passSplit[1]}' @ {m_callingStep}");
        return null;
    }
    static object ConvertBuildingComponent(string[] passSplit)
    {       
        var cType = Type.GetType(passSplit[1]);
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            var cmps = b.BuildingComponents(cType);
            foreach(var c in cmps)
            {
                return c;
            }
        }
        return null;
    }
    static object ConvertBuildingBlock(string[] passSplit)
    {
        foreach(var block in DesignTableManager.Me.m_wildBlockHolder.GetComponentsInChildren<Block>())
        {
            if(block.m_blockInfoID.ToLower().Equals(passSplit[1].ToLower()) && DistrictManager.Me.IsWithinDistrictBounds(block.transform.position))
            {
                return block;
            }
        }
        // foreach (var b in NGManager.Me.m_maBuildings)
        // {
        //     var blocks = b.m_blockHolder.GetComponentsInChildren<Block>();
        //     var blockFound = blocks.Find(o => o.m_blockInfoID.Equals(passSplit[1]));
        //     if (blockFound)
        //     {
        //         return blockFound;
        //     }
        // }
        return null;
    }
    
    static public object ConvertBuilding(string[] passSplit)
    {
        var building = MABuilding.FindBuilding(passSplit[1], true);
        //if(building == null)
        //    Debug.LogError($"{nameof(MAParserSupport)} - {nameof(ConvertBuilding)} - Could not find Building (null). passSplit[0]: '{passSplit[0]}' - '[{passSplit[1]}]'");
        return building;
    }

    static public object ConvertDecoration(string[] passSplit)
    {
        var dec = NGDecoration.FindDecorationByName(passSplit[1]);
        // if (dec == null)
        //     Debug.LogError($"{nameof(MAParserSupport)} - {nameof(ConvertDecoration)} - Could not find Decoration (null). passSplit[0]: '{passSplit[0]}' - '[{passSplit[1]}]'");
        return dec;
    }

		static public object ConvertNamedPoint(string[] passSplit)
		{
				var np = NamedPoint.GetNamedPoint(passSplit[1]);
				// if (np == null)
				//     Debug.LogError($"{nameof(MAParserSupport)} - {nameof(ConvertNamedPoint)} - Could not find NamedPoint (null). passSplit[0]: '{passSplit[0]}' - '[{passSplit[1]}]'");
				return np;
		}

    static public object ConvertComponentBuilding(string[] passSplit)
    {
        var componentType = MAComponentInfo.GetInfo(passSplit[1]);
        var res = NGManager.Me.FindBuildingsWithComponent(componentType, out var comp, true);
        //if (res == false || comp == null || comp.Count == 0)
        //    Debug.LogError($"{nameof(MAParserSupport)} - {nameof(ConvertComponentsInBuildings)} - Could not find Component (null). passSplit[0]: '{passSplit[0]}' - '[{passSplit[1]}]'");
        return comp;
    }

    static public object ConvertComponents(string[] passSplit)
    {
        var componentType = MAComponentInfo.GetInfo(passSplit[1]);
        List<BCBase> components = null;
        var res = NGManager.Me.FindBuildingsWithComponent(componentType, out var comp, true);
        if (res)
        {
            components = new();
            foreach (MABuilding maBuilding in comp)
                if (maBuilding.m_componentsDict.TryGetValue(componentType.m_classType, out var componentsInBuilding))
                    components.AddRange(componentsInBuilding);
        }
        //if (res == false || components == null || components.Count == 0)
        //    Debug.LogError($"{nameof(MAParserSupport)} - {nameof(ConvertComponents)} - Could not find Component (null). passSplit[0]: '{passSplit[0]}' - '[{passSplit[1]}]'");
        return components;
    }
    
    static object ConvertGameObject(string[] passSplit)
    { //MA_Tardis_Crypt(Clone)
        var cSplit = passSplit[1].Split(':');
        if (cSplit.Length != 2)
        {
            MAParser.ParserError($"Invalid GameObject ID '{passSplit[1]}' @ {m_callingStep}");
            return null;
        }
        var foundParent = Utility.FindTransformByPath(cSplit[0]);
        if(foundParent == null)
        {
            MAParser.ParserError($"No Folder with Name '{passSplit[0]}' @ {m_callingStep}");
            return null;
        }
        var names = foundParent.GetComponentsInChildren<MANameObject>();
        if(names == null || names.Length == 0)
        {
            var objTransform = foundParent.Find(cSplit[1].Trim());
            if (objTransform == null)
            {
                MAParser.ParserError($"No MANameObject in Folder '{cSplit[0]}' @ {m_callingStep}");
                return null;             
            }
            return objTransform.gameObject;
        }
        var obj = names.Find(o => o.m_name.Equals(cSplit[1]));
        if(obj == null)
        {
            MAParser.ParserError($"No GameObject with Name '{cSplit[1]}' in Folder '{passSplit[0]}' @ {m_callingStep}");
            return null;
        }
        if(obj.m_target != null) return obj.m_target.gameObject;
        return obj.gameObject;
    }
    
    static public IFormattable ConvertPos(string[] passSplit)
    {
        var coordSplit = passSplit[1].Split(';', ',');
        if (coordSplit.Length != 2 && coordSplit.Length != 3) return null;
        
        float x = 0f, y = 0f, z = 0f;
        if (coordSplit[0].TryFloatInv(out x) == false) return null;
        if (coordSplit.Length == 2)
        {
            if (coordSplit[1].TryFloatInv(out z) == false) return null;
            return new Vector3(x, 0, z).GroundPosition();
        }
        if (coordSplit[1].TryFloatInv(out y) == false) return null;
        if (coordSplit[2].TryFloatInv(out z) == false) return null;
        return new Vector3(x, y, z);
    }
    
    static public object ConvertRot(string[] passSplit)
    {
        var coordSplit = passSplit[1].Split(';');
        if (coordSplit.Length != 1 && coordSplit.Length != 3)
        {
            return null;
        }

        float x = 0f;
        float y = 0f;
        float z = 0f;

        if(coordSplit.Length == 1)
        {
            if (coordSplit[0].TryFloatInv(out y) == false)
            {
                return false;
            }
        }
        else if (coordSplit.Length == 3)
        {
            if (coordSplit[0].TryFloatInv(out x) == false)
            {
                return null;
            }
            if (coordSplit[1].TryFloatInv(out y) == false)
            {
                return null;
            }
            if (coordSplit[2].TryFloatInv(out z) == false)
            {
                return null;
            }
        }

        var rot = Quaternion.Euler(x, y, z);
        return rot;
    }
    public static void TestIt()
    {
        Debug.Log($"TestIt()");
    }
    public static void TestIt(int _one)
    {
        Debug.Log($"TestIt Int({_one})");
    }
    public static void TestIt(string _one, float _two)
    {
        Debug.Log($"TestIt String({_one}) Float({_two})");
    }
    public static void TestIt(MABuilding _one)
    {
        Debug.Log($"TestIt Building({_one.name})");
    }
    public static void TestIt(Vector3 _one)
    {
        Debug.Log($"TestIt Vector3({_one})");
    }
    public static void TestIt(string _one)
    {
        Debug.Log($"TestIt String({_one})");
    } 
    public static void TestIt(string[] _one)
    {
        Debug.Log($"TestIt String({_one})");
    } 
    public static bool WaitTestIt(string _one, string _two)
    {
        Debug.Log($"TestIt String({_one}) String({_two})");
        return true;
    }

    public static void TestAll()
    {
        var t0 = ParseString("TestIt()");
        var t1 = ParseString("TestIt(Hello)");
        var t2 = ParseString("TestIt(\"Hello\")");
        var t2b = ParseString("Where(WaitForTrigger(WaitForBuildingToBeClean(Building[44])))");

        var t3 = ParseString("TestIt(Hello, There)");
        var t4 = ParseString("TestIt(\"Hello, \nthere\")");
        var t5 = ParseString("TestIt(Hello, 1.0)\nFake(1)");
        return;
        TryParse("TestIt(1)",out var r1, "TestAll 1");
        TryParse("TestIt()",out var r2, "TestAll 2");
        TryParse("TestIt(Hello, 1.0)",out var r3, "TestAll 3");
        TryParse("TestIt(Building[22])",out var r4,"TestAll 4");
        TryParse("TestIt(Pos[1;2;3])",out var r5,"TestAll 5");
        TryParse("TestIt(Great All Working)",out var r6,"TestAll 6");
        TryParse("TestIt(Buildnig[22])",out var r7,"TestAll 7");
        TryParse("WaitTestIt(Building[22])",out var r8,"TestAll 8");
        TryParse("Bollocks",out var r9,"TestAll 9");
        TryParse("Bollocks()",out var r10,"TestAll 10");
    } 
   
    static public bool m_debugError = false;
    static public string DebugColor(string _what)
    {
        m_debugError = true;
        return $"<color=#fefc78>{_what}</color>";
    }

    public class ParseStringStore
    {
        public string m_functionName;
        public List<string> m_args = new List<string>();
    }
    static public List<ParseStringStore> ParseString(string _what)
    {
        StringBuilder currentString = new StringBuilder();

        //string currentString = "";
        List<ParseStringStore> funcStrings = new List<ParseStringStore>();
        ParseStringStore  currentParseStringStore = null;
        var inQuote = false;
        var openBrackets = 0;
        foreach (var c in _what)
        {
            switch (c)
            {
                case '"':
                    inQuote = !inQuote;
                    break;
                case '(':
                    if (inQuote) goto default;
                    openBrackets++;
                    if (openBrackets > 1) goto default;

                    currentParseStringStore = new ParseStringStore(){m_functionName = currentString.ToString()};
                    funcStrings.Add(currentParseStringStore);
                    currentString.Clear();
                    break;
                case ')':
                    if (inQuote) goto default;
                    openBrackets--;
                    if(openBrackets > 0) goto default;
                    if(currentString.Length > 0)
                        currentParseStringStore?.m_args.Add(currentString.ToString());
                    currentString.Clear();
                    currentParseStringStore = null;
                    break;
                case ',':
                    if (inQuote || openBrackets > 1) goto default;
                    if (currentString.Length > 0)
                    {
                        if(currentParseStringStore == null)
                        {
                            MAParser.ParserError($"No function name for argument '{currentString}'");
                            return null;
                        }
                        if(currentParseStringStore.m_args == null)
                        {
                            MAParser.ParserError($"No args for function '{currentParseStringStore.m_functionName}'");
                            return null;
                        }
                        currentParseStringStore.m_args.Add(currentString.ToString());
                        
                    }
                    currentString.Clear();
                    break;
                case '\n':
                    if (inQuote) goto default;
                    if(openBrackets != 0) goto ErrorEnd;
                    currentString.Clear();
                    break;
                default:
                    currentString.Append(c);
                    break;
            }
        } 
        ErrorEnd:
        if (openBrackets != 0)
        {
            MAParser.ParserError($"Mismatched brackets in '{_what}'");
            return null;
        }
        if (inQuote)
        {
            MAParser.ParserError($"Mismatched quotes in '{_what}'");
            return null;
        }
        return funcStrings;
    }

    public static List<string> StringToList(string _what)
    {
        var ret = new List<string>();
        foreach(var s in _what.Split(';','|'))
        {
            var word = s.Trim(); 
            if (word.IsNullOrWhiteSpace()) continue;
            ret.Add(word);
        }
        return ret;
    }
    static public List<ParseStringStore> ParseStringMultiLine(string _what)
    {
        StringBuilder currentString = new StringBuilder();

        //string currentString = "";
        List<ParseStringStore> funcStrings = new List<ParseStringStore>();
        ParseStringStore  currentParseStringStore = null;
        var inQuote = false;
        var openBrackets = 0;
        foreach (var c in _what)
        {
            switch (c)
            {
                case '"':
                    inQuote = !inQuote;
                    break;
                case '(':
                    if (inQuote) goto default;
                    openBrackets++;
                    currentParseStringStore = new ParseStringStore(){m_functionName = currentString.ToString()};
                    funcStrings.Add(currentParseStringStore);
                    currentString.Clear();
                    break;
                case ')':
                    if (inQuote) goto default;
                    openBrackets--;
                    if(currentString.Length > 0)
                        currentParseStringStore.m_args.Add(currentString.ToString());
                    currentString.Clear();
                    currentParseStringStore = null;
                    break;
                case ',':
                    if (inQuote) goto default;
                    if(currentString.Length > 0)
                        currentParseStringStore.m_args.Add(currentString.ToString());
                    currentString.Clear();
                    break;
                case '\n':
                    if (inQuote) goto default;
                    if(openBrackets != 0) goto ErrorEnd;
                    currentString.Clear();
                    break;
                default:
                    currentString.Append(c);
                    break;
            }
        } 
        ErrorEnd:
        if (openBrackets != 0)
        {
            MAParser.ParserError($"Mismatched brackets in '{_what}'");
            return null;
        }
        if (inQuote)
        {
            MAParser.ParserError($"Mismatched quotes in '{_what}'");
            return null;
        }
        return funcStrings;
    }
    private static DebugConsole.Command s_execute = new DebugConsole.Command("execute", _s =>
    {
        TryParse(_s, out var result);
        Debug.Log($"Result: {result}"); //execute = SpawnCreature(Creature[Zombie], Pos[-207.9090;-213.1031])
    });

    
   

}
