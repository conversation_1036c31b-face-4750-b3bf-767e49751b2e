using System;
using UnityEditor;
using UnityEngine;

public class MARangeAttackProjectile : MonoBehaviour
{
	private IDamageReceiver.DamageSource source;
	private float speed = 0f;
	private MAAttackInstance attack = null;
	private MACharacterBase targetCharacter = null;
	private Vector3 randomOffset;
	private float randomSign = 1f;
	private Vector3 nonTargetPos = Vector3.zero;
	private TargetObject targetStructure = null;
	private Vector3 structurePos = Vector3.zero;
	private Vector3 prevPosition = Vector3.zero;
	private Transform targetBodyPartTransform;
	private bool toBeDestroyed = false;

	[SerializeField]
	AkEventHolder m_launchEvent;
	[SerializeField]
	AkEventHolder m_impactEvent;

	[SerializeField]
	GameObject m_missImpactVFX;

	[SerializeField]
	private float angleDeg = 45f;
	private float elapsedTime = 0f;
	private Vector3 startPosition = Vector3.zero;
	private Vector3 initialVelocity = Vector3.zero;
	private float gravity = 0f;

	public void Update()
	{
		if (toBeDestroyed)
		{
			gameObject.SetActive(false);
			Destroy(gameObject);
			return;
		}

		UpdateWithTarget();

		UpdateWithStructure();
	}

	public void SetupWithCharacter(MACharacterBase _targetCharacter, float _speed, MAAttackInstance _attack)
	{
		source = IDamageReceiver.DamageSource.AI;
		if(_attack != null && _attack.Attacker != null && _attack.Attacker.IsPossessed)
			source = IDamageReceiver.DamageSource.Possession;
			
		targetCharacter = _targetCharacter;
		speed = _speed;
		attack = _attack;
		prevPosition = transform.position;

		randomOffset = new Vector3(UnityEngine.Random.Range(-1f,1f), UnityEngine.Random.Range(-1f,1f), UnityEngine.Random.Range(-1f,1f));
		randomOffset *= 0.2f;

		nonTargetPos = targetCharacter.transform.position;
		randomSign = (float)(UnityEngine.Random.Range(0, 2) * 2) - 1f;
		nonTargetPos += (transform.forward * UnityEngine.Random.Range(20f, 18f)) + (transform.right * randomSign * UnityEngine.Random.Range(10f, 8f));
		var groundPos = nonTargetPos.GroundPosition();
		if ((nonTargetPos.y >= 0f) || (groundPos.y < 0f))
			nonTargetPos = groundPos;
		targetBodyPartTransform = targetCharacter.m_ragdollController.GetRandomTargetableBodypart();

		PlayLaunchFX();
	}

	private void UpdateWithTarget()
	{
		if (targetCharacter == null)
			return;
		
		bool hasArrived = MoveTowardsCharacter(attack != null);
		if (hasArrived)
		{
			bool hasBlocked = false;
			if (attack != null)
			{
				if (targetCharacter.CanBlockAttack(prevPosition))
				{
					hasBlocked = true;
					targetCharacter.ReduceManaByHit();
					toBeDestroyed = true;
				}
				else
				{
					targetCharacter.SpawnBloodFromHit();

					targetCharacter.ApplyDamageEffect(source, 0f, transform.position, attack);
					targetCharacter.StickProjectile(this, targetBodyPartTransform);
				}
			}
			else
			{
				StartCoroutine(Utility.Co_After(5f, () => { Destroy(gameObject); }));
			}
			PlayImpactFX(hasBlocked);
			targetCharacter = null;
		}
	}

	private bool MoveTowardsCharacter(bool shouldHitTarget)
	{
		var targetPos = nonTargetPos;
		var rc = targetCharacter.m_ragdollController;
		var targetHitPos = targetBodyPartTransform.position + randomOffset;
		if (shouldHitTarget)
		{
			targetPos = targetHitPos;
		}
		prevPosition = transform.position;
		var dir = targetPos - prevPosition;
		if (dir.sqrMagnitude <= (0.1f * 0.1f))
		{
			transform.position = targetPos;
			transform.rotation = Quaternion.LookRotation(targetPos - prevPosition);
			return true;
		}
		
		var pos = prevPosition + (dir.normalized * speed * Time.deltaTime);
		var dirNew = targetPos - pos;
		if (Vector3.Dot(dir, dirNew) < 0f)
		{
			transform.position = targetPos;
			transform.rotation = Quaternion.LookRotation(targetPos - prevPosition);
			return true;
		}
		
		if (!shouldHitTarget)
		{
			var hitPos = new Vector3(targetHitPos.x, pos.y, targetHitPos.z);
			var dirToTarget = hitPos - pos;
			if ((dirToTarget.sqrMagnitude <= (3f * 3f)) && (pos.y > 0.5f))
			{
				nonTargetPos = prevPosition;
				var forward = transform.forward;
				bool useVel = false;
				// var targetVel = targetCharacter.RigidBody.linearVelocity;
				// if (targetVel.sqrMagnitude > (0.01f * 0.01f))
				// {
				// 	targetVel = targetVel.normalized;
				// 	var dot = Vector3.Dot(targetVel, forward);
				// 	bool isParallel = (Mathf.Abs(dot - 1f) <= 0.1f) || (Mathf.Abs(dot + 1f) <= 0.1f);
				// 	if (!isParallel)
				// 	{
				// 		useVel = true;

				// 		nonTargetPos += forward * UnityEngine.Random.Range(10f, 8f);
				// 		nonTargetPos += targetVel * -1f * UnityEngine.Random.Range(3f, 2f);
				// 	}
				// }
				if (!useVel)
				{
					nonTargetPos += forward * UnityEngine.Random.Range(10f, 8f);
					nonTargetPos += transform.right * randomSign * UnityEngine.Random.Range(3f, 2f);
				}
				var groundPos = nonTargetPos.GroundPosition();
				if ((nonTargetPos.y >= 0f) || (groundPos.y < 0f))
					nonTargetPos = groundPos;
				dir = nonTargetPos - prevPosition;
				pos = prevPosition + (dir.normalized * speed * Time.deltaTime);
			}

			int layerMask = LayerMask.GetMask(new string[] { "Default", "Terrain", "Roads" });
			var ray = pos - prevPosition;
			if (Physics.Raycast(prevPosition, ray.normalized, out RaycastHit hitInfo, ray.magnitude, layerMask, QueryTriggerInteraction.Ignore))
			{
				pos = hitInfo.point;
				transform.position = pos;
				transform.rotation = Quaternion.LookRotation(pos - prevPosition);
				
				return true;
			}
		}
		
		transform.position = pos;
		transform.rotation = Quaternion.LookRotation(pos - prevPosition);

		return false;
	}

	public void SetupWithStructure(TargetObject _targetStructure, float _speed, MAAttackInstance _attack, bool direct)
	{
		targetStructure = _targetStructure;
		attack = _attack;
		var pt = targetStructure.GetComponentInChildren<ProjectileTarget>();
		if (pt != null)
		{
			structurePos = pt.RandomPointOnTarget();
		}
		else
		{
			structurePos = targetStructure.transform.position + (Vector3.up * UnityEngine.Random.Range(2f, 3f));
		}
		prevPosition = transform.position;
		elapsedTime = 0f;

		startPosition = transform.position;
		float angle = direct ? 5f : angleDeg;
		float angleRad = angle * Mathf.Deg2Rad;
		Vector3 delta = structurePos - startPosition;
		Vector3 deltaXZ = new Vector3(delta.x, 0f, delta.z);
		float xz = deltaXZ.magnitude;
		float y = delta.y;
		float cosAngle = Mathf.Cos(angleRad);
		float denominator = 2f * ((xz * Mathf.Tan(angleRad)) - y) * cosAngle * cosAngle;
		if (denominator <= 0f)
			denominator = 0.01f;
		gravity = _speed;
		float speedSquared = (gravity * xz * xz) / denominator;
		speed = Mathf.Sqrt(speedSquared);
		Vector3 dirXZ = deltaXZ.normalized;
		initialVelocity = (dirXZ * speed * cosAngle) + (Vector3.up * speed * Mathf.Sin(angleRad));

		PlayLaunchFX();
	}

	private void UpdateWithStructure()
	{
		if (targetStructure == null)
			return;
		
		bool hasArrived = MoveTowardsStructure();
		if (hasArrived)
		{
			PlayImpactFX(false);
			
			float damage = attack.Damage;
			targetStructure.DoDamage(source, ref damage, transform.position);
			transform.SetParent(targetStructure.transform, true);
			StartCoroutine(Utility.Co_After(5f, () => { Destroy(gameObject); }));
			targetStructure = null;
			enabled = false;
		}
	}

	private bool MoveTowardsStructure()
	{
		var targetPos = structurePos;
		prevPosition = transform.position;
		var dir = targetPos - prevPosition;
		if (dir.sqrMagnitude <= (0.1f * 0.1f))
		{
			transform.position = targetPos;
			transform.rotation = Quaternion.LookRotation(targetPos - prevPosition);

			return true;
		}

		var pos = startPosition + (initialVelocity * elapsedTime) + (0.5f * Vector3.down * gravity * elapsedTime * elapsedTime);
		elapsedTime += Time.deltaTime;
		// var pos = prevPosition + (dir.normalized * speed * Time.deltaTime);
		var dirNew = targetPos - pos;
		if (Vector3.Dot(dir, dirNew) < 0f)
		{
			transform.position = targetPos;
			transform.rotation = Quaternion.LookRotation(targetPos - prevPosition);

			return true;
		}

		int layerMask = LayerMask.GetMask(new string[] { "Default", "Terrain", "Roads" });
		var ray = pos - prevPosition;
		if (Physics.Raycast(prevPosition, ray.normalized, out RaycastHit hitInfo, ray.magnitude, layerMask, QueryTriggerInteraction.Ignore))
		{
			pos = hitInfo.point;
			transform.position = pos;
			transform.rotation = Quaternion.LookRotation(pos - prevPosition);
			
			return true;
		}

		transform.position = pos;
		transform.rotation = Quaternion.LookRotation(pos - prevPosition);
		return false;
	}

	void PlayLaunchFX()
	{
		m_launchEvent.Play(gameObject);
	}

	void PlayImpactFX(bool hasBlocked)
	{
		bool didHit = attack != null;
		string hitType = "Foliage";
		if (hasBlocked)
		{
			hitType = targetCharacter.WeaponTargetType();
			targetCharacter.PlayBlockFX(transform.forward);
		}
		else if (didHit)
		{
			hitType = targetCharacter != null ? targetCharacter.GetArmourHitType() : "Wall";
		}
		AudioClipManager.Me.SetSoundSwitch("CombatTarget", $"CombatTarget_{hitType}", gameObject);
		m_impactEvent.Play(gameObject);

		// Stop the in-flight VFX.
		// The nature of the ones on the arrow are that they need 
		// to disappear immediately on impact.
		var ps = GetComponentInChildren<ParticleSystem>();
		if (ps != null)
		{
			ps.gameObject.SetActive(false);
		}

		if (!didHit && m_missImpactVFX != null)
		{
			var go = GameObject.Instantiate(m_missImpactVFX);
			go.transform.SetParent(transform, false);
		}
	}
}
