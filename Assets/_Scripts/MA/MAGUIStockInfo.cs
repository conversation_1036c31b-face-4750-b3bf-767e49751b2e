using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MAGUIStockInfo : MonoBehaviour
{
    public Renderer m_inStockTextRenderer;
    public Renderer m_outStockTextRenderer;
    public Material m_factoryMaterial;
    public MeshRenderer m_inStockMesh;
    public MeshRenderer m_outStockMesh;
    public RoofStockCube m_cubePrefab;
    public Transform m_roofStockCubeHolder;
    public Transform m_roofStockBackground;
    public int m_maxStockWidth = 3;
    public float m_sectionPadY = .5f;
    public float m_blockPad = 0.01f;
    public Image m_progressBar;
}
