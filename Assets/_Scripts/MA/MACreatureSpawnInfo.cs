using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class MACreatureSpawnInfo
{ 
    public static List<MACreatureSpawnInfo> s_creatureSpawnInfos = new List<MACreatureSpawnInfo>();
    public static List<MACreatureSpawnInfo> GetList=>s_creatureSpawnInfos;
    public string DebugDisplayName => m_name;

    public static IList GetTheList() => s_creatureSpawnInfos;

    public Vector3? GetDestinationPos()
    {
        if(m_destination.IsNullOrWhiteSpace() == false)
        {
           // if()
            //{
            MAParserSupport.TryParse(m_destination.Trim(), out bool okay);
            if (okay)
            {
                return MAParser.m_parsedPos;
            }
           // }
        }

        return null;
    }
       
    public enum CreatureSpawnPointType
    {
        None,
        DayNight,
        Proximity,
        Scripted,
    }

    public enum CreatureSpawnType
    {
        None,
        OneShot, 
        Rebirth,
				DailyOneShot,
    }
        
    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_type;
    public string m_destination;
    [ScanField] public string m_creature;
    public string m_spawnType;
    public float m_spawnDelay;
    
    private MACreatureInfo m_creatureInfo;

    private static Vector3 m_parsedPos = Vector3.zero;
    
   // public Vector3? m_destinationPos;
    
    public MACreatureInfo CreatureInfo => m_creatureInfo;
    public CreatureSpawnPointType m_spawnPointType = CreatureSpawnPointType.None;
    public CreatureSpawnType m_creatureSpawnType = CreatureSpawnType.None;
    
    public CreatureSpawnPointType SpawnPointType => m_spawnPointType;
    public CreatureSpawnType SpawnType => m_creatureSpawnType;

    public static bool PostImport(MACreatureSpawnInfo _what)
    {
        _what.m_creatureInfo = MACreatureInfo.GetInfo(_what.m_creature);

        
        // CreatureDestination(Pos[22,12])
        // CreatureDestination(Building[22])
            
        // if(MAParserSupport.TryParse(_what.m_destination, out bool okay, ""))
        // {
        // }
        //if(_what.TryParseDestinationPos(out _what.m_destinationPos) == false)
        //{
        //    Debug.LogError($"MACreatureSpawnInfo - TryParseDestinationPos - Failed to parse destination: '{_what.m_destination}'");
        //}
        if(CreatureSpawnPointType.TryParse(_what.m_type, out CreatureSpawnPointType _outVal))
        {
            _what.m_spawnPointType = _outVal;
        }
        else
        { 
            _what.m_spawnPointType = CreatureSpawnPointType.None;
            Debug.LogError($"MACreatureSpawnInfo - PostImport - Failed to parse type: {_what.m_type}");
            return false;
        }
        
        if(CreatureSpawnType.TryParse(_what.m_spawnType, out CreatureSpawnType _outValSpawnType))
        {
            _what.m_creatureSpawnType = _outValSpawnType;
        }
        else
        { 
            _what.m_creatureSpawnType = CreatureSpawnType.None;
            Debug.LogError($"MACreatureSpawnInfo - PostImport - Failed to parse spawn type: {_what.m_spawnType}");
            return false;
        } 
        
        return true;
    }
    public bool TryParseDestinationPos(out Vector3 _pos)
    {
        _pos = Vector3.zero;
        if (m_destination.IsNullOrWhiteSpace()) return false;
        if(MAParserSupport.TryParse(m_destination, out bool result, $"MACreatureSpawnInfo[{m_name}] - TryParseDestinationPos - Failed to parse destination: '{m_destination}'"))
        {
            _pos = m_parsedPos;
            return true;
        }
        return false;
    }
    public static List<MACreatureSpawnInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_creatureSpawnInfos = NGKnack.ImportKnackInto<MACreatureSpawnInfo>(PostImport);
        return s_creatureSpawnInfos;
    }

    public static MACreatureSpawnInfo GetInfo(string _name) => s_creatureSpawnInfos.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
}