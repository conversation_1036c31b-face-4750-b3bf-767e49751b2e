using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAComponentIcons : MonoBehaviour
{
    public static Dictionary<int, List<Tuple<Vector3, Vector3>>> m_iconPositions =
    new Dictionary<int, List<Tuple<Vector3, Vector3>>>()
    {
        {
            1,
            new List<Tuple<Vector3, Vector3>>()
                {new Tuple<Vector3, Vector3>(new Vector3(0, 0, .1f), new Vector3(1, 1, 1))}
        },
        {
            2, new List<Tuple<Vector3, Vector3>>()
            {
                {new Tuple<Vector3, Vector3>(new Vector3(1, 0, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(-1, 0, .1f), new Vector3(.5f, .5f, .5f))}
            }
        },
        {
            3, new List<Tuple<Vector3, Vector3>>()
            {
                {new Tuple<Vector3, Vector3>(new Vector3(0f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(1, 0, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(-1, 0, .1f), new Vector3(.5f, .5f, .5f))}
            }
        },
        {
            4, new List<Tuple<Vector3, Vector3>>()
            {
                {new Tuple<Vector3, Vector3>(new Vector3(1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))}
            }
        },
        {
            5, new List<Tuple<Vector3, Vector3>>()
            {
                {new Tuple<Vector3, Vector3>(new Vector3(1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                {new Tuple<Vector3, Vector3>(new Vector3(0, 0, .1f), new Vector3(.5f, .5f, .5f))}

            }
        },
    };

    public bool m_showBuildingIcons = true;
    private Block m_block;
    void Start()
    {
        m_block = GetComponent<Block>();
        if(m_block)
            ToggleComponentIcon(m_block, m_showBuildingIcons);
    }

    public void Toggle(bool _toggle)
    {
        ToggleComponentIcon(m_block, _toggle);
    }
    
    public static void ToggleComponentIcon(Block _block, bool _toggle)
    {
        var info = _block.BlockInfo;
        if (info == null) return;
        //Destroy old icons
        var srs = _block.m_toHinges.GetComponentsInChildren<SpriteRenderer>();
        for (int i = srs.Length - 1; i >= 0; i--)
            Destroy(srs[i].gameObject);
        if (_toggle == false) return;
            
        //count valid components
        var componentCount = 0;
        var cSplit = info.m_components.Split(';', ',', '|', '\n');
        foreach(var cs in cSplit)
            if (MAComponentInfo.s_componentInfos.ContainsKey(cs) && MAComponentInfo.s_componentInfos[cs].m_sprite)
                componentCount++;
        //Setup hinges and places
        if(componentCount == 0) return;
        var hinges = GetHinges(_block);
        
        var places = m_iconPositions[Mathf.Min(5, componentCount)];
        foreach (var hinge in hinges)           // for each hinge on the block
        {
            for (var index = 0; index < cSplit.Length && index < places.Count; index++)     //place an icon
            {
                var c = cSplit[index];
                if (MAComponentInfo.s_componentInfos.ContainsKey(c) && MAComponentInfo.s_componentInfos[c].m_sprite && index <= componentCount)
                {
                    var go = Instantiate(NGManager.Me.m_componentIconPrefab.gameObject, hinge.transform);
                    go.transform.localPosition = places[index].Item1;
                    go.transform.localScale = places[index].Item2;
                    go.GetComponentInChildren<SpriteRenderer>().sprite = MAComponentInfo.s_componentInfos[c].m_sprite;
                    go.SetLayerRecursively(hinge.gameObject.layer);
                }
            }
        }
    }

    static List<GameObject> GetHinges(Block _block)
    {
        var results = new List<GameObject>();
        var hinges = _block.m_toHinges.GetComponentsInChildren<SnapHinge>();
        foreach(var h in hinges)
            results.Add(h.gameObject);
        var dhinges = _block.m_toHinges.GetComponentsInChildren<DynamicBuildingHinge>();
        foreach(var h in dhinges)
            results.Add(h.gameObject);
        return results;
    }
}
