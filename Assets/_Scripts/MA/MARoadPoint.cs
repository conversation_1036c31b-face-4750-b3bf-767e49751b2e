using System;
using UnityEngine;

public class MARoadPoint : MonoBehaviour
{
    public enum MainRoadPointType
    {
        StartPoint,
        // OutsideEntryGate,
        // InsideEntryGate,
        // InsideExitGate,
        // OutsideExitGate,
        EndPoint,
    }
    
    public MainRoadPointType m_pointType;
    
    private GateOpener m_accessGate;

    public GateOpener AccessGate
    {
        get
        {
            if (m_accessGate == null)
            {
                var pos = transform.position;
                GateOpener nearest = GateOpener.NearestToPoint(pos);
                if ((nearest.transform.position.GetXZVector2() - pos.GetXZVector2()).magnitude < 10.0f)
                {
                    m_accessGate = nearest;
                }

                m_accessGate = GateOpener.NearestToPoint(transform.position);
            }
            return m_accessGate;
        }
    }
}
