using System;
using UnityEngine;
			
public class BloodEmitter : MonoBehaviour
{
    [Range(0, 60)]
    public float m_bloodRunningAfterAttackSeconds = 1f; //characterBase?
    public float m_splatsPerSecond = 2f; //characterBase?


    [NonSerialized]
    public Vector3 m_normal;
    [NonSerialized]
    public float m_bloodRunningTimeLeft = 0f; //characterBase?
    
    private float m_timeUntilNextSplat = 0f;
    private Transform m_transform = null;
		
    private void Awake()
    {
        m_transform = transform;
        m_bloodRunningTimeLeft = m_bloodRunningAfterAttackSeconds;
    }

    private void Update()
    {
        if(m_bloodRunningTimeLeft > 0)
        {
            m_bloodRunningTimeLeft -= Time.deltaTime;
            m_timeUntilNextSplat -= Time.deltaTime;
            
            if(m_timeUntilNextSplat <= 0f)
            {
                MABloodControl.Me.EmitBloodAtHit(m_transform.position, Vector3.down /*m_normal*/, m_transform, 1);
                m_timeUntilNextSplat = 1 / m_splatsPerSecond;
            }
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }
}