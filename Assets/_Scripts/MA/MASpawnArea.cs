using System;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MASpawnArea : MonoBehaviour
{
	public GameState_SpawnArea m_gameState = null;
    public SphereCollider m_spawnArea = null;
    public MACharacterBase m_characterInArea = null;
    //public bool m_seedSave = true;
    public bool IsOccupied => m_characterInArea != null;

    
    private void Awake()
    {
	    GameObject go = gameObject;
	    if (DestroyIfInvalid(go))
		    return;
	    
        m_spawnArea = go.GetComponent<SphereCollider>();
        if (m_spawnArea == null)
        {
	        m_spawnArea = go.AddComponent<SphereCollider>();
	        //m_spawnArea.transform.localPosition = Vector3.zero;
	        m_spawnArea.center = Vector3.zero;
        };
        
        m_spawnArea.isTrigger = true;
        m_spawnArea.radius = 1;
        
        if (NGManager.Me.m_MADaySpawnPositions == null)
        {
			NGManager.Me.m_MADaySpawnPositions = new();
        }
        
        for(int i = NGManager.Me.m_MADaySpawnPositions.Count -1; i >= 0; i--)
        {
	        if(NGManager.Me.m_MADaySpawnPositions[i] == null)
	        {
		        NGManager.Me.m_MADaySpawnPositions.RemoveAt(i);
	        }
        }
        NGManager.Me.m_MADaySpawnPositions.Add(this);
    }

    private void Start()
    {
	    DestroyIfInvalid(gameObject);
    }

    private void OnDestroy()
    {
	    if (NGManager.Me != null)
	    {
		    NGManager.Me.m_MADaySpawnPositions.Remove(this);
	    }
    }
    
    public static List<MASpawnArea> FindNearby(Vector3 _nearPos)
    {
	    List<MASpawnArea> orphanedSpawnAreas = new();
	    GlobalData.Me.m_spawnPointHolder.GetComponentsInChildren<MASpawnArea>(orphanedSpawnAreas);
	    float distSq = 20f * 20f;
	    orphanedSpawnAreas.RemoveAll(x =>
	    {
		    var save = x.CreateSave();
		    return (x.transform.position - _nearPos).xzSqrMagnitude() > distSq &&
		           (save.m_buildingUid != -1 || save.m_namedPoint.IsNullOrWhiteSpace() == false);
	    });
	    return orphanedSpawnAreas;
    }
    
    public static bool AssertNavPos(Vector3 _pos)
    {
	    var navPos = GlobalData.Me.GetNavAtPoint(_pos);
	    return GlobalData.IsPerfectNavable(navPos) || navPos == GlobalData.NavCostTypes.BuildingInner || navPos == GlobalData.NavCostTypes.EnterBuilding;
    }

    protected virtual void OnTriggerEnter(Collider other)
    {
        MACharacterBase newCharacter = other.gameObject.GetComponentInParent<MACharacterBase>();
        
        if(newCharacter == null)
        {
            return;
        }
        
        if(m_characterInArea == null)
        {
            m_characterInArea = newCharacter;
        }
    }

    protected virtual void OnTriggerStay(Collider other)
    {       
        MACharacterBase newCharacter = other.gameObject.GetComponentInParent<MACharacterBase>();
        
        if(newCharacter == null)
        {
            return;
        }
        
        if(m_characterInArea == null)
        {
            m_characterInArea = newCharacter;
        }
    }
    
    protected virtual void OnTriggerExit(Collider other)
    {
        MACharacterBase newCharacter = other.gameObject.GetComponentInParent<MACharacterBase>();

        if(newCharacter == null)
            return;

        if(m_characterInArea == newCharacter)
        {
            m_characterInArea = null;
        }
    }
    
    public GameState_SpawnArea CreateSave()
    {
	    //if (m_seedSave == false) return null;
	    Transform trParent = m_spawnArea.transform.parent;
        MABuilding building = trParent.GetComponentInParent<MABuilding>();
        NamedPoint namedPoint = trParent.GetComponentInParent<NamedPoint>();
        GameState_SpawnArea saveData = new GameState_SpawnArea()
        {
            m_worldPos = m_spawnArea.transform.position.GroundPosition(),
            m_buildingUid = building != null ? building.m_linkUID : -1,
            m_namedPoint = namedPoint != null ? namedPoint.name : "",
        };
		return saveData;
	}

    public static bool DestroyIfInvalid(GameObject _go)
    {
	    if(_go == null) return false;
	    Transform tr = _go.transform;
	    if (AssertNavPos(tr.position) == false)
	    {
		    #if UNITY_EDITOR
		    Debug.LogWarning($"Invalid MASpawnArea/MADespawnArea area position '{_go.transform.Path()}' at pos '{tr.position}' " +
	                      $"has navPoint Type '{GlobalData.Me.GetNavAtPoint(tr.position)}'. Deleting. Move this point out of the area");
		    _go.SetActive(false);
		    Destroy(_go);
		    #endif
		    return true;
	    }
	    return false;
    }

	private static string CreateSaveString()
	{
		List<GameState_SpawnArea> saveSP = new List<GameState_SpawnArea>();
		foreach (var sp in NGManager.Me.m_MADaySpawnPositions)
		{
			//if (sp.m_seedSave == false) continue;
			saveSP.Add(sp.CreateSave());
		}

		var save = JsonHelper.ToJson<GameState_SpawnArea>(saveSP.ToArray());
		return save;
	}

	public static void SaveAll()
	{
#if UNITY_EDITOR
		string saveString = CreateSaveString();
		var savePath = SeedSavePath;
		System.IO.File.WriteAllText(savePath, saveString);
#endif
	}

	private static string PathToResourcePath(string _assetPath)
	{
		const string c_resourcesString = "/Resources/";
		var resIndex = _assetPath.LastIndexOf(c_resourcesString);
		if (resIndex != -1) _assetPath = _assetPath.Substring(resIndex + c_resourcesString.Length);
		var dotIndex = _assetPath.LastIndexOf(".");
		if (dotIndex != -1) _assetPath = _assetPath.Substring(0, dotIndex);
		return _assetPath;
	}
	//
	// public static void ReadSeedSave()
	// {
	// 	var savePath = SeedSavePath;
	// 	savePath = PathToResourcePath(savePath);
	// 	var res = Resources.Load<TextAsset>(savePath);+
	// 		
	// 	// if (res != null)
	// 	// 	GameManager.Me.m_state.m_daySpawnPosition = res.text;
	// }
	//

	public static MASpawnArea Create(GameState_SpawnArea _saveData)
	{
		MABuilding building = null;
		NamedPoint namedPoint = null;
		if (_saveData.m_buildingUid != -1)
		{
			building = MABuilding.FindBuilding(_saveData.m_buildingUid.ToString(), true);
		}
		else if (string.IsNullOrWhiteSpace(_saveData.m_namedPoint) == false)
		{
			namedPoint = NamedPoint.GetNamedPoint(_saveData.m_namedPoint);
		}

		GameObject CommonSetup(Transform trParent)
		{
			GameObject newSpawnArea = new GameObject("SpawnArea");
			Transform spawnPointsParentTr = trParent.Find("SpawnPoints");
			if (spawnPointsParentTr == null)
			{
				spawnPointsParentTr = new GameObject("SpawnPoints").transform;
				spawnPointsParentTr.localPosition = Vector3.zero;
				spawnPointsParentTr.position = spawnPointsParentTr.position.GroundPosition();
				spawnPointsParentTr.SetParent(trParent);
			}
			newSpawnArea.transform.SetParent(spawnPointsParentTr);
			return newSpawnArea;
		}
		
		GameObject newSpawnArea = null;
		if (building != null)
		{
			newSpawnArea = CommonSetup(building.transform);
			newSpawnArea.name += $" [{newSpawnArea.transform.GetSiblingIndex()}] - Building [{building.m_linkUID}] - [{building.name}]";
		}
		else if (namedPoint != null)
		{
			newSpawnArea = CommonSetup(namedPoint.transform);
			newSpawnArea.name += $" [{newSpawnArea.transform.GetSiblingIndex()}] - NamedPoint [{namedPoint.name}]";
		}
		else
		{
			newSpawnArea = new GameObject("SpawnArea");
			newSpawnArea.transform.SetParent(GlobalData.Me.m_spawnPointHolder);
			newSpawnArea.name += $" [{newSpawnArea.transform.GetSiblingIndex()}]";
		}
		
		MASpawnArea spawnArea = newSpawnArea.AddComponent<MASpawnArea>();
		newSpawnArea.transform.position = _saveData.m_worldPos.GroundPosition();
		spawnArea.m_gameState = _saveData;
		//SphereCollider trigger = newSpawnArea.AddComponent<SphereCollider>();
		MADespawnArea deSpawnArea = newSpawnArea.AddComponent<MADespawnArea>();
		// trigger.isTrigger = true;
		// trigger.radius = 1f;
		// spawnArea.m_spawnArea = trigger;
		return spawnArea;
	}

	private static void LoadObjects()
	{
		var savePath = SeedSavePath;
		savePath = PathToResourcePath(savePath);
		var res = Resources.Load<TextAsset>(savePath);
		string saveString = res?.text;
		if (res != null && res.text.IsNullOrWhiteSpace() == false)
		{
			var saveSP = JsonHelper.FromJson<GameState_SpawnArea>(saveString, true);
			if (saveSP == null) return;
			foreach (var save in saveSP)
			{
				var sp = Create(save);
			}
		}
	}

	public static void LoadAll()
	{
		LoadObjects();
	}
	public const string SeedSavePath = "Assets/Resources/DaySpawnPositions.bytes";
}
[System.Serializable]
public class GameState_SpawnArea
{
    public Vector3 m_worldPos;
    public int m_buildingUid;
    public string m_namedPoint;
}


#if UNITY_EDITOR
[CustomEditor(typeof(MASpawnArea))]
public class MASpawnAreaEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
        MASpawnArea spawnArea = (MASpawnArea)target;

		if(GUILayout.Button($"Save Seed"))
		{
			MASpawnArea.SaveAll();
		}
        if(GUILayout.Button($"Delete"))
        {
			NGManager.Me.m_MADaySpawnPositions.Remove(spawnArea);
            DestroyImmediate(spawnArea.gameObject);
            MASpawnArea.SaveAll();
        }
	}
}
#endif