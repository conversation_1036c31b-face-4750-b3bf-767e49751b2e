//#define DEBUG_TOWN_BOUNDARIES
using System;
using System.Collections;
using System.Collections.Generic; 
using UnityEngine;
using Random = UnityEngine.Random;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class MACreatureControl : MonoSingleton<MACreatureControl>
{
    static DebugConsole.Command z1 = new("z1", _s => { MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Zombie"), MABuilding.FindBuilding("Factory"));    });
    static DebugConsole.Command z2 = new("z2", _s =>
    {
        MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Zombie"), MABuilding.FindBuilding("Factory"));
        MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Zombie"), MABuilding.FindBuilding("Produce Smelter"));
        MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Zombie"), MABuilding.FindBuilding("Produce Farm"));
        MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Zombie"), MABuilding.FindBuilding("Tavern"));
    });

    private static DebugConsole.Command z3 = new("z3", _s =>
    {
        MACreatureControl.Me.Invoke("DemoSpawn", 0);
        MACreatureControl.Me.Invoke("DemoSpawn", 1);
        MACreatureControl.Me.Invoke("DemoSpawn", 2);
        MACreatureControl.Me.Invoke("DemoSpawn", 3);
        MACreatureControl.Me.Invoke("DemoSpawn", 4);
    });
    static DebugConsole.Command z4 = new("z4", _s => { MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Zombie"), MABuilding.FindBuilding("Tavern"));    });

    static DebugConsole.Command h1 = new("h1", _s => { MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Giant"), MABuilding.FindBuilding("Factory"));    });
    private void DemoSpawn()
    {
        MAParser.SpawnCreature("Zombie", MACreatureInfo.GetInfo("Zombie"), MABuilding.FindBuilding("Factory"));        
    }
    [SerializeField] private List<MACharacterBase> m_creaturePrefabList = new List<MACharacterBase>();

    [SerializeField] private bool m_isAlarmActive = false;
    
    [Range(0.01f, 10f)][SerializeField] private float m_alarmCheckInterval = 2f;

    [SerializeField] private bool m_processTownBoundaries = true;
    [SerializeField] public bool m_debugDrawTownBoundaries = false;

    [SerializeField][Range(1, 8)] private float m_desiredScaleOffset = 6f;
    
    public Dictionary<PathManager.Path, Enclosure> m_enclosures = new();
    public bool IsCustomAlarmActive => m_isAlarmActive;
    public string[] CreaturePrefabNames
    {
        get
        {
            List<string> typeList = new List<string>();
            for (int i = 0; i < m_creaturePrefabList.Count; i++)
            {
                if (m_creaturePrefabList[i] != null)
                    typeList.Add(m_creaturePrefabList[i].gameObject.name);
            }
            return typeList.ToArray();
        }
    }

    public List<string> CreatureTypeNames
    {
        get
        {
            List<string> typeList = new List<string>();
            for(int i = 0; i < m_creaturePrefabList.Count; i++)
            {
                string newType = m_creaturePrefabList[i].GetType().Name;
                if(typeList.Contains(newType) == false)
                {
                    typeList.Add(newType);
                }
            }

            return typeList;
        }
    }

    private static bool s_showingDebugCreatures = false;
    private static DebugConsole.Command s_debugCreaturesCmd = new ("debugcreatures", _s =>
    {
        Utility.SetOrToggle(ref s_showingDebugCreatures, _s);
        if (s_showingDebugCreatures)
            GameManager.SetConsoleDisplay(() =>
            {
                var s = "";
                foreach (var c in NGManager.Me.m_MACreatureList)
                    s += $"{c.Name} [{c.m_ID}] {c.GetType().Name} {(c.GetComponent<Rigidbody>().isKinematic ? "[K]" : "")} {(((int)c.GetComponent<Rigidbody>().constraints & (int)RigidbodyConstraints.FreezePosition) != 0 ? "[F]" : "")}\n";
                return s;
            });
    }, "Show a list of active creatures", "<bool>");
    
    public MACharacterBase GetCreaturePrefabByName(string _maCreaturePrefab)
    {
        int iCreature =
            m_creaturePrefabList.FindIndex(x => x != null && x.gameObject.name.ToLower() == _maCreaturePrefab.ToLower()); //GetType().Name
        if(iCreature == -1)
        {
            Debug.LogError($"{GetType()} - GetCreaturePrefabByTypeName {_maCreaturePrefab} does not exist");
            return null;
        }

        return m_creaturePrefabList[iCreature];
    }
    
    public string GetCreaturePrefabByTypeName(string _maTypeName)
    {
        for(int i = 0; i < m_creaturePrefabList.Count; i++)
        {
            if(m_creaturePrefabList[i] == null)
            {
                Debug.LogError("Create list has null item");
                continue;
            }
            string newType = m_creaturePrefabList[i].GetType().Name;
            if(newType == _maTypeName)
            {
                return m_creaturePrefabList[i].gameObject.name;
            }
        }
        Debug.LogError("Unable to find creature: " + _maTypeName);
        return "";
    }
    public MACharacterBase SpawnNewCreatureAtBuilding(MACreatureInfo _info, MABuilding _building, Action<MACharacterBase> _returnInstance = null)
    {
        if(_building == null)
        {
            Debug.LogError($"{GetType()} - SpawnNewCreatureAtBuilding - _building is null when trying to spawn with prefabname '{_info.m_name}'");
            return null;
        }

        List<MASpawnArea> spawnAreas = new();
        _building.GetComponentsInChildren<MASpawnArea>(spawnAreas);
        //spawnAreas.Remove(_building.m_spawnArea);
        if(spawnAreas.Count == 0)
        {
            var creature = SpawnNewCreature(_info, (_newCharacter, _newInfo) =>
            {
                _newCharacter.m_insideMABuilding = _building;
                _newCharacter.Activate(_newInfo, _building, true, _building.DoorPosInner, Vector3.zero);
                _newCharacter.PostLoad();
                _returnInstance?.Invoke(_newCharacter);
            });
            return creature;
        }
        else
        {
            spawnAreas.RemoveAll(x => x.IsOccupied);
            if (spawnAreas.Count > 0)
            {
                var creature = SpawnNewCreatureAtPosition(_info, spawnAreas[0].transform.position,
                    (_newCharacter) =>
                    {
                        _returnInstance?.Invoke(_newCharacter);
                        spawnAreas[0].m_characterInArea = _newCharacter;
                        //_newCharacter.PostLoad();
                    }, _building, "", Vector3.zero);
                spawnAreas.RemoveAt(0);
                return creature;
            }
        }
        
        //fudge:
        
        spawnAreas.Clear();
        _building.GetComponentsInChildren<MASpawnArea>(spawnAreas);
        
        if(spawnAreas.Count == 0)
        {
            var creature = SpawnNewCreature(_info, (_newCharacter, _newInfo) =>
            {
                _newCharacter.m_insideMABuilding = _building;
                _newCharacter.Activate(_newInfo, _building, true, _building.DoorPosInner, Vector3.zero);
                _newCharacter.PostLoad();
                _returnInstance?.Invoke(_newCharacter);
            });
            return creature;
        }
        else
        {
            MASpawnArea sp = spawnAreas.PickRandom();
            var creature = SpawnNewCreatureAtPosition(_info, sp.transform.position,
                (_newCharacter) =>
                {
                    _returnInstance?.Invoke(_newCharacter);
                    sp.m_characterInArea = _newCharacter;
                    //_newCharacter.PostLoad();
                }, _building, "", Vector3.zero);
            spawnAreas.Remove(sp);
            return creature;
        }
    }
    
    public bool SpawnCreatureIfSpaceExists(MACreatureInfo _info, MABuilding _building, out MACharacterBase _returnInstance)
    {
        _returnInstance = null;
        
        if(_building == null)
        {
            Debug.LogError($"{GetType()} - SpawnNewCreatureAtBuilding - _building is null when trying to spawn with prefabname '{_info.m_name}'");
            return false;
        }

        List<MASpawnArea> spawnAreas = new();
        _building.GetComponentsInChildren<MASpawnArea>(spawnAreas);
        //spawnAreas.Remove(_building.m_spawnArea);
        if(spawnAreas.Count == 0)
        {
            var creature = SpawnNewCreature(_info, (_newCharacter, _newInfo) =>
            {
                _newCharacter.m_insideMABuilding = _building;
                _newCharacter.Activate(_newInfo, _building, true, _building.DoorPosInner, Vector3.zero);
                _newCharacter.PostLoad();
            });
            _returnInstance = creature;
            return creature != null;
        }
        else
        {
            spawnAreas.RemoveAll(x => x.IsOccupied || MASpawnArea.AssertNavPos(x.transform.position) == false);
            if (spawnAreas.Count > 0)
            {
                var creature = SpawnNewCreatureAtPosition(_info, spawnAreas[0].transform.position,
                    (_newCharacter) =>
                    {
                        spawnAreas[0].m_characterInArea = _newCharacter;
                        //_newCharacter.PostLoad();
                    }, _building, "", Vector3.up * (Mathf.PI * 2f * Random.Range(0f, 1f)));
                _returnInstance = creature;
                spawnAreas.RemoveAt(0);
                return creature != null;
            }
        }

        return false;
    }
    
    public bool SpawnCreatureIfSpaceExists(MACreatureInfo _info, Vector3 _pos, out MACharacterBase _returnInstance)
    {
        _returnInstance = null;
        if(_pos == Vector3.zero)
        {
            Debug.LogError($"{GetType()} - SpawnNewCreatureAtBuilding - _pos is null when trying to spawn with prefabname '{_info.m_name}'");
            return false;
        }

        List<MASpawnArea> spawnAreas = MASpawnArea.FindNearby(_pos);
        if (spawnAreas.Count == 0)
        {
            spawnAreas.Add(MASpawnArea.Create(new GameState_SpawnArea() { m_buildingUid = -1, m_worldPos = _pos }));
        }
        else
        {
            spawnAreas.RemoveAll(x => x.IsOccupied);
        }

        if (spawnAreas.Count > 0)
        {
            var creature = SpawnNewCreatureAtPosition(_info, spawnAreas[0].transform.position,
                (_newCharacter) =>
                {
                    spawnAreas[0].m_characterInArea = _newCharacter;
                    //_newCharacter.PostLoad();
                }, null, "", Vector3.up * (Mathf.PI * 2f * Random.Range(0f, 1f)));

            _returnInstance = creature;
            spawnAreas.RemoveAt(0);
            return creature != null;
        }

        return true; //we return true because the only issue is that we didn't have any space
    }

		public bool SpawnCreatureIfSpaceExists(MACreatureInfo _info, NamedPoint _namedPoint, int _numLeftToSpawn, out MACharacterBase _returnInstance)
    {
        _returnInstance = null;
				if(_namedPoint == null)
        {
            Debug.LogError($"{GetType()} - SpawnNewCreatureAtBuilding - _namedPoint is null when trying to spawn with prefabname '{_info.m_name}'");
            return false;
        }

				Vector3 pos = _namedPoint.transform.position;
        List<MASpawnArea> spawnAreas = _namedPoint.GetSpawnAreas();
        spawnAreas.RemoveAll(x => x.IsOccupied);

        if (spawnAreas.Count > 0)
        {
            var creature = SpawnNewCreatureAtPosition(_info, spawnAreas[0].transform.position,
                (_newCharacter) =>
                {
                    spawnAreas[0].m_characterInArea = _newCharacter;
                    _newCharacter.CharacterGameState.m_spawnPointName = _namedPoint.name;
                    //_newCharacter.PostLoad();
                }, null, "", Vector3.up * (Mathf.PI * 2f * Random.Range(0f, 1f)));

            _returnInstance = creature;
            spawnAreas.RemoveAt(0);
            return creature != null;
        }

        return true; //we return true because the only issue is that we didn't have any space
    }

    public bool IsValidTimeOfDay(MACharacterBase _characterBase)
    {
        if (_characterBase.GameState.m_isUnderground) return true;
        return IsValidTimeOfDay(_characterBase.CreatureInfo);
    }
    public bool IsValidTimeOfDay(MACreatureInfo _creatureInfo)
    {
        if (_creatureInfo == null) return false;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        return (DayNight.Me.IsWakeTimeForNightCreature && _creatureInfo.IsNightWalker) || (DayNight.Me.IsWakeTimeForDayCreature && _creatureInfo.IsDayWalker) || 
					(_creatureInfo.IsDayWalker && _creatureInfo.IsNightWalker);
#else
				return true;
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
    }

    public MACharacterBase SpawnNewCreatureAtPosition(MACreatureInfo _info, Vector3 _position, Action<MACharacterBase> _returnInstance = null, MABuilding _optionalHome = null, string _name = null, Vector3 _rot = default)
    {
        var creature = SpawnNewCreature(_info, (_characterBase, _creatureInfo) =>
        {
            _characterBase.Activate(_creatureInfo,
                _optionalHome != null ? _optionalHome : FindBuildingForCharacter(_creatureInfo.m_name),
                 true, _position, _rot);
            _characterBase.PostLoad();
            if(!_name.IsNullOrWhiteSpace())
            {
                _characterBase.Name = _name;
            }
            _returnInstance?.Invoke(_characterBase);
        });
        return creature;
    }
    
    public GameObject CreateFakeCreature(MACreatureInfo _info, Vector3 _position, Transform _holder)
    {
        MACharacterBase characterPrefab = GetCreaturePrefabByName(_info.m_prefabName);
        GameObject prefab = characterPrefab.EditorGetRandomSubTypePrefab();
        var go = Instantiate(prefab, _holder);
        go.transform.position = _position;
        RagdollController[] rdcs = go.GetComponentsInChildren<RagdollController>(true);
        Array.ForEach(rdcs,x => x.StartAnimatedState());
        return go;
    }

    public MACharacterBase GetAndSetupNewCreature(MACreatureInfo _creatureInfo, Vector3 _pos, bool _addToCharacterLists = true)
    {
        Vector3 rotation = Vector3.zero;
        MACharacterBase _newCharacter = SpawnNewCreature(_creatureInfo, null);
        _newCharacter.Activate(_creatureInfo, FindBuildingForCharacter(_creatureInfo.m_name), true, _pos, rotation, _addToCharacterLists);
        _newCharacter.PostLoad();
        // _newCharacter.ObjectiveWaypoint = null;
        // _newCharacter.DisableObjective();
        return _newCharacter;
    }

    private static Dictionary<string, Transform> m_typeParents = new();

    public static Transform GetOrCreateTypeParent(MACreatureInfo _creatureInfo)
    { 
        Transform typeParent = null;
        if (m_typeParents.TryGetValue(_creatureInfo.m_creatureType, out typeParent) == false)
        {
            typeParent  = GlobalData.Me.m_characterHolder.Find(_creatureInfo.m_creatureType);
            if (typeParent == null)
            {
                typeParent = CreateCharacterTypeParent(_creatureInfo.m_creatureType);
            }
            m_typeParents.Add(_creatureInfo.m_creatureType, typeParent);
        }
        else
        {
            if (typeParent == null)
            {
                typeParent = CreateCharacterTypeParent(_creatureInfo.m_creatureType);
            }
            m_typeParents[_creatureInfo.m_creatureType] = typeParent;
        }
        return typeParent;
    }
    
    private static Transform CreateCharacterTypeParent(string _typeName)
    {
        GameObject g = new GameObject(_typeName);
        g.transform.SetParent(GlobalData.Me.m_characterHolder, true);
        return g.transform;
    }
    
    private MACharacterBase SpawnNewCreature(MACreatureInfo _creatureInfo, Action<MACharacterBase, MACreatureInfo> _onSpawn)
    {
        if (_creatureInfo == null)
        {
            Debug.LogError($"{GetType()} - SpawnNewCreature - _creatureInfo is null");
            return null;
        }

        Transform typeParent = GetOrCreateTypeParent(_creatureInfo);
        
        var go = Instantiate(_creatureInfo.m_prefab, typeParent);
        MACharacterBase creature = go.GetComponent<MACharacterBase>();
        _onSpawn?.Invoke(creature, _creatureInfo);
        return creature;
    }

    public MACharacterBase SpawnCharacterWithData(GameState_Character _characterData)
    {
        //failsafe: recovering character/creature/animal data still saved without m_creatureInfoName.Remove after new seed save
        MACreatureInfo creatureInfo = null;
        string prefabName = "";
        MACharacterBase creature = null;
        if (_characterData.m_creatureInfoName.IsNullOrWhiteSpace())
        {
            // After chat with Thomas it was agreed this is no longer necessery
            /*prefabName = GetCreaturePrefabByTypeName(_characterData.m_type);

            List<MACreatureInfo> creatureInfosByPrefabName =
                MACreatureInfo.s_creatureInfos.FindAll(x => x.m_prefabName == prefabName);
            if (_characterData.m_name == "debug")
            {
                _characterData.m_name = "";
            }
            else
            {
                if (_characterData.m_type == nameof(MAChicken))
                {
                    _characterData.m_creatureInfoName = "Chicken";
                }
                else if (_characterData.m_type == nameof(MADog))
                {
                    _characterData.m_creatureInfoName = "Dog";
                }
            }

            if (creatureInfosByPrefabName.Count > 1 && _characterData.m_name.IsNullOrWhiteSpace() == false)
            {
                creatureInfo = creatureInfosByPrefabName.Find(x => x.m_name == _characterData.m_name);
                _characterData.m_creatureInfoName = creatureInfo.m_name;
            }
            else
            {
                creatureInfo = creatureInfosByPrefabName[0];
                _characterData.m_creatureInfoName = creatureInfo.m_name;
            }*/
        }
        else
        {
            creatureInfo = MACreatureInfo.GetInfo(_characterData.m_creatureInfoName);
            if (creatureInfo == null)
            {
                Debug.LogError($"{GetType()} - SpawnCharacterWithData - Failed to spawn character with creature name '{_characterData.m_creatureInfoName}' and type '{_characterData.m_type}'");
                return null;
            }
            _characterData.m_creatureInfoName = creatureInfo.m_name;
        }
        //remove above after new seed save

        prefabName = creatureInfo.m_prefabName;
        creature = SpawnNewCreature(creatureInfo, null);
        if (_characterData.m_id <= 0)
        {
            creature.AllocateID();
            _characterData.m_id = creature.m_ID;
        }
        
        if (creature == null)
        {
            Debug.LogError($"{GetType()} - SpawnCharacterWithData - Failed to spawn character with prefab name '{prefabName}', name '{creature.Name} and type '{_characterData.m_type}'");
            return null;
        }
        
        creature.ActivateWithSaveData(creatureInfo, _characterData);

        return creature;
    }
    
    private IEnumerator WaitForGameLoad(float _timeSecs, Action _action, Func<bool> _condition)
    {
        yield return new WaitUntil(() => _condition());
        yield return
            new WaitForSeconds(
                _timeSecs); //TODO: TS remove this once nav grid generation finish and we can properly know about it. slow computers stills tart navigation with van before nav grid gen is finished
        _action?.Invoke();
    }

    //TODO: TS - we should not have this method. we should always be spawning characters knowing exactly whether they should have an owner building or not, inside or not
    public static MABuilding FindBuildingForCharacter(string _characterBaseName) //TODO: TS - we need to link a building to a creature/hero but which one? knack!
    {
        string buildingName = "";
        switch(_characterBaseName.ToLower())
        {
            // case "giant":
            //     buildingName = "Tavern";
            //     break;
            case "werewolf":
                buildingName = "crypt";
                break;
            case "zombie":
                buildingName = "Graveyard";
                break;
            case "lurker":
                buildingName = "Crypt";
                break;
            case "troll":
                buildingName = "Graveyard";
                break;
        }
        var building = MABuilding.FindBuilding(buildingName);
        if(building == null)
        {
            //Debug.LogWarning($"DebugConsole.Command 'spawncreature' - building {buildingName} not found");
        }
        return building;
    }

    /// <summary>
    /// setting homePos to zero means it's Cleared. example: homepos=debug:(1,2,3)
    /// </summary>
    private static DebugConsole.Command s_setCharacterHomePos = new DebugConsole.Command("homepos", _name =>
    {
        string[] splitName = _name.Split(':');
        string _homePos = "";
        if (splitName.Length > 1)
        {
            _name = splitName[0];
            _homePos = splitName[1];
        }

        Vector3 homePos = _homePos.ToVector3();
        MAParser.SetCharacterDespawnPosition(_name, homePos);
    });
    
    private static DebugConsole.Command s_debugSendCharacterHome = new DebugConsole.Command("gohome", _name =>
    {
        MAParser.SetCharacterToGoHome(_name);
    });

    private static DebugConsole.Command s_debugFollow = new DebugConsole.Command("follow", _characters =>
    {
        string[] splitName = _characters.Trim().ToLower().Split(',', ';');

        if (splitName.Length == 2 && int.TryParse(splitName[0], out int charIdLeader) &&
            int.TryParse(splitName[1], out int charIdFollower))
        {
            NGMovingObject charLeader = NGManager.Me.FindCharacterByID(charIdLeader);
            NGMovingObject charFollower = NGManager.Me.FindCharacterByID(charIdFollower);
            if (charLeader && charFollower)
            {
                charFollower.Leader = charLeader;
                Debug.LogError(
                    $"DebugConsole.Command 'follow' - leader '{charLeader.name}' + follower '{charFollower.name}'");
                return;
            }
        }

        Debug.LogError($"DebugConsole.Command 'follow' failed. Params: '{_characters}'");
    });

    private static DebugConsole.Command s_debugSpawnCreature = new DebugConsole.Command("spawncreature", _type =>
    {
        string[] splitName = _type.Split(',');
        string _buildingId = "";

        MACharacterBase character = null;
        object spawnWhere = null;
        MABuilding building = null;
        if (splitName.Length > 1)
        {
            if (splitName.Length > 0)
            {
                _type = splitName[0];
                _buildingId = splitName[1];
            }
            else return;

            if (!string.IsNullOrWhiteSpace(_buildingId))
            {
                building = MABuilding.FindBuilding(_buildingId, true);
            }

            Vector3? pos = null;

            if (building == null)
            {
                if (splitName.Length > 1)
                {
                    building = FindBuildingForCharacter(_type);
                    string[] attempts =
                        { "Pos", "Building", "Decoration", "ComponentBuilding", "Components", "NamedPoint" };
                    for (int i = 0; i < attempts.Length; i++)
                    {
                        if (InputUtilities.GetObjectFromLocationString($"{attempts[i]}[{splitName[1]}]", out spawnWhere,
                                true) && spawnWhere != null)
                        {
                            building = spawnWhere as MABuilding;
                            if (building != null)
                            {
                                character = Me.SpawnNewCreatureAtBuilding(MACreatureInfo.GetInfo(_type), building);
                                Debug.LogError(
                                    $"DebugConsole.Command 'spawncreature' - '{_type}' - id: '{character.m_ID}' '{character.Name}' spawned at building  '{building.name}'");
                                return;
                            }

                            pos = spawnWhere as Vector3?;
                            if (pos != null)
                            {
                                character = Me.SpawnNewCreatureAtPosition(MACreatureInfo.GetInfo(_type), (Vector3)pos);
                                Debug.LogError(
                                    $"DebugConsole.Command 'spawncreature' - '{_type}' - id: '{character.m_ID}' '{character.Name}' spawned at position  '{(Vector3)pos}'");
                                return;
                            }
                        }
                    }
                }
            }
            else
            {
                character = Me.SpawnNewCreatureAtBuilding(MACreatureInfo.GetInfo(_type), building);
                Debug.LogError(
                    $"DebugConsole.Command 'spawncreature' - '{_type}' - id: '{character.m_ID}' '{character.Name}' spawned at building  '{building.name}'");
                return;
            }

            return;
        }
        
        bool result = false;
        LayerMask layerMask = 1 << LayerMask.NameToLayer("Default");
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hitInfo1, int.MaxValue, layerMask))
        {
            building = hitInfo1.collider.GetComponentInParent<MABuilding>();

            if (building != null)
            {
                character = Me.SpawnNewCreatureAtBuilding(MACreatureInfo.GetInfo(_type), building);
                Debug.LogError(
                    $"DebugConsole.Command 'spawncreature' - '{_type}' - id: '{character.m_ID}' '{character.Name}' spawned at building  '{building.name}'");
                return;
            }
        }

        layerMask = 1 << GlobalData.Me.m_moaTerrain.gameObject.layer;
        if (Physics.Raycast(ray, out RaycastHit hitInfo2, int.MaxValue, layerMask))
        {
            character = Me.SpawnNewCreatureAtPosition(MACreatureInfo.GetInfo(_type), hitInfo2.point);
        }
    });
   
    private static DebugConsole.Command s_debugSpawnCreatureLevels = new DebugConsole.Command("spawncreaturelevels", _type =>
    {       
        string[] splitName = _type.Split(',',';');
        string _buildingId = "";
        if(splitName.Length > 1)
        {
            _type = splitName[0];
            _buildingId = splitName[1];
        }
        
        var variants = MACreatureInfo.GetInfoByType(_type);
        
        float secs = 0f;
        foreach(var x in variants)
        {
            if(GameManager.Me != null)
                GameManager.Me.StartCoroutine(PassiveInvoke(secs++, 
                    () => DebugConsole.s_consoleActions["spawncreature"].Item1?.Invoke(string.Join(',', x.m_name, _buildingId))));
        }
    });

    static IEnumerator PassiveInvoke(float secs, Action _action)
    {
        yield return new WaitForSeconds(secs);
        _action?.Invoke();
    }

    public void ProcessTownBoundary(PathManager.Path _wall)
    {
        if((_wall.Set.m_isPerimeterType && _wall.IsCycle) == false) return;

        Transform tr = transform;
        GameObject newEnclosureDataHolder = new GameObject();
        newEnclosureDataHolder.transform.parent = tr;
        newEnclosureDataHolder.name = "Enclosure " + m_enclosures.Count;
 
        PolygonCollider2D polygonCollider = newEnclosureDataHolder.AddComponent<PolygonCollider2D>();
        PolygonCollider2D polygonColliderWide = newEnclosureDataHolder.AddComponent<PolygonCollider2D>();
    
        Enclosure enclosure = new Enclosure(polygonCollider, polygonColliderWide, m_desiredScaleOffset);
        enclosure.Execute(_wall);
        m_enclosures.Add(_wall, enclosure);

        if(m_debugDrawTownBoundaries)
        {
            MakeDebugPolygonRender(0, polygonCollider);
            MakeDebugPolygonRender(1, polygonColliderWide);
        }
        else
        {
#if DEBUG_TOWN_BOUNDARIES
            MakeDebugPolygonRender(0, polygonCollider);
            MakeDebugPolygonRender(1, polygonColliderWide);
#endif
        }

        void MakeDebugPolygonRender(int i, PolygonCollider2D _pointSource)
        {
            GameObject newDebugVisualiser = new GameObject();
            Transform debugTr = newDebugVisualiser.transform;
            debugTr.parent = newEnclosureDataHolder.transform;
            debugTr.localPosition = Vector3.zero;
            MeshRenderer meshRenderer = newDebugVisualiser.AddComponent<MeshRenderer>();
            MeshFilter meshFilter = newDebugVisualiser.AddComponent<MeshFilter>();
            meshRenderer.material.color = new Color(Color.green.r, Color.green.g / (i + 1), Color.green.b, 0.25f);
            debugTr.Rotate(Vector3.right, 90f);//do not rotate a 2d collider
            debugTr.position += Vector3.up * 110;
            meshFilter.mesh = _pointSource.CreateMesh(false, false);
        }

            //watch out - scaling by off number might end up with it missing the final point? certainly the drawn poly shows it
            //also - make sure you apply this to singular enclosures. biggest enclosure to enclose them all etc. etc
    }
    
    public List<Enclosure> GetEnclosures(Vector3 _pos)
    {
        List<Enclosure> enclosures = new();

        foreach(var enclosure in MACreatureControl.Me.m_enclosures)
        {
            if(enclosure.Value.EnclosedArea.OverlapPoint(_pos.GetXZVector2()))
            {
                enclosures.Add(enclosure.Value);
            }
        }
        return enclosures;
    }
    
    protected void Start()
    {
        Action onGameLoad = () =>
        {
            PathManager.m_onGlobalNavGridChange -= OnGlobalNavGridChange;
            PathManager.m_onGlobalNavGridChange += OnGlobalNavGridChange;
        };
        StartCoroutine(WaitForGameLoad(0f, onGameLoad, () => GameManager.Me.PathsLoaded && GlobalData.Me.BatchTerrainOperationsInProgress == false));
    }

    private bool m_initialBoundariesProcessed = false;

    public bool TryProcessBoundaries()
    {
        if (m_processTownBoundaries && m_initialBoundariesProcessed == false)
        {
            ProcessTownBoundaries();
            m_initialBoundariesProcessed = true;
        }
        return m_processTownBoundaries;
    }

    private void ProcessTownBoundaries()
    {
        if(m_processTownBoundaries == false) return;
        
        if(GlobalData.Me.BatchTerrainOperationsInProgress ) return;
        
        foreach(var enclosure in m_enclosures)
        {
            enclosure.Value.DestroyMe();
        }
        m_enclosures.Clear();
        
        foreach(PathManager.Path wall in GameManager.Me.m_state.m_paths)
        {
            if((wall.Set.m_isPerimeterType && wall.IsCycle) == false) continue;
            //PathManager.Path.NumBefore()
            //GameManager.Me.m_state.m_paths.
            ProcessTownBoundary(wall);
        }
    }
    
    private bool m_isBoundaryDirty = false;
    private void OnGlobalNavGridChange()
    {
        //Debug.Log($"{GetType().Name} - OnGlobalNavGridChange");//TODO: MAYE MAKE SURE THINGS ARE ONLY DONE WHEN LOADD ETC , dont even break into callback

        m_isBoundaryDirty = true;
    }

    private void LateUpdate()
    {
        if (m_isBoundaryDirty)
        {
            ProcessTownBoundaries();
            m_isBoundaryDirty = false;
        }
    }

    protected override void _OnDestroy()
    {
        PathManager.m_onGlobalNavGridChange -= OnGlobalNavGridChange;
        
        base._OnDestroy();
    }

    private IEnumerator CheckIfAlarm()
    {/*
        do
        {
            yield return new WaitForSeconds(m_alarmCheckInterval);

            bool activateAlarm = false;
        
            foreach(var creature in NGManager.Me.m_MACreatureList)
            {
                Vector3 creaturePos = creature.transform.position.GetXZ();
                float visionRadiusSq = creature.CreatureInfo.m_visionRadius * creature.CreatureInfo.m_visionRadius;
                Vector3 closestPointToPath = RoadManager.Me.m_pathSet.GetClosestPathToPoint(null, true, creaturePos, visionRadiusSq, out PathManager.Path path, x => x.Set.m_isPerimeterType);
                Debug.Log($"Creature alarm - {(closestPointToPath - creaturePos)} - sqrm {(closestPointToPath - creaturePos).sqrMagnitude} - xzsqrm {(closestPointToPath - creaturePos).xzSqrMagnitude()} - {(closestPointToPath - creaturePos).xzSqrMagnitude() > 1}");
                float distSqToWall = (closestPointToPath - creaturePos).xzSqrMagnitude();
                GameManager.Me.ClearGizmos("pali");
                GameManager.Me.AddGizmoLine("pali",creaturePos.GroundPosition(0.5f), (creaturePos + (closestPointToPath - creaturePos)).GroundPosition(0.5f), Color.blue, 0.05f);
                if(distSqToWall > 1 && distSqToWall < visionRadiusSq)
                {
                    activateAlarm = true;
                }
                else
                {
                    foreach(var VARIABLE in NGManager.Me.m_maBuildings)
                    {
                        float distToBuildinSq = (VARIABLE.GetCentalPosition().GetXZ() - creature.transform.position.GetXZ()).xzSqrMagnitude();
                        if(distToBuildinSq < visionRadiusSq)
                        {
                            activateAlarm = true;
                            break;
                        }
                    }
                }
                if(activateAlarm) break;
            }

            if(activateAlarm != m_isAlarmActive)
            {
                Debug.Log($"{GetType()} - Alarm switched to active: {activateAlarm}");
                ActivateTownAlarm(activateAlarm);
            }
        }
        while (NGManager.Me.IsUnderAttack());
        */
        yield break;
    }

    private void ActivateTownAlarm(bool _activate)
    {
        m_isAlarmActive = _activate;
    }

    // [SerializeField]
    // private MAAreaSelectionCircle m_selectionCircle = new MAAreaSelectionCircle(5f);
    
    void Update()
    {
        // if (m_selectionCircle.m_character == null || m_selectionCircle.m_character.GetComponent<CharacterPickupBehaviour>().Held == false)
        // {
        //     m_selectionCircle.m_character = null;
        //
        //     foreach (MACharacterBase maCharacterBase in NGManager.Me.m_MAHeroList)
        //     {
        //         if (maCharacterBase.GetComponent<CharacterPickupBehaviour>().Held)
        //         {
        //             m_selectionCircle.m_character = maCharacterBase;
        //             break;
        //         }
        //     }
        // }
        // m_selectionCircle.Update();
    }
    
}

#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(MACreatureControl))]
public class MACreatureControlEditor : Editor
{
    private bool m_processTownBoundaries = false;

    public void OnEnable()
    {
        MACreatureControl creatureControl = (MACreatureControl)target;
        if(Application.isPlaying)
            m_processTownBoundaries = creatureControl.TryProcessBoundaries();
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        MACreatureControl creatureControl = (MACreatureControl)target;

        if(Application.isPlaying)
        {
            if(creatureControl.TryProcessBoundaries() != m_processTownBoundaries)
            {
                m_processTownBoundaries = !m_processTownBoundaries;
                if(m_processTownBoundaries)
                {
                    creatureControl.TryProcessBoundaries();
                }
            }
            
            foreach(var info in MACreatureInfo.s_creatureInfos)
            {
                string buildingName = "Crypt";
                if(GUILayout.Button($"Knack Test - SpawnNewCreatureAtBuilding: b: {buildingName}, c: {info.m_name}"))
                {
                    MABuilding building = MACreatureControl.FindBuildingForCharacter(info.m_name);
                    creatureControl.SpawnNewCreatureAtBuilding(info, building);
                }
            }
        }
    }
}
#endif