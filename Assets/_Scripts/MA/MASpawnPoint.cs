using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class MASpawnPoint : MonoBehaviour
{
	public static Dictionary<int, MASpawnPoint> s_spawnPoints = new Dictionary<int, MASpawnPoint>();
	
	const string PrefabPath = "_Prefabs/Spawns/MASpawnPoint";
	public const string SeedSavePath = "Assets/Resources/SpawnPoints.bytes";
	
	public bool m_paused = false;
	public string m_name = "None";

	[SerializeField]
	private Transform m_spawnPositionsHolder;
	
	[SerializeField]
	private SphereCollider m_triggerRadius = null;
	
	[SerializeField]
	private GameObject m_deSpawnArea = null;
	
	[SerializeField] private bool m_isProximityTriggered = false;
	[SerializeField] private bool m_proximityTriggeredThisFrame = false;
	[SerializeField] private bool m_dayNightChangedThisFrame = false;
	
	[SerializeField][ReadOnlyInspector]	private List<Transform> m_spawnPositions = new();
	[SerializeField][ReadOnlyInspector] private List<MASpawnPhase> m_spawnPhases = new();
	[SerializeField][ReadOnlyInspector] protected MABuilding m_linkedBuilding;
	[SerializeField][ReadOnlyInspector] private int m_ID = -1;
	[SerializeField][ReadOnlyInspector] private int m_totalSpawnCountSoFar = 0;
	[SerializeField][ReadOnlyInspector] private int m_phaseSpawnCountSoFar = 0;
	[SerializeField][ReadOnlyInspector] private int m_waveSpawnCountSoFar = 0;
	[SerializeField][ReadOnlyInspector] private int m_iCurrentSpawnPhase = 0;
	[SerializeField][ReadOnlyInspector] private float m_spawnDelay = -1f;
	[SerializeField][ReadOnlyInspector] private float m_timeActive = -1f;
	[SerializeField][ReadOnlyInspector] private bool m_isNight = false;
	[SerializeField][ReadOnlyInspector] private int m_numSpawnsEscapedThisPhase = 0;
	[SerializeField][ReadOnlyInspector] private int m_timesCurrentPhaseDefeated = 0;
	[SerializeField][ReadOnlyInspector] private float m_waveElapsedTime = 0f;
	[SerializeField][ReadOnlyInspector] private bool m_firstWaveOfFirstPhaseOfNight = false;
	
	[HideInInspector] 
	public bool m_debugSelectedForWindow = false;
	
	public Action m_onSpawn = null;
	
	private Coroutine m_spawningNow = null;

	private Dictionary<int, MACharacterBase> m_currentCharacters = new();
	
	private Transform m_debugCreatureHolder;
	
	public int ID => m_ID;
	public int PhaseTotalSpawnCount => IsSpawnPhaseValid ? CurrentSpawnPhase.m_totalPhaseSpawnCount : 0;
	public int PhaseNumWaves => IsSpawnPhaseValid ? CurrentSpawnPhase.m_numWaves : 0;
	public int EscapedThisPhase => m_numSpawnsEscapedThisPhase;
	public int TimesCurrentPhaseDefeated => m_timesCurrentPhaseDefeated;
	public float WaveDelayInSeconds => IsSpawnPhaseValid ? CurrentSpawnPhase.m_waveDelayInSeconds : 0f;
	
	public bool HasBeenTriggered => m_currentCharacters.Count > 0 || m_phaseSpawnCountSoFar > 0 || m_phaseSpawnCountSoFar > 0 || m_timeActive >= 0 || m_totalSpawnCountSoFar > 0;
	public bool IsSpawnPhaseValid => m_iCurrentSpawnPhase >= 0 && m_iCurrentSpawnPhase < m_spawnPhases.Count;
	
	public List<Transform> SpawnPositions => m_spawnPositions;

	public MACreatureSpawnInfo PhaseCreatureSpawnInfo => IsSpawnPhaseValid ? CurrentSpawnPhase.m_maCreatureSpawnInfo : null;

	public bool SpawnConditionsMet => SpawnConditionsCheck();
	
	public bool TriggerConditionsMet => TriggerConditionsCheck();
	
	public float TriggerRadius
	{
		get => m_triggerRadius.radius;
		set
		{
			m_triggerRadius.radius = value;
			var sphere = m_triggerRadius.GetComponentInChildren<MeshRenderer>(true);
			if (sphere)
				sphere.transform.localScale = Vector3.one * m_triggerRadius.radius;
		}
	}
	
	public MASpawnPhase CurrentSpawnPhase
	{
		get
		{
			if (m_spawnPhases.Count == 0) m_spawnPhases.Add(new MASpawnPhase(0));
			if (IsSpawnPhaseValid == false)
			{
				Debug.LogError($"{GetType().Name} - {name}. Spawn Phase Index out of range: '{m_iCurrentSpawnPhase}'");
				return null;//m_iCurrentSpawnPhase %= m_spawnPhases.Count;
			}
			return m_spawnPhases[m_iCurrentSpawnPhase];
		}
	}

	public bool IsSpawnPointOfCharacter(int _characterId)
	{
		return m_currentCharacters.ContainsKey(_characterId);
	}
	
	public bool IsPhaseComplete(MASpawnPhase _phase)
	{
		if (m_numSpawnsEscapedThisPhase > 0) return false;
		int i = m_spawnPhases.FindIndex(x => x == _phase);
		if (i < m_iCurrentSpawnPhase) return true;
		if (i == m_iCurrentSpawnPhase) return m_phaseSpawnCountSoFar >= CurrentSpawnPhase.m_totalPhaseSpawnCount;
		return false;
	}

	private void Awake()
	{
		Transform tr = transform;
#if UNITY_EDITOR
		m_debugCreatureHolder = new GameObject("Debug Creature Holder").transform;
		m_debugCreatureHolder.SetParent(tr, false);
		
		if (GlobalData.Me.m_spawnPointHolder != null && tr.parent != GlobalData.Me.m_spawnPointHolder)
		{
			tr.SetParent(GlobalData.Me.m_spawnPointHolder, true);
		}
#else
		m_editorIndicatorDummy = null;
#endif
		m_spawnPositionsHolder = m_spawnPositionsHolder ?? transform.Find("SpawnPositionsHolder");
		m_triggerRadius = gameObject.GetComponentInChildren<SphereCollider>(true);
		TriggerCollisionDelegate trigger = m_triggerRadius.GetComponent<TriggerCollisionDelegate>();
		trigger.m_onTriggerEnter -= OnTrigger;
		trigger.m_onTriggerEnter += OnTrigger;
		if (GlobalData.Me != null) tr.position = tr.position.GroundPosition();
	}
	
	private void OnEnable()
	{
		m_triggerRadius = gameObject.GetComponentInChildren<SphereCollider>(true); 
		TriggerCollisionDelegate trigger = m_triggerRadius.GetComponent<TriggerCollisionDelegate>();
		trigger.m_onTriggerEnter -= OnTrigger;
		trigger.m_onTriggerEnter += OnTrigger;
		name = $"{GetType().Name} - {m_name}";

		if (m_ID >= 0)
		{
			if (s_spawnPoints.TryAdd(m_ID, this) == false)
			{
				s_spawnPoints[m_ID] = this;
			}
		}
	}

	private void Start()
	{
		Transform tr = transform;
		if (GlobalData.Me != null) tr.position = tr.position.GroundPosition();
	}

	private void OnDestroy()
	{
		s_spawnPoints.Remove(m_ID);
		m_onSpawn = null;
		TriggerCollisionDelegate trigger = m_triggerRadius.GetComponent<TriggerCollisionDelegate>();
		trigger.m_onTriggerEnter -= OnTrigger;
	}

	private void Update()
	{
		// #if UNITY_EDITOR
		// UpdateEditorDummies();
		// #endif

		if (m_paused)
		{
#if UNITY_EDITOR
			if (name.Contains("-Paused") == false) name += " -Paused";
#endif
			return;
		}
		else
		{
#if UNITY_EDITOR
			int iStr = name.IndexOf(" -Paused");
			if (iStr > -1) name = name.Remove(iStr, name.Length - iStr);
#endif
		}

		bool isNight = DayNight.Me.m_isFullNight;
		m_dayNightChangedThisFrame = m_isNight != isNight;
		m_isNight = isNight;

		if (m_dayNightChangedThisFrame)
			m_firstWaveOfFirstPhaseOfNight = true;
		if (m_currentCharacters.Count > 0)
			m_firstWaveOfFirstPhaseOfNight = false;
		
		m_proximityTriggeredThisFrame = m_proximityTriggeredThisFrame != m_isProximityTriggered;
		
		if (TriggerConditionsMet)
		{
			if (m_timeActive >= 0)
			{
				if (m_timeActive < m_spawnDelay)
				{
					m_timeActive += Time.deltaTime;
				}
			}
			else
			{
				m_timeActive = 0f;
			}
		
			if (SpawnConditionsMet)
			{
				if (!m_firstWaveOfFirstPhaseOfNight && (m_waveElapsedTime < WaveDelayInSeconds))
				{
					m_waveElapsedTime += Time.deltaTime;
				}
				else
				{
					SpawnWave();
				}
			}
		}

		if (m_currentCharacters.Count > 0)
		{
			List<int> keysToRemove = new();
			foreach(var characterId in m_currentCharacters.Keys)
			{
				MACharacterBase charBase = m_currentCharacters[characterId];
				if (charBase == null || charBase.CharacterUpdateState.State == CharacterStates.Despawn)
				{
					m_numSpawnsEscapedThisPhase++;
					keysToRemove.Add(characterId);
				} 
				else if (charBase.CharacterGameState.IsAlive == false ||
				   charBase.CharacterUpdateState.State == CharacterStates.Dead ||
				   charBase.CharacterUpdateState.State == CharacterStates.Dying)
				{
					keysToRemove.Add(characterId);
				}
			}

			foreach(var keyToRemove in keysToRemove)
			{
				TryRemoveCharacter(keyToRemove);
			}
		}
		
		if (m_currentCharacters.Count == 0)
		{
			if (IsPhaseComplete(CurrentSpawnPhase))
			{
				if (m_iCurrentSpawnPhase + 1 >= m_spawnPhases.Count)
				{
					m_iCurrentSpawnPhase = m_spawnPhases.Count;
					m_timeActive = -1;
					m_currentCharacters.Clear();
					m_isProximityTriggered = false;
					//m_isMacroTriggered = false;
					//SetupConditions();
					
					gameObject.SetActive(false);
				}
				
				if (m_iCurrentSpawnPhase < m_spawnPhases.Count)
				{
					MACreatureSpawnInfo.CreatureSpawnPointType previousPhaseSpawnType = PhaseCreatureSpawnInfo.SpawnPointType;
					
					m_timesCurrentPhaseDefeated++;
					
					if (CurrentSpawnPhase.m_isPerpetualSpawnPhase == false)
					{
						m_iCurrentSpawnPhase++;

						switch(previousPhaseSpawnType)
						{
							case MACreatureSpawnInfo.CreatureSpawnPointType.DayNight:
								CurrentSpawnPhase.m_disabledUntil = new Func<bool>(() =>
									m_dayNightChangedThisFrame &&
									MACreatureControl.Me.IsValidTimeOfDay(PhaseCreatureSpawnInfo.CreatureInfo));
								break;
							case MACreatureSpawnInfo.CreatureSpawnPointType.Proximity:
								CurrentSpawnPhase.m_disabledUntil = new Func<bool>(() => m_proximityTriggeredThisFrame && m_isProximityTriggered);
								break;
						}
					}
					
					ResetSpawnPhase();
				}
			}
			else
			{
				//end of wave
				if ((TriggerConditionsMet && SpawnConditionsMet) == false)
				{
					if (CurrentSpawnPhase.m_isPerpetualSpawnPhase && m_timesCurrentPhaseDefeated > 0)
					{
						m_iCurrentSpawnPhase++;
						m_timesCurrentPhaseDefeated = 0;
					}
					
					//if we arent eligible to spawn next wave & must reset phase
					ResetSpawnPhase();
					
					m_isProximityTriggered = false;
					
					//SetupConditions();
				}
			}
		}

		m_dayNightChangedThisFrame = false;
		m_proximityTriggeredThisFrame = false;
	}
	
	public void ResetSpawnPoint()
	{
		m_isProximityTriggered = false;
		m_proximityTriggeredThisFrame = false;
		m_dayNightChangedThisFrame = false;
		m_totalSpawnCountSoFar = 0;
		m_phaseSpawnCountSoFar = 0;
		m_waveSpawnCountSoFar = 0;
		m_iCurrentSpawnPhase = 0;
		m_spawnDelay = -1f;
		m_timeActive = -1f;
		m_isNight = false;
		m_numSpawnsEscapedThisPhase = 0;
		m_timesCurrentPhaseDefeated = 0;
		m_waveElapsedTime = 0f;
	}
	
	private void ResetSpawnPhase()
	{
		m_phaseSpawnCountSoFar = 0;
        m_waveSpawnCountSoFar = 0;
        m_numSpawnsEscapedThisPhase = 0;
		m_waveElapsedTime = 0f;
	}

	public void Activate(SaveLoadSpawnPoint _saveData, bool _dontAddToMASpawnPoints = false, bool _showDebug = false)
	{
		m_paused = _saveData.m_paused;
		m_name = _saveData.m_name;
		m_linkedBuilding = NGManager.Me.FindBuildingByID(_saveData.m_linkedBuildingId);
		m_name = _saveData.m_name;
		transform.position = _saveData.m_position.GroundPosition();
		m_iCurrentSpawnPhase = _saveData.m_iCurrentSpawnPhase;
		
		foreach(var spawnPosition in _saveData.m_spawnPositions)
		{
			AddSpawnPosition(spawnPosition);
		}
		
		//TS - failsafe in case old save data has no spawn phases defined:
		if (_saveData.m_spawnPhases.Count == 0)
		{
			AddSpawnPhase(CreateStandardSpawnPhaseInfo());
		}
		
		foreach(var spawnPhase in _saveData.m_spawnPhases)
		{
			AddSpawnPhase(spawnPhase);
		}
		

		TriggerRadius =_saveData.m_radius;
		m_ID = _saveData.m_ID;
		for(int i = _saveData.m_currentCharacterIds.Count - 1; i >= 0; i--)
		{
			MACharacterBase characterBase = NGManager.Me.FindCharacterByID(_saveData.m_currentCharacterIds[i]);
			if (characterBase != null)
			{
				m_currentCharacters.Add(_saveData.m_currentCharacterIds[i], characterBase);
			}
			else
			{
				_saveData.m_currentCharacterIds.RemoveAt(i);
			}
		}

		m_totalSpawnCountSoFar = _saveData.m_totalSpawnCountSoFar;
		m_phaseSpawnCountSoFar = _saveData.m_spawnCountSoFar;
		m_waveSpawnCountSoFar = _saveData.m_waveCountSoFar;
		
		m_numSpawnsEscapedThisPhase = _saveData.m_numSpawnsEscapedThisPhase;
		m_timesCurrentPhaseDefeated = _saveData.m_timesCurrentPhaseDefeated;
		m_timeActive = _saveData.m_timeActive;
		m_spawnDelay = _saveData.m_spawnDelay;
		
		ShowDebug(_showDebug);
		
		name = $"{GetType().Name} - {m_name} - {(enabled ? "" : " - added")}";
		if (_dontAddToMASpawnPoints == false && NGManager.Me.m_MACreatureSpawnPoints.Contains(this) == false)
		{
			if (m_ID < 0)
			{
				int highestId = -1;
				for (int i = 0; i < NGManager.Me.m_MACreatureSpawnPoints.Count; i++)
				{
					if (NGManager.Me.m_MACreatureSpawnPoints[i].m_ID > highestId)
						highestId = NGManager.Me.m_MACreatureSpawnPoints[i].m_ID;
				}

				m_ID = ++highestId;
			}
			NGManager.Me.m_MACreatureSpawnPoints.Add(this);
		}

		m_triggerRadius.enabled = !_dontAddToMASpawnPoints;
		enabled = !_dontAddToMASpawnPoints;

		if (enabled)
		{
			if (s_spawnPoints.TryAdd(m_ID, this) == false)
			{
				s_spawnPoints[m_ID] = this;
			}
			//SetupConditions();
		}
	}
	
	public void Enable()
	{
		if (NGManager.Me.m_MACreatureSpawnPoints.Contains(this) == false)
		{
			int highestId = -1;			
			for(int i = 0; i < NGManager.Me.m_MACreatureSpawnPoints.Count; i++)
			{
				if (NGManager.Me.m_MACreatureSpawnPoints[i].m_ID > highestId)
					highestId = NGManager.Me.m_MACreatureSpawnPoints[i].m_ID;
			}
			m_ID = ++highestId;
            NGManager.Me.m_MACreatureSpawnPoints.Add(this);
		}

		m_triggerRadius.enabled = true;
		enabled = true;
		//SetupConditions();
	}
	
	public void DestroyMe()
	{
		UnLinkBuilding();
		NGManager.Me.m_MACreatureSpawnPoints.Remove(this);
		Destroy(gameObject);
	}
	
	public bool TryRemoveCharacter(int _id)
	{
		if (m_currentCharacters.Remove(_id))
		{
			return true;
		}
		return false;
	}

	private void AddCurrentCharacter(MACharacterBase _characterBase)
	{
		if (_characterBase != null) m_currentCharacters.Add(_characterBase.m_ID, _characterBase);
	}
	
	private void AddCurrentCharacter(int _id)
	{
		MACharacterBase _charBase = NGManager.Me.FindCharacterByID(_id);
		AddCurrentCharacter(_charBase);
	}

	private bool TriggerConditionsCheck()
	{
		if (IntroControl.IsInIntro) return false;
		if (m_spawningNow != null) return false;
		if((m_spawnPhases.Count > 0 && m_iCurrentSpawnPhase < m_spawnPhases.Count) == false) return false;
		
		MACreatureSpawnInfo phaseCreatureSpawnInfo = PhaseCreatureSpawnInfo;
		if (phaseCreatureSpawnInfo != null)
		{
			MACreatureInfo info = PhaseCreatureSpawnInfo.CreatureInfo;
			if (info != null)
			{
				if (MACreatureControl.Me.IsValidTimeOfDay(info) == false) return false;
				if (PhaseCreatureSpawnInfo.SpawnPointType == MACreatureSpawnInfo.CreatureSpawnPointType.Proximity &&
				    m_isProximityTriggered == false) return false;
			}
		}
		
		MASpawnPhase currentSpawnPhase = CurrentSpawnPhase;
        bool enabledAgain = currentSpawnPhase.m_disabledUntil == null || currentSpawnPhase.m_disabledUntil.Invoke();
        if (enabledAgain)
        {					
        	currentSpawnPhase.m_disabledUntil = null;
        }
        else return false;

        return true;
	}

	private bool SpawnConditionsCheck()
	{
		if (GlobalData.Me.BatchTerrainOperationsInProgress ||
		    GameManager.Me.LoadComplete == false ||
		    IntroControl.IsInIntro) return false;

		MACreatureSpawnInfo phaseCreatureSpawnInfo = PhaseCreatureSpawnInfo;
		if (phaseCreatureSpawnInfo == null) return false;
        
		if (MACreatureControl.Me.IsValidTimeOfDay(phaseCreatureSpawnInfo.CreatureInfo) == false) return false;
        
		switch(phaseCreatureSpawnInfo.SpawnType)
		{
			case MACreatureSpawnInfo.CreatureSpawnType.OneShot:
				if (m_phaseSpawnCountSoFar != 0) return false;
				break;
			case MACreatureSpawnInfo.CreatureSpawnType.Rebirth:
				if (m_phaseSpawnCountSoFar >= PhaseTotalSpawnCount) return false;
				if (m_currentCharacters.Count != 0) return false;
				break;
			// RW-05-MAR-25: This type has the following rules:
			// * All characters it's previously spawned must be dead.
			// * They can't have died today - it has to be at least one day ago.
			// * After the specified day, it keeps checking every subsequent day forever.
			case MACreatureSpawnInfo.CreatureSpawnType.DailyOneShot:
				if (m_currentCharacters.Count != 0) return false;
				break;
		}

		if (m_timeActive < m_spawnDelay) return false;
        
		switch(phaseCreatureSpawnInfo.SpawnPointType)
		{
			case MACreatureSpawnInfo.CreatureSpawnPointType.Proximity:
				if(m_isProximityTriggered == false) return false;
				break;
			case MACreatureSpawnInfo.CreatureSpawnPointType.DayNight:
				if (MACreatureControl.Me.IsValidTimeOfDay(PhaseCreatureSpawnInfo.CreatureInfo) == false);
				break;
			// case MACreatureSpawnInfo.CreatureSpawnPointType.Macro:
			// 	 m_isMacroTriggered
			// 	break;
		}

		return true;
	}

	private void UnLinkBuilding()
	{
		m_linkedBuilding = null;
	}

	private void SpawnWave()
	{
		m_timeActive = m_spawnDelay;
		m_waveElapsedTime = 0f;
		m_firstWaveOfFirstPhaseOfNight = false;

		int phaseMaxSpawnCount = PhaseTotalSpawnCount;
		int phaseNumWaves = PhaseNumWaves;
		int waveSize = phaseMaxSpawnCount / phaseNumWaves;
		int remainder = phaseMaxSpawnCount % phaseNumWaves;
		int waveCountLeft = phaseNumWaves - m_waveSpawnCountSoFar;
		
		if (waveCountLeft <= remainder)
		{
			waveSize += 1;
		}
		
		Queue<SpawnInfo> spawnInfos = new();
		int startingIndex = UnityEngine.Random.Range(0, m_spawnPositions.Count);
		int countSpawnedInWave = 0;
		while(countSpawnedInWave < waveSize && m_phaseSpawnCountSoFar < PhaseTotalSpawnCount)
		{
			int creatureLevel = 1;
			Vector3 spawnPos = m_spawnPositions.Count == 0 ? transform.position :
				m_spawnPositions[(startingIndex + countSpawnedInWave) % m_spawnPositions.Count].position;
				
			SpawnInfo newSpawnInfo = new SpawnInfo()
			{
				m_info = PhaseCreatureSpawnInfo.CreatureInfo,
				m_position = spawnPos,
				m_returnInstance = OnSpawn,
				m_optionalHomeBuilding = m_linkedBuilding,
			};
			spawnInfos.Enqueue(newSpawnInfo);
			countSpawnedInWave++;
			m_phaseSpawnCountSoFar++;
			m_totalSpawnCountSoFar++;
		}
		
		m_spawningNow = MACreatureControl.Me.StartCoroutine(SpawnEachFrame(spawnInfos));
		
		m_waveSpawnCountSoFar++;

		m_onSpawn?.Invoke();
	}

	private void AddSpawnPosition(SaveLoadSpawnPoint.BasicTransformDetail _savedTransform)
	{
		if (m_deSpawnArea != null)
		{
			GameObject clonedDeSpawnArea = Instantiate(m_deSpawnArea, m_spawnPositionsHolder, false);
			Transform spawnPos = clonedDeSpawnArea.transform;
			clonedDeSpawnArea.name = $"SpawnPosition [{m_spawnPositions.Count}]";
			spawnPos.position = _savedTransform.m_position.GetVector3XZ().GroundPosition();
			spawnPos.rotation = Quaternion.Euler(_savedTransform.m_rotation);
			m_spawnPositions.Add(spawnPos);
		}
	}
	
	private void AddSpawnPhase(MASpawnPhase.SaveLoadSpawnPhase _spawnPhase)
	{
		MASpawnPhase spawnPhase = new MASpawnPhase(_spawnPhase);
		m_spawnPhases.Add(spawnPhase);
	}
	
	private void OnTrigger(Collider _other)
	{
		if(TriggerConditionsMet == false) return;

		MACharacterBase ch = _other.gameObject.GetComponent<MACharacterBase>();
		if (ch != null)
		{
			if (PhaseCreatureSpawnInfo.m_spawnPointType == MACreatureSpawnInfo.CreatureSpawnPointType.Proximity)
			{
				HashSet<MAWorkerInfo> workerInfoTargets = PhaseCreatureSpawnInfo.CreatureInfo.WorkerTargets;
				MAWorkerInfo wi = ch.GetMovingInfo() as MAWorkerInfo;
				if (workerInfoTargets.Contains(wi))
				{
					m_isProximityTriggered = true;
				}
				else
				{
					HashSet<MACreatureInfo> creatureInfoTargets = PhaseCreatureSpawnInfo.CreatureInfo.CreatureTargets;
					MACreatureInfo ci = ch.GetMovingInfo() as MACreatureInfo;
					if (creatureInfoTargets.Contains(ci))
					{
						m_isProximityTriggered = true;
					}
				}
			}
		}
	}

	private void OnSpawn(MACharacterBase _newCharacter)
	{
		AddCurrentCharacter(_newCharacter);
		Vector3? pos = PhaseCreatureSpawnInfo.GetDestinationPos();
		if (pos != null)
		{
			_newCharacter.SetToGuard((Vector3)pos, _newCharacter.CharacterSettings.m_guardModeOverrideRadius);
		}
		else
		{
			Debug.LogError("");
		}
	}
	
	public static MASpawnPoint Create(string _name, MACreatureSpawnInfo _creatureSpawnInfo, MABuilding _building, Vector3 _position, float _radius, bool _dontAddToMASpawnPoints = false, bool _showDebug = false)
	{
		var prefab = Resources.Load<MASpawnPoint>(PrefabPath);
		var instance = Instantiate(prefab, GlobalData.Me.m_spawnPointHolder);
		
		SaveLoadSpawnPoint saveLoadSpawnPoint = new SaveLoadSpawnPoint();
		saveLoadSpawnPoint.m_name = _name;
		saveLoadSpawnPoint.m_linkedBuildingId = _building?.m_linkUID ?? -1;
		saveLoadSpawnPoint.m_position = _position;
		saveLoadSpawnPoint.m_radius = _radius;
		saveLoadSpawnPoint.m_spawnDelay = _creatureSpawnInfo.m_spawnDelay;
		int highestId = -1;			
		for(int i = 0; i < NGManager.Me.m_MACreatureSpawnPoints.Count; i++)
		{
			if (NGManager.Me.m_MACreatureSpawnPoints[i].m_ID > highestId)
				highestId = NGManager.Me.m_MACreatureSpawnPoints[i].m_ID;
		}
		saveLoadSpawnPoint.m_ID = ++highestId;
		instance.Activate(saveLoadSpawnPoint, _dontAddToMASpawnPoints, _showDebug);
		
		return instance;
	}
	
	public static MASpawnPoint Create(SaveLoadSpawnPoint _saveData, bool _dontAddToMASpawnPoints = false, bool _showDebug = false)
	{
		var prefab = Resources.Load<MASpawnPoint>(PrefabPath);
		var instance = Instantiate(prefab, GlobalData.Me.m_spawnPointHolder);
		instance.Activate(_saveData, _dontAddToMASpawnPoints, _showDebug);
		return instance;
	}
	
	private class SpawnInfo
	{
		public MACreatureInfo m_info;
		public Vector3 m_position;
		//public int m_level;
		public Action<MACharacterBase> m_returnInstance = null;
		public MABuilding m_optionalHomeBuilding = null;
	}
	
	private IEnumerator SpawnEachFrame(Queue<SpawnInfo> _spawnInfos)
	{
		while(_spawnInfos.Count > 0)
		{
			SpawnInfo spawnInfo = _spawnInfos.Dequeue();
			MACreatureControl.Me.SpawnNewCreatureAtPosition(spawnInfo.m_info, spawnInfo.m_position, OnSpawn, spawnInfo.m_optionalHomeBuilding);
			yield return new WaitForEndOfFrame();
		}

		m_spawningNow = null;
		yield break;
	}

	public void MacroTrigger()
	{
		switch(PhaseCreatureSpawnInfo.m_spawnPointType)
		{
			// case MACreatureSpawnInfo.CreatureSpawnPointType.DayNight:
			// 	break;
			case MACreatureSpawnInfo.CreatureSpawnPointType.Proximity:
				m_isProximityTriggered = true;
				break;
				// case MACreatureSpawnInfo.CreatureSpawnPointType.Macro:
				// 	m_isMacroTriggered = true;
				break;
		}
	}
	
	private MASpawnPhase.SaveLoadSpawnPhase CreateStandardSpawnPhaseInfo()
	{
		return new MASpawnPhase.SaveLoadSpawnPhase()
		{
			m_name = $"SpawnPhase [{m_spawnPhases.Count}]",
			m_numWaves = 1,
			m_totalPhaseSpawnCount = 1,
			m_waveDelayInSeconds = 0f,
		};
	}
	
	
	[Serializable] 
	public class SaveLoadSpawnPoint
	{
		public SaveLoadSpawnPoint()
		{
		}

		public SaveLoadSpawnPoint(MASpawnPoint _s, bool _seedSave)
		{
			m_name = _s.m_name;
			m_position = _s.transform.position;
			m_radius = _s.TriggerRadius;
			m_linkedBuildingId = _s.m_linkedBuilding?.m_linkUID ?? -1;
			m_spawnDelay = _s.m_spawnDelay;
			m_ID = _s.m_ID;
			m_initialSpawnCount = _s.PhaseTotalSpawnCount;
			
			m_spawnPositions.Clear();
			for(int i = _s.m_spawnPositions.Count - 1; i >= 0; i--)
			{
				if(_s.m_spawnPositions[i] == null)
					_s.m_spawnPositions.RemoveAt(i);
				else
					m_spawnPositions.Add(new BasicTransformDetail(_s.m_spawnPositions[i]));
			}
			
			m_spawnPhases.Clear();
			foreach(MASpawnPhase spawnPhase in _s.m_spawnPhases)
			{
				m_spawnPhases.Add(new MASpawnPhase.SaveLoadSpawnPhase(spawnPhase));
			}

			if (_seedSave == false)
			{
				foreach(var id in _s.m_currentCharacters.Keys)
				{
					m_currentCharacterIds.Add(id);
				}

				m_paused = _s.m_paused;
				m_totalSpawnCountSoFar = _s.m_totalSpawnCountSoFar;
				m_spawnCountSoFar = _s.m_phaseSpawnCountSoFar;
				m_timeActive = _s.m_timeActive;
				m_iCurrentSpawnPhase = _s.m_iCurrentSpawnPhase;
				m_numSpawnsEscapedThisPhase = _s.m_numSpawnsEscapedThisPhase;
				m_timesCurrentPhaseDefeated = _s.m_timesCurrentPhaseDefeated;
			}
		}

		public bool m_paused = false;
		public string m_name;
		public Vector3 m_position;
		public float m_radius;
		public int m_linkedBuildingId;
		public int m_iCurrentSpawnPhase = 0;
		public int m_numSpawnsEscapedThisPhase;
		public int m_timesCurrentPhaseDefeated;

		[Serializable]
		public class BasicTransformDetail
		{
			public string m_name;
			public Vector2 m_position;
			public Vector3 m_rotation;

			public BasicTransformDetail()
			{
				
			}
			public BasicTransformDetail(Transform _transform)
			{
				m_name = _transform.name;
				m_position = _transform.position.GroundPosition().GetXZVector2();
				m_rotation = _transform.rotation.eulerAngles;
			}
		}
		
		public List<BasicTransformDetail> m_spawnPositions = new();
		public List<MASpawnPhase.SaveLoadSpawnPhase> m_spawnPhases = new();
		
		public int m_ID = -1;
		public int m_initialSpawnCount = 1;
		public List<int> m_currentCharacterIds = new();
		public int m_totalSpawnCountSoFar = 0;
		public int m_spawnCountSoFar = 0;
		public int m_waveCountSoFar = 0;
		public float m_spawnDelay = -1f;
		public float m_timeActive = -1f;
	}
	
	public static void CleanUp()
	{
		s_spawnPoints.Clear();
	}
	
	public static void LoadAll(string _loadString)
	{
		LoadObjects(_loadString);
	}

	public static string SaveAll()
	{
		var data = CreateSaveString(false);
		return data;
	}
	
	public static void WriteSeedSave(string _seedName = null)
	{
		var savePath = (_seedName == null) ? SeedSavePath : _seedName;
		var save = CreateSaveString(true);
		System.IO.File.WriteAllText(savePath, save);
	}

	private static string CreateSaveString(bool _seedSave)
	{
		var saveSP = new List<SaveLoadSpawnPoint>();
		foreach (var s in NGManager.Me.m_MACreatureSpawnPoints)
		{
			saveSP.Add(new SaveLoadSpawnPoint(s, _seedSave));
		}

		var save = JsonHelper.ToJson<MASpawnPoint.SaveLoadSpawnPoint>(saveSP.ToArray());
		return save;
	}

	private static void LoadObjects(string _saveString)
	{
		if (_saveString.IsNullOrWhiteSpace() == false)
		{
			var saveSP = JsonHelper.FromJson<SaveLoadSpawnPoint>(_saveString, true);
			if (saveSP == null) return;
			foreach (var save in saveSP)
			{
				var sp = Create(save);
			}
		}
	}

	private static string PathToResourcePath(string _assetPath)
	{
		const string c_resourcesString = "/Resources/";
		var resIndex = _assetPath.LastIndexOf(c_resourcesString);
		if (resIndex != -1) _assetPath = _assetPath.Substring(resIndex + c_resourcesString.Length);
		var dotIndex = _assetPath.LastIndexOf(".");
		if (dotIndex != -1) _assetPath = _assetPath.Substring(0, dotIndex);
		return _assetPath;
	}

	public static void ReadSeedSave(string _seedName = null)
	{
		var savePath = (_seedName == null) ? SeedSavePath : _seedName;
		savePath = PathToResourcePath(savePath);
		var res = Resources.Load<TextAsset>(savePath);
		if (res != null)
			GameManager.Me.m_state.m_spawnPointData = res.text;
	}
}