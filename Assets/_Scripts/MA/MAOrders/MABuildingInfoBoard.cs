using TMPro;
using UnityEngine;

public class MABuildingInfoBoard : MonoBehaviour
{
    public TMP_Text m_titleText;
    public TMP_Text m_descriptionText;
    public TMP_Text m_fallbackText;
    
    public string m_defaultText;
    
    public void SetText(string _text)
    {
        if(m_titleText.gameObject.activeSelf != true)
        {
            m_fallbackText.gameObject.SetActive(false);
            m_titleText.gameObject.SetActive(true);
            m_descriptionText.gameObject.SetActive(true);
        }
        
        m_descriptionText.text = _text;
    }
    
    public void Awake()
    {
        SetDefaultText();
    }
    
    public void SetDefaultText()
    {
        m_fallbackText.text = m_defaultText;
        
        m_fallbackText.gameObject.SetActive(true);
        m_titleText.gameObject.SetActive(false);
        m_descriptionText.gameObject.SetActive(false);
    }
}
