using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCGuildBedroom : BCWorkerBase
{
#if UNITY_EDITOR    
    override public void DebugShowGUIDetails(GUIStyle _labelStyle, bool _showBase = true)
    {
        if(_showBase)
            base.DebugShowGUIDetails(_labelStyle, _showBase);
    }
#endif
    public override List<MAHeroBase> GetHeroesAllocated() => m_workersAllocated.ConvertToTypeList(x => x as MAHeroBase);
    public override List<MAHeroBase> GetHeroesPresent() => m_workersPresent.ConvertToTypeList(x => x as MAHeroBase);
    
    public override void GetCombinedValue(ref CombinedStat _value)
    {
        if(_value == null) _value = new HeroBedroomSlotCombinedStat();
        base.GetCombinedValue(ref _value);
    }
    
    override public void UpdateInternal()
    {
        UpdateOccupants();
    }

    public override bool Arrive(MACharacterBase _character)
    {
        bool result = base.Arrive(_character);
        if (result)
        {
            m_building.m_componentsDict.TryGetValue(typeof(BCGuildBedroom), out var bedrooms);
            _character.gameObject.SetActive(false); // TS - if you want him to stay awake inside a building please speak to thomas
            //_character.CharacterUpdateState.ApplyState(CharacterStates.Idle);
        }
        return result;
    }

    public override bool Allocate(MACharacterBase _worker)
    {
        if(base.Allocate(_worker))
        {
            _worker.Home = this;
            return true;
        }
        return false;
    }
    
    void UpdateOccupants()
    {
        if(m_workersPresent.Count == 0) return;
        
        Building.m_isInhabited = true;
        
        var values = m_building.GetHeroesGuildMultipliers();
        
        for (var i = m_workersPresent.Count - 1; i >=0 ; i--)
        {
            ShowChimneySmoke();
            var hero = m_workersPresent[i] as MAHeroBase;
            if (hero == null)
                continue;
            
            if(BCGuildHealing.RequiresHealing(hero))
            {
                TryHeal(hero, values.healMultiplier);
                continue;
            }
            
            if(BCGuildTraining.RequiresTraining(hero) && m_building.HasBuildingComponent<BCGuildTraining>())
            {
                TryTrain(hero, values.trainMultiplier);
                continue;
            }
            
            // RW-03-APR-25: When a hero dies in a cave, they're teleported back to the Heroes Guild to recover health. This functionality
            // would be better taken care of by a block (either a healing block or a bedroom), but what if those don't exist? That hasn't 
            // been agreed by design yet, so I'm putting this here to give the hero _some_ path to recovery, but there's still some stuff
            // which needs to be finalised which regards to what's going on in this area.
            if (!hero.IsIncapacitated || hero.Health > hero.ReincarnationHealth)
            {
                m_building.EjectHero(hero);
            }
        }
    }
    
    private bool TryHeal(MAHeroBase _hero, float _multiplier)
    {
        foreach(var h in m_building.BuildingComponents<BCGuildHealing>())
        {
            if(h.TryHeal(_hero, _multiplier)) return true;
        }
        return false;
    }
    
    private bool TryTrain(MAHeroBase _hero, float _multiplier)
    {
        foreach(var h in m_building.BuildingComponents<BCGuildTraining>())
        {
            if(h.TryTrain(_hero, _multiplier)) return true;
        }
        HealHero(_hero, _multiplier);
        return false;
    }
    
    private void HealHero(MACharacterBase _hero, float _multiplier)
    {
        _hero.SetHealthRecoverRateOverride(_hero.CreatureInfo.m_deadHealthRecoveryRate * _multiplier);
        _hero.UpdateHealthRecovery();
        _hero.ResetHealthRecoveryRate();
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (BCHeroPanel.PanelID, () => new BCHeroPanel());
}

public class BCHeroPanel : BCUIPanel
{
    public const string PanelID = "heroes";
    public List<BCWorkerBase> m_bases = new();
    
    public override int TableSpacing => -10;
    public override bool ShowPrimarySection => false;

    public BCHeroPanel() : base(PanelID, "Heroes") { }

    public override string GetDescription()
    {
        BCBase.CombinedStat stat = null;
        foreach (var c in m_bases) c.GetCombinedValue(ref stat);
        return stat?.GetValue();
    }
    
    public override void AddComponent(BCBase _component)
    {
        if(_component as BCWorkerBase)
        {
            m_bases.Add(_component as BCWorkerBase);
        }
        // Dont call base
    }
    
    override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
    {
        bool createdTitle = false;
        List<MACharacterBase> characters = new();
        foreach(var workerBase in m_bases)
        {
            foreach(var character in workerBase.m_workersAllocated)
            {
                if(characters.Contains(character)) continue;
                if(character.Home?.Building != workerBase.Building)
                    continue;
                
                if(createdTitle == false)
                {
                    MABuildingWorkerPanelLine.CreateTitle(_holder);
                    createdTitle = true;
                }
                
                characters.Add(character);
                yield return () => MABuildingWorkerPanelLine.Create(_holder, character);
            }
            if(characters.Count == 0)
            {
                yield return () => MADesignInfoSheetLine.Create(_holder, "None", null, true);
            }
        }
    }
}