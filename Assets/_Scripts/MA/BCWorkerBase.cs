using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class BCWorkerBase : BCBase
{
    [KnackField] public int m_maxWorkers = 1;
    
    [Save(ESaveFlags.NoNulls)] public List<MACharacterBase> m_workersPresent = new(); 
    [Save(ESaveFlags.NoNulls)] public List<MACharacterBase> m_workersAllocated = new();
    [HideInInspector] private bool m_debugShowWorkerPresant = false;
    [HideInInspector] private bool m_debugShowWorkerAllocated = false;
#if UNITY_EDITOR
    override public void DebugShowGUIDetails(GUIStyle _labelStyle, bool _showBase = true)
    {
        if(_showBase) 
            base.DebugShowGUIDetails(_labelStyle, _showBase);
        var labelStyle = new GUIStyle(EditorStyles.foldout){richText = true};
        if (m_workersPresent != null)
        {
            m_debugShowWorkerPresant = EditorGUILayout.Foldout(m_debugShowWorkerPresant, $"m_workersPresent[{m_workersPresent.Count}]", true);
            if (m_debugShowWorkerPresant && m_workersPresent.Count > 0)
            {
                EditorGUI.indentLevel++;
                foreach (var w in m_workersPresent)
                {
                    EditorGUILayout.LabelField(w.name, labelStyle);
                }
                EditorGUI.indentLevel--;
            }
        }

        if (m_workersAllocated != null)
        {
            m_debugShowWorkerAllocated = EditorGUILayout.Foldout(m_debugShowWorkerAllocated, $"m_workersAllocated[{m_workersAllocated.Count}]", true, labelStyle);
            if (m_debugShowWorkerAllocated && m_workersAllocated.Count > 0)
            {
                EditorGUI.indentLevel++;
                for (var i = m_workersAllocated.Count - 1; i >= 0; i--)
                {
                    var w = m_workersAllocated[i];
                    if (w == null)
                    {
                        m_workersAllocated.RemoveAt(i);
                        continue;
                    }
                    EditorGUILayout.LabelField(w.name, labelStyle);
                }

                EditorGUI.indentLevel--;
            }
        }
    }
#endif
    
    public override int GetFreeSlots() => m_maxWorkers - m_workersAllocated.Count;
    public override int GetMaxSlots() => m_maxWorkers;

    protected override void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
    {
        var doorPos = _previousOwner ? _previousOwner.GetDoorPos() : GetDoorPos();
        for (var i = m_workersPresent.Count-1; i >= 0; i--)
        {
            var worker = m_workersPresent[i];
            worker.SetMoveToPosition(doorPos, false, PeepActions.Ejected);
            Leave(worker);
        }
        base.Deactivate(_previousOwner, _quantityRemainingInBuilding, _action);
    }

    override public bool Allocate(MACharacterBase _worker)
    {
        if(m_workersAllocated.Contains(_worker))
            return true;
            
        if (GetFreeSlots() <= 0)
            return false;

        m_workersAllocated.Add(_worker);
        return true;
    }
    override public bool Deallocate(MACharacterBase _worker)
    {
        if (m_workersAllocated.Contains(_worker) == false)
            return false;
        
        if(m_workersPresent.Remove(_worker))
        {
            m_workersAllocated.Remove(_worker);

            if(_worker.MAReturnToHome() == false)
            {
                _worker.MASetStateMoveOutOfBuilding(_worker.transform.position);
            }
            return true;
        }

        m_workersAllocated.Remove(_worker);
        return true;
    }
    override public bool IsAllocated(MACharacterBase _worker)
    {
        return m_workersAllocated.Contains(_worker);
    }
    override public bool Arrive(MACharacterBase _worker)
    {
        if (m_workersAllocated.Contains(_worker) == false)
            return false;
            
        if(m_building.Health <= 0)
            return false;

        if(m_workersPresent.Contains(_worker) == false)
            m_workersPresent.Add(_worker);
        return true; 
    }
    override public bool Leave(MACharacterBase _worker)
    {
        if (m_workersPresent.Contains(_worker) == false)
            return false;
        m_workersPresent.Remove(_worker);
        return true;
    }
    
    public class BedroomSlotCombinedStat : WorkerSlotCombinedStat 
    {
        private static string s_highlighted = MAMessageManager.GetBedroomIcon(MAMessageManager.ESlotState.Highlighted);
        private static string s_occupied = MAMessageManager.GetBedroomIcon(MAMessageManager.ESlotState.Occupied);
        private static string s_unoccupied = MAMessageManager.GetBedroomIcon(MAMessageManager.ESlotState.Unoccupied);
        
        override protected string Highlighted => s_highlighted;
        override  protected string Occupied => s_occupied;
        override  protected string UnOccupied => s_unoccupied;
        //override public string GetIcon(MAMessageManager.ESlotState _state) => MAMessageManager.GetBedroomIcon(_state); 
    }
    public class HeroBedroomSlotCombinedStat : WorkerSlotCombinedStat 
    { 
        private static string s_highlighted = MAMessageManager.GetHeroBedroomIcon(MAMessageManager.ESlotState.Highlighted);
        private static string s_occupied = MAMessageManager.GetHeroBedroomIcon(MAMessageManager.ESlotState.Occupied);
        private static string s_unoccupied = MAMessageManager.GetHeroBedroomIcon(MAMessageManager.ESlotState.Unoccupied);
        
        override protected string Highlighted => s_highlighted;
        override  protected string Occupied => s_occupied;
        override  protected string UnOccupied => s_unoccupied;
        //override public string GetIcon(MAMessageManager.ESlotState _state) => MAMessageManager.GetHeroBedroomIcon(_state);
    }
    public class HeroSlotCombinedStat : WorkerSlotCombinedStat
    {
        private static string s_highlighted = MAMessageManager.GetHeroIcon(MAMessageManager.ESlotState.Highlighted);
        private static string s_occupied = MAMessageManager.GetHeroIcon(MAMessageManager.ESlotState.Occupied);
        private static string s_unoccupied = MAMessageManager.GetHeroIcon(MAMessageManager.ESlotState.Unoccupied);
        
        override protected string Highlighted => s_highlighted;
        override  protected string Occupied => s_occupied;
        override  protected string UnOccupied => s_unoccupied;
        //override public string GetIcon(MAMessageManager.ESlotState _state) => MAMessageManager.GetHeroIcon(_state);
    }
    
    public class WorkerSlotCombinedStat : CombinedStat
    {
        public int m_freeSlots;
        public int m_takenSlots;
        public int m_draggingSlots;
        
        private static string s_highlighted = MAMessageManager.GetWorkerIcon(MAMessageManager.ESlotState.Highlighted);
        private static string s_occupied = MAMessageManager.GetWorkerIcon(MAMessageManager.ESlotState.Occupied);
        private static string s_unoccupied = MAMessageManager.GetWorkerIcon(MAMessageManager.ESlotState.Unoccupied);
        
        virtual protected string Highlighted => s_highlighted;
        virtual protected string Occupied => s_occupied;
        virtual protected string UnOccupied => s_unoccupied;
        
        //virtual public string GetIcon(MAMessageManager.ESlotState _state) => MAMessageManager.GetWorkerIcon(_state);

        public override string GetValue(ValueDisplay _display = ValueDisplay.Default)
        {
            var result = "";
            bool sizeUp = _display == ValueDisplay.Default;
            string sizePrefix = sizeUp ? "<size=150%>" : "";
            string sizePostfix = sizeUp ? "</size>" : "";
            
            if(m_draggingSlots > 0)
            {
                result += $"{m_draggingSlots+m_takenSlots}x{sizePrefix}{Highlighted}{sizePostfix}";
            }
            else if(m_takenSlots > 0)
            {
                result += $"{m_draggingSlots+m_takenSlots}x{sizePrefix}{Occupied}{sizePostfix}";
            }
               
            if(m_freeSlots > 0)
            {
                if(result.Length > 0) result += $" ";
                result += $"{m_freeSlots}x{sizePrefix}{UnOccupied}{sizePostfix}";
            }

            return result;
        }
    }
    
    public override void GetCombinedValue(ref CombinedStat _value)
    {
        if(_value == null)
            _value = new WorkerSlotCombinedStat();
        
        var stat = _value as WorkerSlotCombinedStat;
        if(stat == null) 
            return; 
        
        var draggingWorker = InputUtilities.GetCurrentDraggingWorker();
        stat.m_freeSlots += m_maxWorkers - m_workersAllocated.Count;
        stat.m_takenSlots += m_workersAllocated.Count;
         
        if(draggingWorker != null && m_workersAllocated.Contains(draggingWorker))
        {
            stat.m_takenSlots--;
            stat.m_draggingSlots++;
        }
    }
}