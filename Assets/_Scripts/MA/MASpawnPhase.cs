using System;
using UnityEngine;
using Object = System.Object;

#if UNITY_EDITOR
using UnityEditor;
#endif

[Serializable]
public class MASpawnPhase : Object
{
	[ReadOnlyInspector]
	public string m_name = "unassigned phase";
	[ReadOnlyInspector]
	public MACreatureSpawnInfo m_maCreatureSpawnInfo;
	[ReadOnlyInspector]
	public int m_totalPhaseSpawnCount = 1;
	[ReadOnlyInspector]
	public int m_numWaves = 1;
	[ReadOnlyInspector]
	public bool m_isPerpetualSpawnPhase = true;
	[ReadOnlyInspector]
	public float m_waveDelayInSeconds = 0f;
	
	public Func<bool> m_disabledUntil = null;
	public Func<bool> m_continuePhaseUntil = null;
	
	private SpawnPhaseGUI m_spawnPhaseGUI = null;
	
	public MASpawnPhase(int _index)
	{
		m_name = $"SpawnPhase {_index}";
		m_isPerpetualSpawnPhase = true;
	}

	public MASpawnPhase(SaveLoadSpawnPhase _spawnPhaseData)
	{
		m_name = _spawnPhaseData.m_name;
		m_maCreatureSpawnInfo = MACreatureSpawnInfo.GetInfo(_spawnPhaseData.m_maCreatureSpawnInfoName);
		m_totalPhaseSpawnCount = _spawnPhaseData.m_totalPhaseSpawnCount;
		m_numWaves = _spawnPhaseData.m_numWaves;
		m_isPerpetualSpawnPhase = _spawnPhaseData.m_isPerpetualSpawnPhase;
		m_waveDelayInSeconds = _spawnPhaseData.m_waveDelayInSeconds;
	}
	
	public SpawnPhaseGUI GetGUI()
	{
		if(m_spawnPhaseGUI == null)
		{
			m_spawnPhaseGUI = new SpawnPhaseGUI();
		}
		return m_spawnPhaseGUI;
	}

#if UNITY_EDITOR
	public class CustomEditorIntSlider
	{
		private int editorVal = 0;

		public void Draw(ref int val, int min, int max, string _name)
		{
			val = Mathf.Clamp(val, min, max);
			editorVal = EditorGUILayout.IntSlider(_name, val, min, max);
			if(val != editorVal)
			{
				val = editorVal;
			}
		}
	}
#endif

	[Serializable]
	public class SaveLoadSpawnPhase
	{
		public string m_name;
		public string m_maCreatureSpawnInfoName;
		public int m_numWaves = 1;
		public int m_totalPhaseSpawnCount = 1;
		public int m_levelPoolThisPhase = 1;
		public bool m_isPerpetualSpawnPhase = true;
		public float m_waveDelayInSeconds = 0f;

		public SaveLoadSpawnPhase()
		{
		}
			
		public SaveLoadSpawnPhase(MASpawnPhase _spawnPhase)
		{
			m_name = _spawnPhase.m_name;
			m_maCreatureSpawnInfoName = _spawnPhase.m_maCreatureSpawnInfo.m_name;
			m_numWaves = _spawnPhase.m_numWaves;
			m_totalPhaseSpawnCount = _spawnPhase.m_totalPhaseSpawnCount;
			m_isPerpetualSpawnPhase = _spawnPhase.m_isPerpetualSpawnPhase;
			m_waveDelayInSeconds = _spawnPhase.m_waveDelayInSeconds;
		}
	}
	
	public class SpawnPhaseGUI
	{
#if UNITY_EDITOR
		private CustomEditorIntSlider m_editorMinSlider = new CustomEditorIntSlider();

		public void Draw(MASpawnPhase _phase, MASpawnPoint _point, bool _showDebug, string[] _spawnPointInfoNames, int[] _spawnPointInfoInts)
		{
			EditorGUI.indentLevel++;
			EditorGUILayout.Space();
			GUIStyle newStyle = new GUIStyle(EditorStyles.label);
			string name;
			
			if(_point.IsPhaseComplete(_phase))
			{
				if(_point.CurrentSpawnPhase == _phase)
				{
					name = $"{_phase.m_name} (Current. Complete.)";
				}
				else
				{
					name = $"{_phase.m_name} (Complete.)";
				}
			}
			else if(_point.CurrentSpawnPhase == _phase)
			{
				if((_point.TriggerConditionsMet && _point.SpawnConditionsMet))
				{
					name = $"{_phase.m_name} (Current. Triggered = true). Escapees: {_point.EscapedThisPhase}.{(_phase.m_disabledUntil != null ? " Disabled until Next trigger" : "")}";
				}
				else
				{
					name = $"{_phase.m_name} (Current. Triggered = false). Escapees: {_point.EscapedThisPhase}.{(_phase.m_disabledUntil != null ? " Disabled until Next trigger" : "")}";
				}
			}
			else
			{
				name = _phase.m_name;
			}
			EditorGUILayout.LabelField(name);
			
			var selectedSpawnType = 0;
			if(_phase.m_maCreatureSpawnInfo != null)
			{
				selectedSpawnType = MACreatureSpawnInfo.s_creatureSpawnInfos.IndexOf(_phase.m_maCreatureSpawnInfo)+1;
			}
			var infoSelectedPopup = EditorGUILayout.IntPopup($"Type:", selectedSpawnType, _spawnPointInfoNames, _spawnPointInfoInts);
			if (infoSelectedPopup != selectedSpawnType)
			{
				_phase.m_maCreatureSpawnInfo = MACreatureSpawnInfo.GetInfo(_spawnPointInfoNames[infoSelectedPopup]);
			}
			
			m_editorMinSlider.Draw(ref _phase.m_totalPhaseSpawnCount, 1, 100, "Total Spawn Count");
			m_editorMinSlider.Draw(ref _phase.m_numWaves, 1, _phase.m_totalPhaseSpawnCount, "Num Waves");
			
			_phase.m_isPerpetualSpawnPhase = EditorGUILayout.ToggleLeft("Perpetual Spawn Phase", _phase.m_isPerpetualSpawnPhase);
			
			_phase.m_waveDelayInSeconds = EditorGUILayout.FloatField("Wave Delay In Seconds", _phase.m_waveDelayInSeconds);

			EditorGUILayout.Space();
			EditorGUI.indentLevel--;
		}
#endif
	}
}