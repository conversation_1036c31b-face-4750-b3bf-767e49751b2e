using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class BCTap : BCBase
{
	[KnackField] public float m_clickAddsHowMuch = 0.25f;
    [KnackField] public float m_clickHoldAddsHowMuch = 0.1f;
    [KnackField] public float m_workerSpeedMultiplier = 1f;
    
    public bool InLongPress => m_inLongPress;
    
    private bool m_inLongPress = false;
    private float m_holdDuration = 0;
    private Coroutine decayCoroutine;
    public float m_multiplier = 0f;
    private const float c_clickDurationAdd = 1f;
    
    
    private float PowerAvailable { get { return m_power[m_currentPowerIndex]; } set { m_power[m_currentPowerIndex] = value; } }
    private float NextPower { get { return m_power[NextPowerIndex]; } set { m_power[NextPowerIndex] = value; } }

	private int NextPowerIndex => (m_currentPowerIndex+1)&1;
    private int m_currentPowerIndex = 0;
    private float[] m_power = new float[2];

    private string BuildingSpecificAnimation() => m_building.BuildingSpecificHandAnimation();
    
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        if (m_building.gameObject.GetComponent<NGStandardClick>() == null)
        {
            m_building.gameObject.AddComponent<NGStandardClick>();
            m_building.GetComponent<NGStandardClick>().enabled = true;
        }
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }

    override public bool ConsumePower(ref float _powerNeeded)
    {
		var powerToTake = Mathf.Min(PowerAvailable, _powerNeeded);
		_powerNeeded -= powerToTake;
		PowerAvailable -= powerToTake;
		
		powerToTake = Mathf.Min(NextPower, _powerNeeded);
		_powerNeeded -= powerToTake;
		NextPower -= powerToTake;
		
		return _powerNeeded <= 0;
    }
    
    public float GetWorkerSpeedMultiplier()
    {
		return 1f + m_multiplier * m_workerSpeedMultiplier;
    }
    
    private void UpdateMultiplier()
	{
		// Adjust the divisor in the exponential formula to slow growth by 70%
		float growthRate = 2f * 1.7f; // Original rate (2f) scaled by 1.7 to slow by 70%
		m_multiplier = 4 * (1 - Mathf.Exp(-m_holdDuration / growthRate));
		
		var playAudio = m_multiplier > 0.1f;
		foreach(var action in m_building.ActionComponents)
		{
			PlayBlockAudio(playAudio, action.Block.m_tapMultiplierEvent, m_building.gameObject, ref action.m_tapAudioHandle);
			if(playAudio)
			{
				action.Block.m_tapMultiplierAudioSpeed.SetValue(m_building.gameObject, m_multiplier);
			}
		}
		
		//Debug.LogError($"Duration {m_holdDuration} mult {m_multiplier}");
	}

    public override void OnBeginLongPress(PointerEventData _eventData)
	{
		if (enabled == false) return;
		
		switch (_eventData.button)
		{
			case PointerEventData.InputButton.Left:
				m_building.DoComponentWork(MABuilding.TapWorkStage.PressStart);
				m_inLongPress = true;
				PlayerHandManager.Me.StartBuildingSpecificAnimation(BuildingSpecificAnimation());
				EndDecay();
				break;
		}
	}
	public override void OnUpdateLongPress(PointerEventData _eventData, float _timeInLongPress)
	{
		m_holdDuration += Time.deltaTime;
		UpdateMultiplier();
		
		NextPower += m_multiplier * m_clickHoldAddsHowMuch * MAUnlocks.Me.m_tapHoldMultiplier;
		
		m_building.DoComponentWork(MABuilding.TapWorkStage.Press);
	}
	public override void OnEndLongPress(PointerEventData _eventData)
	{
		if (enabled == false) return;
		switch (_eventData.button)
		{
			case PointerEventData.InputButton.Left:
				m_building.DoComponentWork(MABuilding.TapWorkStage.PressEnd);
				++GameManager.Me.m_state.m_gameInfo.m_numBuildingHolds;
				PlayerHandManager.Me.EndBuildingSpecificAnimation();
				m_building.QueueAudio(NGManager.Me.m_didWorkEndAudio);
				m_building.QueueAudio(NGManager.Me.m_didWorkAudio);
				StartDecay();
				break;
		}
	}
	
    override public void OnTap(PointerEventData _eventData)
    {
		if(_eventData.button != PointerEventData.InputButton.Left)
			return;
		
		m_holdDuration += c_clickDurationAdd;
		UpdateMultiplier();
			
		++GameManager.Me.m_state.m_gameInfo.m_numBuildingClicks;
	    
		NextPower += m_multiplier * m_clickAddsHowMuch;

		m_building.DoComponentWork(MABuilding.TapWorkStage.Tap);
		
	    StartDecay();
    }

    private void StartDecay()
    {
	    EndDecay(); // Ensure no overlapping coroutines
	    decayCoroutine = StartCoroutine(DecayWorkMultiplier());
    }

    private void EndDecay()
    {
	    if (decayCoroutine != null)
	    {
		    StopCoroutine(decayCoroutine);
		    decayCoroutine = null;
	    }
    }

    public override void LateUpdateInternal()
    {
		PowerAvailable = 0;
		m_currentPowerIndex = NextPowerIndex;
		base.LateUpdateInternal();
    }

    // Coroutine to decay the multiplier quickly
    private IEnumerator DecayWorkMultiplier()
    {
	    const float c_decayPerSecond = 0.25f;
	    const float c_maxDecayTime = 24;
	    m_holdDuration = Mathf.Min(m_holdDuration, c_decayPerSecond * c_maxDecayTime);
	    while (m_holdDuration > 0)
	    {
		    m_holdDuration -= Time.deltaTime * c_decayPerSecond;
		    //m_holdDuration *= 1 - .006f.TCLerp();
		    if (m_holdDuration < .001f) m_holdDuration = 0;
		    UpdateMultiplier();
		    yield return null;
	    }
    }
}
