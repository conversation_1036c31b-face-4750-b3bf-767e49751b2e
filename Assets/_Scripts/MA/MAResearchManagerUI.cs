using System;
using System.Collections.Generic;
using TMPro;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class MAResearchManagerUI : MonoSingleton<MAResearchManagerUI>, IPointerClickHandler
{
    public MAIconAnimated m_exitButtonNotification;
    public List<MAIconAnimated> m_buttonNotifications = new();
    public List<MAIconAnimated> m_buttonAlerts = new();
    public const string PrefabName = "_Prefabs/Research/MAResearchManagerUIV4";
    public static MAFactionInfo.FactionType m_faction = MAFactionInfo.FactionType.Commoners;
    public static bool m_isActive = false;
    public Image m_background;
    [HideInInspector]public RectTransform m_researchItemHolder;
    [HideInInspector] public Transform m_lineHolder;
    public List<MAResearchItemUI> m_researchItems = new ();
    public Sprite[] m_backgroundSprites = new Sprite[(int)MAFactionInfo.FactionType.Last];
  	public MAResearchItemUI m_draggingItem = null;
    public Sprite m_newItemSprite;
    public Color m_lockedConnectionColor;
    public Color m_unlockedConnectionColor;
    public float m_lockedThickness = 2f;
    public float m_unlockedThickness = 4f;
    public bool m_showArrow = false;
    public Image[] m_tabs = new Image[(int) MAFactionInfo.FactionType.Last];
    public float m_tabClosedY = -52;
    public float m_tabOpenY = -52;
    public GameObject[] m_backgrounds = new GameObject[(int) MAFactionInfo.FactionType.Last];
    public string[] m_selectedTabNames = new string[(int) MAFactionInfo.FactionType.Last] {"Royal", "Lords", "Peoples", "Mystic"};
    public RectTransform[] m_researchItemHolders = new RectTransform[(int) MAFactionInfo.FactionType.Last];
    public Transform[] m_lineHolders = new Transform[(int) MAFactionInfo.FactionType.Last];
 //   public Transform[] m_dragMeHolders = new Transform[(int) MAFactionInfo.FactionType.Last];
    public bool m_editMode = false;
    public bool NodeDragMode => (Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt) || Input.GetKey(KeyCode.LeftApple) || Input.GetKey(KeyCode.RightApple));
    public Transform m_editorButtonsHolder;
    public TMP_Text m_writeChangesButtonText;
    public Button m_editButton;
    public void CorrectLine(MAResearchItemUI _old, MAResearchItemUI _new)
    {
        var lines =m_lineHolder.GetComponentsInChildren<MAUILine>();
        foreach (var line in lines)
        {
            if(line.m_startObject == _old.gameObject)
            {
                line.m_startObject = _new.gameObject;
            }
            if(line.m_endObject == _old.gameObject)
            {
                line.m_endObject = _new.gameObject;
            }
        }
    }
    public void ShowBackground(MAFactionInfo.FactionType _faction)
    {
        m_faction = _faction;
        m_background.sprite = m_backgroundSprites[(int)_faction];
        m_researchItemHolder.DestroyChildren();

    }
    public void OnPointerClick(PointerEventData eventData)
    {
        if(m_draggingItem) return;
    }
    public bool HasEmptyItem()
    {
        var emptyItem = m_researchItems.Find(x => x.IsAssigned == false);
        return emptyItem != null;
    }

    public void AddItem(MAResearchItemUI _item)
    {
        if (m_researchItems.Contains(_item) == false)
            m_researchItems.Add(_item);
        else
        {
            Debug.LogError($"MAResearchManagerUI.AddItem {_item.m_info.m_name} already in list");
        }
    }
    
    public void RemoveItem(MAResearchItemUI _item)
    {
        foreach(var ri in MAResearchInfo.s_factionResearchDict[m_faction])
        {
            if(ri.m_linkToList.Contains(_item.m_info))
            {
                ri.m_linkToList.Remove(_item.m_info);
            }
        }
        var lines =m_lineHolder.GetComponentsInChildren<MAUILine>();

        foreach (var line in lines)
        {
            if(line.m_startObject == _item.gameObject || line.m_endObject == _item.gameObject)
            {
               line.DestroyMe();
            }
        }
        if (m_researchItems.Contains(_item))
            m_researchItems.Remove(_item);
        else
        {
            Debug.LogError($"MAResearchManagerUI.RemoveItem {_item.m_info.m_name} not in list");
        }
    }

    public void Refresh()
    {
        m_lineHolder.DestroyChildren();
        foreach (var ri in m_researchItems)
        {
            ri.Activate();
            if (ri.m_info == null) continue;
            foreach (var link in ri.m_info.m_linkToList)
            {
                var to = m_researchItems.Find(x => x.m_info == link);
                if (to == null) continue;
                var color = (ri.m_info.m_acquired) ? m_unlockedConnectionColor : m_lockedConnectionColor;
                var thickness = (ri.m_info.m_acquired) ? m_unlockedThickness : m_lockedThickness;
                var lineArrow = (ri.m_info.m_acquired) ? true : false;
                var line = MAUILine.Create(ri.OutputLineTarget, ri.OutputLineExit,  to.InputLineTarget, to.InputLineEntry, m_lineHolder, thickness, color, m_editMode|m_showArrow|lineArrow);
            }
        }
        int changesCount = 0;
        foreach (var ri in MAResearchInfo.s_researchInfos)
        {
            if(ri != null)
                changesCount += ri.m_changed ? 1 : 0;
        }
        if (changesCount == 0)
        {
            m_writeChangesButtonText.text = $"No Changes";
            m_writeChangesButtonText.fontStyle = FontStyles.Normal;
        }
        else
        {
            m_writeChangesButtonText.text = $"Write Changes To Knack\n[{changesCount}]";
            m_writeChangesButtonText.fontStyle = FontStyles.Bold;
        }
        RefreshNotifications();
    }
    public bool IsInfoAssigned(MAResearchInfo _info)
    {
        if (_info == null) return false;
        foreach(var i in m_researchItems)
        {
            if(i.m_info == null) continue;
            if(i.m_info == _info)
                return true;
            if(i.m_info.m_name.Equals(_info.m_name))
                return true;
        }
        var assignedItem = m_researchItems.Find(x => (x.m_info != null && x.m_info.IsBlank == false ) ? x.m_info.m_name == _info.m_name : false);
        return assignedItem != null;
    }

    public MAResearchItemUI GetMouseOver()
    {
        MAResearchItemUI overReseachItem = null;
        Vector2 mousePos = Input.mousePosition;

        if (RectTransformUtility.ScreenPointToLocalPointInRectangle(m_researchItemHolder, mousePos, null, out Vector2 anchoredPos))
        {
            var over = Utility.GetUIObjectAt(Input.mousePosition);
            if (over)
            {
                var overRI = over.GetComponentInParent<MAResearchItemUI>();
                if (overRI)
                    return overRI;
            }
        }
        return null;
    }

    public void ClearBackground()
    {
        for (var i = m_researchItems.Count - 1; i >= 0; i--)
        {
            var ii = m_researchItems[i];
            if(ii!=null)
                ii.DestroyMe();
        }
        m_researchItems.Clear();
        m_researchItemHolder.DestroyChildren();
    }
    public void ClickedTab(int _type)
    {
        var factionClicked = (MAFactionInfo.FactionType)_type; 
        if(m_faction == factionClicked) return;
        
        switch (factionClicked)
        {
            case MAFactionInfo.FactionType.Commoners:
                AudioClipManager.Me.PlaySound("PlaySound_Arcadium_TAB_PEOPLES", GameManager.Me.gameObject);
                break;
            case MAFactionInfo.FactionType.Lords:
                AudioClipManager.Me.PlaySound("PlaySound_Arcadium_TAB_LORDS", GameManager.Me.gameObject);
                break;
            case MAFactionInfo.FactionType.Royal:
                AudioClipManager.Me.PlaySound("PlaySound_Arcadium_TAB_ROYALS", GameManager.Me.gameObject);
                break;
            case MAFactionInfo.FactionType.Mystic:
                AudioClipManager.Me.PlaySound("PlaySound_Arcadium_TAB_MYSTICS", GameManager.Me.gameObject);
                break;
        }

        m_faction = factionClicked;
        if(MAResearchItemInfo.Me)
            MAResearchItemInfo.Me.DestroyMe();
        Activate(m_faction);
    }

    public void SetupInfo(MAFactionInfo.FactionType _faction)
    {
        ClearBackground();
        foreach(var i in MAResearchInfo.s_factionResearchDict[_faction])
        {
            if(i.IsValidPosition == false)
                continue;
            var riUI = MAResearchItemUI.Create(m_researchItemHolder, i.PositionVector, i.ScaleVector, MAResearchItemUI.ItemState.Create, i);
           // riUI.AssignResearchItem(i);
            m_researchItems.Add(riUI);
        }
        Canvas.ForceUpdateCanvases();
        Refresh();
    }
    
    public MAResearchItemUI AssignResearch(MAResearchInfo _item)
    {
        var emptyItem = m_researchItems.Find(x => x.IsAssigned == false);
        if (emptyItem)
        {
            return emptyItem.AssignResearchItem(_item);
        }

        return null;
    }
    
    public bool AfforableItemAvailable(int _index)
    {
        MAFactionInfo.FactionType faction = (MAFactionInfo.FactionType)_index;
        
        if(MAResearchManagerUI.IsFactionLocked(faction))
            return false;
            
        if(MAResearchInfo.s_factionResearchDict.TryGetValue(faction, out var researchItems))
        {
            foreach(var item in researchItems)
            {
                if(item.m_acquired || item.IsUnlocked() == false || item.CanPlayerAfford() == false)
                    continue;
             
                return true;   
            }
        }
        return false;
    }
    
    public bool TabRequiresAlert(int _index)
    {
        MAFactionInfo.FactionType faction = (MAFactionInfo.FactionType)_index;
        
        if(faction == m_faction)
            return false;
        if(MAResearchManagerUI.IsFactionLocked(faction))
            return false;
            
        if(MAResearchInfo.s_factionResearchDict.TryGetValue(faction, out var researchItems))
        {
            foreach(var item in researchItems)
            {
                if(item.m_acquired)
                    continue;
                    
                if(item.m_highLight)
                    return true;
            }
        }
        return false;
    }
    
    private void SetAnimiationIconEnabled(bool _enabled, int _index, List<MAIconAnimated> _type)
    {
        if(_index < 0 || _index >= m_researchItems.Count) return;
        
        _type[_index]?.Set(_enabled ? 1 : 0, 0, 4);
    }
    
    public void RefreshNotifications()
    {
        for(int i = 0; i < m_tabs.Length; i++)
        {
            SetAnimiationIconEnabled(AfforableItemAvailable(i), i, m_buttonNotifications);
            SetAnimiationIconEnabled(TabRequiresAlert(i), i, m_buttonAlerts);
        }
    }
        
    void Activate(MAFactionInfo.FactionType _faction)
    {
        m_editButton.gameObject.SetActive(false);
        #if UNITY_EDITOR || DEVELOPMENT_BUILD
            m_editButton.gameObject.SetActive(true);
        #endif
        m_isActive = true;
        if(_faction != MAFactionInfo.FactionType.None)
            m_faction = _faction;
        for(int i = 0; i < m_tabs.Length; i++)
        {
            bool isSelected = i == (int) m_faction;
            var tab = m_tabs[i];
            var text = tab.GetComponentInChildren<TMP_Text>();
            var finfo = MAFactionInfo.GetInfoByName(m_selectedTabNames[i]);
            
            if (MAResearchInfo.s_factionResearchDict.ContainsKey((MAFactionInfo.FactionType) i))
                text.text = $"{finfo.m_title}";
                //text.text = $"{finfo.m_title} [{MAResearchInfo.s_factionResearchDict[(MAFactionInfo.FactionType) i].Count}]";
            else
                tab.gameObject.SetActive(false);
            
            if (isSelected)
            {
                m_researchItemHolder = m_researchItemHolders[i];
                m_lineHolder = m_lineHolders[i];
            }
            m_backgrounds[i].gameObject.SetActive(isSelected);
        }
        
        RefreshNotifications();

        SetupInfo(m_faction);
        SetupActiveTabs();
        UpdateTabs();
    }

    public void SetupActiveTabs()
    {
        ToggleTab(MAFactionInfo.FactionType.Commoners, IsFactionLocked(MAFactionInfo.FactionType.Commoners) == false);
        ToggleTab(MAFactionInfo.FactionType.Royal, IsFactionLocked(MAFactionInfo.FactionType.Royal) == false);
        ToggleTab(MAFactionInfo.FactionType.Lords, IsFactionLocked(MAFactionInfo.FactionType.Lords) == false);
        ToggleTab(MAFactionInfo.FactionType.Mystic, IsFactionLocked(MAFactionInfo.FactionType.Mystic) == false);

    }
    public void ToggleTab(MAFactionInfo.FactionType tabFaction, bool enable)
    {
        int index = (int)tabFaction;
        if (m_tabs.Length > index)
        {
            bool isSelected = index == (int) m_faction;
            var button = m_tabs[index].GetComponentInChildren<Button>();
            button.interactable = enable && isSelected == false;
        }
    }

    public void SnapToResearchItem(string itemName)
    {
        var item = m_researchItems.Find((riui) => { return riui.name.Equals(itemName); });
        if (item != null)
        {
            var child = item.transform.FindChildRecursiveByName("IconBackground");
            var rect = child?.GetComponent<RectTransform>();
            if (rect == null)
            {
                rect = item.GetComponent<RectTransform>();
            }
            var scrollRect = m_researchItemHolder.GetComponentInParent<ResearchManagerScrollRect>();
            scrollRect.SnapTo(rect);
        }
    }
    

#if UNITY_EDITOR
    public void ClickedWriteChanges()
    {
        bool anyChanged = false;
        int count = 0;
        try
        {
            foreach (var ri in MAResearchInfo.s_researchInfos)
            {
                if (ri.m_changed || true)
                {
                    EditorUtility.DisplayCancelableProgressBar("Writing To MAReseachInfo", $"Name '{ri.m_name}'", (float)count / (float)MAResearchInfo.s_researchInfos.Count);
                    ri.m_linkToString = "";
                    ri.m_linkToList.ForEach(o => ri.m_linkToString += $"{o.m_key}|");
                    ri.m_linkToString = ri.m_linkToString.TrimEnd('|');
                    ri.m_updateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    if (ri.m_index == null) ri.m_index = "";
                    if(ri.m_key == null) ri.m_key = $"{m_faction}::{ri.m_name}";
                    if(ri.m_unlock == null) ri.m_unlock = "";
                    
                    if(ri == MAResearchInfo.s_researchInfos[MAResearchInfo.s_researchInfos.Count - 1] )
                        Debug.Log("Test");
                    NGKnack.GetKnackPutWholeRecord(ri);
                    anyChanged = true;
                    count++;
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }

        if (anyChanged)
        {
            EditorUtility.ClearProgressBar();
            NGKnack.CacheKnack(typeof(MAResearchInfo));
        }
    }
#endif
    void Update()
    {
        if (m_editMode && Input.GetMouseButtonDown(1) && 
            RectTransformUtility.RectangleContainsScreenPoint(GetComponent<RectTransform>(), Input.mousePosition))
        {
            ShowPopupWindow();
        }

        UpdateTabs();
                
        UpdateZoom();
    }
    
    private void UpdateTabs()
    {
        for(int i = 0; i < m_tabs.Length; i++)
        {
            var tab = m_tabs[i];
            bool isSelected = i == (int)m_faction;
            // They're now buttons
            //tab.transform.localPosition = new Vector3(tab.transform.localPosition.x, isSelected ? m_tabOpenY : m_tabClosedY, tab.transform.localPosition.z);
        }
    }
    
    //public RectTransform targetRectTransform;
    public float scaleSpeed = 0.2f; // Speed of scaling
    public float minScale = 0.1f;   // Minimum scale value
    public float maxScale = 1.9f;   // Maximum scale value

    void UpdateZoom()
    {
        RectTransform targetRectTransform = m_researchItemHolder.transform.parent.GetComponent<RectTransform>();
        // Check if the middle mouse button (mouse wheel) is pressed
        if (Input.GetMouseButtonDown(2))
        {
            // Reset the scale to 1 while keeping the center position and scroll position
            SetContentScale(targetRectTransform, Vector3.one, Input.mousePosition);
        }

        // Get mouse scroll wheel input
        float scrollInput = Input.GetAxis("Mouse ScrollWheel");

        // If there's input, adjust the scale
        if (scrollInput != 0f)
        {
            // Get the current scale
            Vector3 currentScale = targetRectTransform.localScale;

            // Calculate new scale by adding scroll input * scale speed
            float newScale = currentScale.x + scrollInput * scaleSpeed;

            // Clamp the new scale between minScale and maxScale
            newScale = Mathf.Clamp(newScale, minScale, maxScale);

            // Apply the new scale to the content RectTransform, zooming towards the mouse
            SetContentScale(targetRectTransform,new Vector3(newScale, newScale, newScale), Input.mousePosition);
        }
    }
    void SetContentScale(RectTransform contentTransform, Vector3 newScale, Vector3 mousePosition)
    {
        // Get the RectTransform size of the content before scaling
        Vector2 contentSizeBefore = contentTransform.rect.size;

        // Get the mouse position relative to the ScrollRect's content
        Vector2 localMousePos;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(contentTransform, mousePosition, null, out localMousePos);

        // Calculate the normalized mouse position in the content (0 to 1)
        Vector2 pivotPoint = new Vector2(
            (localMousePos.x + contentSizeBefore.x * contentTransform.pivot.x) / contentSizeBefore.x,
            (localMousePos.y + contentSizeBefore.y * contentTransform.pivot.y) / contentSizeBefore.y
        );

        // Scale the content
        contentTransform.localScale = newScale;

        // Get the RectTransform size of the content after scaling
        Vector2 contentSizeAfter = contentTransform.rect.size;

        // Calculate the difference in size
        Vector2 sizeDelta = contentSizeAfter - contentSizeBefore;

        // Adjust the scroll position based on the mouse position and the size change
        Vector2 newAnchoredPosition = contentTransform.anchoredPosition - new Vector2(
            sizeDelta.x * pivotPoint.x,
            sizeDelta.y * pivotPoint.y
        );

        // Apply the new anchored position to keep the zoom centered on the mouse
        contentTransform.anchoredPosition = newAnchoredPosition;
        var scrollRect = contentTransform.GetComponentInParent<ScrollRect>();
        // Reapply the scroll position (optional, to fine-tune scroll view behavior)
        scrollRect.normalizedPosition = new Vector2(
            Mathf.Clamp01(scrollRect.normalizedPosition.x),
            Mathf.Clamp01(scrollRect.normalizedPosition.y)
        );
    }
    void SetContentScale3(RectTransform targetRectTransform, Vector3 newScale)
    {
        var scrollRect = targetRectTransform.GetComponentInParent<ScrollRect>();
        // Capture the current scroll position as normalized values (0 to 1)
        Vector2 scrollPosition = scrollRect.normalizedPosition;

        // Set the new scale of the content RectTransform
        targetRectTransform.localScale = newScale;

        // Reapply the saved scroll position to preserve the current scroll view
        scrollRect.normalizedPosition = scrollPosition;
    }
    void ShowPopupWindow()
    {
        if (MAResearchPopupMenu.Me)
        {
            MAResearchPopupMenu.Me.DestroyMe();
            return;

        }
        var over = GetMouseOver();
        var popup = MAResearchPopupMenu.Create(transform, over);
    //    popup.Activate(m_faction);
    }    

    void ToggleDragMode(bool _flag)
    {
        foreach(var ri in m_researchItems)
            ri.EnableDrag(_flag);
    }
    public Toggle m_dragMoves;
    public Toggle m_dragCreates;
    public Toggle m_dragConnects;
    public void ToggleDragMoves()
    {
        Debug.LogError($"ToggleDragMoves moves={m_dragMoves.isOn} creates={m_dragCreates.isOn} connects={m_dragConnects.isOn}");
        if(m_dragMoves.isOn)
            ToggleDragMode(true);
    }
    public void ToggleDragCreates()
    {
        Debug.LogError($"ToggleDragCreates moves={m_dragMoves.isOn} creates={m_dragCreates.isOn} connects={m_dragConnects.isOn}");
        if(m_dragCreates.isOn)
            ToggleDragMode(true);
    }
    public void ToggleDragConnects()
    {
        Debug.LogError($"ToggleDragConnects moves={m_dragMoves.isOn} creates={m_dragCreates.isOn} connects={m_dragConnects.isOn}");
        if(m_dragConnects.isOn)
            ToggleDragMode(true);
    }
    public void ClickedEdit()
    {
        m_editMode = !m_editMode;
        foreach(var ri in m_researchItems)
            ri.EnableDrag(m_dragCreates.isOn || m_dragMoves.isOn);
        m_editorButtonsHolder.gameObject.SetActive(m_editMode);
 //       foreach(var dh in m_dragMeHolders)
  //          dh.gameObject.SetActive(m_editMode);
        Refresh();
    }
    public void ClickedClose()
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_EXIT", GameManager.Me.gameObject);
        if(MAResearchItemInfo.Me)
            MAResearchItemInfo.Me.DestroyMe();
        DestroyMe();
    }
    override public void DestroyMe()
    {
        m_isActive = false;
       base.DestroyMe();
    }
    
    public static bool IsFactionLocked(MAFactionInfo.FactionType _faction)
    {
                
        switch(_faction)
        {
            case MAFactionInfo.FactionType.Commoners:
                return MAUnlocks.Me.m_researchTabCommoners == false;
            case MAFactionInfo.FactionType.Royal:
                return MAUnlocks.Me.m_researchTabRoyal == false;
            case MAFactionInfo.FactionType.Lords:
                return MAUnlocks.Me.m_researchTabLords == false;
            case MAFactionInfo.FactionType.Mystic:
                return MAUnlocks.Me.m_researchTabMystic == false;
        }
        return true;
    }
    
    public static MAResearchManagerUI Create(Transform _holder, MAFactionInfo.FactionType _faction = MAFactionInfo.FactionType.None)
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_OPEN", GameManager.Me.gameObject);        
        var prefab = Resources.Load<MAResearchManagerUI>(PrefabName);
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_faction);
        
        //if (!MAUnlocks.Me.BuildHeroGuild)
        //{
        //    instance.SetupArcadiumForTutorial();
        //}

        return instance;
    }
}
