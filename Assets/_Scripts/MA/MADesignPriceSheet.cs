using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MADesignPriceSheet : MAGUIBase
{
    private NGDesignInterface.DesignScoreInterface m_designInterface;
    public TMP_Text m_productDescriptionText;
    public TMP_Text m_summaryText;
    public MADesignPriceSheetLine m_designPriceSheetLinePrefab;
    public Transform m_designPriceSheetLineHolder;
    [Multiline(5)] public string m_productDescription = "[Gauge] [Product], [ToMake] to make, and [Popular]";
    private int m_designHash;
    
    public void Update()
    {
        RefreshView();
    }

    public void Activate()
    {
        base.Activate();
        RefreshView();
    }
    public void RefreshView()
    {
        if (DesignTableManager.Me.Product == null)
        {
            m_designPriceSheetLineHolder.DestroyChildren();
            m_designHash = 0;
            m_productDescriptionText.text = "";
            return;
        }
        
        bool dsiChanged = m_designInterface != DesignTableManager.Me.DesignInterface; 
        m_designInterface = DesignTableManager.Me.DesignInterface;
        
        if(m_designInterface.Parts.Count == 0)
        {
            m_designPriceSheetLineHolder.DestroyChildren();
            m_productDescriptionText.text = "No Parts";
            m_designHash = 0;
            m_summaryText.text = "";
            return;
        }

        m_productDescriptionText.text = GetProductString();
        
        if(m_designHash != m_designInterface.m_designHash || dsiChanged)
        {
            m_designHash = m_designInterface.m_designHash;
            
            var summary = GetSummary();
            
            if(summary.IsNullOrWhiteSpace() == false)
                summary += "\n\n";
                
            summary += GetMakingInfo();
            
            m_summaryText.text = summary;
           
            RefreshPartView();
        }
    }
    
    private string GetSummary()
    {
        int orderQuantity = 0;
        if(DesignTableManager.Me.m_designInPlaceBuilding != null)
        {
            var order = DesignTableManager.Me.m_designInPlaceBuilding.Order;
            if(order.IsNullOrEmpty() == false)
            {
                orderQuantity = order.m_orderQuantity;
            }
            if(order.IsInfinateOrder)
                return "";
        }
        var cost = m_designInterface.TotalCost();
        var sellingPrice = m_designInterface.SellingPrice;
        var currency = GlobalData.CurrencySymbol;
        var profit = (sellingPrice*orderQuantity) - cost;
        
        return  $"Filling an order for {orderQuantity} will make a profit of:\n( {currency}{sellingPrice:F2} x {orderQuantity} ) - {currency}{cost:F2} = <b>{currency}{profit:F2}</b>";
    }
    
    private string GetMakingInfo()
    {
        if(m_designInterface == null || m_designInterface.Parts == null)
            return "";
            
        float workerMakesTime = 0;
        float tapMakesTime = 0;
        Dictionary<NGBlockInfo, int> partLines = new Dictionary<NGBlockInfo, int>();
        foreach (var p in m_designInterface.Parts)
        {
            var name = p.m_block;
            if (name.m_isInvisible) continue;
            if (partLines.ContainsKey(name))
            {
                partLines[name]++;
            }
            else
            {
                partLines[name] = 1;
            }
            workerMakesTime += p.m_block.m_workerTimeToMake;
            tapMakesTime += p.m_block.m_numTapsToMake;
        }
        
        return $"Worker makes 1 in <b>{workerMakesTime.ToHMSTimeString()}</b> or Tap <b>{tapMakesTime:F0}</b> times to make 1";
    }
    
    private string GetProductString()
    {
        var averageRarity = m_designInterface.AveragePartRarity;
        var expensiveNames = new List<string>(){"Cheap", "Affordable", "Expensive", "Outrageous", "Extreme"};
        var popularNames = new List<string>(){"Popular", "Trendy", "Disliked", "Shunned", "Unwanted"};
        var productString = m_productDescription;
        productString = productString.Replace("[Gauge]", MADesignGuage.GetNameValue(m_designInterface.ProductLineName, m_designInterface.TotalScore, false));
        productString = productString.Replace("[Product]", m_designInterface.ProductLineName);
        productString = productString.Replace("[ToMake]", expensiveNames[(int)(averageRarity*expensiveNames.Count)]);
        productString = productString.Replace("[Popular]", popularNames[(int)(averageRarity*expensiveNames.Count)]);
        return productString;
    }
    
    public void RefreshPartView()
    {
        m_designPriceSheetLineHolder.DestroyChildren();

        foreach(var line in m_designInterface.PriceSheetData)
        {
            MADesignPriceSheetLine.Create(m_designPriceSheetLinePrefab, m_designPriceSheetLineHolder, line, true);
        }
    }
    public static MADesignPriceSheet Create(Transform _holder)
    {
        var prefab = Resources.Load<MADesignPriceSheet>("_Prefabs/Dialogs/MADesignPriceSheet");
        var instance = Instantiate(prefab, _holder);
        instance.Activate();
        return instance;
    }
}
