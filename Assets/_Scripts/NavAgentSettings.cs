using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "NavAgentSettings", menuName = "Scriptable Objects/NavAgentSettings")]
public class NavAgentSettings : ScriptableObject
{
	public bool m_isRoadVehicle = false;
	
	[Min(0f)]
	public float m_minimumRadius = 0.25f;
	
	public LayerMask m_excludeRayCastTargets;
	[Header("Collision shape Bobbing (stairs * other obstacles)")]
	
	public float m_forwardPush = 0.01f;
	public float m_upBob = 0.05f;
	public float m_highScan = 0.05f;
	public float m_lowScan = 0.01f;
	public float m_forwardScanMargin = 0.4f;
	public float m_scalarTolerance = 0.01f;
	
	[Tooltip("(Non-vehicle-only) contact normal y squared needs to exceed this value for a bump-up. Lower means more bump-ups")]
	public float m_colliderBumpUpEpsilon = 0.75f;
	public float m_colliderBumpUpMaxHeight = 0.8f;
	public float m_colliderBumpUpSpeed = 0.5f;

	
	[Range(0.1f,15f)]
	public float m_stuckTimeExcessLimit = 10.0f;
	public bool m_snapKinematicYHeightToPath = false;
	
	public float m_constantJitter = .05f;
	public float m_localJitter = .02f;
	
	[Tooltip("shaky Y rotation can shake-off a head-on collision to get it unstuck")]
	public bool m_shakyForwardYEnabled = true;
	
	[Tooltip("set this to false if path finding is disabled and you follow a preset path that never needs to change")]
	public bool m_autoNavigationEnabled = true;
	
	public List<string> m_avoidHandleCollisionsWithNamesContaining = new List<string>() { "land" };

	public float m_forwardDownRayOriginOffset =  0.2f ;
	[Range(1, 100)]
	public float m_upBobMultiplier = 50f;
	[Range(1, 100)]
	public float m_forwardBobMultiplier = 0.5f;
}
