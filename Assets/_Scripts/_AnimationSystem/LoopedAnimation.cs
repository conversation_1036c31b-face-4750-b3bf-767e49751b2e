using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public interface IReactGenericAnimation {
	void Terminate();
	void Interrupt();
}

public class LoopedAnimation : IReactGenericAnimation {

	[System.Serializable]
	public class LoopedAnimationSet
	{
		[SerializeField] string m_into = "";
		[SerializeField] string m_out = "";
		[SerializeField] string m_idleLoop = "";
		[SerializeField] List<string> m_randomLoops = new List<string>();

		public float m_minTimeBetweenAnimations = 0.0f;
		public float m_maxTimeBetweenAnimations = 0.0f;

		public string Into { get { return m_into.IsNullOrWhiteSpace() ? m_idleLoop : m_into; } }
		public string Out { get { return m_out.IsNullOrWhiteSpace() ? m_idleLoop : m_out; } }
		public string IdleLoop { get { return m_idleLoop; } }
		public List<string> RandomLoops { get { return m_randomLoops.Count == 0 ? null : m_randomLoops; } }

		public LoopedAnimationSet(string _into, string _loop, string _out)
		{
			m_into = _into;
			m_idleLoop = _loop;
			m_out = _out;
		}
	}

	private NGMovingObject m_who;
	private int m_loopInProgress;
	private List<string> m_randomLoops;
	private GameObject m_parentVisual;
	private string m_into, m_idle, m_out;
	private List<string> m_parentRandoms;
	private string m_parentInto, m_parentIdle, m_parentOut;
	private Transform m_arrive;
	private System.Action<NGMovingObject> m_onFinish;
	private float m_minDelay, m_maxDelay;
	private float m_leaveTime;
	private AnimationHandler m_parentAnimHandler;
	private Coroutine m_randomActionCoroutine;
	private bool m_shouldLeave;
	public LoopedAnimation(NGMovingObject _who, string _into, string _idle, string _out, List<string> _loops, float _minDelay, float _maxDelay, float _timeUntilLeaves,
		GameObject _parentVisual, string _parentInto, string _parentIdle, string _parentOut, List<string> _parentRandoms,
		Transform _arrive, System.Action<NGMovingObject> _onFinish)
	{
		m_who = _who;
		m_into = _into;
		m_idle = _idle;
		m_out = _out;
		m_randomLoops = _loops;
		m_parentVisual = _parentVisual;
		m_parentInto = _parentInto;
		m_parentIdle = _parentIdle;
		m_parentOut = _parentOut;
		m_parentRandoms = _parentRandoms;
		m_arrive = _arrive;
		m_onFinish = _onFinish;
		m_minDelay = _minDelay; m_maxDelay = _maxDelay;
		m_leaveTime = Time.time + _timeUntilLeaves;
		m_shouldLeave = _timeUntilLeaves > 0f;
		if (m_parentVisual != null) m_parentAnimHandler = m_parentVisual.GetComponent<AnimationHandler>();
		StartAnimating();
		//Debug.LogErrorFormat("Start {0} in {1}", m_who.name, m_arrive.transform.Path());
	}
	public void Interrupt()
	{
		if (m_loopInProgress == 1)
		{
			//Debug.LogErrorFormat("Interrupt {0} in {1}", m_who.name, m_arrive.transform.Path());
			m_loopInProgress = 2;
			if (!m_who.StopWorkerLoopAnimation())
				m_onFinish(m_who);

			if (m_parentVisual != null)
				m_parentAnimHandler.FinishLoopingAnimation(false);
		}
	}
	public void Terminate()
	{
		if (m_loopInProgress > 0)
		{
			//Debug.LogErrorFormat("Terminate {0} in {1}", m_who.name, m_arrive.transform.Path());
			m_loopInProgress = -1;
			m_who.Visuals.AnimHandler.FinishLoopingAnimation(true);
			if (m_parentVisual != null)
				m_parentAnimHandler.FinishLoopingAnimation(true);
			if (m_randomActionCoroutine != null)
			{
				GameManager.Me.StopCoroutine(m_randomActionCoroutine);
			}
		}
	}
	IEnumerator Co_RandomAction(System.Action<bool> _cb)
	{
		float delay = Random.Range(m_minDelay, m_maxDelay);

		if (m_shouldLeave && Time.time + delay >= m_leaveTime)
		{
			Interrupt();
			yield break;
		}

		yield return new WaitForSeconds(delay);

		while (!m_who.isActiveAndEnabled)
		{
			yield return null;
		}

		if (m_who.IsHeld)
			yield break;

		if (m_loopInProgress != 1)
			yield break;

		if (m_randomLoops != null && m_randomLoops.Count > 0)
		{
			int animIdx = Random.Range(0, m_randomLoops.Count);
			if (m_parentVisual != null)
				m_who.InsertWorkerSyncedAnimation(m_randomLoops[animIdx],
					(bool _interrupted2) => {
						if (_interrupted2)
						{
							m_onFinish(m_who);
							return;
						}
						if (m_loopInProgress == 1)
						{
							_cb(_interrupted2);
						}
					},
					null,
					NGMovingObject.AnimationTransformMatchType.None, // Assumed already in right position
					m_parentAnimHandler,
					m_parentRandoms[animIdx]);
			else
				m_who.InsertWorkerAnimationIntoLoop(m_randomLoops[animIdx],
					(bool _interrupted2) => {
						if (_interrupted2)
						{
							m_onFinish(m_who);
							return;
						}
						if (m_loopInProgress == 1)
						{
							_cb(_interrupted2);
						}
					});
		}
		else
		{
			_cb(false);
		}
	}
	void StartAnimating()
	{
		m_loopInProgress = 1;
		System.Action<bool> randomAction = null;
		randomAction = (bool _interrupted) =>
		{
			if (_interrupted)
			{
				m_onFinish(m_who);
				return;
			}

			if (m_loopInProgress > -1)
			{
				m_randomActionCoroutine = GameManager.Me.StartCoroutine(Co_RandomAction(randomAction));
			}
		};

		// Play into animations
		if (m_parentVisual != null)
			m_who.PlayWorkerSyncedLoopAnimation(m_into, randomAction, m_idle, null, m_out, (bool _interrupted) => { var cb = m_loopInProgress > -1; m_loopInProgress = 0; if (cb) m_onFinish(m_who); },
				m_parentAnimHandler, m_parentInto, null, m_parentIdle, null, m_parentOut, null);
		else
			m_who.PlayerWorkerLoopAnimation(m_into, m_idle, m_out, randomAction, null, (bool _interrupted) => { var cb = m_loopInProgress > -1; m_loopInProgress = 0; if (cb) m_onFinish(m_who); }, m_arrive, NGMovingObject.AnimationTransformMatchType.MatchWithTimedRotation, 0, false);
	}
}