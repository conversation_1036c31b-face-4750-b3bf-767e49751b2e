#if UNITY_EDITOR

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

[InitializeOnLoad]
public static class SerializationDebug
{
	static SerializationDebug()
	{
		//Debug.LogError($"Set Serialization Debug");
		EditorPrefs.SetBool("DeveloperMode", false);
		EditorPrefs.SetBool("ShowSerializedObjectDebug", false);
	}
}

public class AnimationLibraryWindow : EditorWindow 
{
	// =================================================================================================================
	// inner classes
	
	public struct ClipSearchData
	{
		public AnimationClipDataManager.AnimationClipHolder Parent;
		public int ClipIndex;
		public AnimationClipData ClipData;
		
		public ClipSearchData(AnimationClipDataManager.AnimationClipHolder _parent, int _clipIndex,  AnimationClipData _clipData)
		{
			Parent = _parent;
			ClipIndex = _clipIndex;
			ClipData = _clipData;
		}
	}
	
	// =================================================================================================================
	// Variables
	
	// -----------------------------------------------------------------------------------------------------------------
	// Constants
	
	const int c_folderLabelWidth = 150;
	const int c_fieldWidth = 512;
	const int c_libraryCheckInterval = 5;
	const int c_indentPerLevel = 15;
	const int c_foldoutWidth = 12;

	// -----------------------------------------------------------------------------------------------------------------
	// Unicodes
	const char c_upArrowChar = '\u2191';
	const char c_downArrowChar = '\u2193';

	// -----------------------------------------------------------------------------------------------------------------
	// Static
	
	static AnimationClipDataManager.AnimationClipHolder s_root;
	
	static Vector2 s_scrollPos;
	static string s_newName;
	static int s_indentLevel;
	static AnimationClipDataManager.AnimationClipHolder CurrentSelectedFolder { get { return s_currentSelectedFolder; } set { s_currentSelectedFolder = value; OnSelectionChanged(s_root); }}
	static AnimationClipDataManager.AnimationClipHolder s_currentSelectedFolder;
	
	static AnimationClipDataManager.AnimationClipHolder s_folderToMove;
	static AnimationClipData s_fileToMove;
	
	static string s_currentSearchTerm;
	static string s_previousSearchTerm;
	static List<ClipSearchData> s_currentSearchResults;
	
	static float s_folderSideWidth;
	static float s_fileSideWidth;
	
	static bool s_renamingInProgress;
	static bool s_movingInProgress;
	static bool s_searching;
	
	static string s_currentFilePathString;
	
	// Params
	static bool s_showParams;
	static bool s_showAttachInfo;
	static bool s_showAttachedAnimation;
	static bool s_showTagInfo;
	static bool s_showMoveInterface;
	static bool s_searchByName = true;
	static bool s_searchByClip = true;
	
	static Color s_selectionColour {get {return new Color(0f,0.1f,0.75f,0.5f); } }

	// -----------------------------------------------------------------------------------------------------------------
	// Private
	
	AnimationClipDataManager m_library;
	double m_nextLibraryCheck;
	
	EditorDisplayUtilities.FoldoutStateTracker m_folderTracker;
	
	EditorGUISplitView horizontalSplitView = new EditorGUISplitView (EditorGUISplitView.Direction.Horizontal);
	
	// =================================================================================================================
	// Init
	
	[MenuItem("Window/Animation Library")]
	public static void Init()
	{
		var window = GetWindow<AnimationLibraryWindow> ("Animation Library");
		window.InitialiseWindow();
		DontDestroyOnLoad (window);
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	void InitialiseWindow()
	{
		m_nextLibraryCheck = EditorApplication.timeSinceStartup;
	}
	
	// =================================================================================================================
	// Update
	
	/*void OnInspectorUpdate()
	{
		Repaint() ;   
	}*/

	// -----------------------------------------------------------------------------------------------------------------

	void OnGUI()
	{
		// Internal Update
		InternalUpdate();

		// Visual Update
		VisualUpdate();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	void InternalUpdate()
	{
		CheckForAnimationLibrary();
	}
	
	// -----------------------------------------------------------------------------------------------------------------

	public void Rescan()
	{
		m_library = null;
		CheckForAnimationLibrary();
	}

	void VisualUpdate()
	{
		s_scrollPos = GUILayout.BeginScrollView(s_scrollPos, false, true, GUILayout.ExpandWidth(true));
		
		if(m_library == null)
		{
			EditorDisplayUtilities.DrawTitle("Animation Library - Not Found in Scene", 32);
		}
		else
		{
			s_root = m_library.GetAnimationLibraryDisplayData();
			
			DisplayHeading();
			
			DisplaySubheaderInfo(m_library.gameObject, this);
			
			DisplayParameters();
			
			List<int> indexList = new List<int>();
			DisplayAnimationDataItems(s_root);
		}

		GUILayout.EndScrollView ();
	}
	
	// =================================================================================================================
	// Internal
	
	void CheckForAnimationLibrary()
	{
		if (m_library != null) return;
		m_library = PrefabUtility.LoadPrefabContents("Assets/_Prefabs/AnimationClipLibrary.prefab").GetComponent<AnimationClipDataManager>();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static bool RemoveFolder(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipDataManager.AnimationClipHolder _folder)
	{
		if(_root == _folder)
			return false;
		
		if(_root.Folders == null || _root.Folders.Count < 1)
			return false;
		
		for(int i = 0; i < _root.Folders.Count; i++)
		{
			if(_root.Folders[i] == _folder)
			{
				_root.Folders.RemoveAt(i);
				return true;
			}
			
			if(RemoveFolder(_root.Folders[i], _folder))
				return true;
		}
		return false;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	public static bool RemoveFile(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipData _file)
	{
		for(int i = 0; i < _root.Items.Count; i++)
		{
			if(_root.Items[i] == _file)
			{
				_root.Items.RemoveAt(i);
				return true;
			}
		}
		
		for(int i = 0; i < _root.Folders.Count; i++)
		{
			if(RemoveFile(_root.Folders[i], _file))
				return true;
		}
		return false;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void RemoveFolderAtIndex(AnimationClipDataManager.AnimationClipHolder _root, List<int> _indexList)
	{
		var currentRoot = _root;
		
		for(int i = 0; i < _indexList.Count - 1; i++)
		{
			int idx = _indexList[i];
			if(currentRoot.Folders.Count > idx)
				currentRoot = currentRoot.Folders[idx];
			else
				return;
		}
		
		int lastIdx = _indexList[_indexList.Count - 1];
		if(currentRoot.Folders.Count > lastIdx)
			currentRoot.Folders.RemoveAt(lastIdx);
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void RemoveItemAtIndex(AnimationClipDataManager.AnimationClipHolder _parent, int _clipIndex)
	{
		if(_parent.Items.Count > _clipIndex)
			_parent.Items.RemoveAt(_clipIndex);
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static List<ClipSearchData> SearchForClipByName(AnimationClipDataManager.AnimationClipHolder _root, string _searchTerm)
	{
		List<ClipSearchData> results = new List<ClipSearchData>();
		
		if(string.IsNullOrEmpty(_searchTerm))
			return results;
		
		// Get all clips
		var allClips = FindAllClips(_root);
		
		// Filter results
		string lowerTerm = _searchTerm.ToLower();
		for(int i = 0; i < allClips.Count; i++)
		{
			var clipData = allClips[i].ClipData;

			if( clipData == null ) continue;

			if(s_searchByName)
			{
				string lowerName = clipData.Name.ToLower();
				if(lowerName.Contains(lowerTerm))
				{
					results.Add(allClips[i]);
					continue;
				}
			}

			if( clipData.Clip == null ) continue;
			
			if(s_searchByClip)
			{
				string lowerClipName = clipData.Clip.name.ToLower();
				if(lowerClipName.Contains(lowerTerm))
					results.Add(allClips[i]);
			}
		}
		
		return results;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static List<ClipSearchData> FindAllClips(AnimationClipDataManager.AnimationClipHolder _folder)
	{
		var toReturn = new List<ClipSearchData>();
            
		// Get all clips here
		for(int i = 0 ; i < _folder.Items.Count; i++)
		{
			toReturn.Add(new ClipSearchData(_folder, i,_folder.Items[i]));
		}
            
		// Get all clips from subfolders
		for(int i = 0; i < _folder.Folders.Count; i++)
		{
			toReturn.AddRange(FindAllClips(_folder.Folders[i]));
		}
            
		return toReturn;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static List<AnimationClipDataManager.AnimationClipHolder> FindPathToFolder(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipDataManager.AnimationClipHolder _folder)
	{
		if(_root == _folder)
			return new List<AnimationClipDataManager.AnimationClipHolder>{_folder};
		
		if(_root.Folders == null || _root.Folders.Count < 1)
			return null;
		
		for(int i = 0; i < _root.Folders.Count; i++)
		{
			var result = FindPathToFolder(_root.Folders[i], _folder);
			if(result != null)
			{
				
				var toReturn = new List<AnimationClipDataManager.AnimationClipHolder>();
				toReturn.Add(_root);
				toReturn.AddRange(result);
				return toReturn;
			}
		}
			
		return null;
	}
	
	// -----------------------------------------------------------------------------------------------------------------

	static void OnSelectionChanged(AnimationClipDataManager.AnimationClipHolder _root)
	{
		if(CurrentSelectedFolder == null) return;
		var path = FindPathToFolder(_root, CurrentSelectedFolder);
		s_currentFilePathString = PathToString(path);
	}

	// =================================================================================================================
	// Movement functions
	
	static void MoveFolderUp(AnimationClipDataManager.AnimationClipHolder _parent, AnimationClipDataManager.AnimationClipHolder _folder)
	{
		int idx = _parent.Folders.IndexOf(_folder);
		if(idx != 0)
		{
			_parent.Folders.RemoveAt(idx);
			_parent.Folders.Insert(idx-1, _folder);
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void MoveFolderDown(AnimationClipDataManager.AnimationClipHolder _parent, AnimationClipDataManager.AnimationClipHolder _folder)
	{
		int idx = _parent.Folders.IndexOf(_folder);
		if(idx != _parent.Folders.Count - 1)
		{
			_parent.Folders.RemoveAt(idx);
			_parent.Folders.Insert(idx + 1, _folder);
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void MoveFolderToFolder(AnimationClipDataManager.AnimationClipHolder _newParent, AnimationClipDataManager.AnimationClipHolder _folder)
	{
		RemoveFolder(s_root, _folder);
						
		_newParent.Folders.Add(_folder);
		s_folderToMove = null;
		s_movingInProgress = false;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void MoveFileUp(AnimationClipDataManager.AnimationClipHolder _parent, int _index)
	{
		if(_index != 0)
		{
			var file = _parent.Items[_index];
			_parent.Items.RemoveAt(_index);
			_parent.Items.Insert(_index - 1, file);
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void MoveFileDown(AnimationClipDataManager.AnimationClipHolder _parent, int _index)
	{
		if(_index != _parent.Items.Count - 1)
		{
			var file = _parent.Items[_index];
			_parent.Items.RemoveAt(_index);
			_parent.Items.Insert(_index + 1, file);
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void MoveFileToFolder(AnimationClipDataManager.AnimationClipHolder _newParent, AnimationClipData _file)
	{
		RemoveFile(s_root, _file);
		
		_newParent.Items.Add(_file);
		s_fileToMove = null;
		s_movingInProgress = false;
	}
	
	// =================================================================================================================
	// Display Structure
	
	static void DisplayHeading()
	{
		EditorDisplayUtilities.DrawTitle("Animation Library", 32);
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void GrabCSVExportData(string _group, AnimationClipDataManager.AnimationClipHolder _folder, List<string> _output) {
		_group = _group + "/" + _folder.Name;
		for(int i = 0 ; i < _folder.Items.Count; i++) {
			_output.Add($"{_group},{_folder.Items[i].Name}");
		}
		for(int i = 0; i < _folder.Folders.Count; i++) {
			GrabCSVExportData(_group, _folder.Folders[i], _output);
		}
	}

	static bool s_autoTagOpen = false;
	static string s_autoTagPostfix = "", s_autoTagPostfixReplace = "", s_autoTagName = "";
	static void DisplaySubheaderInfo(GameObject _libraryObj, AnimationLibraryWindow _window)
	{
		GUILayout.BeginHorizontal();
		{
			GUILayout.BeginHorizontal(GUILayout.Width(c_folderLabelWidth));
			{
				GUILayout.Label("Parameters", EditorStyles.boldLabel);

			}GUILayout.EndHorizontal();
		
			GUILayout.FlexibleSpace();

				GUILayout.BeginHorizontal();

					const float c_maxHeight = 18;
					Color oldColor = GUI.backgroundColor;
					GUI.backgroundColor = Color.white;
					if (GUILayout.Button("Rescan", GUILayout.MaxHeight(c_maxHeight), GUILayout.MaxWidth(60f)))
						_window.Rescan();
					GUI.backgroundColor = Color.black;
					if(GUILayout.Button("Re-import", GUILayout.MaxHeight(c_maxHeight), GUILayout.MaxWidth(120f)))
						AnimationClipDataManager.UpgradeRecursive(s_root);
					//if (GUILayout.Button("Clean", GUILayout.MaxHeight(42f), GUILayout.MaxWidth(120f)))
					//	AnimationClipDataManager.Clean(s_root);
					GUI.backgroundColor = s_autoTagOpen ? Color.green * .5f : Color.green;
					if(GUILayout.Button("Auto-populate Tags", GUILayout.MaxHeight(c_maxHeight), GUILayout.MaxWidth(120f)))
						s_autoTagOpen = !s_autoTagOpen;
					GUI.backgroundColor = Color.red;
					if(GUILayout.Button("Save", GUILayout.MaxHeight(c_maxHeight), GUILayout.MaxWidth(56f)))
					{
						AnimationClipDataManager.UpgradeRecursive(s_root);
						SavePrefab(_libraryObj);
					}
					GUI.backgroundColor = Color.blue;
					if(GUILayout.Button("Export CSV", GUILayout.MaxHeight(c_maxHeight), GUILayout.MaxWidth(120f)))
					{
						var clipList = new List<string>();
						GrabCSVExportData("", s_root, clipList);
						System.IO.File.WriteAllLines("animationDetails.csv", clipList);
					}
					GUI.backgroundColor = oldColor;
					
					GUILayout.Space(2);
				GUILayout.EndHorizontal();
			
		}GUILayout.EndHorizontal();

		GUILayout.BeginVertical();
			if (s_autoTagOpen) {
				GUILayout.BeginHorizontal();
					GUILayout.FlexibleSpace();
					GUILayout.Label("Tag");
					s_autoTagName = GUILayout.TextField(s_autoTagName, GUILayout.Width(150));
					GUILayout.Label("Postfix Match");
					s_autoTagPostfix = GUILayout.TextField(s_autoTagPostfix, GUILayout.Width(150));
					GUILayout.Label("Postfix Replace");
					s_autoTagPostfixReplace = GUILayout.TextField(s_autoTagPostfixReplace, GUILayout.Width(150));
					if (GUILayout.Button("Go!")) {
						AnimationClipDataManager.AutoPopulateTags(CurrentSelectedFolder, s_autoTagName, s_autoTagPostfix, s_autoTagPostfixReplace);
					}
				GUILayout.EndHorizontal();
			}
		GUILayout.EndVertical();

	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayParameters()
	{
		s_showParams = EditorGUILayout.Foldout(s_showParams, "Show Parameters", true);	
		
		if(s_showParams)
		{
			EditorGUI.indentLevel++;
			
			s_showAttachInfo = EditorGUILayout.Toggle("Show Attach Info", s_showAttachInfo);
			s_showAttachedAnimation = EditorGUILayout.Toggle("Show Attach Animation", s_showAttachedAnimation);
			s_showTagInfo = EditorGUILayout.Toggle("Show Tag Info", s_showTagInfo);
			s_showMoveInterface = EditorGUILayout.Toggle("Show Move Interface", s_showMoveInterface);
			s_searchByName = EditorGUILayout.Toggle("Search by Name", s_searchByName);
			s_searchByClip = EditorGUILayout.Toggle("Search by Clip", s_searchByClip);

			EditorGUI.indentLevel--;
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	void DisplayAnimationDataItems(AnimationClipDataManager.AnimationClipHolder _root)
	{
		EditorGUILayout.BeginHorizontal();
		
		horizontalSplitView.BeginSplitView ();
		var folderSideRect = EditorGUILayout.BeginVertical(EditorStyles.helpBox);
		s_folderSideWidth = folderSideRect.width <= 0 ? s_folderSideWidth : folderSideRect.width;
		
		EditorDisplayUtilities.DrawTitle("Folders", 14);
		
		if(s_movingInProgress)
			DisplayCancelMoveButton();
		else
			DisplaySearchBar();
		
		s_indentLevel = 0;
		DisplayFolderSide(_root, _root, new List<int>());
		
		EditorGUILayout.EndVertical();
		
		horizontalSplitView.Split();
		
		var fileSideRect = EditorGUILayout.BeginVertical(EditorStyles.helpBox);

		s_fileSideWidth = fileSideRect.width <= 0 ? s_fileSideWidth : fileSideRect.width;
		if(s_searching)
			DisplaySearchResults(_root, s_fileSideWidth);
		else
			DisplayFileSide(_root, CurrentSelectedFolder, s_fileSideWidth);
		
		EditorGUILayout.EndVertical();
		horizontalSplitView.EndSplitView();
		
		EditorGUILayout.EndHorizontal();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayCancelMoveButton()
	{
		GUILayout.BeginHorizontal();
		
		Color oldColor = GUI.backgroundColor;
		GUI.backgroundColor = Color.red;
		if(GUILayout.Button("Cancel Move"))
		{
			s_folderToMove = null;
			s_movingInProgress = false;
		}
		GUI.backgroundColor = oldColor;
		
		GUILayout.EndHorizontal();
	}
	
	// =================================================================================================================
	// Search bar
	
	static void DisplaySearchBar()
	{
		GUILayout.BeginHorizontal();
		
		s_currentSearchTerm = EditorDisplayUtilities.TextField("Search: ", s_currentSearchTerm);
		
		
		// GUILayout.FlexibleSpace();
		if(GUILayout.Button("Clear", GUILayout.Width(s_folderSideWidth * 0.2f)))
		{
			s_currentSearchTerm = "";
		}
		
		s_searching = !string.IsNullOrEmpty(s_currentSearchTerm);
		
		GUILayout.EndHorizontal();
	}
		
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplaySearchResults(AnimationClipDataManager.AnimationClipHolder _root, float _width)
	{
		// Perform Search
		if(s_currentSearchTerm != s_previousSearchTerm)
		{
			s_previousSearchTerm = s_currentSearchTerm;
			
			s_currentSearchResults = SearchForClipByName(_root, s_currentSearchTerm);
		}
		
		// Display results
		for(int i = 0; i < s_currentSearchResults.Count; i++)
		{
			var result = s_currentSearchResults[i];
			DisplaySearchResultData(_root, result, _width);
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplaySearchResultData(AnimationClipDataManager.AnimationClipHolder _root, ClipSearchData _searchData, float _width)
	{
		GUILayout.BeginHorizontal();
		
		if(GUILayout.Button("<<", GUILayout.MaxWidth((int)(_width * 0.1f))))
		{
			CurrentSelectedFolder = _searchData.Parent;
			s_currentSearchTerm = "";
			
			// Activate foldouts recursively
			var path = FindPathToFolder(_root, _searchData.Parent);
			if(path != null)
			{
				for(int i = 0; i < path.Count; i++)
				{
					path[i].FoldoutOpen = true;
				}
			}
		}
		
		DisplayStandardClipData(_searchData.ClipData, _searchData.Parent, _searchData.ClipIndex, _width * 0.9f);
		
		GUILayout.EndHorizontal();
	}
	
	// =================================================================================================================
	// Folder Side
	
	static void DisplayFolderSide(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipDataManager.AnimationClipHolder _folder, List<int> _indexList)
	{
		if(s_movingInProgress && _folder == s_folderToMove) return;
		
		 DisplayFolderButtons(_root, _folder, _indexList);
		
		 //GUILayout.Label(_folder.Name, EditorStyles.boldLabel);
		 if(_folder.FoldoutOpen)
		 {
			// Display Folders
			if(_folder.Folders != null && _folder.Folders.Count > 0)
			{
				// GUILayout.BeginVertical(EditorStyles.helpBox);

				for(int i = 0; i < _folder.Folders.Count; i++)
				{
					var folder = _folder.Folders[i];
					//GUILayout.Label(folder.Name, EditorStyles.boldLabel);
					List<int> newIndexList = new List<int>(_indexList);
					newIndexList.Add(i);
					s_indentLevel += c_indentPerLevel;
					DisplayFolderSide(_root, folder, newIndexList);
					s_indentLevel -= c_indentPerLevel;
				}
				
				// GUILayout.EndVertical();
			}
		 }
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayFolderButtons(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipDataManager.AnimationClipHolder _folder, List<int> _indexList)
	{
		var r = EditorGUILayout.BeginHorizontal();
		{
			if(CurrentSelectedFolder == _folder)
				EditorGUI.DrawRect(r, s_selectionColour);
			
			DisplayCoreFolderButtons(_folder);
			
			EditorGUILayout.BeginHorizontal();
			{
				GUILayout.Space(s_indentLevel);
				
				if(_folder.RenameInProgress)
				{
					DisplayFolderRenameField(_folder);
				}
				else if(s_movingInProgress)
				{
					GUILayout.FlexibleSpace();
					
					if(GUILayout.Button("Move Here", EditorStyles.miniButton))
					{
						if(s_folderToMove != null)
							MoveFolderToFolder(_folder, s_folderToMove);
						else if(s_fileToMove != null)
							MoveFileToFolder(_folder, s_fileToMove);
					}
				}
				else
				{
					DisplayFolderRenameButton(_folder, _indexList);
					
					GUILayout.FlexibleSpace();
					
					if(s_showMoveInterface)
						DisplayFolderMoveButtons(_root, _folder);
					else
						DisplayFolderAddRemoveButtons(_root, _folder, _indexList);
					
				}
				
			} EditorGUILayout.EndHorizontal();	
		} EditorGUILayout.EndHorizontal();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static GUIContent GetStandardIcon(string _s) => EditorGUIUtility.IconContent(_s);

	static void DisplayCoreFolderButtons(AnimationClipDataManager.AnimationClipHolder _folder)
	{
		EditorGUILayout.BeginHorizontal(GUILayout.Width(c_folderLabelWidth));
		{
			GUILayout.Space(s_indentLevel);
				
			bool wasOpen = _folder.FoldoutOpen;
			if (_folder.Folders.Count > 0)
			{
				if (GUILayout.Button(_folder.FoldoutOpen ? "▼" : "►", EditorStyles.label, GUILayout.Width(c_foldoutWidth)))
					_folder.FoldoutOpen = !_folder.FoldoutOpen;
			}
			else
				GUILayout.Space(c_foldoutWidth);

				
			if(GUILayout.Button($"{_folder.Name} [{_folder.Items.Count},{_folder.Folders.Count}]", EditorStyles.label))
			{
				CurrentSelectedFolder = _folder;
			}
				
			GUILayout.FlexibleSpace();
				
			// Deselect on fold away
			/*if(wasOpen && !_folder.FoldoutOpen)
			{
				// Fold close all children
				_folder.DoActionOnAllChildren(
					(_f) => 
					{
						_f.FoldoutOpen = false;
					});
					
				if(CurrentSelectedFolder == _folder)
					CurrentSelectedFolder = null;
			}*/
					
			// Select on foldout
			if(!wasOpen && _folder.FoldoutOpen)
				CurrentSelectedFolder = _folder;
				
				
		} EditorGUILayout.EndHorizontal();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayFolderRenameField(AnimationClipDataManager.AnimationClipHolder _folder)
	{
		s_newName = EditorGUILayout.TextField(s_newName);
					
		bool exitRename = false;
					
		if(GUILayout.Button("Confirm"))
		{
			_folder.Name = s_newName;
			exitRename = true;
		}
					
		if(Event.current.isKey && Event.current.keyCode == KeyCode.Return)
		{
			_folder.Name = s_newName;
			exitRename = true;

			Event.current.Use();
		}
					
		if(GUILayout.Button("Cancel"))
		{
			exitRename = true;
		}
					
		if(exitRename)
		{
			_folder.RenameInProgress = false;
			s_renamingInProgress = false;
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayFolderRenameButton(AnimationClipDataManager.AnimationClipHolder _folder, List<int> _indexList)
	{
		GUI.enabled = _indexList.Count != 0 && !s_renamingInProgress;
		if(GUILayout.Button(GetStandardIcon("Custom"), EditorStyles.miniButton, GUILayout.Width(24)))
		{            
			_folder.RenameInProgress = true;
			s_renamingInProgress = true;
			s_newName = _folder.Name;
		}
		GUI.enabled = true;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayFolderAddRemoveButtons(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipDataManager.AnimationClipHolder _folder, List<int> _indexList)
	{						
		// Add/Delete buttons
		if(GUILayout.Button("+", EditorStyles.miniButtonLeft))
		{            
			_folder.Folders.Add(new AnimationClipDataManager.AnimationClipHolder("New Folder"));
			_folder.FoldoutOpen = true;
		}
						
		GUI.enabled = _indexList.Count != 0;
						
		if(GUILayout.Button("-", EditorStyles.miniButtonRight))
		{
			if(EditorUtility.DisplayDialog("Delete Folder",
				string.Format("Are you sure you want to delete {0}?", _folder.Name),
				"Yes",
				"Cancel"))
				RemoveFolderAtIndex(_root, _indexList);
		}
						
		GUI.enabled = true;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayFolderMoveButtons(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipDataManager.AnimationClipHolder _folder)
	{
		// Move up/down buttons
		
		GUI.enabled = _root != _folder;
		
		if(GUILayout.Button(c_upArrowChar.ToString(), EditorStyles.miniButtonLeft))
		{            
			// Move up
			var path = FindPathToFolder(_root, _folder);
							
			if(path != null && path.Count > 1)
			{
				var parent = path[path.Count - 2];
				MoveFolderUp(parent, _folder);
			}
		}
		
		if(GUILayout.Button("Move", EditorStyles.miniButtonMid))
		{
			s_movingInProgress = true;
			s_folderToMove = _folder;
		}
						
		if(GUILayout.Button(c_downArrowChar.ToString(), EditorStyles.miniButtonRight))
		{
			// Move down
			var path = FindPathToFolder(_root, _folder);
							
			if(path != null && path.Count > 1)
			{
				var parent = path[path.Count - 2];
				MoveFolderDown(parent, _folder);
			}
		}
		
		GUI.enabled = true;
	}
	
	// =================================================================================================================
	// File Side
	
	static void DisplayFileSide(AnimationClipDataManager.AnimationClipHolder _root, AnimationClipDataManager.AnimationClipHolder _folder, float _width)
	{
		if(_folder == null) return;
		
		// Draw Title
		EditorDisplayUtilities.DrawTitle(s_currentFilePathString, 14);
		
		// Display Items
		if(_folder.Items != null)
		{
			for(int i = 0; i < _folder.Items.Count; i++)
			{
				var item = _folder.Items[i];
				DisplayClipData(item, _folder, i, _width);
			}	
		}
		
		// New clip button
		GUILayout.Space(5);
		if(GUILayout.Button("New Clip"))
		{
			_folder.Items.Add(new AnimationClipData());
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayClipData(AnimationClipData _clip, AnimationClipDataManager.AnimationClipHolder _parent, int _clipIndex, float _width)
	{
		GUILayout.BeginHorizontal();
		
		DisplayStandardClipData(_clip, _parent, _clipIndex, _width);

		GUILayout.EndHorizontal();
	
		if(s_showAttachedAnimation)
		{
			_clip.AttachedAnimationName = EditorDisplayUtilities.TextFieldWithToolTip("Attached Animation Name:", _clip.AttachedAnimationName, (int)(_width * 0.3f));
			GUILayout.Space(5);
		}
		
		if(s_showAttachInfo)
		{
			for(int i = 0; i < _clip.AttachData.Count; i++)	
				DisplayAttachClipData(_clip.AttachData[i], _clip, i, _width);
			
			GUILayout.BeginHorizontal();
			
			GUILayout.FlexibleSpace();
			
			if(GUILayout.Button("New Attach Data"))
			{
				_clip.AttachData.Add(new AnimationAttachData());
			}
			
			GUILayout.EndHorizontal();
			
			GUILayout.Space(5);
		}		
		
		if(s_showTagInfo)
		{
			for(int i = 0; i < _clip.TagData.Count; i++)	
				DisplayTagClipData(_clip.TagData[i], _clip, i, _width);
			
			GUILayout.BeginHorizontal();
			
			GUILayout.FlexibleSpace();
			
			if(GUILayout.Button("New Tag Data"))
			{
				_clip.TagData.Add(new AnimationTagData());
			}
			
			GUILayout.EndHorizontal();
			
			GUILayout.Space(5);
		}

	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayStandardClipData(AnimationClipData _clip, AnimationClipDataManager.AnimationClipHolder _parent, int _clipIndex, float _width)
	{
		_clip.Name = EditorDisplayUtilities.TextField("Name:", _clip.Name, (int)(_width * 0.3f));
		
		_clip.BlendTime = EditorDisplayUtilities.FloatField("Blend Time:", _clip.BlendTime, (int)(_width * 0.05f));			
		
		GUILayout.FlexibleSpace();
		_clip.Clip = EditorDisplayUtilities.ObjectField(_clip.Clip, (int)(_width * 0.3f));
		
		if(s_showMoveInterface)
			DisplayFileMoveButtons(_parent, _clipIndex);
		else
			DisplayFileRemoveButton(_clip, _parent, _clipIndex);
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayAttachClipData(AnimationAttachData _data, AnimationClipData _parent, int _index, float _width)
	{
		GUILayout.BeginHorizontal();
		
		GUILayout.Label("Attach Info");
			
		GUILayout.FlexibleSpace();
			
		_data.AttachBone = (AttachmentBone)EditorGUILayout.EnumPopup(_data.AttachBone, GUILayout.MaxWidth((int)(_width * 0.1f)));

		_data.PropPrefab = EditorDisplayUtilities.ObjectField(_data.PropPrefab, (int)(_width * 0.3f));
		
		if(GUILayout.Button("-", EditorStyles.miniButton))
		{
			_parent.AttachData.RemoveAt(_index);
		}
		
		GUILayout.EndHorizontal();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayTagClipData(AnimationTagData _data, AnimationClipData _parent, int _index, float _width)
	{
		GUILayout.BeginHorizontal();
		
		GUILayout.Label("Tag Info");
			
		GUILayout.FlexibleSpace();
			
		_data.Tag = EditorDisplayUtilities.TextField("Tag:", _data.Tag, (int)(_width * 0.3f));

		//_data.Clip = EditorDisplayUtilities.ObjectField(_data.Clip, (int)(_width * 0.3f));
		_data.ClipPath = EditorDisplayUtilities.TextField("", _data.ClipPath, (int)(_width * 0.3f));
		
		if(GUILayout.Button("-", EditorStyles.miniButton))
		{
			_parent.TagData.RemoveAt(_index);
		}
		
		GUILayout.EndHorizontal();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayFileRemoveButton(AnimationClipData _clip, AnimationClipDataManager.AnimationClipHolder _parent, int _clipIndex)
	{
		if(GUILayout.Button("-", EditorStyles.miniButton)) //, GUILayout.MaxWidth((int)(_width * 0.05f))))
		{
			if(EditorUtility.DisplayDialog("Delete Animation",
				string.Format("Are you sure you want to delete {0}?", _clip.Name),
				"Yes",
				"Cancel"))
				RemoveItemAtIndex(_parent, _clipIndex);
		}
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static void DisplayFileMoveButtons(AnimationClipDataManager.AnimationClipHolder _parent, int _clipIndex)
	{
		// Move up/down buttons
						
		if(GUILayout.Button(c_upArrowChar.ToString(), EditorStyles.miniButtonLeft))
		{            
			// Move up
			MoveFileUp(_parent, _clipIndex);
		}
		
		if(GUILayout.Button("Move", EditorStyles.miniButtonMid))
		{
			s_fileToMove = _parent.Items[_clipIndex];
			s_movingInProgress = true;
		}
						
		if(GUILayout.Button(c_downArrowChar.ToString(), EditorStyles.miniButtonRight))
		{
			// Move down
			MoveFileDown(_parent, _clipIndex);
		}
	}
	
	// =================================================================================================================
	// Utility
	
	public static void SavePrefab(GameObject _obj)
	{
		PrefabUtility.SaveAsPrefabAsset(_obj, "Assets/_Prefabs/AnimationClipLibrary.prefab");
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	static string PathToString(List<AnimationClipDataManager.AnimationClipHolder> _folders)
	{
		string toReturn = "";
		
		for(int i = 0; i < _folders.Count; i++)
		{	
			toReturn += _folders[i].Name;
			
			if(i != _folders.Count - 1)
				toReturn += " > ";
		}
		
		return toReturn;
	}
	
	// =================================================================================================================
}

#endif
