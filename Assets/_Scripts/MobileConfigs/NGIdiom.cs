
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable] 
public class NGIdiom
{
    public static List<NGIdiom> s_idioms = new List<NGIdiom>();
    public static List<NGIdiom> GetList => s_idioms;
    public string DebugDisplayName => m_idiom;

    public string id;
    public bool m_debugChanged;
    public string m_idiom;
    public float m_uIScale;
    public float m_hUDScale;
    
    public static List<NGIdiom> LoadInfoStage(System.Func<NGIdiom, bool> _callbackOnEachElement)
    { 
        s_idioms = NGKnack.ImportKnackInto<NGIdiom>(_callbackOnEachElement); 
        return s_idioms;
    }
    public static bool PostLoad(NGIdiom _what)
    {
        return true;
    }
    public static List<NGIdiom> LoadInfo()
    { 
        s_idioms = NGKnack.ImportKnackInto<NGIdiom>(PostLoad); 
        return s_idioms;
    }
    
    public static NGIdiom Idiom
    {
        get
        {
            if(s_idioms.Count > 1)
            {
                int iIdiom = s_idioms.FindIndex(x => x.m_idiom == NGDeviceID.DeviceId.m_idiom);
                NGIdiom idiom = s_idioms[iIdiom];
                s_idioms.Clear();
                s_idioms.Add(idiom);
            }

            return s_idioms.Count > 0 ? s_idioms[0] : null;
        }
    }
}
