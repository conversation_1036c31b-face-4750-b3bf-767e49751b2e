using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class NGHeroTile : NGDirectionCardBase
{
    public Transform m_priceHolder;
    override public string DragAudio => "PlaySound_WorkerCard_Take";
    override public string DragCancelAudio => "PlaySound_WorkerCard_Return";
    override public string DragAssignedAudio => "PlaySound_WorkerCard_Release";

    public override bool ScaleCardDownOnDrag { get { return true; } }
    
    protected override void Activate(NGBusinessGift _gift, MAGameFlow _gameFlow, INGDecisionCardHolder _cardHolder)
    {
        base.Activate(_gift, _gameFlow, _cardHolder);
        
        var sprite = _gift.GetSprite;
        if(sprite != null)
            m_image.sprite = sprite;
            
        if (m_priceHolder)
        {
            m_priceHolder.gameObject.SetActive(_gift.m_cardPrice > 0);
            if (m_price != null)
            {
                if(Cost == 0)
                    m_price.gameObject.SetActive(false);
                m_price.text = GlobalData.CurrencySymbol + Cost.ToString("F0");
            }
        }
    }    
    protected override void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _cardHolder)
    {
        base.Activate(_gift, _maParserSection, _cardHolder);
        if (m_priceHolder)
        {
            m_priceHolder.gameObject.SetActive(_gift.m_cardPrice > 0);
            if (m_price != null)
            {
                if(Cost == 0)
                    m_price.gameObject.SetActive(false);
                m_price.text = GlobalData.CurrencySymbol + Cost.ToString("F0");
                
            }
        }
    }
    override public void GiveReward()
    {
        var hero = m_dragActivatedObject as MAHeroBase;
        NGManager.Me.AddHero(hero);
        PayGiftCost();
        base.GiveReward();
        MAParserSupport.TryParse(m_gift.m_cardPower, out var power);
        BCActionTavern.PauseUpdateTime = 15;
        GameManager.Me.m_state.m_gameStats.m_births.Increment(1);
    }
    
    public override void ActionCancelled(bool _lockCardUntilDragEvent = false)
    {
        base.ActionCancelled(_lockCardUntilDragEvent);
    }

    protected override void OnCardDraggedBackOverHolder()
    {
        
    }

    private bool HaveFreeBedroom()
    {
        int totalBedroomSlots = 0;
        foreach(var building in NGManager.Me.m_maBuildings)
        {
            if(building.GetFreeHeroBedrooms() > 0)
                return true;
        }
        return false;
    }
    
    protected override CardUnavailableReason GetCardUnavailableReason()
    {
        var hero = m_dragActivatedObject as MACharacterBase;
        if(HaveFreeBedroom() == false && (hero == null || hero.Home == null))
            return CardUnavailableReason.NotEnoughBedroomSlots;
        return base.GetCardUnavailableReason();
    }
    
    protected override bool IsValidDrag(PointerEventData _eventData)
    {
        var worker = m_dragActivatedObject as MACharacterBase;
        if(worker == null)
            return false;
        
        var target = worker.GetComponent<Pickup>().LastTarget;
        if(target == null) 
            return false;
        return target.GetComponent<BuildingCardHolderSegmentSlot>() == null && target.GetComponent<MABuilding>() != null;
    }
    
    public MACreatureInfo GetCreatureInfo()
    {
        return MACreatureInfo.GetInfo(m_gift.m_power);
    }
    
    override protected NGMovingObject DragActivate(PointerEventData _eventData)
    {
        //CharacterPickupBehaviour.OverridePickupOptions = PickupAction.AssignHouse;
        var info = GetCreatureInfo();
        if (info == null)
        {
            Debug.LogError($"No such creature as '{m_gift.m_power}'");
            return null;
        }
        var pos = PlayerHandManager.Me.Fingertip.position + Vector3.down * 3f;
        var hero = MACharacterBase.Create(info, pos, false);
        var rot = Quaternion.LookRotation((Camera.main.transform.position - hero.transform.position).GetXZ(), Vector3.up);
        hero.transform.rotation = rot;
        hero.Internal_BeginDrag(_eventData, true, _eventData.clickCount);
        return hero;
    }
}
