using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;

public class NGSaveImport : MonoSingleton<NGSaveImport>
{
    virtual protected System.Type MyType => null;
    public string m_debugString;

    public static void Save(ref string _s)
    {
        var t = typeof(NGUnlocks);
        var properties = t.GetProperties(BindingFlags.Static | BindingFlags.Public);
        var saveString = "";
        foreach (var p in properties)
        {
            //if(p.PropertyType.Name.Equals(�Bool�))
            saveString += $"{ p.PropertyType.Name}:{ p.Name}:{p.GetValue(null).ToString()}\n";
        }
        saveString = saveString.TrimEnd('\n');
        Me.m_debugString = saveString;
        _s = saveString;
    }
    public static void Load(string _l)
    {
        if (_l.IsNullOrWhiteSpace() || Me == null) return;
        var split = _l.Split('\n');
        foreach (var s in split)
        {
            var colonSplit = s.Split(':');
            if (colonSplit.Length != 3) { Debug.LogError($"NGUnlocks: Bad Load string of '{s}'"); continue; }
            var t = typeof(NGUnlocks);
            ReactReflection.NGDecodeAndSetProperty(Me.MyType, null, colonSplit[1], colonSplit[2], $"NGSaveImport::{s}");
        }
    }
    public static void Change(string _what)
    {
        var split = _what.Split('=');
        var what = split[0].Trim();
        var value = true;
        if (split.Length >= 2)
            bool.TryParse(split[1].ToLower().Trim(), out value);
        var property = typeof(NGUnlocks).GetProperty(what, BindingFlags.Static | BindingFlags.Public | BindingFlags.IgnoreCase);
        if (property == null) { Debug.LogError($"NGUnlocks.Change can't find '{_what}' to change."); return; }
        property.SetValue(null, value);
    }
}
