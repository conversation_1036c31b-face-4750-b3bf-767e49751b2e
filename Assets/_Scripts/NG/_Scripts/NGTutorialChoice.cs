using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using Random = UnityEngine.Random;
public class NGTutorialChoice : NGTutorialMessageBase
{
    public TMP_Text m_preBarMessage;
    public Animator m_animator;
    public GameObject m_colourBit;
    public Color[] m_colours;
    public float m_textPush = 0;

    private static int m_col = 0;

    void Update()
    {
        if (Input.GetMouseButton(0))
        {
      //       Ray ray = NGTutorialManager.Me.m_boardroomCamera.ScreenPointToRay(Input.mousePosition);
		    // Physics.Raycast(ray.origin, ray.direction, out var hit);
      //       if (hit.transform != null)
      //       {
      //           if (hit.transform.gameObject == gameObject)
      //               ClickedButton();
      //       }
        }
        

        var canvas = m_preBarMessage.transform.parent;
        canvas.localPosition = Vector3.zero;
        canvas.position += (Camera.main.transform.position - canvas.position).normalized * m_textPush;
    }

    /*override protected void Activate(NGTutorial _line, string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        base.Activate(_line, _preBarText, _postBarText, _trackFuncString, _type, _pose);
        m_preBarMessage.text = _preBarText;
        m_preBarMessage.gameObject.SetActive(_preBarText.IsNullOrWhiteSpace() == false);

        if (m_colours == null || m_col < 0 || m_col >= m_colours.Length) return;
        
        MeshRenderer mr = m_colourBit.GetComponent<MeshRenderer>();
        Material mat = mr.material;

        mat.SetColor("_TintColour", m_colours[m_col]);
        mat.SetColor("_FresnelColor", m_colours[m_col] * 0.5f);
        mat.SetFloat("_AnimSpeed", Random.Range(0.8f, 1.0f));
        m_col++;
    }*/

    override public void ClickedButton()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        int num = 0;
        foreach (Transform child in transform.parent.parent)
        {
            foreach (Transform t in child)
            {
                num++;
            }
        }
        if (num < 5) return;

        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        AudioClipManager.Me.PlaySoundOld("PlaySound_BubbleFlyAway", transform);

        int c = 1;
        foreach(Transform child in transform.parent.parent)
        {
            foreach (Transform t in child)
            {
                if (t == transform)
                {
                    //NGTutorialManager.Me.VCChoice = c;
                    NGPlayer.Me.SetPlayerAlignment(c); 
                    m_animator.SetTrigger("Collect");
                    Destroy(t.gameObject, 1.0f);
                }
                else 
                {
                    Destroy(t.gameObject);
                }
            }
            c++;
        }
        m_buttonFinished = true;
    }

    override public bool IsFinished()
    {
        return m_buttonFinished;
    }

    public override bool CanDestroy(string _type) => false;
    public override bool IsOKToContinue()
    {
        return true;
    }

}
