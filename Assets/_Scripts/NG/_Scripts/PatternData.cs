using System.Collections;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using UnityEngine;
[System.Serializable]
public class PatternData
{
    public string id;
    public bool m_debugChanged;
    public int m_iD;
    public string m_name;
    public float m_partScoreMultiplier;
    public float m_sellingPriceMultiplier;
    public string m_rarity;
    public string m_path;
    public float m_cost;
    public string m_tags;
    public bool m_unlocked;
    public string m_set;
    public string m_description;
    public int m_effectUsed;
    public int m_goldCost;
    
    public float m_workerTimeToMake;
    public float m_numTapsToMake;
    public float m_materialCost;
    [ScanField] public string m_materials;
    public float m_tapBuff;
    public float m_maxWorkerBuff;
    public float m_efficiencyBuff;
    public float m_inputStockBuff;
    public float m_outputStockBuff;
    [ScanField] public string m_resourceBuff;

    private List<NGCarriableResource> m_materialsCache = null; 
    public List<NGCarriableResource> Materials { get
    {
        if (m_materialsCache != null) return m_materialsCache;
        m_materialsCache = new List<NGCarriableResource>();
        foreach (var s in m_materials.Split(';', '|', '\n'))
        {
            if(NGCarriableResource.s_carriableResources.TryGetValue(s, out var resource))
                m_materialsCache.Add(resource);
        }

        return m_materialsCache;
    }}
    public float MaterialCost => m_materialCost * Materials.Count;

    public static List<PatternData> s_entries = new ();
    public static List<PatternData> GetList=>s_entries;
    public string DebugDisplayName => m_name;

    public static bool PostImportARecord(PatternData _what)
    {
        if (string.IsNullOrEmpty(_what.m_name))
        {
            Debug.LogError($"PatternData item has no name");
            return false;
        }
        if (string.IsNullOrEmpty(_what.m_name))
        {
            Debug.LogError($"PatternData item has no path {_what.m_name}");
            return false;
        }
        if (string.IsNullOrEmpty(_what.m_rarity))
        {
            Debug.LogError($"PatternData item {_what.m_name} has no rarity set please update the Knack Table");
            _what.m_rarity = "Common";
        }
        return true;
    }
    public bool HasBuff 
    {
        get
        {
            if (m_tapBuff.IsZero()
                && m_maxWorkerBuff.IsZero()
                && m_efficiencyBuff.IsZero()
                && m_inputStockBuff.IsZero()
                && m_outputStockBuff.IsZero()
                && m_resourceBuff.IsNullOrWhiteSpace())
                return false;
            return true;
        }
    }

    public (string, float, NGCarriableResource) GetBuff()
    {
        if (m_tapBuff.IsZero() == false)
        {
            return ("Tap", m_tapBuff, null);
        }
        if (m_maxWorkerBuff.IsZero() == false)
        {
            return ("MaxWorker", m_maxWorkerBuff, null);
        }
        if (m_efficiencyBuff.IsZero() == false)
        {
            return ("Efficiency",m_efficiencyBuff, null);
        }
        if (m_inputStockBuff.IsZero() == false)
        {
            return ("InputStock", m_inputStockBuff, null);
        }
        if (m_outputStockBuff.IsZero() == false)
        {
            return ("OutputStock", m_outputStockBuff, null);
        }
        if (m_resourceBuff.IsNullOrWhiteSpace() == false)
        {
            var resource = NGCarriableResource.GetInfo(m_resourceBuff);
            return ("Resource", 0f, resource);
        }
        return (null, 0f, null);
    }

    public static List<PatternData> LoadInfo()
    {
        s_entries = NGKnack.ImportKnackInto<PatternData>(PostImportARecord);
        return s_entries;
    }
}
