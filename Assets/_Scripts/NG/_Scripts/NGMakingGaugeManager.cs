#if false
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGMakingGaugeManager : MonoSingleton<NGMakingGaugeManager>
{
	[System.Serializable]
	public class MineSprites
	{
		public string m_buildingName;
		public Sprite m_image;
	}
	public NGMakingGaugeFill m_NGMakingGaugeFillPrefab;
	public NGMakingGaugeInOut m_NGMakingGaugeInOutPrefab;
	public NGMakingGaugeClock m_NGMakingGaugeClock;
    public Transform m_GUIHolder;
    public bool m_useMakingGaugeInOut;
    public bool m_useMakingGaugeClock = false;
    public bool m_isStickyGuage;
    public List<MineSprites> m_mineSprites;

    public Sprite GetMineSprite(NGCommanderBase _factory)
    {
	    if (m_mineSprites == null || m_mineSprites.Count == 0) return null;
	    Sprite sprite = m_mineSprites[0].m_image;
	    var foundSprite =
		    m_mineSprites.Find(o => o.m_buildingName.Equals(_factory.Name, StringComparison.OrdinalIgnoreCase));
	    if (foundSprite != null) sprite = foundSprite.m_image;
	    return sprite;
    }

    private static DebugConsole.Command s_skip = new DebugConsole.Command("gauge", _s =>
    {

	    Me.m_useMakingGaugeClock = false;
	    Me.m_useMakingGaugeInOut = false;
	    switch (_s.ToLower())
	    {
		    case "clock":
		    case "new":
			    Me.m_useMakingGaugeClock = true;
			    break;
		    case "inout":
		    case "old":
			    Me.m_useMakingGaugeInOut = true;
			    break;
		    case "round":
		    case "original":
		    case "fill":
			    break;
		    default:
			    Me.m_useMakingGaugeInOut = true;
			    break;
	    }
    });

}
#endif