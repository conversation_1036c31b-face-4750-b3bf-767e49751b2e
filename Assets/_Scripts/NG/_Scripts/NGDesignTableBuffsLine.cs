using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class NGDesignTableBuffsLine : MonoBehaviour
{
    public Image[] m_buffIcons;
    public TMP_Text m_animText;
    public Animator m_anim;

    public bool m_active = false;
    
    public void DeleteMe()
    {
        Destroy(gameObject);
    }

    private int AddBuffIconString(int _index, string _type, string _value)
    {
        m_buffIcons[_index].gameObject.SetActive(true);
        m_buffIcons[_index].sprite = DesignTableManager.BuffIcon(_type);
        var text = m_buffIcons[_index].GetComponentInChildren<TMPro.TextMeshProUGUI>();
        text.text = _value;
        return ++ _index;
    }

    void Activate()
    {
        
    }

    public static NGDesignTableBuffsLine Create(NGDesignTableBuffsLine _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var activate = go.GetComponent<NGDesignTableBuffsLine>();
        activate.Activate();
        return activate;
    }
}
