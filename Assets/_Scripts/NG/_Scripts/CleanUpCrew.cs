using System.Collections.Generic;

public class CleanUpCrew
{
	private readonly List<ReactPickupPersistent> m_productsToEmergencyCleanUp = new List<ReactPickupPersistent>();
	private readonly List<ReactPickupPersistent> m_factoryPickupsToCleanUp = new List<ReactPickupPersistent>();
	public List<ReactPickupPersistent> ProductsToEmergencyCleanUp => m_productsToEmergencyCleanUp;
	public List<ReactPickupPersistent> ProductsToCleanUpByFactories => m_factoryPickupsToCleanUp;
	public bool EmergencyCleanup { get; private set; } = false;
	
	public void Update()
	{
		UpdatePickUpCleanUp();
	}
	
	private bool TryAddToFactoryCleanUp(ReactPickupPersistent _pickup, List<NGCommanderBase> _cleanUpFactories)
	{/*
		if(_pickup.IsBeingDragged == false && _pickup.ShouldBeCleanedUp() && _pickup.m_holder == null && _pickup.m_beingTakenTo == null)
		{
			foreach(NGFactory ngFactory in _cleanUpFactories)
			{
				if(ngFactory.NumInputs < ngFactory.MaxInputs && ngFactory.HasInput(_pickup.Contents) && ngFactory is not NGMine)
				{
					m_factoryPickupsToCleanUp.Add(_pickup);
					break;
				}
				else if(ngFactory.NumOutputs < ngFactory.MaxOutputs && _pickup.Contents == ngFactory.m_outputIs)
				{
					m_factoryPickupsToCleanUp.Add(_pickup);
					break;
				}		
			}
		}*/
		return false;
	}

	private void AddToEmergencyPickups(ReactPickupPersistent _pickup)
	{
		/*if(_pickup.IsDormant && _pickup.m_holder == null)
		{
			int iWorkerTargetingThis = NGManager.Me.m_NGWorkerList.FindIndex(_cleaner =>
				_cleaner.m_cleaningPickup == _pickup ||
				_cleaner.GameState.m_targetObject == "");
                        
			if(iWorkerTargetingThis == -1)
			{
				m_productsToEmergencyCleanUp.Add(_pickup);
			}
		}*/
	}

	private void UpdatePickUpCleanUp()
	{
		// bool dockExists = NGManager.Me.m_NGDockList.FindIndex(x => x.State == NGDock.DockStates.WaitingForDispatch) > -1;
		// if(!dockExists && EmergencyCleanup)
		// {
		// 	EmergencyCleanup = false;
		// }
		
		m_productsToEmergencyCleanUp.Clear();
		m_factoryPickupsToCleanUp.Clear();
		
		if(!NGUnlocks.WorkerCleanup)
			return;
		
		ReactPickupPersistent[] pickups = GlobalData.Me.m_pickupsHolder.GetComponentsInChildren<ReactPickupPersistent>();

		if(pickups.Length == 0)
			return;

		List<NGCommanderBase> cleanUpFactories = null;
		
		int workersFromCleanUpFactoriesCount = 0;
		if(NGUnlocks.WorkerCleanup)
		{
			cleanUpFactories = NGManager.Me.FactoryList.FindAll(_x => _x.IsWorkerCleanUpEnabled &&
			                                                          _x.InOwnedDistrictState == NGCommanderBase.EInOwnedDistrictState.InOwnedDistrict);
			if(cleanUpFactories.Count == 0 && NGManager.Me.EmergencyCleanup == false)
				return;
			
			//cleanUpFactories?.ForEach(_f => workersFromCleanUpFactoriesCount += _f.Workers.Count);
		}

		int workerCount = 0;
		// if(dockExists)
		// {
		// 	workerCount = NGManager.Me.m_NGWorkerList.Count;
		// }

		foreach(ReactPickupPersistent pickup in pickups)
		{
			if(pickup.m_contents.IsProduct)
			{
				if(pickup.PickupData == null) continue;
				if(pickup.PickupData.m_data >= 0 && pickup.PickupData.m_data < GameManager.Me.m_state.m_products.Count)
				{
					if(workersFromCleanUpFactoriesCount > 0)
					{
						if(TryAddToFactoryCleanUp(pickup, cleanUpFactories) == false && m_productsToEmergencyCleanUp.Count < workerCount)
						{
							AddToEmergencyPickups(pickup);
						}
					}
					else if(m_productsToEmergencyCleanUp.Count < workerCount)
					{
						AddToEmergencyPickups(pickup);
					}
				}
			}
			else if(workersFromCleanUpFactoriesCount > 0)
			{
				TryAddToFactoryCleanUp(pickup, cleanUpFactories);
			}
		}
		
		int num = m_productsToEmergencyCleanUp.Count;
		if(EmergencyCleanup)
		{
			EmergencyCleanup = num > 0;
		}
		else
		{
			EmergencyCleanup = num > NGManager.Me.m_numberOfProductPickupsToTriggerCleanup;
		}
	}
}
