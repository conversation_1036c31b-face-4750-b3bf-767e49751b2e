#if false
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class NGMakingGaugeFill : NGMakingGaugeBase
{
	//public bool m_destroying;

	public TMP_Text m_message; 
	public TMP_Text m_title;
	
	public Image m_inputProductImage; 	
	public TMP_Text m_inputStock;

	public Image m_outputProductImage;
	public TMP_Text m_outputStock;

	public GameObject m_tipHolder;
	public TMP_Text m_tip;
	public Image m_tapBar;

    private bool m_isMine;
    private bool m_isDock;
    private bool m_isBank;
	private bool m_isDispatch;
	private bool m_isRoadSite;

    // the current gauge fill is .09 - .091
    private float ClickPower => m_building.ClickAddsHowMuch;
	private int NumTapsToMake => (int)(1f / ClickPower * 0f);

	private Animator m_anim;
	
	public void DestroyMe()
	{
		if(Instance(m_inputID) == this)
			s_activeGauge.Remove(m_inputID);

		if (m_building != null && m_building.MakingGauge == this)
			m_building.MakingGauge = null;
		Destroy(gameObject);
	}

	override public void ShowNew(Type _type, string _message, bool _showDetails = false)
	{
		base.ShowNew(_type,_message, _showDetails);
		SetPosition();
		//	m_mecanicsHolder.gameObject.SetActive(false);
		m_message.gameObject.SetActive(true);
		m_message.text = _message;
	}
	override protected void Update()
	{
		base.Update();
		/*if (m_building.IsMaking == false && NGMakingGaugeManager.Me.m_isStickyGuage == false)
		{
			if (m_destroying == false)
				StartDestroy();
			return;
		}
		var combo = m_building.CalculateProduceScoreMultiplier(true);
		if(m_combo)
			m_combo.text = $"x{combo:n1}";

		var productScore = m_building.ToProductScore;
		if (m_tip)
		{
			m_tipHolder.SetActive(!Pickup.IsLongHold);
			/*if (m_building as NGRoadBuildingSite != null)
				m_tip.text = "TIP:  <size=18>Try tap+hold to build.";
			else if (m_building as NGBuildingSite != null)
				m_tip.text = "TIP:  <size=18>Try tap+hold to build.";
			else if (m_building as NGBank != null)
				m_tip.text = "TIP:  <size=18>Try tap+hold to pay.";
			else if (m_building as NGRestPlace != null)
				m_tip.text = "TIP:  <size=18>Try tap+hold to serve.";
			else
				m_tip.text = "TIP:  <size=18>Try tap+hold to make.";
		}

		float barFill = Mathf.Min(1f, productScore);
		m_tapBar.fillAmount = barFill;
		if (m_building.InputsAre.Items.Count > 0)
		{
			m_inputStock.text = $"{m_building.InputsAre.GetLowestStock()} / {m_building.MaxInputs:F0}";
		}
        else
        {
			m_inputStock.text = "";
		}
		var ratio = Mathf.Clamp(m_building.InputsAre.GetLowestStock() / m_building.MaxInputs, 0f, 1f);
		
		m_outputStock.text = $"{m_building.NumOutputs:F0} / {m_building.MaxOutputs}";*/

	}
	override public void Activate(NGCommanderBase _building, int _inputID)
	{
		base.Activate(_building, _inputID);

		if(m_title)
			m_title.text = m_building.m_title;
		var pos = m_building.LocalHighestPoint;
		transform.position = new Vector3( transform.position.x, pos.y, transform.position.z);

        /*m_isMine = _building is NGMine;
        m_isDock = _building is NGDock;
        m_isBank = _building is NGBank;
        m_isDispatch = _building is NGDispatch;
        m_isRoadSite = _building is NGRoadBuildingSite;*/

        if (m_isMine || m_isDock || m_isDispatch)
		{
			m_inputProductImage.sprite = NGMakingGaugeManager.Me.GetMineSprite(m_building);
		}
		else
		{
			//GameManager.Me.GetCarriableResourceSprite(_building.InputsAre.GetLowestStockItem(), _building, _s => m_inputProductImage.sprite = _s);
		}
		if (m_isBank || m_isRoadSite)
		{
            m_outputProductImage.sprite = NGMakingGaugeManager.Me.GetMineSprite(m_building);
		}
		else
		{
			GameManager.Me.GetCarriableResourceSprite(NGCarriableResource.GetInfo("None"), _building, _s => m_outputProductImage.sprite = _s);
		}
		SetPosition();
		m_anim = GetComponent<Animator>();
	}


}
#endif