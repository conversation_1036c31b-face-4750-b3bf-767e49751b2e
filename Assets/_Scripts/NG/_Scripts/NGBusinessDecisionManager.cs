using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Reflection;
using System;
using UnityEngine.Serialization;

public class NGBusinessDecisionManager : MonoSingleton<NGBusinessDecisionManager>
{
    public string MainBlockName = "Main"; 
    public enum NewGameType
    {
        None = -1,
        NGDirections,
        NGDirectionsFast,
        LoadFastGame,
        NGDirectionsTest,
    }
    [System.Serializable]
    public class UpgradeIcons
    {
        public string m_name;
        public Sprite m_sprite;
    }

    [Header("Decisions")]
    public List<NGBusinessDirection> m_businessDirections = new List<NGBusinessDirection>();

    [Header("Prefabs&Sprites")] 
    public MAGameFlowMessage m_gameFlowMessagePrefab;
    public NGBusinessDecisionDialog m_businessDecisionDialogPrefab;
   // public NGBusinessObjective m_NGBusinessObjectivePrefab;
    public NGBusinessRewardsSequence m_NGBusinessRewardsSequence1CardPrefab;
    public NGBusinessRewardsSequence m_NGBusinessRewardsSequence2CardPrefab;
    public NGBusinessRewardsSequence m_NGBusinessRewardsSequence3CardPrefab;
    public NGBusinessRewardsSequence m_NGBusinessRewardsSequence4CardPrefab;
    public NGBusinessRewardsSequence m_NGBusinessRewardsSequence4OrMoreCardPrefab;
    public NGPickupUpgradeCard m_pickupUpgradeCardPrefab;
    public NGDirectionGiftInfo m_NGDirectionGiftInfoPrefab;
    public NGDirectionGiftElement m_NGDirectionGiftElementPrefab;
    public NGBusinessDirectionBanner m_businessDirectionBannerPrefab;
    public NGBuildTile m_buildTilePrefab;
    public NGUpgradeTile m_NGUpgradeTilePrefab;
    public NGUnlockTile m_NGUnlockTilePrefab;
    public NGUpgradeVanTile m_NGUpgradeVanTilePrefab;
    public NGUnlockVanTile m_NGUnlockVanTilePrefab;
    public NGCurrencyTile m_NGCurrencyTilePrefab;
    public NGDecorationTile m_NGDecorationTilePrefab;
//    public NGNFTTile m_NGNFTTilePrefab;
    public NGFillTile m_fillTilePrefab;
    public NGBlockTile m_ngBlockTilePrefab;
    public NGChoicesTile m_ngChoicesTilePrefab;
    public NGCommandTile m_ngCommandTilePrefab;
    public NGWorkerTile m_ngWorkerTilePrefab;
    public NGHeroTile m_ngHeroTilePrefab;
    public NGOrderTile m_ngOrderTilePrefab;
    //public SATTownSatisfactionHUDElement m_SATTownSatisfactionHUDElementPrefab;
    public Sprite[] m_iconSprites = new Sprite[(int)NGBusinessGift.GiftType.Last];
    public UpgradeIcons[] m_upgradeIcons;
    public BusinessFlowTutorialMessage m_businessFlowTutorialMessageBig;
    public BusinessFlowTutorialMessage m_businessFlowTutorialMessagePopup;
    public Canvas m_buildingGiftPrefab;
    //public ResearchSceneManager researchSceneManagerPrefab;
    public MAGameFlowDialog m_MAgameFlowDialogPrefab;
    public MAAdvisorDialog m_MAAdvisorDialogPrefab;
    [Header("Holders")]
    public Transform m_decisionGUIHolder;
    public Transform m_NGBusinessObjectiveHolder;
    public Transform m_pickupUpgradeCardHolder;
    public Transform m_NGDirectionDialogHolder;
    public Transform m_NGDirectionAdvisorDialogHolder;
    public Transform m_SATTownSatisfactionHUDElementHolder;
    public Transform m_NGTradingHouseGUIHolder;
    public Transform NGValutGUIHolder;
    public Transform m_businessFlowTutorialMessageBigHolder;
    public Transform m_reseaarchSceneManagerHolder;
    [Header("Settings")]
    public bool m_hasShownFirst = false;
    public int m_currentLevel = -1;
    public int m_currentBusinessDirectionCount;
    public bool m_flowStarted = false;
    public float m_flowStartedTime;
    public NGBusinessDirectionBanner m_businessDirectionBanner;
    public List<ReactItemInfo.ItemInfoBase> m_validBuildings;
    public float m_timeBeforeFirstMessage = 30f;
    public bool IsOnboarding => OnBoardingManager.Me.m_isActive;
    public bool m_activated;
    public bool m_isPaused;
    public int m_playerLevel;
    public Color m_noMoneyForCardColour;
    public NGBusinessDirection CurrentDirection => (m_businessDirections != null && m_currentBusinessDirectionCount >= 0 && m_currentBusinessDirectionCount < m_businessDirections.Count) ? m_businessDirections[m_currentBusinessDirectionCount] : null;
    public int PlayerLevel { get => m_playerLevel; set => m_playerLevel = value; }

    public NGBusinessGift m_debugGift;
    public bool m_addDebugGift;

 
    public void Activate()
    {
        m_activated = true;
        //m_validBuildings = ItemDetailsHelper.FindAll(_x => _x is ReactItemInfo.BuyItem);
//        LoadInfos();
  //      NGDirectionCharacter.LoadInfo();
   //     m_businessDirections = LoadAllDirections();
       if (GameManager.HasLoadedFromSeed)
       {
           MAParserManager.StartOfGame();
//            if (NGBusinessFlow.s_flowsDict.ContainsKey(MainBlockName))
//            {
                //var l = NGBusinessFlow.s_flowsDict[MainBlockName];
               // MAGameFlow.StartFlow(l);
//            } 
       }
    }

    void Update()
    {
        if (GameManager.Me == null || GameManager.Me.LoadCompletePreFadeIn == false || m_flowStarted == false) return;
        if (m_activated == false) Activate();
        UpdateFlows();
        if (m_addDebugGift) 
        {
            m_addDebugGift = false;
            if (NGBusinessDecisionDialog.Me)
            {
                if (NGBusinessDecisionDialog.Me.m_gifts != null)
                {
                    var n = new NGBusinessGift();
                    ReactReflection.CopyClass<NGBusinessGift>(m_debugGift, n);
                    NGBusinessDecisionDialog.Me.m_gifts.Add(n);
                    NGBusinessDecisionDialog.Me.Activate();
                }
            }
        }
  }

  

    void UpdateFlows()
    {
        MAGameFlow.UpdateFlows();
        MAGameFlow.UpdateTriggers();
    }

    bool IsReadyToDisplayFirstBusinessDirection() {
        if (NGPlayer.Me.TimeSpentInGame < m_timeBeforeFirstMessage)
            return false;
        if (PlayerDetailsGUI.IsInstanceInScene)
            return false;
        if (NGPlayer.Me.PlayerName == NGPlayer.c_tutorialPlayerName)
            return false;
        return true;
    }


    void TappedBanner()
    {
        m_hasShownFirst = true;
    }

    /*public ReactItemInfo.BuyItem GetBuildingInfo(string _name)
    {
        var building = m_validBuildings.Find(o => o.Name == _name);
        var index = m_validBuildings.IndexOf(building);
        var buildingBuyItem = building as ReactItemInfo.BuyItem;
        return buildingBuyItem;
    }*/

    enum ClassNames
    {
        None,
        BusinessDirection,
        BusinessDecision,
        DirectionGift,
        BusinessGift,

        DefineBusinessDirection,
        DefineBusinessDecision,
        DefineBusinessGift,
        Note,

    }
    private static Dictionary<ClassNames, List<string>> m_fieldDictionary = new Dictionary<ClassNames, List<string>>();

    private static void UpdateClassNamesDirectory(ClassNames _type, List<string> _fields)
    {
        if (m_fieldDictionary.ContainsKey(_type))
            m_fieldDictionary[_type] = _fields;
        else
            m_fieldDictionary.Add(_type, _fields);
    }

    public static string WhichDirectory 
    {
        get
        {
            switch(GameManager.Me.m_state.m_saveStartPoint)
            {
                case NewGameType.LoadFastGame:
                    return $"/NGBalance/{NewGameType.NGDirections.ToString()}";
                default:
                    return $"/NGBalance/{GameManager.Me.m_state.m_saveStartPoint.ToString()}"; 
            }
        }
    }

    public static bool s_loadInfoFromCSV = true;

    public void LoadInfos()
    {
/*        NGBusinessAdvisor.LoadInfo();
        NGBusinessDirection.LoadInfo();
        NGBusinessFlow.LoadInfo();
        NGBusinessDecision.LoadInfo();
        NGBusinessGift.LoadInfo();*/

    }

    public static List<NGBusinessDirection> LoadAllDirections()
    {
        return null;
    }

    public void Save(ref SaveContainers.SaveCountryside _s)
    {
        _s.m_saveBusinessDecision = new SaveContainers.SaveBusinessDecision();
        _s.m_saveBusinessDecision.m_playerLevel = PlayerLevel;

        _s.m_saveBusinessDecision.m_flowStarted = m_flowStarted;
        _s.m_saveBusinessDecision.m_flowStartedTime = Time.time - m_flowStartedTime;

        NGUnlocks.Save(ref _s.m_saveBusinessDecision.m_unlockState);
        MAUnlocks.Save(ref _s.m_saveBusinessDecision.m_MAunlockState);
        MAResearchInfo.SaveAll(ref _s.m_saveBusinessDecision.m_researchState);
        NGBalance.Save(ref _s.m_saveBusinessDecision.m_balanceState);
        MAGameInterface.Save(ref _s.m_saveBusinessDecision.m_gameInterfaceState);
 //       NGDirectionCharacter.SaveActiveCharacter(ref _s);
 //       NGBusinessFlow.Save(ref _s);
        MAGameFlow.SaveAll(ref _s);
        MAParserManager.SaveAll(ref _s);
        BCChopObject.SaveAll(ref _s.m_choppedTrees);

    }

    public void EarlyLoad(SaveContainers.SaveCountryside _l)
    {
        if (GameManager.HasLoadedFromSeed) return;
        
        m_flowStarted = _l.m_saveBusinessDecision.m_flowStarted;
        m_flowStartedTime = _l.m_saveBusinessDecision.m_flowStartedTime + Time.time;
        PlayerLevel = _l.m_saveBusinessDecision.m_playerLevel;
        NGUnlocks.Load(_l.m_saveBusinessDecision.m_unlockState);
        MAUnlocks.Load(_l.m_saveBusinessDecision.m_MAunlockState);
    }
        
    public void Load(SaveContainers.SaveCountryside _l)
    {
        if (GameManager.HasLoadedFromSeed) return;
        
        MAResearchInfo.LoadAll(_l.m_saveBusinessDecision.m_researchState);

        NGBalance.Load(_l.m_saveBusinessDecision.m_balanceState);
        MAGameInterface.Load(_l.m_saveBusinessDecision.m_gameInterfaceState);
        //NGDirectionCharacter.LoadActiveCharacter(_l);
        MAGameFlow.LoadAll(_l);
        MAParserManager.LoadAll(_l);
        BCChopObject.LoadAll(_l.m_choppedTrees);

        //NGBusinessFlow.Load(_l);

    }

    public void AnOkayClicked(ContextMenu _currentContextMenu)
    {
        Debug.Log("Okay Clicked");
    }

    public void AnOkayClicked(ContextMenuData.ButtonType _currentContextMenu)
    {
        Debug.Log("Okay Clicked");
    }

    public void AnOkayClicked(Button _button)
    {
        if (_button.name == "Accept_Button")
        {
        }
    }

    public void PauseObjectives(bool _toggle)
    {
        m_isPaused = _toggle;
    }

    public void StartFlow()
    {
        m_flowStarted = true;
        m_flowStartedTime = Time.time;
    }
    /*public List<WaitForEvent> m_waitForEvents = new ();
    public class WaitForEvent
    {
        public WaitForEvent(string _audioString, Action _atEndAction, float _waitTime = 5f)
        {
            m_audioString = _audioString;
            m_action = _atEndAction;
            TriggerEventAudio(_audioString, _waitTime);
            NGBusinessDecisionManager.Me.m_waitForEvents.Add(this);
        }
     
        public string m_audioString;
        public Action m_action;
        private Coroutine m_dismissMessageCoroutine = null;

        public void TriggerEventAudio(string _audioID, float _waitTime = 5f)
        {
       //     if (_messageType.Contains("subtitles",StringComparison.OrdinalIgnoreCase))
            {
                if (_audioID.IsNullOrWhiteSpace() == false)
                {
                    string clipName = NGTutorialManager.Me.DecodeAudio(_audioID);
                    string clip = "PlaySound_" + clipName;

                    m_dismissMessageCoroutine = NGBusinessDecisionManager.Me.StartCoroutine(DismissMessageAfterAudio(clip));
                }
                else
                {
                    m_dismissMessageCoroutine = NGBusinessDecisionManager.Me.StartCoroutine(DismissMessageAfterTime(_waitTime));
                }
            }
        }
        private IEnumerator DismissMessageAfterTime(float _time)
        {
            yield return new WaitForSeconds(_time);
            if(m_action != null)
                m_action();
            NGBusinessDecisionManager.Me.m_waitForEvents.Remove(this);
        }

        private IEnumerator DismissMessageAfterAudio(string _clip)
        {
            yield return new WaitForSeconds(0.5f);

            while(AudioClipManager.Me.IsPlayingVO(_clip))
            {
                yield return null;
            }
            if(m_action != null)
                m_action();
            NGBusinessDecisionManager.Me.m_waitForEvents.Remove(this);
        }
    }*/
}
