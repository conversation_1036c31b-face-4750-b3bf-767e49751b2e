using System.Collections;
using System;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class NGDirectionCharacter
{
	public string id;
	public string m_name;
	public string m_firstName;
	public string m_title;
	public string m_spriteName;
	public Sprite m_image;
	public int m_currentDirection;
	public List<List<NGBusinessDirection>> m_directionChapters = new List<List<NGBusinessDirection>>();
	public List<NGBusinessDirection> m_directions = new List<NGBusinessDirection>();
	public string FullName => $"{m_firstName} {m_name}";
	public NGBusinessDirection CurrentDirection => (m_currentDirection >= 0 && m_currentDirection < m_directions.Count) ? m_directions[m_currentDirection] : null;


	public static int m_startDirectionCharacter = 0;
	public static int m_directionCharacter = -1;
	public static NGDirectionCharacter DirectionCharacter { get { return (m_directionCharacter >= 0 && m_directionCharacter < s_characters.Count) ? s_characters[m_directionCharacter] : null; } set { m_directionCharacter = s_characters.IndexOf(value); } }
	public static List<NGDirectionCharacter> s_characters = new List<NGDirectionCharacter>();
	public static List<NGDirectionCharacter> Characters { get { return s_characters; } private set { s_characters = value; } }
//	private static NGDirectionDialog m_directionDialog;

	public static void UpdateDirections()
    {
		if(DirectionCharacter == null)
        {
			m_directionCharacter = m_startDirectionCharacter;
        }
		if (DirectionCharacter != null)
			DirectionCharacter.UpdateDirection();
	}

	void UpdateDirection()
    {
		if (CurrentDirection.Update())
			ResolveDirection();
    }

	void ResolveDirection()
	{
		m_currentDirection++;
		if (m_currentDirection >= m_directions.Count)
		{
			return;
		}
//		m_directionDialog = NGDirectionDialog.Create(CurrentDirection);
	}

	public static NGDirectionCharacter Find(string _who)
	{
		var nameSplit = _who.Split(':', '|');
		var name = nameSplit[0].Trim();

		var advisor = Characters.Find(o => o.m_name.Equals(name, StringComparison.OrdinalIgnoreCase));
		if(advisor == null) 
			Debug.LogError($"Advsor '{_who}' not found in m_directionCharacters");
		return advisor;
	}

	public static int FindIndex(string _who)
    {
		var advisor = Find(_who);
		if (advisor == null) 
			return -1;
		return Characters.IndexOf(advisor);
    }
	public static void LoadInfo()
	{
		Characters = DataUtilities.NGLoadInfoTable("REACTXLS/NGBalance/NGCharacters", () => { return new NGDirectionCharacter(); });
		foreach(var c in Characters)
		{
			if(c.m_spriteName.IsNullOrWhiteSpace() || c.m_image != null) continue;
			c.m_image = Resources.Load<Sprite>(c.m_spriteName);
			//if(c.m_image == null) { Debug.LogError($"Sprite Image {c.m_spriteName} not found for {c.m_name}"); }
		}
	}

    public void Save(ref SaveContainers.SaveCountryside _s)
    {
		_s.m_saveBusinessDecision.m_currentDirection = m_currentDirection;
		CurrentDirection?.Save(ref _s);
    }

	public void Load(SaveContainers.SaveCountryside _l)
    {
		m_currentDirection = _l.m_saveBusinessDecision.m_currentDirection;
		CurrentDirection?.SetupFromLoad(_l);
	}

	public static void SaveActiveCharacter(ref SaveContainers.SaveCountryside _s)
    {
		_s.m_saveBusinessDecision.m_characterIndexes = new List<int>();
		foreach (var c in s_characters)
			_s.m_saveBusinessDecision.m_characterIndexes.Add(c.m_currentDirection);
		_s.m_saveBusinessDecision.m_currentCharacter = m_directionCharacter;
		if(DirectionCharacter != null)
			DirectionCharacter.Save(ref _s);
	}
    public static void LoadActiveCharacter(SaveContainers.SaveCountryside _l)
    {
        for (int i = 0; i < _l.m_saveBusinessDecision.m_characterIndexes.Count; i++)
        {
            if(i < s_characters.Count)
				s_characters[i].m_currentDirection = _l.m_saveBusinessDecision.m_characterIndexes[i];
        }
		m_directionCharacter = _l.m_saveBusinessDecision.m_currentCharacter;
		if (DirectionCharacter != null)
			DirectionCharacter.Load(_l);
    }
}
