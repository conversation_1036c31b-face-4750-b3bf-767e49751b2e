using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Random = UnityEngine.Random;

public class Balloon : MonoBehaviour
{
	public float m_ropeLength = 1f;
	public float m_maxRopeLength = 4f;// do not let the max be below the default
	public Transform m_ropeConnection;
	public bool m_clickable = true;
    private LineRenderer m_rope;
    private SpringJoint m_anchor;
	private Vector3 LineStart => m_anchor.transform.position;
	private Vector3 LineEnd => m_ropeConnection.position;
	[SerializeField] private Transform m_balloon;
	[SerializeField] private TMP_Text m_countText;
	[SerializeField] private Renderer m_textRenderer;
	private MaterialPropertyBlock m_propBlockText;
	[SerializeField] private Image m_image; 
	public CommanderBalloons.Type m_type;
	public NGBalloonManager.BalloonType m_balloonType;//TS: - since balloons are under construction i added this here to circumvent the problem of us using two different enum balloon types.(from balloonCommander and from Balloonmanager)_
	public Rigidbody Body => m_body;
	private Rigidbody m_body;
	private CommanderBalloons m_infoController;

	public Color m_labelColour = Color.black;

	public bool m_hasCanvasElements = false;
	
	private static readonly int HASH_TEXTVALUE = Shader.PropertyToID("_Value");
	private static readonly int HASH_TEXTCOLOUR = Shader.PropertyToID("_NumbersCol");
	
	public void Setup(CommanderBalloons _infoController,  Sprite _sprite = null){
		m_infoController = _infoController;
		Vector3 pos = Vector3.zero;
		if (m_anchor != null) pos = new Vector3(Random.value - 1f, 4f, Random.value - 1f);
		m_balloon.position = transform.position + pos;
		m_balloon.localRotation = Quaternion.identity;
		
		if (_sprite)
			m_balloonMaterial.SetTexture("_IconTex", _sprite.texture); //m_image.sprite = _sprite;

		if(m_textRenderer != null){
			m_propBlockText = new MaterialPropertyBlock();
			m_textRenderer.GetPropertyBlock(m_propBlockText);
			m_propBlockText.SetColor(HASH_TEXTCOLOUR, m_labelColour);
			m_textRenderer.SetPropertyBlock(m_propBlockText);
			m_countText.text = "";
		}
		
		m_anchor = GetComponentInChildren<SpringJoint>();
		m_rope = GetComponentInChildren<LineRenderer>();
		m_body = m_balloon.GetComponent<Rigidbody>();
	}
	
	public void SetHeight(float _dist){
		if (m_anchor != null)
			m_anchor.maxDistance = _dist;
	}

	private Material m_balloonMaterial;

	void Start() {
		if (m_image) {
			if (m_hasCanvasElements)
				m_image.enabled = false;
			else
				m_image.transform.parent.gameObject.SetActive(false); // switch off icon, we're now using a shader to combine the icon with the balloon
		}
		if (m_image.sprite != null)
			m_balloonMaterial.SetTexture("_IconTex", m_image.sprite.texture);

		// set rope display
		if (m_anchor == null) return;
		SetHeight(m_ropeLength);
		// prevent image inheriting wrong local scale
		m_image.transform.localScale = Vector3.one;
	}
	
	void Awake() {
		var balloon = m_balloon.Find("Visuals")?.Find("Balloon");
		m_balloonMaterial = balloon?.GetComponent<MeshRenderer>()?.material;
		if (GetComponent<NGColliderPasser>() == null) gameObject.AddComponent<NGColliderPasser>();
	}


	
	private Vector3 m_originalPosition;
	private Vector3 m_originalScale;
	float m_currentDisconnect = 0, m_targetDisconnect = 0;
	void UpdateDisconnect()
	{
		m_currentDisconnect = Mathf.Lerp(m_currentDisconnect, m_targetDisconnect, .1f);
		m_balloon.position = Vector3.Lerp(m_balloon.position, m_originalPosition, m_currentDisconnect);
		if (!NGManager.Me.m_balloonsJiggleScale)
		{
			Vector3 lcl = m_balloon.localScale, lss = m_balloon.lossyScale;
			var localScaleAdjust = new Vector3(lcl.x * m_originalScale.x / lss.x, lcl.y * m_originalScale.y / lss.y, lcl.z * m_originalScale.z / lss.z);
			m_balloon.localScale = Vector3.Lerp(m_balloon.localScale, localScaleAdjust, m_currentDisconnect);
		}
	}
	public void Disconnect()
	{
		if (m_currentDisconnect < .1f)
		{
			m_originalPosition = m_balloon.position;
			m_originalScale = m_balloon.lossyScale;
		}
		m_currentDisconnect = m_targetDisconnect = 1;
	}
	public void Reconnect()
	{
		m_targetDisconnect = 0;
	}

    private void DisplayRope()
    {
	    if (m_rope == null) return;
	    m_rope.SetPosition(0, LineStart);
	    m_rope.SetPosition(1, LineEnd);
    }

    public void UpdateBalloonTime(string _value)
    {
		m_image.transform.parent.gameObject.SetActive(true);
		if (m_countText)
		{
			m_countText.gameObject.SetActive(true);
			m_countText.text = _value;
		}
	}
    
    public void SetText(string _text){
	    if(string.IsNullOrEmpty(_text))
	    {
		    m_countText.gameObject.SetActive(false);
		    m_countText.text = "";
	    }
	    else
	    {
		    m_countText.gameObject.SetActive(true);
		    m_countText.text = _text;
	    }
    }
    
	public void UpdateBalloon(float _value, float _maxValue, Sprite _sprite = null){
		if (_value >= _maxValue){
			if(m_textRenderer != null)
				m_textRenderer.enabled = false;
			
			m_countText.text = "FULL";
			m_countText.gameObject.SetActive(true);
		} else {
			if(m_textRenderer != null) {
				m_textRenderer.enabled = true;
				m_propBlockText.SetFloat(HASH_TEXTVALUE, _value);
				m_textRenderer.SetPropertyBlock(m_propBlockText);
				m_countText.text = "";
			} else {
				m_countText.text = _value.ToString("F0");
				m_countText.gameObject.SetActive(true);
			}
		}
		
		var percentFull = Mathf.Clamp(_value / _maxValue, 0f, 1f);
		var heightIncrease = percentFull * m_maxRopeLength;
		SetHeight(m_ropeLength + heightIncrease);
		if(_sprite)
			m_balloonMaterial.SetTexture("_IconTex", _sprite.texture); //m_image.sprite = _sprite;
        
	}

	public void BalloonClicked()
	{
		if (m_ClickedBalloonAction != null)
			m_ClickedBalloonAction(this);
		// DO SOMETHING?
	}

	public void UpdateBalloonScale(float _scale)
	{
		m_balloon.localScale = Vector3.one * _scale;
	}

	public Action<Balloon> m_updateAction;
	public Action<Balloon> m_ClickedBalloonAction;
	void Activate(string _balloonText, Sprite _sprite, Action<Balloon> _updateAction, Action<Balloon> _clickedBalloonAction)
	{
		m_updateAction = _updateAction;
		m_ClickedBalloonAction = _clickedBalloonAction;
		m_countText.gameObject.SetActive(false);
		if (m_countText && _balloonText.IsNullOrWhiteSpace() == false)
		{
			m_countText.gameObject.SetActive(true);
			m_countText.text = _balloonText;
		}
		else
			m_countText.gameObject.SetActive(false);
		Setup(null);
	}
	void Update()
	{
		DisplayRope();
		if (m_updateAction != null)
			m_updateAction(this);
	}

	void LateUpdate() {
		if (NGManager.Me == null) return; // exit/visiting
		UpdateDisconnect();
	}

	public void DestroyMe()
	{
		Destroy(gameObject);
	}

	private void OnDestroy()
	{
		if (NGBalloonManager.Me != null)
		{
			NGBalloonManager.Me?.RemoveBalloon(this);
		}
	}

	public void CheckBalloonClicked()
	{
		if(Input.GetMouseButtonDown(0) == false) return;
		RaycastHit hit;
		Ray ray;
		
		ray = Camera.main.ScreenPointToRay(Input.mousePosition);
		if (Physics.Raycast(ray, out hit))
		{
			if (hit.collider.gameObject.transform.GetComponentInParent<Balloon>() == this)
			{
				BalloonClicked();
			}
		}
	}
	public static Balloon Create(Balloon _prefab, Transform _holder, string _balloonText, Sprite _sprite, Action<Balloon> _updateAction, Action<Balloon> _clickedBalloonAction)
	{
		var go = Instantiate(_prefab.gameObject, _holder);
		var b = go.GetComponent<Balloon>();
		b.Activate(_balloonText, _sprite, _updateAction, _clickedBalloonAction);
		return b;
	}
}
