using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class NGGiftIcon : MonoBehaviour
{
    public Image m_whatImage;
    public TMP_Text m_whatText;
    public Sprite[] m_giftSprite = new Sprite[(int)NGBusinessGift.GiftType.Last];
    NGBusinessGift m_gift;
    void Activate(NGBusinessGift _gift)
    {
        m_gift = _gift;

    }
    public static NGGiftIcon Create(NGGiftIcon _prefab, Transform _holder, NGBusinessGift _gift)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var bd = go.GetComponent<NGGiftIcon>();
        bd.Activate(_gift);
        return bd;
    }
}
