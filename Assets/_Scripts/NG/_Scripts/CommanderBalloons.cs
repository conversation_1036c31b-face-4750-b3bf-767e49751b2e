using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CommanderBalloons : MonoBehaviour
{
	private NGCommanderBase m_commander;
	public Balloon[] m_balloons;
	public bool[] m_balloonFlag;
	public enum Type{
		None = -1,
		Workers                                            = 0,
		Products,
		Letter,
		Upgrade,
		Repair,
		Resources,
		Tap,
		Flick,
		Warning,
		Reward,
		Research,
		ResearchComplete,
		Timer,
		ProductionStopped,
		Last
	}
	[SerializeField] private GameObject[] m_balloonPrefabs;
	public static List<CommanderBalloons> s_commanderBalloons = new List<CommanderBalloons>();

	public void Activate(MABuilding _maBuilding)
	{
		m_commander = _maBuilding;

	}

	public void Activate(NGCommanderBase _commander){
		m_commander = _commander;
		m_balloonFlag = new bool[(int)Type.Last];
		s_commanderBalloons.Add(this);
		if (m_commander.MaxWorkers > 0) AddBalloon(Type.Workers);
		//if (m_commander.MaxInputs > 0) AddBalloon(Type.Resources);
		//if (m_commander.MaxOutputs > 0) AddBalloon(Type.Products);
		if (m_commander.IsStoryTeller) AddBalloon(Type.Letter);
		if (m_commander.IsUpgradableBuilding) AddBalloon(Type.Upgrade);
		if (m_commander.IsRepairable) AddBalloon(Type.Repair);
		AddBalloon(Type.ProductionStopped);
		AddBalloon(Type.Flick);
		AddBalloon(Type.Tap);
		AddBalloon(Type.Warning);
		AddBalloon(Type.Reward);
		// if (_commander as NGResearchLab != null)
		// {
		// 	AddBalloon(Type.Research);
		// 	AddBalloon(Type.ResearchComplete);
		// }
	}

	public void AddBalloon(Type _type, Sprite _sprite = null){
		GameObject go = Instantiate(m_balloonPrefabs[(int)_type]);
		go.transform.SetParent(transform);
		go.transform.localPosition = Vector3.zero;
		var holder = go.GetComponent<Balloon>();
		holder.Setup(this, _sprite);
		m_balloons[(int)_type] = holder;
		ToggleActive(_type, false);
		holder.gameObject.SetActive(false);
		m_balloonFlag[(int)_type] = false;
	}

	public bool IsActive(Type _type)
	{
		if (m_balloons[(int) _type] == null || (int) _type >= m_balloons.Length)
			return false;
		return m_balloons[(int) _type].gameObject.activeSelf;
	}

	public void DeactivateAll(Type _ignoreType = Type.None)
	{
		foreach (var b in m_balloons)
		{
			if (b != null && b.m_type != _ignoreType)
				ToggleActive(b.m_type, false);
		}
	}

	public void ToggleActive(Type _type, bool _flag)
    {
		if (m_balloons[(int)_type] == null || (int)_type >= m_balloons.Length)
			return;

		// Turn them all off first, we can only have 1 active at a time
		if(_flag)
		{
			for(int i = 0; i < (int)Type.Last; ++i)
				m_balloonFlag[i] = false;
		}

		m_balloonFlag[(int)_type] = _flag;
	}

	public void SetBaloonFromFlag()
    {
		for (int i = 0; i < m_balloonFlag.Length; i++)
			if (m_balloons[i] != null && m_balloons[i].gameObject.activeSelf != m_balloonFlag[i])
				m_balloons[i].gameObject.SetActive(m_balloonFlag[i]);
	}

	public void EnableBalloon(int _index, bool _enable)
	{
		if (!_enable)
		{
			if (m_balloons[_index] != null)
				ToggleActive((Type)_index, false);
		}
		else
		{			
			if (m_balloons[_index] == null)
			{
				AddBalloon((Type)_index);
			}

			ToggleActive((Type)_index, true);
		}
	}

	public void UpdateAndSetTime(int _type, string _value)
	{
		EnableBalloon(_type, true);
		m_balloons[(int)_type].UpdateBalloonTime(_value);
	}

	public void UpdateAndSetOnly(int _type, int _value, int _maxValue, Sprite _sprite = null)
	{
		EnableBalloon(_type, true);
		UpdateBalloon((Type)_type, _value, _maxValue, _sprite);
	}

	public void UpdateAndSet(int _type, int _value, int _maxValue, Sprite _sprite = null)
	{
		EnableBalloon(_type, true);
		UpdateBalloon((Type)_type, _value, _maxValue, _sprite);
	}

	public void UpdateBalloon(Type _type, float _value, float _maxValue, Sprite _sprite = null)
	{
		m_balloons[(int)_type].UpdateBalloon(_value, _maxValue, _sprite);
	}

	/* Method to call when you want to return the prominence of the balloons to normal */ 
	public void ResetBalloonHeight(Type _balloonType)
	{
		var balloon = m_balloons[(int)_balloonType];
		if (balloon)
		{
            balloon.Disconnect();
            balloon.SetHeight(balloon.m_ropeLength);
            balloon.Reconnect();
        }
    }
	/* Method to call when you want to level out the prominence of the balloons */ 
	public void ResetAllBalloonHeights()
	{
		foreach(var balloon in m_balloons)
		{
			if (balloon)
			{
				balloon.Disconnect();
				balloon.SetHeight(balloon.m_ropeLength);
				balloon.Reconnect();
			}
		}
	}
	/* Method called by react commander to enable info balloons - Need to add prefab to the ReactManger*/
	public static CommanderBalloons Create(NGCommanderBase _commander, Transform _holder)
	{
		var go = Instantiate(NGManager.Me.m_NGInfoBalloonControllerPrefab.gameObject, _holder);
		CommanderBalloons ibc = go.GetComponent<CommanderBalloons>();
		ibc.Activate(_commander);
		return	ibc;
	}

	public void	ScaleAllBalloons(float _scale)
    {
		foreach (var b in m_balloons)
			if (b) b.UpdateBalloonScale(_scale);
    }

	public void TriggerGuidenceBalloon(Type _type)
    {
		if(_type != Type.Tap) EnableBalloon((int)Type.Tap, false);
		if(_type != Type.Flick) EnableBalloon((int)Type.Flick, false);
		if(_type != Type.None) EnableBalloon((int)_type, true);
    }
	
	public void DisconnectFromAnimation(Animator _animator){
		foreach (var b in m_balloons) if (b) b.Disconnect();
		StartCoroutine(ReconnectAfterAnimation(_animator, transform.parent));
	}

	IEnumerator ReconnectAfterAnimation(Animator _animator, Transform _parent){
		yield return new WaitForSeconds(_animator.GetCurrentAnimatorStateInfo(0).length);
		foreach (var b in m_balloons) if (b) b.Reconnect();
	}
}
