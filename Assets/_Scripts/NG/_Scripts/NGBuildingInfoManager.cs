/*using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Linq;
using System.Reflection;
 
public class NGBuildingInfoManager : MonoSingleton<NGBuildingInfoManager>
{
    [System.Serializable]
    public class NGBuildingInfo
    {
        public string id;
        public string m_prefabName;
        public string m_name;
        [ScanField] public string m_inputIs;
        [ScanField] public string m_inputsAre;
        [ScanField] public string m_outputIs;
        public int m_maxWorkers;
        public int m_maxInputs;
        public int m_maxOutputs;
        public int m_initialNumInputs;
        public string m_inputsPerOutput;
        public float m_workerAddsPerSecond;
        public float m_clickAddsHowMuch;
        public float m_clickHoldAddsHowMuch;
        public float m_consecutiveMultiplier;
        public float m_consecutiveHoldMultiplier;
        public float m_workerReducesEnergyWorking;
        [ScanField] public string m_workerDeliversTo;
        [Scan<PERSON>ield] public string m_acceptsDrop;
        public string m_acceptsDropOld;
        public float m_outputComplexity;
        public float m_complexityModifier;
        public float m_produceOutputMultiplier;
        public float m_buildComplexityPerBlock;
        public float m_restockTime;
        public bool m_isEfficencyBuilding;
        [InfoField] public int m_recomendedBlockCount;
        [InfoField] public int m_maxScoredParts;
        [InfoField] public float m_playerLevelScoreCap;
        public string m_title;
        public string m_description;
        public string m_catagorySelectorTitle;
        public string m_drawerLabels;
        public string m_setByReflection;
        public NGCommanderBase m_prefab;
        public string m_buildingType;
        public string m_workerJobName;
        public int m_maxBuilders;
        public float m_maxBezierDist;
        public float m_countMultiplier;
        public string m_firstBuiltTutorial;
        [ScanField] public string m_firstBuiltBlockStock;
        public string m_component;

        public string PrefabName => m_prefabName;
     /*   public float GetDesignScoreOLD(GameState_Design _design, float[] _competitionMultipliers, float[] _individualScores, Dictionary<NGCarriableResource, float> _materials, out float _taps, out float _timeToBuild) {
            return DesignUtilities.GetDesignScoreOLD(_design, PrefabName, _competitionMultipliers, m_recomendedBlockCount, m_maxScoredParts, m_playerLevelScoreCap, _individualScores, _materials, true, out _taps, out _timeToBuild);
        }*/
    /*    public float GetDesignInfo(GameState_Design _design, float[] _competitionMultipliers, out float _price, out float capacity, out float _satisfaction, float[] _individualScores, Dictionary<NGCarriableResource, float> _materials, out float _taps, out float _timeToBuild) {
            var score = GetDesignScore(_design, _competitionMultipliers, _individualScores, _materials, out _taps, out _timeToBuild);
            capacity = DesignUtilities.GetDesignCapacity(_design);
            _price = 100f * (1+score);//TODO GetPrice(score);
            _satisfaction = score;//TODO GetSatisfaction(score);
            return score;
        }*//*

        public static NGBuildingInfo GetInfo(string _name)
        {
            var entry = s_buildingInfo.Find(o => o.PrefabName.Equals(_name));
            return entry;
        }

        public static NGCommanderBase GetPrefab(string _name)
        {
            var entry = GetInfo(_name);
            if (entry == null) return null;
            return entry.m_prefab;
        }
        public void CopyToPrefab()
        {
            /*var factory = m_prefab as NGFactory;

            var fields = this.GetType().GetFields(BindingFlags.Public|BindingFlags.Instance);
            
            string[] inputs = null;
            string[] inputCounts = null;
            foreach(var f in fields)
            {
                var copyField = true;
                foreach (var a in f.CustomAttributes)
                    if (a.AttributeType == typeof(InfoField)) copyField = false;
                //IF Field is CreateProper Field then Resolve
                var value = f.GetValue(this);
                if(value == null)
                    continue;
                switch (f.Name)
                {
                    case "m_acceptsDrop":
                        if (factory is null) continue;
                        factory.m_acceptsDrop = new List<string>();
                        var acceptsDrop = (value as string).SplitFields();
                        foreach(var ad in acceptsDrop) 
                            factory.AddAcceptDrops(ad);
                        break;
                    case "m_inputsPerOutput":
                        inputCounts = (value as string).SplitFields();
                        if (inputCounts.Length < 1) value = "0";
                        else value = inputCounts[0];
                        break;
                    case "m_inputsAre":
                        inputs = (value as string).SplitFields();
                        copyField = false;
                        break;
                    case "m_outputIs":
                        var toResource = NGCarriableResource.GetInfo(value as string);
                        var fieldInfo = m_prefab.GetType().GetField(f.Name);
                        fieldInfo.SetValue(m_prefab, toResource);
                        copyField = false;
                        break;
                    case "m_workerDeliversTo":
                        if (factory is null) continue;
                        var split = m_workerDeliversTo.SplitFields();
                        factory.WorkerDeliversTo.Clear();
                        factory.WorkerDeliversTo.AddRange(split);
                        copyField = false;
                        break;
                    case "m_setByReflection":
                        foreach (var s in m_setByReflection.SplitFields())
                        {
                            if(s.IsNullOrWhiteSpace()) continue;
                            var parts = s.Split('=');
                            if(parts.Length != 2) continue;
                            ReactReflection.DecodeLine(m_prefab, s);
                        }
                        copyField = false;
                        break;
                }

                if (copyField)
                {
                    ReactReflection.NGDecodeAndSetField(m_prefab, f.Name, Convert.ToString(value, System.Globalization.CultureInfo.InvariantCulture), $"CopyToPrefab({f.Name})", false);
                }
            }
            float defaultInputsPerOutput = 0;
            if (inputCounts  != null && inputCounts.Length > 0) float.TryParse(inputCounts[0], out defaultInputsPerOutput);
            if (inputs != null) {
                m_prefab.InputsAre = new NGStock();
                for (int i = 0; i < inputs.Length; ++i)
                {
                    var r = NGCarriableResource.GetInfo(inputs[i]);
                    if(r != null && r.IsNone == false)
                    {
                        float inputsPerOutput = defaultInputsPerOutput;
                        if (inputCounts != null && i < inputCounts.Length) float.TryParse(inputCounts[i], out inputsPerOutput);
                        m_prefab.InputsAre.Set(r, inputsPerOutput, m_initialNumInputs);
                    }
                }
            }*//*
        }
        public static bool PostImport(NGBuildingInfo _what)
        {
            if (_what.PrefabName.IsNullOrWhiteSpace() == false)
            {
                _what.m_prefab = DataUtilities.NGLoadPrefabCommanberBase(_what.PrefabName);
                if (_what.m_prefab == null) return false;
                if(_what.m_prefab)
                    _what.CopyToPrefab();
            }
            return true;
        }
        public static List<NGBuildingInfo> s_buildingInfo;
        public static List<NGBuildingInfo> LoadInfo()
        {
            if(NGKnack.PDMBuildInfo)
                s_buildingInfo = NGKnack.ImportKnackInto<NGBuildingInfo>(PostImport, "NGBuildingInfo-PDM");
            else
                s_buildingInfo = NGKnack.ImportKnackInto<NGBuildingInfo>(PostImport); 
            BlockBalanceManager.AddPartTypeData(s_buildingInfo);
            return s_buildingInfo;
        }
        public static NGBuildingInfo GetBuildingInfo(string _buildingName)
        {
            if (s_buildingInfo == null) return null;
                
            return s_buildingInfo.Find(o => o.PrefabName.Equals(_buildingName));
        }

        public static NGBuildingInfo GetBuildingInfoByGiftName(string _buildingName)
        {
            return s_buildingInfo.Find(o => o.m_name.Equals(_buildingName));
        }

        public static void CheckFirstBuilt(string _buildingName)
        {
            if (GameManager.Me.LoadComplete == false) return;
            var info = GetBuildingInfo(_buildingName);
            if (info == null) return;
            if (info.m_firstBuiltTutorial.IsNullOrWhiteSpace() == false)
            {
  //              if (NGTutorialManager.Me.TutorialPhaseHasBeenPlayed(info.m_firstBuiltTutorial) == false)
    //                NGTutorialInterface.Me.Dialog(info.m_firstBuiltTutorial);
            }

            if (info.m_firstBuiltBlockStock.IsNullOrWhiteSpace() == false)
            {
                var parts = info.m_firstBuiltBlockStock.Split('|', ';', '\n');
                var unlocks = "";
                foreach (var p in parts)
                {
                    GameManager.AddUnlock(p, 1);
                    unlocks += $"{p}+1~";
                }
            }
                
        }
        
    }
    public void Activate()
    {
    }
}
*/