using UnityEngine;

public class ProductionContribution
{
	public enum Contributor
	{
		Worker,
		Tap,
		Hold,
		Other,
	}

	private Contributor m_latestContributor = Contributor.Worker;
	private float m_contributionRemainder = 0f;
	public float HoldClickContribution { get; private set; } = 0;
	public float TapClickContribution { get; private set; } = 0;
	public float WorkerContributionCurrentProduct => Mathf.Clamp01(1f - HoldClickContribution - TapClickContribution);
	
	public void Contribute(Contributor latestContributor, float _amount, float _currentTotal)
	{
		m_latestContributor = latestContributor;
		m_contributionRemainder = Mathf.Clamp01(_currentTotal - 1f);
		_amount = Mathf.Clamp01(_amount - m_contributionRemainder);
		if(latestContributor == Contributor.Hold)
			HoldClickContribution += _amount;
		else if(latestContributor == Contributor.Tap)
			TapClickContribution += _amount;
	}

	public void ResetContributions()
	{
		switch (m_latestContributor)
		{
			case Contributor.Hold:
				HoldClickContribution = m_contributionRemainder;
				TapClickContribution = 0;
				break;
			case Contributor.Tap:
				TapClickContribution = m_contributionRemainder;
				HoldClickContribution = 0;
				break;
			default:
				HoldClickContribution = 0;
				TapClickContribution = 0;
				break;
		}

		m_contributionRemainder = 0;
	}
}
