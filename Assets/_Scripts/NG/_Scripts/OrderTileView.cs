using System;
using TMPro;
using UnityEditor.Rendering;
using UnityEngine;
using UnityEngine.UI;

public class OrderTileView : MonoBehaviour
{
    public bool m_enableGiverIcon;
    public TMP_Text m_orderQuantityRemaining;
    public TMP_Text m_orderQuality;
    public TMP_Text m_productLine;
    public TMP_Text m_detailedText;
    public Image m_orderQualityImage;
    public Image m_giverIcon;

    NGOrderTile m_orderTile;
    

    public void Start()
    {
        m_orderTile = GetComponentInParent<NGOrderTile>();
        Update();
    }

    public void Update()
    {
        if(m_orderTile == null || m_orderTile.Order == null || m_orderTile.Order == MAOrder.EmptyOrder || m_orderTile.Order.OrderInfo == null) return;
        
        var order = m_orderTile.Order;
        var quality = MADesignGuage.GetQualityEntry(order.m_productLine, order.m_orderQuality);
        
        if(m_orderQuantityRemaining != null)
        {
            if(order.IsInfinateOrder) 
                m_orderQuantityRemaining.text = "∞";
            else
                m_orderQuantityRemaining.text = order.RemainingToManufacture.ToString();
        }
        
        if(m_orderQuality != null)
        {
            if(order.m_orderQuality.IsZero() == false)
                m_orderQuality.text = quality.m_name;
            else if(m_orderQuality.transform.parent.gameObject.activeSelf)  // Use parernt as this holds the background
                m_orderQuality.transform.parent.gameObject.SetActive(false); 
        }
            
        if(m_productLine != null)
            m_productLine.text = order.ProductDisplayName;
            
        if(m_orderQualityImage != null)
            m_orderQualityImage.color = quality.m_color;
            
        if(m_giverIcon != null)
        {
            if(order.OrderInfo.m_giverSprite != null && m_enableGiverIcon)
                m_giverIcon.sprite = order.OrderInfo.m_giverSprite;
            else if(m_giverIcon.gameObject.activeSelf)
                m_giverIcon.gameObject.SetActive(false);
        }
        
        if(m_detailedText != null)
        {
            m_detailedText.text = Description(order);
        }
    }
    
    private string Description(MAOrder _order)
    {
        var quantity = _order.IsInfinateOrder ? "infinite" : _order.m_orderQuantity.ToString();
        var quality = MADesignGuage.GetNameValue(_order.ProductLine, _order.m_orderQuality);
        
        if(_order.OrderInfo.m_cardDescription.IsNullOrWhiteSpace())
        {
            string info = $"Order for {quantity} {quality} {_order.ProductDisplayName}";
            if(_order.OrderInfo.m_giver.IsNullOrWhiteSpace())
                info += ".";
            else
                info += $", from {_order.OrderInfo.m_giver}.";
                
            if(_order.Rewards.Length > 0)
                info += "They will give you the rewards shown below on completion.";
            
            return info;
        }

        string desc = _order.OrderInfo.m_cardDescription;
        desc = desc.Replace("[quality]", quality);
        desc = desc.Replace("[quantity]", quantity);
        return desc;
    }
}
