using System;
using System.Collections.Generic;
using Cans.Analytics;
using UnityEngine;

public class NGPlayer : MonoSingleton<NGPlayer> 
{	
	public enum ALIGNMENT 
	{ //this enum does not seem like it's in use (only 4 choices, not 5). Neither is the class PlayerAlignmentOption and related parts of the code.
		PROFIT_BEFORE_PEOPLE,
		ENVOROMENT_BEFORE_PROFIT,
		WORK_LIFE_BALANCE,
		PERSONAL_WEALTH,
	}

	private Dictionary<int, string> m_AlignmentChoices = new()
	{
		{ 1, "rich 3" },
		{ 2, "genius 1" },
		{ 3, "honest 5 " },
		{ 4, "bone 2" },
		{ 5, "gorgeous 4" },
	};
	
	public string PlayerName = c_tutorialPlayerName;
    public string PlayerFirstname = "";
    public string PlayerSurname = "";
    public string PlayerNickname = "";
	public string m_companyName = "";
	public string m_missionStatement = "";
	public bool HasSetDetails {get; private set;} = false;
	public CurrencyContainer m_cash = new CurrencyContainer (CurrencyContainer.CurrencyType.Cash, "Cash", "PlaySound_Cash_IN", "PlaySound_Cash_OUT");
	public CurrencyContainer m_royalFavors = new CurrencyContainer (CurrencyContainer.CurrencyType.RoyalFavours, "Royal Favors", "", "");
	public CurrencyContainer m_lordsFavors = new CurrencyContainer (CurrencyContainer.CurrencyType.LordsFavours, "Lords Favors", "", "");
	public CurrencyContainer m_mysticFavors = new CurrencyContainer (CurrencyContainer.CurrencyType.MysticFavours, "Mystic Favors", "", "");
	public CurrencyContainer m_commonersFavors = new CurrencyContainer (CurrencyContainer.CurrencyType.PeoplesFavours, "Commoners Favors", "", "");
	public int m_businessStage; 
	public bool m_runInBackground;
	public float TimeSpentInGame => 175f + Time.realtimeSinceStartup;
	public const string c_tutorialPlayerName = "Tutorial Player";
	private int m_alignment;
	public string Alignment
	{
		get
		{
			if (m_AlignmentChoices.TryGetValue(m_alignment, out string alignment))
			{
				return alignment;
			}
			return "n/a";
		}
	}
	
	public void UpdateTotalUSDSpend(double _spend) {}

	public static int CalculateIdeasToGold(int ideasAmount) {
		float goldPerIdeaRatio = NGManager.Me.m_goldPerIdeaRatio;
		int goldRequired = Mathf.Max(1, Mathf.FloorToInt(ideasAmount * goldPerIdeaRatio));
		return goldRequired;
	}
	
	public bool ArePlayerDetailsTemporary() { return PlayerName == c_tutorialPlayerName || string.IsNullOrEmpty(PlayerName); }
	public void EditPlayerDetails(string _fname, string _sname, string _company_name, string _missionStatement, Action callback) {
		//TODO? Player.Me.m_playerName = _name;
		PlayerName = _fname + " " + _sname; 
        PlayerFirstname = _fname;
        PlayerSurname = _sname;
		m_companyName = _company_name;
		m_missionStatement = _missionStatement;

		//TODO NetClientGame.ChangePlayerDetails(_name, _company_name, _missionStatement, callback);
		callback();
		//TODO MongoPlayerManager.Me.ChangePlayerDetails(_name, _company_name, _missionStatement);
	}
	public static List<string> GetUnlockedBuyItemIds(ReactItemInfo.ItemInfoBase baseItem) {
		/*TODO if(baseItem is ReactItemInfo.ProductPackInfo) {
			ReactItemInfo.ProductPackInfo productInfo = (ReactItemInfo.ProductPackInfo)baseItem;
			return new List<string>(productInfo.m_productIDs);			
		}
		if(baseItem is ReactItemInfo.RewardBucketInfo) {
			return new List<string>();
		}
		if(baseItem is ReactItemInfo.HarvesterInfo) {
			return new List<string>();
		}*/
		return new List<string>() { baseItem.Name };
	}
	void UnlockBuyItem (string _uid) {
		var ppi = ItemDetailsHelper.GetBuyItemByName(_uid);
		ppi.Activate(true);
		ppi.OnUnlock(true);
		// Unlock any buildings which this block would unlock.
		/*var buildingBuyItem = ItemDetailsHelper.GetBuildingBuyItemForBlock(_uid);
		if (buildingBuyItem != null && buildingBuyItem.UnlockedRequiredBlocks()) {
			buildingBuyItem.Activate(true);
		}*/
	}
	public void AddUnlockedBuyItem (List<string> itemUIDs, Dictionary<string, int> batchUnlocks=null)
	{
		var unlocks = new Dictionary<string, int>();
		foreach(string uid in itemUIDs)
		{
			bool alreadyUnlocked = GameManager.IsUnlocked(uid);
			int add = GameManager.IsConsumableUnlock(uid) ? 1 : -1;
			unlocks[uid] = add;
			if (!alreadyUnlocked) UnlockBuyItem(uid);
		}

		if(batchUnlocks != null)
		{
			// server optimization: add this to a single batch to be sent to server by the caller
			foreach(var unlock in unlocks)
				batchUnlocks[unlock.Key] = unlock.Value;
		}
		else
		{
			// unlock them all at once, so that it uses 1 server call instead of dozens from inside the loop.
			GameManager.AddUnlocks(unlocks);
		}
	}

	public static void Save(ref SaveContainers.SaveGame _s) {
		if (Me) {
			_s.m_reactPlayerSave.m_name = Me.PlayerName;
			_s.m_reactPlayerSave.m_firstName = Me.PlayerFirstname;
			_s.m_reactPlayerSave.m_surname = Me.PlayerSurname;
			_s.m_reactPlayerSave.m_nickname = Me.PlayerNickname;
			_s.m_reactPlayerSave.m_companyName = Me.m_companyName;
			_s.m_reactPlayerSave.m_missionStatement = Me.m_missionStatement;
			_s.m_reactPlayerSave.m_businessStage = Me.m_businessStage;
			_s.m_reactPlayerSave.m_runInBackground = Me.m_runInBackground;
		}
	}
	public static void Load(SaveContainers.SaveGame _s) {
		if (Me) {
			Me.PlayerName = _s.m_reactPlayerSave.m_name;
			Me.PlayerFirstname = _s.m_reactPlayerSave.m_firstName;
			Me.PlayerSurname = _s.m_reactPlayerSave.m_surname;
			Me.PlayerNickname = _s.m_reactPlayerSave.m_nickname;
			Me.m_companyName = _s.m_reactPlayerSave.m_companyName;
			Me.m_missionStatement = _s.m_reactPlayerSave.m_missionStatement;
			Me.m_businessStage = _s.m_reactPlayerSave.m_businessStage;
			Me.m_runInBackground = _s.m_reactPlayerSave.m_runInBackground;
			Application.runInBackground = Me.m_runInBackground;
		}

	}

	public void SetPlayerAlignment(int alignment)
	{
		m_alignment = alignment;
		if(GameManager.IsVisitingInProgress == false)
		{
			AnalyticsEvent analyticsEvent = AnalyticsManager.Me.Events.SignUp;
			analyticsEvent.AddParameter(EventParams.alignment, Alignment);
			analyticsEvent.AddParameter(EventParams.playerName, PlayerName);
			analyticsEvent.AddParameter(EventParams.businessName, m_companyName);
			analyticsEvent.AddParameter(EventParams.businessDescription, m_missionStatement);
			AnalyticsManager.Me.LogEvent(analyticsEvent);
		}
	}
	public bool AddFavors(MAFactionInfo.FactionType _faction, int _amount, string _reason)
	{
		switch (_faction)
		{
			case MAFactionInfo.FactionType.Royal:
				m_royalFavors.Add(CurrencyContainer.TransactionType.InGame, (long)_amount, _reason);
				break;
			case MAFactionInfo.FactionType.Lords:
				m_lordsFavors.Add(CurrencyContainer.TransactionType.InGame, (long)_amount, _reason);
				break;
			case MAFactionInfo.FactionType.Commoners:
				m_commonersFavors.Add(CurrencyContainer.TransactionType.InGame, (long)_amount, _reason);
				break;
			case MAFactionInfo.FactionType.Mystic:
				m_mysticFavors.Add(CurrencyContainer.TransactionType.InGame, (long)_amount, _reason);
				break;
		}

		return true;
	}
	public bool SpendFavors(MAFactionInfo.FactionType _faction, float _amount, string _reason)
	{
		switch (_faction)
		{
			case MAFactionInfo.FactionType.Royal:
				m_royalFavors.Spend(_amount, _reason, "","");
				break;
			case MAFactionInfo.FactionType.Lords:
				m_lordsFavors.Spend(_amount, _reason, "","");
				break;
			case MAFactionInfo.FactionType.Commoners:
				m_commonersFavors.Spend(_amount, _reason, "","");
				break;
			case MAFactionInfo.FactionType.Mystic:
				m_mysticFavors.Spend(_amount, _reason, "","");
				break;
		}

		return true;
	}
	public int GetFavors(MAFactionInfo.FactionType _faction)
	{
		switch (_faction)
		{
			case MAFactionInfo.FactionType.Royal:
				return (int)m_royalFavors.Balance;
			case MAFactionInfo.FactionType.Lords:
				return (int)m_lordsFavors.Balance;
			case MAFactionInfo.FactionType.Commoners:
				return (int)m_commonersFavors.Balance;
			case MAFactionInfo.FactionType.Mystic:
				return (int)m_mysticFavors.Balance;
		}
		return 0;
	}
}
