using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cans.Analytics;
using UnityEngine;
using TMPro;
using UnityEngine.Serialization;
using UnityEngine.TextCore.Text;

public class NGManager : MonoSingleton<NGManager>
{
	// === SSerializer support transforms
	public static void SetSSerializerTransforms(bool _clearBeforeSet = true)
	{
		if (_clearBeforeSet)
		{
			SSerializer.ClearGlobalTypeTransforms();
			SSerializer.ClearAlwaysSerializeNamespaces();
		}
		SSerializer.AddGlobalTypeTransform(typeof(MABuilding), "NGManager", "NGCommanderBase");
		SSerializer.AddGlobalTypeTransform(typeof(MACharacterBase), "NGManager", "MACharacterBase");
		SSerializer.AddGlobalTypeTransform(typeof(MAVehicle), "NGManager", "MAVehicle");
		SSerializer.AddGlobalTypeTransform(typeof(GameObject), "NGManager", "GO");
		SSerializer.AddGlobalTypeTransform(typeof(MAOrder), "NGManager", "MAOrder");
		SSerializer.AddAlwaysSerializeNamespace("UnityEngine");
	}

	public static string ExportNGCommanderBase(NGCommanderBase _o) => $"B{_o?.m_linkUID ?? -1}";
	public static NGCommanderBase ImportNGCommanderBase(string _s)
	{
		if (string.IsNullOrEmpty(_s)) return null;
		int id = int.Parse(_s[1..]);
		return Me.m_NGCommanderList.Find(_o => _o.m_linkUID == id);
	}

	public static string ExportMACharacterBase(MACharacterBase _o) => $"W{_o?.m_ID ?? -1}";
	public static NGMovingObject ImportMACharacterBase(string _s)
	{
		if (string.IsNullOrEmpty(_s)) return null;
		int id = int.Parse(_s[1..]);
		return Me.FindCharacterByID(id);
	}
	
	public static string ExportMAVehicle(MAVehicle _o) => $"D{_o?.m_ID ?? -1}";
	public static NGMovingObject ImportMAVehicle(string _s)
	{
		if (string.IsNullOrEmpty(_s)) return null;
		int id = int.Parse(_s[1..]);
		return Me.FindVehicleByID(id);
	}

	public static string ExportMAOrder(MAOrder _o)
	{
		if(_o.IsNullOrEmpty())
			return "O-1";
		return $"O{_o.OrderId}";
	}
	public static MAOrder ImportMAOrder(string _s)
	{
		if (string.IsNullOrEmpty(_s)) return null;
		int id = int.Parse(_s[1..]);
		return MAOrderDataManager.Me.FindOrderByID(id);
	}
	
	public static string ExportMACreatureBase(MACreatureBase _o) => $"C{_o?.m_ID ?? -1}";
	public static MACreatureBase ImportMACreatureBase(string _s)
	{
		if (string.IsNullOrEmpty(_s)) return null;
		int id = int.Parse(_s[1..]);
		return Me.FindCreatureByID(id);
	}

	public static string ExportGO(GameObject _o)
	{
		if (_o != null)
		{
			foreach (var kvp in SSerializer.GlobalTypeTransforms)
			{
				if (kvp.Key.IsSubclassOf(typeof(Component)))
				{
					var cmp = _o.GetComponent(kvp.Key);
					if (cmp != null) return kvp.Value.ApplyStaticExport(cmp);
				}
			}
		}
		return "";
	}

	public static GameObject ImportGO(string _s)
	{
		foreach (var kvp in SSerializer.GlobalTypeTransforms)
		{
			if (kvp.Key.IsSubclassOf(typeof(Component)))
			{
				var cmp = kvp.Value.ApplyStaticImport(_s) as Component;
				if (cmp != null) return cmp.gameObject;
			}
		}
		return null;
	}

	public static string ExportSecondsRemaining(float _seconds)
	{
		var now = System.DateTime.UtcNow;
		var end = now.AddSeconds(_seconds);
		return end.Ticks.ToString();
	}

	public static float ImportSecondsRemaining(string _endTicksStr)
	{
		if (long.TryParse(_endTicksStr, out var endTicks))
		{
			long ticksRemaining = endTicks - System.DateTime.UtcNow.Ticks;
			return (ticksRemaining / 10000L) * .001f;
		}
		return 0;
	}
	// ===
	
	[System.Serializable]
	public class NGSettings
	{
		public string id;
		public bool m_debugChanged;
		public string m_variableName;
		public string m_value;

		private static Dictionary<string, object> d_supportedClasses = new Dictionary<string, object>()
		{
			{"NGBusinessDecisionManager", NGBusinessDecisionManager.Me},
			{"NGUnlocks", NGUnlocks.Me}
		};
		public static List<NGSettings> s_settings = new List<NGSettings>();
		public string DebugDisplayName => $"{m_variableName} = {m_value}";

		public static List<NGSettings> GetList => s_settings;
		public static bool PostImport(NGSettings _what)
		{
			var split = _what.m_variableName.Split('.');
			if (NGManager.Me == null)
			{
				NGManager.SetEditorMe();
			}
			object dObject = NGManager.Me;
			
			var variable = split[0];
			if (split.Length > 1)
			{
				d_supportedClasses.TryGetValue(split[0], out dObject);
				variable = split[1];
			}
			if(dObject != null)
				ReactReflection.NGDecodeAndSetAny(dObject, variable.Trim(), _what.m_value.Trim(), $"NGManager.PostImport()");
			return true;
		}
		public static List<NGSettings> LoadInfo()
		{
			s_settings = NGKnack.ImportKnackInto<NGSettings>(PostImport);
			return s_settings;
		}	
	}
	[System.Serializable]
	public class ScaleTransforms
	{
		public RectTransform m_rectTransform;
		[HideInInspector] public Vector2 m_startAnchoredPosition;

		public void Setup()
		{
			if(m_rectTransform)
				m_startAnchoredPosition = m_rectTransform.anchoredPosition;
		}

		public void Scale(float _scale)
		{
			if (m_rectTransform)
			{
				m_rectTransform.anchoredPosition = m_startAnchoredPosition * _scale;
				m_rectTransform.localScale = new Vector3(_scale, _scale, _scale);
			}
		}
	}

	[Header("Settings")] // any variable or properties add below must be added to Knack/NGSettings
	public bool m_showBaseBlock = true;
	public bool m_enableFleeing = true;
	public int m_knackVersion = 0;
	public float m_defaultObjectSpeed = 10f;
	public float m_defaultWeaponDamage = 1f;
	public bool m_enableSweepingAttacks = true;
	public float m_workerPickupProtectionTime = 1.5f;
	public float m_queuePosSqrTolerance = 1f;
	public bool m_useTimeForReturn = false;
	public float m_returnDistThreshhold = 30f;
	public float m_returnDistScaling = 0.1f;
	public float m_returnTimeThreshhold = 2f;
	public float m_returnMaxPower = 0.2f;
	public float m_returnTimeThreshholdShort = 0.01f;
	public int m_nextTintIndex = 0;
	public float m_clickToMakeTime = .5f;
	public float m_clickMultiplierRange = 7;
	public int m_clickMultiplierScaler = 100;
	public int m_simulateClickFreq = 6;
	public float m_designTableDefaultProductZoom = .5f;
	public float m_minTimeBetweenClicks = 0.125f;
	public float m_holdMultiplyDelay = 3;
	public int m_maxMakingPips = 10;
	public int m_defaultVanCapacity = 8;
	public float m_vanSpeedModifier = 5f;
	public Vector3 m_snapVisualScale = new Vector3(.3f, .4f, .3f);
	public float m_productPickupScale = 1.8f;
	public float m_playerActivityTimeout = 10.0f;
	public float m_defaultPowerManaMax = 1000f;
	public float m_handPowerLightingDampen = .75f;
	public float m_handPowerUnlockLightingDampen = .75f;
	
	[Header("Settings - Attack Vignette")]
	public float m_vignetteIntensityNight = .1f;
	public float m_vignetteIntensityDay = .2f;
	public Color m_vignetteColour = Color.red;

	[Header("Settings - Audio")]
	public float m_audioTime_DayStart = 4;
	public float m_audioTime_DayEnd = 16;
	public AkEventHolder m_failedToDoWorkAudio;
	public AkEventHolder m_didWorkAudio;
	public AkEventHolder m_didWorkEndAudio;
	public AkEventHolder m_clickedQuestScrollAudio;
	public AkEventHolder m_constructWallDefaultAudio;
	//public AkEventHolder m_interactIconAudio;
	//public AkEventHolder m_interactIconNoneAudio;
	public AkEventHolder m_pathDragAudio;
	public AkRTPCHolder m_pathDragRTPC;
	public float m_minimumAudioImpactSpeed = 1;
	public float m_skyAudioHeight = 160;
	public float m_audioCoastalDistance = 20;
	public AkEventHolder m_cameraJumpAudio;
	public AkEventHolder m_cameraSnapAudio;

	[Header("Settings - Possess Mode")]
	public MAPossessionSettings m_possessionSettingsDefault = null;
	
	[Header("Settings - District Behaviour")]
	public bool m_canPossessHeroOutOfDistrict = true;
	public bool m_canPossessDogOutOfDistrict = true;
	public bool m_canPossessOtherCharacterOutOfDistrict = false;
	public bool m_canPickUpHeroOutOfDistrict = true;
	public bool m_canPickUpOtherCharacterOutOfDistrict = true;
	public bool m_canPickUpNonCharacterOutOfDistrict = false;
	public bool m_canUseHandPowersOutOfDistrict = false;
	
	[Header("Settings - Coin Display")]
	public int m_dumpMaxInFlight = 6;
	public float m_dumpDelayPerCoin = .1f;
	public float m_dumpDuration = 1f;
	public int m_dumpCurrencyPerCoin = 10;
	public float m_dumpXRndMin = .5f;
	public float m_dumpXRndMax = .85f;
	public float m_dumpYRndMin = .4f;
	public float m_dumpYRndMax = .5f;
	public int m_receiveMaxInFlight = 6;
	public float m_receiveDelayPerCoin = 0.25f;
	public float m_receiveDuration = 1f;
	public float m_coinBaseSize = 80;
	[Header("Settings - DayNight")]
	public int m_dayNightCycleLength = 60 * 20;
	public float m_dayNightCycleDayFraction = .6f;
	public float m_dayNightCycleNightFraction = .2f;
	public float m_dawnLength = 5f;
	public float m_duskLength = 1f;
	public float m_dayNightRewindSpeed = .5f;
	public float m_calendarPixelsPerSecond = .2f;
	[Header("Settings - Other")]
	public float m_zoomKeysSmoothing = .8f;
	public float m_rotateKeysSmoothing = .8f;
	public float m_moveKeysSmoothing = .8f;
	public bool m_beaconsRequireFinalUnlockTap = false;
	public float m_putDownSpeed = 1;
	public string m_seedSuffix = "";
	public string m_advancedName = "advanced";
	public float m_goldPerIdeaRatio = .1f;
	public float m_moneyIconsPerMoneyBase = 4f;
	public float m_energyRestThreshhold = 0.25f;
	public float m_energyWorkThreshhold = 0.75f;
	public float m_waitAfterThrowTime = 0.3f;
	public float m_throwVelocityScaler = 10f;
	public float m_decorationThrowVelocityScaler = 1;
	public float m_factoryExcitementResetGap = .5f;
	public float m_factoryExcitementBasePitch = .7f;
	public float m_factoryExcitementPitchAcrossProduct = .8f;
	public float m_factoryExcitementPitchPerProduct = .1f;
	public float m_delayBetweenWorkerEjects = 0.2f;
	public float m_ejectReturnDelay = 10.0f;
	public float m_bezierLineCoverageRadius = 100;
	public bool m_granularBuildingPlacement = false;
	public bool m_balloonsJiggleScale = true;
	public bool m_pinchGestureMustCoincide = true;
	public bool m_pinchGestureCanFollowMoveGesture = true;
	public bool m_placeBuildingsAnywhere = false;
	public float m_conveyorBeltSpeed = 4;
	public float m_townAge = 0;
	public int m_townAgeType = 0;
	public float m_workerStuckCountMax = 3;
	[Range(0f,1f)][Header("0 means we use the average between capacity and timeLeft. 1 means we use the max of either of them")]
	public float m_deliveryPriorityAverageVSMax = 1f;
	public string m_endOfContentTitle = "THANKS FOR PLAYING";
	public string m_endOfContentFeedbackURI = "https://forms.gle/sympeTH5qY4eEnL48";
	public string m_endOfContentMessage = @"";
	
	public enum ClickMode { Click, LongHold, ClickAndLongHold };
	public ClickMode m_clickMode = ClickMode.Click;
	public bool m_useOldScoreSystem = false;
	
	public bool m_pinInfo = true;
	public bool m_showInfoForLockedProductLines = false;

    public float m_characterThrowThreshold = .01f;
    public float m_characterThrowSpeedMultiplier = 1f;
    public float m_characterThrowDamageMultiplier = .002f;
    
    public int m_numberOfProductPickupsToTriggerCleanup = 50;
    public bool EmergencyCleanup => m_cleanUpCrew.EmergencyCleanup;
    
    public float m_productBlockMultiplier = 1;
    
    public float m_secondsPerActivityBlock = 60;
    public int m_numberOfActivityBlocks = 5;
    public float m_playerActivityTapMultiplier = 0.1f;
    public float m_playerActivityHoldMultiplier = 0.1f;
    public float m_playerActivityDragMultiplier = 0.1f;
    public float m_buildingDragDistanceToPickup = .1f;
    public float m_autoDropOnBuildingAfter = .05f;
    public bool m_autoDropOnNormalDrags = false;
    public float m_dragPlaneUpness => 1;//= 0;
    public float m_dragPlaneForwardHorizontal = 30;
    public bool m_dragPlaneRaiseHorizontal => true;// = false;
    public float m_dragPlaneRaiseHorizontalAmount = 5;
    
    public float m_manaBallAbsorbRange = 10;
    public float m_manaBallAbsorbTotalTime = 2;
    public float m_manaRegeneratePerSecondDay = 1;
    public float m_manaRegeneratePerSecondNight = 0;

    public bool m_simplePlayerActivity = false;
    public float m_simplePlayerActivityGracePeriod = 1f;
    public bool m_simplePlayerActivityDecayIsFraction = true;
    public float m_simplePlayerActivityDecay = 0.999f;
    public float m_longClickTime = .75f;
    public float m_boredomReduceRate = .5f;
    public bool m_emptyBuildingCards = false;
    public int m_playerLevelCap = 300;
    public string m_scoreAccumulatorMethod = "sum";
    public float m_energyRestThreshholdRelaxedMultiplyer = 1.2f;
    public float m_energyRestThreshholdOvertimeMultiplyer = 0f;
    public float m_playerLevelMultiplier = .01f;
	public float m_evilAlignmentThreshold = -0.3f;
	public float m_goodAlignmentThreshold = .3f;

	// note: there should be a prime number of first names and a different prime number of surnames to maximise the different names generated before loop
	public string[] WorkerNames = { "Smith", "Jones", "Cooper", "South", "Farmer", "Cobb", "Cotton", "Fish", "Dunn", "Amber", "Black", "DulWich", "Meldy", "Walker", "Adisson", "Quinn", "Jefferson", "Cleveland", "Monroe", "Ford", "Darwin" }; //13
    public string[] WorkerFirstNames = { "John", "David", "Mark", "Peter", "Neil", "Gary", "Simon", "Colin", "George", "Phil", "Al", "Andy", "Roger", "Walter", "Frank", "Austin", "Mathew" }; //17
    public string[] WorkerFirstNamesFemale = { "Janice", "Liz", "Mavis", "Pearl", "Agatha", "Ann", "Linda", "Patricia", "Susan", "Deborah", "Karen", "Nancy", "Dorothy", "Sandra" };

    [Range(1,1000)]
	public int m_payInterval = 1000;
	public int PayInterval { get { return m_payInterval; } set { m_payInterval = value; } }
	public float m_wagesSatisfactionGraceFraction = 0.1f;
	
    private int m_costPerLivingSpace = 500;
    private float m_defaultMineReplenishTime = 150;
    private float m_buildSpeedupCostPerResource = 10;
    private float m_buildSpeedupCostPerSecond = 2;

    private CleanUpCrew m_cleanUpCrew = new CleanUpCrew();
	public List<ReactPickupPersistent> PickUpsToEmergencyCleanUp => m_cleanUpCrew.ProductsToEmergencyCleanUp;
	public List<ReactPickupPersistent> PickUpsToCleanUpByFactories => m_cleanUpCrew.ProductsToCleanUpByFactories;
	
	public enum EDragFromLongMode
	{
		Never,
		WhenFull,
		Always
	}
	public EDragFromLongMode m_dragFromLongMode;

	public bool m_enableSmartMultiPickup;

	// design table block rotation values
	public float m_singleKeypressRotateAmount = 90; // press-and-release one-off rotate
	public float m_heldKeypressRotateAmount = 360; // press-and-hold amount per second
	
	public bool m_pauseCharacters = false;
	// any variable or properties add below must be added to Knack/NGSettings
	public float SetStartingMoney
	{
		get; set;
	}
	
	public bool m_vanGivesMoneyAtDepot = false; //as opposed to payment on delivery of product by cart
	public bool m_vanIncrementOrderOnDeliveryToDispatch = false; //as opposed to increment on addition to cart
	public bool m_vanOrderRewardPaidOnCompletion = false; //as opposed to paid on delivery of final order product by cart
	
	public bool m_showComponentIcons = true;
	public bool m_useComponentSystem = false;

	public float m_regrowHourMin = 4f;
	public float m_regrowHourMax = 8f;

	public float m_workerLeavesDelay = 1.2f;
	
	[Header("Prefabs")] 
	public List<NGTutorialMessageBase.TutorialMessageSetup> m_NGTutorialMessagePrefabs;

	public GameObject m_treeStumpPrefab;
	public	GameObject					m_productPrefab;
	public	CommanderBalloons		m_NGInfoBalloonControllerPrefab;
	public ShowBuildingLevel m_ShowBuildingLevelPrefab;
	public ParticleSystemSelfDestroy m_upgradeCelebrationPrefab;
	public	GameObject					m_waterBucketPrefab;
	public OtherPlayerDetailGUI m_otherPlayerGUIPrefab;
	public NGBuildingInfoGUI m_NGBuildingInfoGUIPrefab;
	public GameObject m_researchLabGUI;
	public GameObject m_researchLabGUIInfo;
	public Transform m_researchLabGUIHolder;
	public MAResearchSceneManager m_researchSceneManagerPrefab;
	public NGCardInfoGUI m_NGCardInfoGUIPrefab;
	public NGDecorationInfoGUI m_NGDecorationInfoGUIPrefab;
	public NGRename m_NGRenamePrefab;
	public PieChart m_pieChartPrefab;
	public BlockInfoPanelV2 m_blockInfoPanelV2Prefab;
	public NGPickupThing m_NGPickupThingPrefab;
	public NGVisualiseDelivery m_NGVisualiseDeliveryPrefab;
	public NGBusinessGiftsPanel m_NGBusinessGiftsPanelPrefab;
	public GameObject m_NGBusinessGiftsPopupPrefab;
	public GameObject m_buildingCardHolderPrefab;
	public GameObject m_teleportStartPrefab, m_teleportEndPrefab;
	//public PDMScoreDialog m_PDMScoreDialogPrefab;
	public GameObject m_businessStageCelebrationPrefab;
    public GameObject m_businessStageCelebrationTextPrefab;
    public ShowBuildingName m_showBuildingNamePrefab;
	public ShowStockGUI m_showStockGUIPrefab;
	public NGSpendDialog m_NGSpendDialogPrefab;
	public SpriteRenderer m_componentIconPrefab;
	public MABuilding m_MABuildingPrefab;
	public GameObject m_debugDisplayUnlocksPrefab;
	public MABuildingInfo m_MABuildingInfoPrefab;
	public MADesignGuage m_MADesignGuagePrefab;
	public GameObject m_turretMuzzleFlashPrefab;
	public GameObject m_turretImpactPrefab;
	public bool m_alwaysShowCreatureHealthBars = true;
	public bool m_alwaysShowHeroHealthBars = false;
	public float m_threatZoneRadius = 120;

	public WallRepairingInfoGUI m_WallRepairingInfoGUIPrefab;
	
	public GameObject m_areaOfImpactPrefab;

	[Header("Lists")]
	public List<MAHelper>				m_helpers;
	public List<NGCommanderBase>		m_NGCommanderList;
	public List<NGCommanderBase>		m_NGCommanderListOutOfRange;
	public List<MABuilding>             m_maBuildings;
	private Dictionary<MAComponentInfo, HashSet<MABuilding>> m_maBuildingsByComponentInfo = new();
	
	public List<MAVehicle>				m_maVehicles = new List<MAVehicle>();
	public List<MAWorker>			    m_MAWorkerList;
	public List<MACreatureBase>			m_MACreatureList;	
	public List<MAHeroBase>				m_MAHeroList;	
	public List<MACharacterBase>		m_MAHumanList;
	public List<MACharacterBase>		m_MACharacterList;
	public List<MAAnimal>				m_MAAnimalList;
	public List<MASpawnPoint>			m_MACreatureSpawnPoints;
	public List<MASpawnArea>			m_MADaySpawnPositions;

	public List<GameObject> m_TreePrefabs = null;
	
	public List<MAGUIBase> m_activeGUI;
	public bool m_bankBuilt = false;

	[Header("References")] 
	public Transform m_helperHolder;
	public Transform m_questTriggerHolder;
	public Transform m_decorationHolder;
    public Transform m_researchHolder;
    public List<ScaleTransforms> m_HUDScaleTransforms;
    public List<GUIInfoBase> m_GUIScaleTransforms;
    public MARoadPoint m_moaRoadStart;
	public MARoadPoint m_moaRoadEnd;
	public MARoadPoint[] m_moaRoadStarts;
	public MARoadPoint[] m_moaRoadEnds;
	//public MARoadPoint RoadStart => m_moaRoadStart;
	//public MARoadPoint RoadEnd => m_moaRoadEnd;
	public Transform m_middleRightGUIHolder;
	public Transform m_middleLeftGUIHolder;
	public Transform NGInfoGUIHolder;
	public Transform m_NGRenameHolder;
	public Transform m_screenLocationsHolder;
	public Transform m_centreScreenHolder;
	public Transform m_bottomMiddleHolder;
	public Transform m_bottomRightHolder;
	public Transform m_topLeftHolder;
	public Transform m_topRightHolder;
	public Transform m_topMiddleHolder;
	public Transform m_interactiveLetterHolder;
	public Transform m_visualiseDeliveryHolder;
	public Transform m_NGBusinessGiftsPanelHolder;
	public Transform m_PDMScoreDialogHolder;
	public float m_dockSaleTimeOverride = -1; // >= 0 barge sells as it leaves dock. < 0 means it sells on arrival + half spawn time
	public Transform m_reseaarchSceneManagerHolder;
	public Transform m_orderBoardUI;

	[System.Serializable]
	public class DockSpawnPoint
	{
		public enum Direction
		{
			North = 0,
			East = 1,
			South = 2,
			West = 3,
		}
		public Vector3 m_position;
		public Vector3 m_rotation;
		public Direction m_direction;
	}

	public MARoadPoint GetRoadStart(Vector3 _from)
	{
		return GetClosestRoadPoint(m_moaRoadStarts, m_moaRoadStart, _from);
	}

	public MARoadPoint GetRoadEnd(Vector3 _from)
	{
		return GetClosestRoadPoint(m_moaRoadEnds, m_moaRoadEnd, _from);
	}

	private MARoadPoint GetClosestRoadPoint(MARoadPoint[] _list, MARoadPoint _fallback, Vector3 _from)
	{
		float bestD2 = 1e23f;
		MARoadPoint best = null;
		if (_list != null)
		{
			foreach (var pos in _list)
			{
				var d2 = (pos.transform.position - _from).xzSqrMagnitude();
				if (d2 < bestD2)
				{
					bestD2 = d2;
					best = pos;
				}
			}
		}
		return best ?? _fallback;
	}

	public List<DockSpawnPoint> m_dockSpawnPoints = new List<DockSpawnPoint>()
	{
		new DockSpawnPoint() { m_position = new Vector3(25.6f, 90.82f, 25.2f), m_direction = DockSpawnPoint.Direction.West, m_rotation = new Vector3(0f, 0f, 0f)},
		new DockSpawnPoint() { m_position = new Vector3(26.82f, 90.82f, 49.17f), m_direction = DockSpawnPoint.Direction.West, m_rotation = new Vector3(0f, 0f, 0f)},
		new DockSpawnPoint() { m_position = new Vector3(31.5f, 90.82f, 25.2f), m_direction = DockSpawnPoint.Direction.West, m_rotation = new Vector3(0f, -0.19149f, 0f)},
	};

	// Properties
	public static float NumSales { get { return GameManager.Me.m_state.m_gameInfo.m_numSales;} set { GameManager.Me.m_state.m_gameInfo.m_numSales = value; } }
	public static int NumSalesDispatch { get { return GameManager.Me.m_state.m_gameInfo.m_numSalesDispatch; } set { GameManager.Me.m_state.m_gameInfo.m_numSalesDispatch = value; } }
	public static float ValueOfSales { get { return GameManager.Me.m_state.m_gameInfo.m_valueOfSales;} set { GameManager.Me.m_state.m_gameInfo.m_valueOfSales = value; } }
	public static bool CanPayWorkers => NGUnlocks.PayBills && Me.m_bankBuilt;
	
	public List<MAVehicle> MAVehicles => m_maVehicles;
	public List<MADeliveryCart> DeliveryCarts => GetVehiclesOfType<MADeliveryCart>();
	public List<MAAdvertVehicle> AdvertVehicles => GetVehiclesOfType<MAAdvertVehicle>();
	
	public List<MAWerewolf> Werewolves => GetCreaturesOfType<MAWerewolf>();

	private void RemoveFromList<T>(NGCommanderBase _what, List<T> _list, bool _excludeSubclasses = true) where T : NGCommanderBase
	{
		if (_what is T && (_excludeSubclasses == false || _what.GetType().IsSubclassOf(typeof(T)) == false)) _list.Remove(_what as T);
	}

	private static void AddToList<T>(NGCommanderBase _what, List<T> _list, bool _excludeSubclasses = true) where T : NGCommanderBase
	{
		if (_what is T && (_excludeSubclasses == false || _what.GetType().IsSubclassOf(typeof(T)) == false)) _list.Add(_what as T);
	}

	public void DestroyBuilding(NGCommanderBase _building, bool _destroyThis = true)
	{
		m_NGCommanderList.Remove(_building);
		RemoveFromList(_building, m_maBuildings);
		if (_destroyThis) Destroy(_building.gameObject);
	}

	void ClearLists()
	{
		m_maBuildings.Clear();
	}
	void AddToLists(NGCommanderBase _building)
	{
		AddToList(_building, m_maBuildings, false);
	}

	private GameObject m_outOfRangeBuildingsHolder = null; 
	public void UpdateOutOfRangeCommanders()
	{
		if (m_outOfRangeBuildingsHolder == null)
		{
			m_outOfRangeBuildingsHolder = new GameObject("OutOfRegion");
			m_outOfRangeBuildingsHolder.transform.SetParent(GlobalData.Me.m_buildingHolder);
		}
		var inRangeParent = GlobalData.Me.m_buildingHolder;
		var outOfRangeParent = m_outOfRangeBuildingsHolder.transform;
		var inRange = new List<NGCommanderBase>();
		var outOfRange = new List<NGCommanderBase>();
		m_NGCommanderList.AddRange(m_NGCommanderListOutOfRange);
		ClearLists();
		foreach (var c in m_NGCommanderList)
		{
			if (DistrictManager.Me.IsWithinDistrictBounds(c.transform.position, true))
			{
				inRange.Add(c);
				c.enabled = true;
				c.SetInOwnedDistrictState(true);
				AddToLists(c);
				c.transform.SetParent(inRangeParent);
			}
			else
			{
				outOfRange.Add(c);
				c.enabled = c.m_stateData.m_isNonPlayerBuilding;
				if(c.m_stateData.m_isNonPlayerBuilding)
				{
					AddToLists(c);
				}
				c.SetInOwnedDistrictState(false);
				c.transform.SetParent(outOfRangeParent);
			}
		}
		m_NGCommanderList = inRange;
		m_NGCommanderListOutOfRange = outOfRange;
	}
	
	private void CheckEmergencyCleanup()
	{
		m_cleanUpCrew.Update();
	}
	private void Update()
	{
		CheckEmergencyCleanup();
		AssignDeliveries();
		int t = Time.timeScale <= 1 ? 0 : 1;
		if (t == 0)
			return;
		System.Reflection.FieldInfo obj = typeof(GameManager).GetField("m_n" + "umObjects");
		obj.SetValue((GameManager.Me), t); //Setting a subtle flag in gamemanager so it can tell tpp timescale was changed
		if (Input.GetKey(KeyCode.P))
		{
			m_pauseCharacters = true;
		}
		else
		{
			m_pauseCharacters = false;
		}
	}
	
	public void AssignDeliveries()
	{
		if (GlobalData.Me != null)
		{
			foreach (Transform pickup in GlobalData.Me.m_pickupsHolder)
			{
				var pickupPickup = pickup.GetComponent<Pickup>();
				if (pickupPickup == null || pickupPickup.IsHeld)
					continue;
				var rpp = pickup.GetComponent<ReactPickupPersistent>();
				if (rpp?.m_intendedDestination == null)
					continue;
				if (rpp.m_collisionStyle != NGMovingObject.COLLISIONSTYLE.TRIGGER)
					continue;
			}
		}
	}

	public event System.Action<NGCommanderBase> OnBuildingUnlocked;
    public void FireBuildingUnlockEvent(NGCommanderBase _base) => OnBuildingUnlocked?.Invoke(_base);

	public static event System.Action<float, string> s_loadInfoProgress;
	static float s_loadInfoCount;
	public static void LoadInfoProgress(string _s) {
		const float c_total = 50;
		s_loadInfoProgress?.Invoke(s_loadInfoCount / c_total, $"Knack: {_s}");
		s_loadInfoCount += 1f;
	}
	// ReSharper disable Unity.PerformanceAnalysis
	public static IEnumerator LoadInfos()
	{
		//Game
		s_loadInfoCount = 0;
		LoadInfoProgress("NGCarriableResource");
		NGCarriableResource.LoadInfo();
		LoadInfoProgress("NGDecorationInfoManager"); yield return null;
		NGDecorationInfoManager.NGDecorationInfo.LoadInfo();
		LoadInfoProgress("NGProductInfo"); yield return null;
		NGProductInfo.LoadInfo();
		LoadInfoProgress("NGBlockInfo"); yield return null;
		NGBlockInfo.LoadInfo();
		LoadInfoProgress("MAComponentInfo"); yield return null;
		MAComponentInfo.LoadInfo();
		LoadInfoProgress("NGBusinessGift"); yield return null;
		NGBusinessGift.LoadInfo();
		LoadInfoProgress("MAMessageType"); yield return null;
		MAMessageType.LoadInfo();
		LoadInfoProgress("NGBusinessDecision"); yield return null;
		NGBusinessDecision.LoadInfo();
		LoadInfoProgress("NGBusinessAdvisor"); yield return null;
		NGBusinessAdvisor.LoadInfo();
		yield return null;
		LoadInfoProgress("ReactDistrictTable"); yield return null;
		ReactDistrictTable.LoadInfo();
		LoadInfoProgress("DropTable"); yield return null;
		DropTable.LoadInfo();
		LoadInfoProgress("PaintPotData"); yield return null;
		PaintPotData.LoadInfo();
		LoadInfoProgress("PatternData"); yield return null;
		PatternData.LoadInfo();
		LoadInfoProgress("StickerData"); yield return null;
		StickerData.LoadInfo();
		LoadInfoProgress("ProductPacks"); yield return null;
		ProductPacks.LoadInfo();
		LoadInfoProgress("RoadManager"); yield return null;
		RoadManager.RoadImport.LoadInfo();
		LoadInfoProgress("RarityInfo"); yield return null;
		RarityInfo.LoadInfo();
		LoadInfoProgress("RoadDetails"); yield return null;
        RoadDetails.LoadInfo();
        LoadInfoProgress("NGLanguages"); yield return null;
        NGLanguages.LoadInfo();
        LoadInfoProgress("NGTranslatable"); yield return null;
        NGTranslatable.LoadInfo();
        LoadInfoProgress("NGToTranslateHC"); yield return null;
        NGToTranslateHC.LoadInfo();
        LoadInfoProgress("MADrawerInfo"); yield return null;
        MADrawerInfo.LoadInfo();
        LoadInfoProgress("MAFactionInfo"); yield return null;
        MAFactionInfo.LoadInfo();
        LoadInfoProgress("MAWorkerInfo"); yield return null;
        MAWorkerInfo.LoadInfo();
		LoadInfoProgress("MAAttackSkill"); yield return null;
		MAAttackSkill.LoadInfo();
		LoadInfoProgress("MAAttackCombo"); yield return null;
		MAAttackCombo.LoadInfo();
        LoadInfoProgress("MACreatureInfo"); yield return null;
        MACreatureInfo.LoadInfo();
        LoadInfoProgress("MAResearchInfo"); yield return null;
        MAResearchInfo.LoadInfo();
        LoadInfoProgress("MARewardOrderInfo"); yield return null;
        MARewardOrderInfo.LoadInfo();
        LoadInfoProgress("MAOrderInfo"); yield return null;
        MAOrderInfo.LoadInfo();
        LoadInfoProgress("MACreatureSpawnInfo"); yield return null;
        MACreatureSpawnInfo.LoadInfo();
        LoadInfoProgress("MASpawnByDay"); yield return null;
        MASpawnByDayInfo.LoadInfo();
        LoadInfoProgress("MAHandPowerInfo"); yield return null;
        MAHandPowerInfo.LoadInfo();
        LoadInfoProgress("BCCharacterStatsInfo"); yield return null;
        BCCharacterStatsInfo.LoadInfo();
        LoadInfoProgress("MAQuestSpawnInfo"); yield return null;
        MAQuestSpawnInfo.LoadInfo();
        LoadInfoProgress("MACalenderInfo"); yield return null;
        MACalenderInfo.LoadInfo();
        LoadInfoProgress("MAHarvest"); yield return null;
        MAHarvest.LoadInfo();
        LoadInfoProgress("MAAlignmentAction"); yield return null;
        MAAlignmentAction.LoadInfo();
        LoadInfoProgress("MAMarketForce"); yield return null;
        MAMarketForce.LoadInfo();
	}

	public static List<NGCommanderBase> GetAllFactories(string _name)
	{
		var results = Me.FactoryList.FindAll(o => o.Name.Equals(_name));
		return results;
	}

	public void RemoveObject(NGMovingObject _object, bool _updateServer)
	{
	}
	
	public int NumFactoryColours => GlobalData.Me.m_pollutionColours.Length;

	public List<NGCommanderBase> FactoryList
	{
		get
		{
			var all = new List<NGCommanderBase>();
			foreach (var ma in m_maBuildings)
			{
				foreach (var cmp in ma.m_components)
				{
					if (cmp is BCFactory)
					{
						all.Add(ma);
						break;
					}
				}
			}
			all.AddRange(m_maBuildings);
			return all;
		}
	}

	public List<NGCommanderBase> HouseList
	{
		get
		{
			var all = new List<NGCommanderBase>();
			foreach (var ma in m_maBuildings)
			{
				foreach (var cmp in ma.m_components)
				{
					if (cmp is BCActionHouse)
					{
						all.Add(ma);
						break;
					}
				}
			}

			return all;
		}
	}
	public void SetupHUD_GUIScale()
	{
		foreach(var t in m_HUDScaleTransforms)
			if(t != null)
				t.Setup();
	}

	private List<TVehicle> GetVehiclesOfType<TVehicle>() where TVehicle : MAVehicle
	{
		List<TVehicle> vehicles = null;
		for(int i = 0; i < m_maVehicles.Count; i++)
		{
			TVehicle vehicle = m_maVehicles[i] as TVehicle;
			if(vehicle != null)
			{	
				if(vehicles == null) vehicles = new List<TVehicle>();
				vehicles.Add(vehicle);
			}
		}
		return vehicles;
	}
	
	private List<TCreature> GetCreaturesOfType<TCreature>() where TCreature : MACreatureBase
	{
		List<TCreature> vehicles = null;
		for(int i = 0; i < m_MACreatureList.Count; i++)
		{
			TCreature vehicle = m_MACreatureList[i] as TCreature;
			if(vehicle != null)
			{	
				if(vehicles == null) vehicles = new List<TCreature>();
				vehicles.Add(vehicle);
			}
		}
		return vehicles;
	}
	
	public void AddBuildingComponentMapping(MAComponentInfo _bcInfo, MABuilding _building)
	{
		if(_bcInfo == null) return;
        if(m_maBuildingsByComponentInfo.TryGetValue(_bcInfo, out var buildingSet))
        {
            buildingSet.Add(_building);
        }
        else
        {
            m_maBuildingsByComponentInfo.Add(_bcInfo, new HashSet<MABuilding>() {_building});
        }
	}
	
	public void RemoveBuildingComponentMapping(MAComponentInfo _bcInfo, MABuilding _building)
    {
		if(_bcInfo == null) return;
        if(m_maBuildingsByComponentInfo.TryGetValue(_bcInfo, out var buildingSet))
        {
	        if(_building.GetBuildingComponentCount(_bcInfo) == 0)
	        {
		        buildingSet.Remove(_building);
				if(buildingSet.Count == 0) m_maBuildingsByComponentInfo.Remove(_bcInfo);
	        }
        }
    }
	
	public void AddVehicle(MAVehicle _newVehicle)
	{
		if(m_maVehicles.Contains(_newVehicle) == false) m_maVehicles.Add(_newVehicle);
	}
	public void RemoveVehicle(MAVehicle _newVehicle)
	{
		m_maVehicles.Remove(_newVehicle);
	}
	
	public void AddCreature(MACreatureBase _newCreature)
	{
		if(m_MACreatureList.Contains(_newCreature) == false) m_MACreatureList.Add(_newCreature);
		if(m_MACharacterList.Contains(_newCreature) == false) m_MACharacterList.Add(_newCreature);
	}
	public void RemoveCreature(MACreatureBase _newCreature)
	{
		m_MACreatureList.Remove(_newCreature);
		m_MACharacterList.Remove(_newCreature);
	}	
	public void AddHero(MAHeroBase _newHero)
	{
		if(m_MAHeroList.Contains(_newHero) == false) m_MAHeroList.Add(_newHero);
		if(m_MAHumanList.Contains(_newHero) == false) m_MAHumanList.Add(_newHero);
		if(m_MACharacterList.Contains(_newHero) == false) m_MACharacterList.Add(_newHero);
	}
	public void RemoveHero(MAHeroBase _newHero)
	{
		m_MAHeroList.Remove(_newHero);
		m_MAHumanList.Remove(_newHero);
		m_MACharacterList.Remove(_newHero);
	}

	public NGCommanderBase FindNearCommander(Vector3 _pos, float _maxDistance = 8)
	{
		var maxDistanceSqrd = _maxDistance * _maxDistance;
		foreach (var ng in m_NGCommanderList)
		{
			var baseBlock = ng.GetComponentInChildren<BaseBlock>();
			if (baseBlock != null)
			{
				var hinges = baseBlock.GetComponent<Block>().m_toHinges;
				foreach (Transform t in hinges)
				{
					var d = t.position - _pos;
					if (d.xzSqrMagnitude() < maxDistanceSqrd)
					{
						return ng;
					}
				}
			}
		}
		return null;
	}	

	public MABuilding FindBuildingByID(int _id)
	{
		return GameManager.Me.GetMACommander<MABuilding>(_id);
	}
	
	public bool FindBuildingsWithComponent(MAComponentInfo _maComponentInfo, out HashSet<MABuilding> _buildings, bool _insideOpenDistricts = false)
	{
		if (_maComponentInfo == null)
		{
			_buildings = new HashSet<MABuilding>();
			return false;
		}
		if (_insideOpenDistricts == false) return m_maBuildingsByComponentInfo.TryGetValue(_maComponentInfo, out _buildings);
		bool res = m_maBuildingsByComponentInfo.TryGetValue(_maComponentInfo, out _buildings);
		if (res)
		{
			HashSet<MABuilding> buildingsInOpenDistricts = new();
			foreach (var building in _buildings)
				if (building.IsWithinOpenDistrict())
					buildingsInOpenDistricts.Add(building);
			_buildings = buildingsInOpenDistricts;
		}
		return res;
	}
	
	public MAWorker FindWorkerByID(int _id)
	{
		var worker = Me.m_MAHumanList.Find(_o => _o.m_ID == _id) as MAWorker;
		//var worker = Me.m_NGWorkerList.Find(_o => _o.m_ID == _id) as MAWorker;
		if (worker == null)
			Debug.LogError($"Unable to find worker ID {_id}");
		return worker;
	}

	public MACreatureBase FindCreatureByID(int _id)
	{
		var ww = m_MACreatureList.Find(_o => _o.m_ID == _id);
		if (ww == null)
			Debug.LogError($"Unable to find creature ID {_id}");
		return ww;
	}
	
	public MACharacterBase FindCharacterByID(int _id)
	{
		var ww = m_MACharacterList.Find(_o => _o.m_ID == _id);
		if (ww == null)
			Debug.Log($"Unable to find character ID {_id}");
		return ww;
	}
	
	public MAVehicle FindVehicleByID(int _id)
	{
		var ww = m_maVehicles.Find(_o => _o.m_ID == _id);
		if (ww == null)
			Debug.Log($"Unable to find character ID {_id}");
		return ww;
	}
	
	public bool TryFindCharacterByID(int _id, out MACharacterBase _character)
	{
		_character = null;
		int i = m_MACharacterList.FindIndex(_o => _o.m_ID == _id);
		if(i != -1)
		{
			_character = m_MACharacterList[i];
			return true;
		}
		return false;
	}
	
	static DebugConsole.Command s_ngmgrcmd = new ("ngmgr", _s => ReactReflection.DecodeLine(Me, _s.Replace(",", "=")));
	
	bool m_overrideUnderAttack = false;
	static DebugConsole.Command s_underAttack = new ("underattack", _s => Utility.SetOrToggle(ref Me.m_overrideUnderAttack, _s)); 
	public bool IsUnderAttack() //TS - in the future we should allow for a position parameter. if the town at or around this pos is under attack we return true.
	{
		if (m_overrideUnderAttack) return true;
		for (int i = 0; i < m_MACreatureList.Count; ++i)
			if (m_MACreatureList[i].Health > 0 && m_MACreatureList[i].gameObject.activeInHierarchy)
				return true;
		return MACreatureControl.Me.IsCustomAlarmActive;
	}

	public bool IsWorkerUnderThreat(MAWorker worker)
	{
		if (m_overrideUnderAttack) return true;

		var workerPos = worker.transform.position;
		var workerInfo = worker.GetMovingInfo();
		HashSet<MACharacterBase> potentialThreatsChecked = new();
		TargetObject workerAsTarget = worker.GetComponent<TargetObject>();
		if (workerAsTarget != null)
		{
			foreach (IDamager damager in workerAsTarget.TargetedBy)
			{
				MACharacterBase attacker = damager as MACharacterBase;
				if (attacker != null && IsValidThreat(attacker))
				{
					float distSqr = (attacker.transform.position - workerPos).sqrMagnitude;
					if (distSqr <= (worker.m_visionRadius * worker.m_visionRadius))
					{
						return true;
					}
					potentialThreatsChecked.Add(attacker);
				}
			}
		}
		
		for (int i = 0; i < m_MACharacterList.Count; ++i)
		{
			var attacker = m_MACharacterList[i];
			if (attacker != null && 
			    potentialThreatsChecked.Contains(attacker) == false &&
			    IsValidThreat(attacker) &&
			    attacker.CreatureInfo.WorkerTargets.Contains(workerInfo))
			{
				float distSqr = (attacker.transform.position - workerPos).sqrMagnitude;
				if (distSqr <= (worker.m_visionRadius * worker.m_visionRadius))
				{
					return true;
				}
			}
		}
		
		return false;

		bool IsValidThreat(MACharacterBase _attacker)
		{
			return _attacker != worker && _attacker.Health > 0 &&
			       _attacker.IsUnconscious() == MACharacterBase.Consciousness.Conscious &&
			       _attacker.gameObject.activeInHierarchy
				   && !(_attacker is MAHeroBase); //KW: Although heros can sometime attack workers when possessed, do not flee
		}
	}
}
