using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGLegacyBase : MonoBehaviour {
    [SerializeField] private string m_name;
    public virtual void DestroyMe() 
    {
        if (gameObject != null) 
            Destroy(gameObject); 
    }
    virtual public KeyboardShortcutManager.EShortcutType KeyboardShortcut => KeyboardShortcutManager.EShortcutType.HeldObjects;
    public virtual string Name { get => m_name; set => m_name = value; }
}
