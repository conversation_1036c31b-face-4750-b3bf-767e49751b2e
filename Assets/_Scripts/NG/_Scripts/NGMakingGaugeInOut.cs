#if false
using System.Collections;
using System.Collections.Generic;
using System.Net.Mime;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class NGMakingGaugeInOut : NGMakingGaugeBase
{

	public Vector2 m_touchOffset;

	public Transform m_mecanicsHolder;

	public TMP_Text m_message;
	public Image m_messageBackground;
	public TMP_Text m_title;
	public TMP_Text m_level;
	public Transform m_tapCounterHolder;
	public Image m_inputProductImage; 
	public Image m_inputImageAlpha;
	public Image m_machine;
	public Image m_outputProductImage;
	public Image m_outputImageAlpha;
	//public PDMMakingGaugePip m_pipPrefab;
	public TMP_Text m_inputStock;
	public TMP_Text m_outputStock;
	public TMP_Text m_debugProductScore;
	public Image m_InputAnimImage;
	public GameObject m_tipHolder;
	public TMP_Text m_tip;
	public bool m_showDebug;
	public float m_minStockFont = 16;
	public float m_maxStockFont = 32;
	public TMP_Text m_excessOutput;
	public Image m_tapBar;
	public bool m_isUsingBar;
	public Image m_background;
	public Image m_tipBackground;

    private bool m_isMine;
    private bool m_isDock;
    private bool m_isBank;
	private bool m_isDispatch;
	private bool m_isRoadSite;

    // the current gauge fill is .09 - .091
    private float ClickPower => 0f;
	private int NumTapsToMake => (int)(1f / ClickPower * 0);

	private Animator m_anim;

	override public void ShowNew(Type _type, string _message, bool _showDetails = false)
	{
		base.ShowNew(_type,_message, _showDetails);
		SetPosition();
		//	m_mecanicsHolder.gameObject.SetActive(false);
		m_message.gameObject.SetActive(true);
		m_message.text = _message;
		if(m_messageBackground)
			m_messageBackground.gameObject.SetActive(true);
	}

	override protected void Update()
	{
		base.Update();
		/*if (m_building.IsMaking == false && NGMakingGaugeManager.Me.m_isStickyGuage == false)
		{
			if (m_destroying == false)
				StartDestroy();
			return;
		}
		var combo = m_building.CalculateProduceScoreMultiplier(true);
		if(m_combo)
			m_combo.text = $"x{combo:n1}";

		var productScore = m_building.ToProductScore;
		m_tip.text = GetTipText();
		m_tipHolder.SetActive(!Pickup.IsLongHold);
  

        if (m_isUsingBar)
		{
			float barFill = Mathf.Min(1f, productScore);
			m_tapBar.fillAmount = barFill;
//			if (m_building.Contribution != null && m_tipHolder.activeSelf == false &&
//			    m_building.Contribution.HoldClickContribution <= .1f)
		}
		else
		{
			// var pips = m_tapCounterHolder.GetComponentsInChildren<PDMMakingGaugePip>();
			//
			// int numPipsToActivate = (int)(productScore / (ClickPower / m_building.DesignTapsToMake));
			// for (int i = pips.Length - 1; i >= 0; i--)
			// {
			// 	pips[i].Show(i < numPipsToActivate);
			// }
		}
		if (m_building.InputsAre.Items.Count > 0)
		{
			m_inputStock.text = $"{m_building.InputsAre.GetLowestStock()} / {m_building.MaxInputs:F0}";
		}
        else
        {
			m_inputStock.text = "";
		}
		var ratio = Mathf.Clamp(m_building.InputsAre.GetLowestStock() / m_building.MaxInputs, 0f, 1f);
		if (!m_isMine && !m_isBank)
		{
			m_inputStock.fontSize = Mathf.Lerp(m_minStockFont, m_maxStockFont, ratio);
		}
		
		m_outputStock.text = $"{m_building.NumOutputs:F0} / {m_building.MaxOutputs}";
		ratio = Mathf.Clamp(m_building.NumOutputs / m_building.MaxOutputs, 0f, 1f);
		m_outputStock.fontSize = Mathf.Lerp(m_minStockFont, m_maxStockFont, ratio);

		if (m_isMine || m_isBank)
		{
			m_inputProductImage.fillAmount = (float)m_building.InputsAre.GetLowestStock() / m_building.MaxInputs;
		}
		else
			m_inputProductImage.fillAmount = 1f - productScore;
		m_outputProductImage.fillAmount = productScore;
		m_debugProductScore.gameObject.SetActive(m_showDebug);
		m_debugProductScore.text = $"{ClickPower:F2} / {productScore:F2}";
		if(productScore > 1f - ClickPower && m_anim)
			m_anim.SetBool("InputAnim", true);*/
	}
	

	public float m_maxProgressHeight = 20 * 7;
	override public void Activate(NGCommanderBase _building, int _inputID)
	{
		base.Activate(_building, _inputID);
		if(m_title)
			m_title.text = m_building.m_title;
		var level = ClickPower / 0.1f;
		if(m_level)
			m_level.text = $"Level {level:F0}";
		var phrt = m_tapCounterHolder.GetComponent<RectTransform>();
		var pos = m_building.LocalHighestPoint;
		transform.position = new Vector3( transform.position.x, pos.y, transform.position.z);
		var numTaps = (1 / ClickPower);
		m_tapCounterHolder.DestroyChildren();
		m_excessOutput.gameObject.SetActive(false);
		if (numTaps >= 1f)
		{
			if (numTaps <= NGManager.Me.m_maxMakingPips)
			{
				m_tapCounterHolder.gameObject.SetActive(true);

				var width = 0f;
				// for (int i = 0; i < (int)numTaps; i++)
				// {
				// 	var pip = PDMMakingGaugePip.Create(m_pipPrefab, m_tapCounterHolder);
				// 	var h = pip.GetComponent<RectTransform>();
				// 	width += h.sizeDelta.x * h.localScale.x;
				// }

				//if (width > m_maxProgressHeight)
				//	phrt.localScale = new Vector3(m_maxProgressHeight / width, phrt.localScale.y, phrt.localScale.z);
				//			phrt.localScale = new Vector3(phrt.localScale.x, m_maxProgressHeight / width, phrt.localScale.z);
			}
			else
			{
				m_isUsingBar = true;
				m_tapBar.gameObject.SetActive(true);
				m_tapBar.fillAmount = m_building.ToProductScore;
			}
		}
		else
		{
			m_tapCounterHolder.gameObject.SetActive(false);
			m_excessOutput.gameObject.SetActive(true);
			m_excessOutput.text = $"x {numTaps:F1}";
		}

        /*m_isMine = _building is NGMine;
        m_isDock = _building is NGDock;
        m_isBank = _building is NGBank;
        m_isDispatch = _building is NGDispatch;
        m_isRoadSite = _building is NGRoadBuildingSite;*/

        if (m_isMine || m_isDock || m_isDispatch)
		{
			m_inputProductImage.sprite = NGMakingGaugeManager.Me.GetMineSprite(m_building);
		}
		else
		{
			//GameManager.Me.GetCarriableResourceSprite(_building.InputsAre.GetLowestStockItem(), _building, _s => m_inputProductImage.sprite = _s);
		}
		m_inputImageAlpha.sprite = m_inputProductImage.sprite;
		if (m_isBank || m_isRoadSite)
		{
            m_outputProductImage.sprite = NGMakingGaugeManager.Me.GetMineSprite(m_building);
		}
		else
		{
			GameManager.Me.GetCarriableResourceSprite(NGCarriableResource.GetInfo("None"), _building, _s => m_outputProductImage.sprite = _s);
		}
		m_outputImageAlpha.sprite = m_outputProductImage.sprite;
		m_InputAnimImage.sprite = m_inputProductImage.sprite;
		SetPosition();
		m_anim = GetComponent<Animator>();
	}

}
#endif