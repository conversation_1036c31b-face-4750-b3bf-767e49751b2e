using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class HoldButton : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerUpHandler, IPointerClickHandler, IPointerDownHandler
{
	private bool m_pressed;
	private bool m_held;
	private float m_timer;
	private Image m_image;
    private Color m_tint;

	public float m_heldThreshold = 1.0f;
	public UnityEvent m_holdEvent;
	public UnityEvent m_pressEvent;

	void Start()
	{
		m_image = gameObject.GetComponent<Image>();
        if (m_image != null)
            m_tint = m_image.color;
    }

	void Update()
	{
		if (m_pressed && !m_held)
		{
			m_timer += Time.deltaTime;
			if (m_timer > m_heldThreshold)
			{
				m_holdEvent?.Invoke();
				m_pressed = false;
			}
		}
	}

	public void OnPointerUp(PointerEventData eventData)
	{
		m_pressed = false;
		if (m_timer > m_heldThreshold)
			m_held = true;

		if(m_image != null)
            m_image.color = m_tint;
    }

    public void OnPointerDown(PointerEventData eventData)
	{
		m_pressed = true;
		m_held = false;
		m_timer = 0f;

		if (m_image != null)
			m_image.color = m_tint *0.8f;
    
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
    }

    public void OnPointerClick(PointerEventData eventData)
	{
		if (!m_held)
			m_pressEvent.Invoke();
	}

	public void OnClick()
	{
		Debug.Log("Pressed!");
	}

	public void OnHeld()
	{
		Debug.Log("Held!");
	}
}
