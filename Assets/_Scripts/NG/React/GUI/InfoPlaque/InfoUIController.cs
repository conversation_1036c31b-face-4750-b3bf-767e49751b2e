using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using System;

public class InfoUIController : BaseBezierClosableUIController
{
	[SerializeField] private TextMeshProUGUI m_title;
	[SerializeField] private TextMesh<PERSON><PERSON>UGUI m_description;
	[SerializeField] private UIButton m_button;
	[SerializeField] private TextMeshP<PERSON>UGUI m_buttonText;
	[SerializeField] private UIImage m_image;
	[SerializeField] private GameObject m_imageHolder;
	private Action m_onClose;

	public static InfoUIController Open(string _title, string _desc, string _rarity, Sprite _sprite = null) {
		InfoUIController uiController = InfoPlaqueManager.Me.LoadUI<InfoUIController>();
		uiController.Setup(_title, _desc, _rarity, Vector3.zero, _sprite);
		uiController.Show();
		return uiController;
	}

	public void Setup(string _title, string _desc, string _rarity, Vector3 p, Sprite _sprite = null, bool _button = true) {
		m_title.text = _title;
		string r = "";
		if(_rarity != null)
			r = "\n" + _rarity;
		m_description.text = _desc + r;
		
		if(_sprite == null)
			m_imageHolder.SetActive(false);
		else
			m_image.sprite = _sprite;

		var newp = p;
		RectTransform rect = m_image.GetComponent<RectTransform>();
		if (p.x < Screen.width / 2)
			newp.x = p.x + rect.GetWidth();

		transform.position = newp;
		m_button.gameObject.SetActive(_button);
	}

	public void SetupActionButton(Action _onClick, string _title, bool _closeAfter = true) {
		m_buttonText.text = _title;

		m_onClose = _onClick;

		m_button.onButtonClick += (o) => {
			m_onClose = null;
			_onClick.Invoke();
			if(_closeAfter) Close();
		};
	}

	protected override void Close_Internal(bool _playCloseSound = true) {
		if(m_onClose != null)
			m_onClose.Invoke();

		m_onClose = null;
		base.Close_Internal(_playCloseSound);
	}
}
