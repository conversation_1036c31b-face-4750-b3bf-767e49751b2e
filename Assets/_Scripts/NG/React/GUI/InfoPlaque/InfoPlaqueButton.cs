using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;
using TMPro;

namespace InfoPlaqueOverhaul {
	public class InfoPlaqueButton : InfoPlaqueElement {
		[SerializeField] Button m_btn;
		[SerializeField] TMP_Text m_text;
		[SerializeField] GameObject m_moneyHolder;
		[SerializeField] Text m_money;
		[SerializeField] GameObject m_goldHolder;
		[SerializeField] Text m_gold;
		[SerializeField] GameObject m_ticketsHolder;
		[SerializeField] Text m_tickets;
		[SerializeField] GameObject m_bamHolder;
		[SerializeField] Text m_bams;
		[SerializeField] GameObject m_fabricHolder;
		[SerializeField] Text m_fabric;
		[SerializeField] GameObject m_produceHolder;
		[SerializeField] Text m_produce;
		[SerializeField] GameObject m_metalHolder;
		[SerializeField] Text m_metal;
		[SerializeField] GameObject m_mineralHolder;
		[SerializeField] Text m_mineral;
		[SerializeField] GameObject m_woodHolder;
		[SerializeField] Text m_wood;
		ButtonData m_data;

		void Awake () {
			m_btn.onClick.AddListener (OnClick);
			m_moneyHolder.SetActive (false);
			m_goldHolder.SetActive (false);
			m_bamHolder.SetActive (false);
			m_ticketsHolder.SetActive (false);
			m_fabricHolder.SetActive (false);
			m_produceHolder.SetActive (false);
			m_metalHolder.SetActive (false);
			m_mineralHolder.SetActive (false);
			m_woodHolder.SetActive (false);
		}

		public void SetInteractibility (bool _interactable) {
			m_btn.interactable = _interactable;
		}

		public void Initialise (ButtonData _data) {
			m_data = _data;
			m_text.text = m_data.Text;
			m_text.gameObject.SetActive (!string.IsNullOrEmpty (m_data.Text));

			if (m_data.Money != int.MinValue) {
				m_money.text = m_data.Money.ToMoneyString ();
				m_moneyHolder.SetActive (true);
			}
			SetValue (_data.Gold, m_goldHolder, m_gold);
			SetValue (_data.Tickets, m_ticketsHolder, m_tickets);

			if (m_data.Bams != int.MinValue) {
				m_bams.text = m_data.Bams.ToMoneyString ();
				m_bamHolder.SetActive (true);
			}
			SetValue (_data.Fabric, m_fabricHolder, m_fabric);
			SetValue (_data.Produce, m_produceHolder, m_produce);
			SetValue (_data.Metal, m_metalHolder, m_metal);
			SetValue (_data.Mineral, m_mineralHolder, m_mineral);
			SetValue (_data.Wood, m_woodHolder, m_wood);

			SetInteractibility(_data.MakeInteractable);
		}
		private void SetValue (int _value, GameObject _go, Text _text) {
			if (_value != int.MinValue) {
				_text.text = $"{_value}";
				_go.SetActive (true);
			}
		}

		void OnClick () {
			var infoPlaque = transform.FindComponentInParents<InfoPlaqueOverhaul.InfoPlaque> ();
			if (!infoPlaque.IsInteractable)
				return;

            //AudioClipManager.Me.PlaySound (InfoPlaqueManager.Me.m_buttonClickSound, transform);

            HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
            
			if (m_data.DisableBtnOnClick)
				m_btn.interactable = false;

			if (m_data.OnClickCallback != null)
				m_data.OnClickCallback ();

			if (m_data.DismissInfoPlaqueOnPress) {
				infoPlaque.DismissByInternalButton (m_data); // fail dangerously
				m_data.OnClickCallback = null;
			}
		}
	}
}