using System;
using System.Collections.Generic;
using System.Data;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using TMPro;
using UnityEngine.Rendering.Universal;

public struct GameStatTableData
{
	public string m_name;
	public string m_col1;
	public string m_col2;
	public string m_col3;
	public string m_col4;
	
	public int ColumnCount()
	{
		if(m_col1 == null) return 0;
		if(m_col2 == null) return 1;
		if(m_col3 == null) return 2;
		if(m_col4 == null) return 3;
		return 4;
	}
	
	public GameStatTableData(string _name, string _col1, string _col2 = null, string _col3 = null, string _col4 = null)
	{
		m_name = _name;
		m_col1 = _col1;
		m_col2 = _col2;
		m_col3 = _col3;
		m_col4 = _col4;
	}
}


public class TownInfoUIController : BaseClosableUIController
{
	public static bool Showing => s_currentInstance != null;
	private static TownInfoUIController s_currentInstance;
	
	public static TownInfoUIController Open(bool _recalc = true)
	{
		if(s_currentInstance != null)
			return s_currentInstance;
			
		if(_recalc)
			GameManager.Me.m_state.m_gameStats.UpdateBuildingStats();
			
		AudioClipManager.Me.PlayUISound("PlaySound_TitleScreenSettings");
		s_currentInstance = InfoPlaqueManager.Me.LoadUI<TownInfoUIController>();
		s_currentInstance.Setup();
		s_currentInstance.Show();

		return s_currentInstance;
	}

	protected override void OnDestroy()
	{
		if(s_currentInstance == this)
			s_currentInstance = null;
		base.OnDestroy();
	}

	public void Setup()
	{
		
	}
}