using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class AlignmentSelectionGUI : MonoBehaviour
{
	[SerializeField] TextMeshProUGUI m_title;
	[SerializeField] TextMeshProUGUI m_description;
	[SerializeField] Image m_image;
	[SerializeField] Button m_goBackButton;
	[SerializeField] Button m_acceptButton;

	PlayerAlignmentOption m_option;
	Action m_onHideCallback;

	// -------------------------------------------------------------------------------------------------
	public bool IsVisibile { get { return gameObject.activeInHierarchy; }}
	// -------------------------------------------------------------------------------------------------
	void Awake()
	{
		m_goBackButton.onClick.AddListener(GoBack);
		m_acceptButton.onClick.AddListener(Accept);
	}

	// -------------------------------------------------------------------------------------------------
	public void Show(PlayerAlignmentOption _option, Action _onHide)
	{
		m_option = _option;

		m_title.text = _option.AlignmentData.Name;
		m_description.text = _option.AlignmentData.Description;
		m_image.sprite = _option.AlignmentData.Icon;
		m_onHideCallback = _onHide;

		gameObject.SetActive(true);
	}

	// -------------------------------------------------------------------------------------------------
	void GoBack()
	{
		Hide();
	}

	// -------------------------------------------------------------------------------------------------
	void Accept()
	{
		PlayerAlignmentOption.Select(m_option);
		Hide();
	}

	// -------------------------------------------------------------------------------------------------
	public void Hide()
	{
		gameObject.SetActive(false);
		if (m_onHideCallback != null)
			m_onHideCallback();
	}
}