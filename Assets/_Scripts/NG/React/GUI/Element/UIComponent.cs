using UnityEngine;
using UnityEngine.EventSystems;

public abstract class UIComponent : <PERSON><PERSON><PERSON><PERSON><PERSON>, IUIComponent {

	private enum State {
		Unknown,
		Initialized
	}

	private State state = State.Unknown;

	protected void CheckInitialized() {
		if(state != State.Initialized) {
			OnInitialize();
		}
	}
	public void OnCloneComplete() {
		CheckInitialized();
	}

	/// <summary>
	/// Called on active and inactive objects, once they are created
	/// Awake is called only on active objects, hence it's sealed and cannot be used
	/// </summary>
	protected virtual void OnInitialize() {
		state = State.Initialized;
	}

	sealed protected override void Awake() {
		base.Awake();

		CheckInitialized();
	}

}

public abstract class UIComponent<TComponentType> : UIComponent where TComponentType : Component {

	private TComponentType _component;
	protected TComponentType component {
		get {
			CheckInitialized();
			return _component;
		}
		private set {
			_component = value;
		}
	}

	protected override void OnInitialize() {
		base.OnInitialize();
		component = GetComponent<TComponentType>();
	}

}