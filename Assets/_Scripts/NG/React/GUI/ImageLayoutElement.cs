using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

#if UNITY_EDITOR
using UnityEditor;
#endif

[ExecuteInEditMode]
[RequireComponent(typeof(UnityEngine.UI.Image))]
public class ImageLayoutElement : LayoutElement
{
	enum EFitMode
	{
		Vertical,
		Horizontal,
		BoxFit
	}

	[SerializeField] EFitMode m_fit;
	[SerializeField] float m_maxHeight = -1f;
	[SerializeField] float m_maxWidth = -1f;

	Sprite m_sprite;
	Image m_img;

	public override float preferredWidth
	{
		set { base.preferredWidth = value; }
		get
		{
			if (m_img == null) m_img = this.GetComponent<Image>();
			if (m_sprite == null || m_sprite != m_img.sprite) m_sprite = m_img.sprite;
			if (m_sprite == null) return base.preferredWidth;

			float height = base.preferredHeight;
			if (m_fit == EFitMode.BoxFit)
			{
				float width;
				CalculateBoxFit(out width, out height);
				return width;
			}

			if (height <= 0) return base.preferredWidth;
			return height * (m_sprite.rect.width / m_sprite.rect.height);
		}
	}

	public override float preferredHeight
	{
		set { }
		get {
			if (m_img == null) m_img = this.GetComponent<Image> ();
			if (m_sprite == null || m_sprite != m_img.sprite) m_sprite = m_img.sprite;
			if (m_sprite == null) return base.preferredHeight;

			float width = base.preferredWidth;
			if (m_fit == EFitMode.BoxFit)
			{
				float height;
				CalculateBoxFit(out width, out height);
				return height;
			}

			if (width <= 0) return base.preferredHeight;

			return width * (m_sprite.rect.height / m_sprite.rect.width);
		}
	}

	void CalculateBoxFit(out float _width, out float _height)
	{
		var aspectRatio = (m_sprite.rect.height / m_sprite.rect.width);
		bool isHightLimit = aspectRatio > 1f;

		if(isHightLimit)
		{
			_height = m_maxHeight;
			_width = m_maxHeight / aspectRatio;
		}
		else
		{
			_height = m_maxWidth * aspectRatio;
			_width = m_maxWidth;
		}
	}

	//--------------------------------------------------------------------------------------------------------
	// Editor
	//--------------------------------------------------------------------------------------------------------
#if UNITY_EDITOR
	[CustomEditor(typeof(ImageLayoutElement), true)]
	[CanEditMultipleObjects]
	public class ImageLayoutElementEditor : Editor
	{
		SerializedProperty m_IgnoreLayout;
		SerializedProperty m_PreferredWidth;
		SerializedProperty m_PreferredHeight;
		SerializedProperty m_MaxWidth;
		SerializedProperty m_MaxHeight;
		SerializedProperty m_LayoutPriority;

		SerializedProperty m_FitMode;

		protected virtual void OnEnable()
		{
			m_IgnoreLayout = serializedObject.FindProperty("m_IgnoreLayout");
			m_MaxWidth = serializedObject.FindProperty("m_maxWidth");
			m_MaxHeight = serializedObject.FindProperty("m_maxHeight");
			m_PreferredWidth = serializedObject.FindProperty("m_PreferredWidth");
			m_PreferredHeight = serializedObject.FindProperty("m_PreferredHeight");
			m_LayoutPriority = serializedObject.FindProperty("m_LayoutPriority");
			m_FitMode = serializedObject.FindProperty("m_fit");
		}

		public override void OnInspectorGUI()
		{
			serializedObject.Update();
			EditorGUILayout.PropertyField(m_IgnoreLayout);
			EditorGUILayout.Space();

			if(!m_IgnoreLayout.boolValue)
			{
				EditorGUI.BeginChangeCheck();
				EditorGUILayout.PropertyField(m_FitMode);
				bool wasChange = EditorGUI.EndChangeCheck();

				var mode = (EFitMode)m_FitMode.enumValueIndex;
				switch (mode)
				{
					case EFitMode.Vertical:
						LayoutElementField(m_PreferredWidth, t => t.rect.width);
						if (wasChange)
						{
							m_PreferredHeight.floatValue = -1;
							m_MaxHeight.floatValue = -1f;
							m_MaxWidth.floatValue = -1f;
						}
						break;
					case EFitMode.Horizontal:
						LayoutElementField(m_PreferredHeight, t => t.rect.height);
						if (wasChange)
						{
							m_PreferredWidth.floatValue = -1;
							m_MaxHeight.floatValue = -1f;
							m_MaxWidth.floatValue = -1f;
						}
						break;
					case EFitMode.BoxFit:
						LayoutElementField(m_MaxWidth, t => t.rect.width);
						LayoutElementField(m_MaxHeight, t => t.rect.height);
						if (wasChange)
						{
							m_PreferredWidth.floatValue = -1;
							m_PreferredHeight.floatValue = -1;
						}
						break;
				}
			}

			EditorGUILayout.Space();
			EditorGUILayout.PropertyField(m_LayoutPriority);
			serializedObject.ApplyModifiedProperties();
		}

		//--------------------------------------------------------------------------------------------------------
		// SH: stolen from layout element editor source
		//--------------------------------------------------------------------------------------------------------
		void LayoutElementField(SerializedProperty property, System.Func<RectTransform, float> defaultValue)
		{
			Rect position = EditorGUILayout.GetControlRect();

			// Label
			GUIContent label = EditorGUI.BeginProperty(position, null, property);

			// Rects
			Rect fieldPosition = EditorGUI.PrefixLabel(position, label);

			Rect toggleRect = fieldPosition;
			toggleRect.width = 16;

			Rect floatFieldRect = fieldPosition;
			floatFieldRect.xMin += 16;

			// Checkbox
			EditorGUI.BeginChangeCheck();
			bool enabled = EditorGUI.ToggleLeft(toggleRect, GUIContent.none, property.floatValue >= 0);
			if (EditorGUI.EndChangeCheck())
			{
				// This could be made better to set all of the targets to their initial width, but mimizing code change for now
				property.floatValue = (enabled ? defaultValue((target as LayoutElement).transform as RectTransform) : -1);
			}

			if (!property.hasMultipleDifferentValues && property.floatValue >= 0)
			{
				// Float field
				EditorGUIUtility.labelWidth = 4; // Small invisible label area for drag zone functionality
				EditorGUI.BeginChangeCheck();
				float newValue = EditorGUI.FloatField(floatFieldRect, new GUIContent(" "), property.floatValue);
				if (EditorGUI.EndChangeCheck())
				{
					property.floatValue = Mathf.Max(0, newValue);
				}
				EditorGUIUtility.labelWidth = 0;
			}

			EditorGUI.EndProperty();
		}
	}
#endif
}