using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class CaptureDataWindow : EditorWindow
{
    private static string Join(List<string> _list, string _sep)
    {
        if (_list == null || _list.Count == 0) return "";
        var s = _list[0];
        for (int i = 1; i < _list.Count; ++i)
            s += _sep + _list[i];
        return s;
    }

    private Texture2D m_graphTexture;

    private void RefreshGraph()
    {
        if (m_graphTexture != null)
        {
            DestroyImmediate(m_graphTexture);
            m_graphTexture = null;
        }

        if (m_selected.Count == 0)
        {
            m_graphTexture = null;
            return;
        }
        
        List<List<float>> data = new();
        List<(float, int, string)> dataTotals = new();
        int dataCount = -1;
        for (int i = 0; i < m_selected.Count; ++i)
        {
            if (System.IO.File.Exists(m_selected[i]) == false)
            {
                m_selected.RemoveAt(i);
                --i;
                continue;
            }
            var thisData = new List<float>();
            data.Add(thisData);
            var lines = System.IO.File.ReadAllLines(m_selected[i]);
            float total = 0;
            for (int j = 1; j < lines.Length; ++j)
            {
                var line = lines[j];
                var bits = line.Split(',');
                if (bits.Length < 9) continue;
                var frameMS = floatinv.Parse(bits[1]);
                thisData.Add(frameMS);
                total += frameMS;
            }
            dataTotals.Add((total, dataTotals.Count, m_selected[i]));
            if (dataCount == -1)
                dataCount = thisData.Count;
            else if (dataCount != thisData.Count)
                return;
        }
        
        dataTotals.Sort((b, a) => a.Item1.CompareTo(b.Item1));

        int w = data[0].Count, h = 256;
        var tex = new Texture2D(w, h, TextureFormat.RGBA32, false);
        for (int i = 0; i < w * h; ++i) tex.SetPixel(i % w, i / w, Color.clear);
        const float c_maxGraphTimepF = 150f;
        for (int x = 0; x < w; ++x)
        {
            for (int dd = 0; dd < data.Count; ++dd)
            {
                int d = dataTotals[dd].Item2;
                
                Color c = m_colours[d + 1];
                var ms = (int) (data[d][x] * (h / c_maxGraphTimepF));
                if (ms > h) ms = h;
                for (int y = 0; y < ms; ++y)
                {
                    var oc = tex.GetPixel(x, y);
                    var fc = oc * .7f + c * .7f;
                    fc.a = 1;
                    tex.SetPixel(x, y, c);
                }
            }
            for (int y = 1; y <= 9; ++y)
            {
                var msf = (int) (16.66666f * y * (h / c_maxGraphTimepF));
                var oc = tex.GetPixel(x, msf);
                var fc = oc * .9f + Color.white * .4f;
                tex.SetPixel(x, msf, fc);
            }
        }
        tex.Apply(false, false);
        m_graphTexture = tex;
    }

    Color[] m_colours = { Color.white, new Color(.9f, .4f, .3f), new Color(.3f, .9f, .4f), new Color(.3f, .4f, .9f), new Color(.9f, .8f, .3f), new Color(.8f, .3f, .9f), new Color(.3f, .9f, .8f) };
    Vector2 m_scroll, m_scrollButtons;
    void OnGUI()
    {
        GetDetails();
        if (m_graphTexture == null) RefreshGraph();

        GUILayout.BeginHorizontal();

        GUILayout.BeginVertical(GUILayout.Width(256 + 24));
        GUILayout.BeginHorizontal();
        GUILayout.Label("Filters", GUILayout.Width(48));
        m_filterMask = EditorGUILayout.MaskField(m_filterMask, m_filterArray);
        if (GUILayout.Button("Save", GUILayout.Width(64)))
        {
            var pngFile = EditorUtility.SaveFilePanel("Save graph to", Application.persistentDataPath, "graph.png", "png");
            if (string.IsNullOrEmpty(pngFile) == false)
            {
                var png = m_graphTexture.EncodeToPNG();
                System.IO.File.WriteAllBytes(pngFile, png);
            }
        }
        GUILayout.EndHorizontal();
        m_scrollButtons = GUILayout.BeginScrollView(m_scrollButtons);
        var allBits = (1 << m_filterArray.Length) - 1;
        var isAll = (m_filterMask & allBits) == allBits;
        for (int i = 0; i < m_details.Count; ++i)
        {
            var detail = m_details[i];
            int selected = m_selected.IndexOf(detail.Item5);
            GUI.backgroundColor = m_colours[selected + 1];
            if (isAll == false)
            {
                bool show = true;
                for (int j = 0; j < detail.Item1.Count; ++j)
                {
                    var bit = 1 << Array.IndexOf(m_filterArray, detail.Item1[j]);
                    if ((m_filterMask & bit) == 0)
                    {
                        show = false;
                        break;
                    }
                }
                if (show == false)
                {
                    if (m_selected.Remove(detail.Item5))
                        RefreshGraph();
                    continue;
                }
            }
            if (GUILayout.Button($"{Join(detail.Item1, "|")} {detail.Item2} {detail.Item3.ToString("d-M-yy HH:mm")}", GUILayout.Width(256)))
            {
                if (selected != -1)
                    m_selected.Remove(detail.Item5);
                else if (m_selected.Count < m_colours.Length - 1)
                    m_selected.Add(detail.Item5);
                RefreshGraph();
            }
        }
        GUI.backgroundColor = Color.white;
        GUILayout.EndVertical();
        GUILayout.EndScrollView();
        m_scroll = GUILayout.BeginScrollView(m_scroll);
        if (m_graphTexture != null)
            GUILayout.Label(m_graphTexture);
        else if (m_selected.Count > 0)
            GUILayout.Label("Data set mismatch");
        else
            GUILayout.Label("Select data to display");
        GUILayout.EndScrollView();
        GUILayout.EndHorizontal();
        GUI.backgroundColor = Color.white;
    }

    List<(List<string>, string, DateTime, string, string)> m_details = new ();
    HashSet<string> m_filters = new();
    string[] m_filterArray;
    List<string> m_selected = new();
    int m_filterMask = -1;

    void GetDetails()
    {
        m_details.Clear();
        m_filters.Clear();
        var files = System.IO.Directory.GetFiles(Application.persistentDataPath, "FPSCapture_*.csv", System.IO.SearchOption.TopDirectoryOnly);
        for (int i = 0; i < files.Length; i++)
        {
            var line = files[i].Replace(".csv", "");
            line = line.Substring(line.LastIndexOf('/') + 1);
            var bits = line.Split('_');
            if (bits.Length < 5) continue;
            var layers = new List<string>();
            for (int j = 1; j < bits.Length - 2; j++)
            {
                layers.Add(bits[j]);
                m_filters.Add(bits[j]);
            }
            var type = "";
            var dateStr = bits[bits.Length - 2];
            var timeStr = bits[bits.Length - 1];
            var date = DateTime.ParseExact($"{dateStr}-{timeStr}", "yyyy-MM-dd-HH-mm-ss", System.Globalization.CultureInfo.InvariantCulture);
            m_details.Add((layers, type, date, line, files[i]));
        }
        m_details.Sort((b, a) => a.Item3.CompareTo(b.Item3));
        m_filterArray = new string[m_filters.Count];
        int next = 0;
        foreach (var filter in m_filters)
            m_filterArray[next++] = filter;
    }

    [MenuItem("Window/Capture Data")]
    static void ShowWindow()
    {
        var window = GetWindow<CaptureDataWindow>();
        window.titleContent = new GUIContent("Capture Data");
        window.Show();
    }
}
