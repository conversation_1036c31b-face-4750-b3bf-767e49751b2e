using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class DebugConsoleWindow : EditorWindow
{
    private string m_command = "";
    private Vector2 m_dropdownScroller = default;
    private Vector2[] m_dropdownScrollers = { default, default, default };

    private string m_newTooltip;
    private Vector2 m_lastTooltipPos = Vector2.zero;
    private string m_lastTooltip = null;
    private void BeginTooltipChecks()
    {
        m_newTooltip = null;
    }
    private void CheckTooltip(string _tooltip)
    {
        if (Event.current.type == EventType.Repaint && GUILayoutUtility.GetLastRect().Contains(Event.current.mousePosition))
            m_newTooltip = _tooltip;

    }
    private void CompleteTooltipChecks()
    {
        var windowWidth = position.width;
        if (m_newTooltip != null)
        {
            GUI.color = Color.white;
            GUI.backgroundColor = Color.white;
            var tooltip = new GUIContent(m_newTooltip);
            var boxStyle = EditorStyles.objectField;
            var labelStyle = new GUIStyle(EditorStyles.label);
            labelStyle.richText = true;
            var realSize = labelStyle.CalcSize(tooltip);
            var size = realSize + Vector2.one * 12;
            if (m_lastTooltip != m_newTooltip)
                m_lastTooltipPos = Event.current.mousePosition;
            var pos = m_lastTooltipPos + new Vector2(size.x * -.5f, 20);
            if (pos.x + size.x > windowWidth)
                pos.x = windowWidth - size.x;
            else if (pos.x < 0)
                pos.x = 0;
            GUI.Box(new Rect(pos, size), "", boxStyle);
            GUI.Label(new Rect(pos + Vector2.one * 6, realSize), tooltip, labelStyle);
        }
        m_lastTooltip = m_newTooltip;
    }

    static float Float(string _s)
    {
        if (floatinv.TryParse(_s, out var f))
            return f;
        return 0;
    }

    static GUILayoutOption LabelWidth(string _s, float _add)
    {
        return GUILayout.Width(EditorStyles.label.CalcSize(new GUIContent(_s)).x + _add);
    }

    private string[] m_commandValues = new string[64];
    void ShowCommandParameters()
    {
        var allValues = new List<List<string>>();
        var allValuePositions = new List<int>();
        for (int i = 0; i < m_command.Length; ++i)
        {
            if (m_command[i] == '<')
            {
                var subs = new List<string>();
                var currentSub = "";
                for (int j = i + 1; j < m_command.Length; ++j)
                {
                    if (m_command[j] == ',' || m_command[j] == '>')
                    {
                        subs.Add(currentSub.Trim());
                        currentSub = "";
                        if (m_command[j] == '>')
                        {
                            if (subs.Count > 0)
                                allValues.Add(subs);
                            allValuePositions.Add(i);
                            allValuePositions.Add(j);
                            i = j;
                            break;
                        }
                    }
                    else
                    {
                        var c = m_command[j];
                        if (subs.Count == 0) c = char.ToLower(c);
                        currentSub += c;
                    }
                }
            }
        }
        const float c_lineHeight = 16;
        //if (allValues.Count > 0)
        {
            GUILayout.BeginHorizontal();
            bool runCommand = false;
            for (int i = 0; i < allValues.Count; ++i)
            {
                var valueDetails = allValues[i];
                var fieldType = valueDetails[0];
                float rangeMin = 0, rangeMax = 1;
                if (valueDetails.Count >= 3)
                {
                    floatinv.TryParse(valueDetails[1], out rangeMin);
                    floatinv.TryParse(valueDetails[2], out rangeMax);
                    if (valueDetails.Count >= 4 && string.IsNullOrEmpty(valueDetails[3]) == false)
                        GUILayout.Label(valueDetails[3], LabelWidth(valueDetails[3], 3), GUILayout.Height(c_lineHeight));
                }
                else
                {
                    valueDetails.Add("false");
                    valueDetails.Add("true");
                }
                var oldValue = m_commandValues[i];
                switch (fieldType)
                {
                    case "float":
                        m_commandValues[i] = EditorGUILayout.Slider(Float(m_commandValues[i]), rangeMin, rangeMax, GUILayout.Height(c_lineHeight)).ToString();
                        break;
                    case "int":
                        m_commandValues[i] = EditorGUILayout.IntSlider((int)Float(m_commandValues[i]), (int)rangeMin, (int)rangeMax, GUILayout.Height(c_lineHeight)).ToString();
                        break;
                    case "bool":
                        m_commandValues[i] = EditorGUILayout.Toggle(m_commandValues[i] == valueDetails[2], GUILayout.Width(16), GUILayout.Height(c_lineHeight)) ? valueDetails[2] : valueDetails[1];
                        break;
                }
                if (oldValue != m_commandValues[i]) runCommand = true;
            }
            // produce command
            var s = m_command;
            for (int i = allValues.Count - 1; i >= 0; --i)
            {
                var start = allValuePositions[i * 2 + 0];
                var end = allValuePositions[i * 2 + 1];
                s = s.Substring(0, start) + m_commandValues[i] + s.Substring(end + 1);
            }
            //GUILayout.FlexibleSpace();
            if (allValues.Count > 0)
                GUILayout.Label(s, LabelWidth(s, 6));
            else
                GUILayout.Label(" ", GUILayout.Height(c_lineHeight));
            if (runCommand && DebugConsole.Me != null)
                DebugConsole.Me.ExecuteConsole(s, true);
            GUILayout.EndHorizontal();
        }
    }

    
    private string m_filter = "";

    int m_commandCursorIndex = -1, m_commandCursorSelect = -1;
    int m_focusCommandNextUpdate = 0;
    
    string m_lastPreEqualsCmd = "";

    void SetCommand(string _cmd)
    {
        m_command = _cmd;
        m_commandCursorIndex = m_commandCursorSelect = m_command.Length;
        m_focusCommandNextUpdate = 1;
        m_filter = "";
    }

    void OnGUI()
    {
        var runCommand = false;
        var showClr = new Color(.9f, .9f, 1);
        var dropClr = new Color(1, 1, .85f);
        var pinClr = new Color(1, .85f, .95f);
        var clearClr = Color.white;//new Color(1, .75f, .75f);
        var runClr = new Color(.75f, 1, .75f);
        var pinBtnClr = new Color(1, .75f, 1);
        
        var windowWidth = position.width;
        var windowHeight = position.height;
        var scrollPaneWidth = (windowWidth - 10) / 2;
        var scrollPaneHeightSingle = windowHeight - 48;
        var scrollPaneHeightDouble = scrollPaneHeightSingle / 2 - 9;
        
        var placeholderStyle = new GUIStyle(EditorStyles.miniLabel);
        placeholderStyle.fontStyle = FontStyle.Italic;

        var e = Event.current;
        //if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Tab) e.Use();
        
        BeginTooltipChecks();
        
        bool tabForward = false, tabBackward = false;

        const string c_commandField = "CommandField";
        EditorGUILayout.BeginHorizontal(GUILayout.ExpandWidth(true));
        EditorGUILayout.BeginHorizontal(EditorStyles.textField);
        GUI.SetNextControlName(c_commandField);
        var placeholder = "Enter a debug console command";
        var isPlaceholder = string.IsNullOrEmpty(m_command);
        GUI.color = isPlaceholder ? Color.gray : Color.white;
        var wasCmd = m_command;
        m_command = GUILayout.TextField(isPlaceholder ? placeholder : m_command, isPlaceholder ? placeholderStyle: EditorStyles.label, GUILayout.ExpandWidth(true)).Replace(placeholder, "");
        var canRun = string.IsNullOrEmpty(m_command) == false;
        var isFocused = GUI.GetNameOfFocusedControl() == c_commandField;
        if (m_command.EndsWith("=") && m_command.Contains("&&") == false)
        {
            if (wasCmd.EndsWith("=") == false)
                m_lastPreEqualsCmd = wasCmd;
            var equalIndex = m_command.IndexOf('=');
            if (equalIndex > -1 && equalIndex < m_command.Length - 1)
                m_command = m_command.Substring(0, equalIndex + 1);
        }
        if (isFocused)
        {
            var editor = (TextEditor) GUIUtility.GetStateObject(typeof(TextEditor), GUIUtility.keyboardControl);
            if (m_focusCommandNextUpdate == 0)
                (m_commandCursorIndex, m_commandCursorSelect) = (editor.cursorIndex, editor.selectIndex);
            else
                (editor.cursorIndex, editor.selectIndex) = (m_commandCursorIndex, m_command.Length);
            editor.scrollOffset = Vector2.zero;
        }
        if (isFocused && e.type == EventType.KeyUp)
        {
            if (e.keyCode == KeyCode.Equals)
                SetCommand($"{m_lastPreEqualsCmd}=");
            else if ((e.keyCode >= KeyCode.A && e.keyCode <= KeyCode.Z) || (e.keyCode >= KeyCode.Alpha0 && e.keyCode <= KeyCode.Alpha9) || e.keyCode == KeyCode.Backspace)
                m_filter = "";
            if (e.keyCode == KeyCode.LeftBracket || e.keyCode == KeyCode.RightBracket)
                SetCommand(DebugConsole.Me != null ? DebugConsole.Me.ConsoleHistory(e.keyCode == KeyCode.LeftBracket || e.keyCode == KeyCode.Comma) : "");
        }
        if (isFocused && e.type == EventType.KeyDown)
        {
            switch (e.keyCode)
            {
                case KeyCode.None:
                    switch (e.character)
                    {
                        case (char)10:
                            runCommand = true;
                            m_filter = "";
                            break;
                        case (char)27:
                            m_command = "";
                            m_filter = "";
                            break;
                    }
                    break;
                case KeyCode.Tab:
                    if (string.IsNullOrEmpty(m_filter))
                        m_filter = m_command.Split('=')[0];
                    m_commandCursorIndex = m_filter.Length; m_commandCursorSelect = m_command.Length;
                    if (e.modifiers == EventModifiers.Shift)
                        tabBackward = true;
                    else
                        tabForward = true;
                    GUI.FocusControl(c_commandField);
                    m_focusCommandNextUpdate = 2;
                    break;
            }
        }
        
        CheckTooltip("Enter a debug console command\nPress <color=#c0ffc0>Enter</color> to run the command, <color=#ffc0c0>Escape</color> to clear\n<color=#c0c0ff>Tab</color> and <color=#c0c0ff>Shift+Tab</color> to auto-complete commands");

        GUI.color = clearClr;
        GUI.enabled = canRun;
        if (GUILayout.Button(new GUIContent("\u2716", "Clear   <color=#808080>Esc</color>"), EditorStyles.label, GUILayout.Width(16)))
            m_command = "";
        GUILayout.EndHorizontal();
        
        const int c_pinButtonWidth = 32;
        const int c_runButtonWidth = 50;
        EditorGUILayout.BeginHorizontal(EditorStyles.helpBox, GUILayout.Width(4+c_pinButtonWidth+4+c_runButtonWidth+4));
        GUI.enabled = DebugConsole.Me != null;
        GUI.color = GUI.backgroundColor = Color.white;
        GUI.enabled = canRun && DebugConsole.Me != null;
        GUI.color = pinBtnClr;
        if (GUILayout.Button(new GUIContent("\U0001F4CC", "Pin"), EditorStyles.miniButton, GUILayout.Width(c_pinButtonWidth)))
            DebugConsole.Me.TogglePinned(m_command);
        GUI.color = runClr;
        GUI.enabled = canRun;
        if (GUILayout.Button(new GUIContent("\u21B5", "Execute   <color=#808080>Enter</color>"), EditorStyles.miniButton, GUILayout.Width(c_runButtonWidth)))
            runCommand = true;
        GUI.enabled = true;
        GUI.color = Color.white;
        GUILayout.EndHorizontal();
        GUILayout.EndHorizontal();

        ShowCommandParameters();

        EditorGUILayout.BeginHorizontal();
        for (int p = 0; p < 2; ++p)
        {
            var toShow = null as List<string>;
            string highlight = null;
            if (p == 0)
            {
                toShow = new List<string>();
                var filter = string.IsNullOrEmpty(m_filter) ? m_command.ToLower() : m_filter.ToLower();
                var equalsIndex = filter.IndexOf('=');
                if (equalsIndex > -1) filter = filter.Substring(0, equalsIndex);
                var hasFilter = string.IsNullOrEmpty(filter) == false;
                var currentCommand = m_command.ToLower();
                equalsIndex = currentCommand.IndexOf('=');
                if (equalsIndex > -1) currentCommand = currentCommand.Substring(0, equalsIndex);
                highlight = currentCommand;
                foreach (var kvp in DebugConsole.s_consoleActions)
                {
                    if (hasFilter == false || kvp.Key.ToLower().Contains(filter))
                    {
                        var cmd = kvp.Key;
                        if (kvp.Value.Item3 != null)
                            cmd = $"{cmd}={kvp.Value.Item3}";
                        cmd = $"{cmd}##{kvp.Value.Item2}";
                        toShow.Add(cmd);
                    }
                }
                toShow.Sort((a, b) => b.CompareTo(a));
                if ((tabForward || tabBackward) && toShow.Count > 0)
                {
                    int selIndex = 0;
                    for (int i = 0; i < toShow.Count; ++i)
                    {
                        var showStr = toShow[i].Split(new string[] {"##"}, System.StringSplitOptions.None)[0];
                        showStr = showStr.Split('=')[0];
                        if (showStr == currentCommand)
                        {
                            selIndex = i;
                            break;
                        }
                    }
                    if (tabBackward) selIndex = (selIndex + 1) % toShow.Count;
                    else selIndex = (selIndex - 1 + toShow.Count) % toShow.Count;
                    m_command = toShow[selIndex].Split(new string[] {"##"}, System.StringSplitOptions.None)[0];
                }
            }
            else if (DebugConsole.Me != null)
                toShow = DebugConsole.Me.m_consoleHistory;

            var clr = p == 0 ? showClr : dropClr;
            GUI.backgroundColor = GUI.color = clr;

            EditorGUILayout.BeginVertical(GUILayout.Width(scrollPaneWidth));
            GUILayout.Label(p == 0 ? "Commands" : "History", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Height(p == 0 ? scrollPaneHeightSingle : scrollPaneHeightDouble));
            m_dropdownScrollers[p] = EditorGUILayout.BeginScrollView(m_dropdownScrollers[p], GUILayout.ExpandHeight(true));

            if (toShow != null)
            {
                var buttonStyle = new GUIStyle(EditorStyles.label);
                buttonStyle.richText = true;
                for (int i = toShow.Count - 1; i >= 0; --i)
                {
                    bool res;
                    var bits = toShow[i].Split(new string[] { "##" }, System.StringSplitOptions.None);
                    var cmd = bits[0];
                    var equalsIndex = cmd.IndexOf('=');
                    if (equalsIndex >= 0)
                    {
                        var cmdOnly = cmd.Substring(0, equalsIndex);
                        if (cmdOnly == highlight) cmdOnly = $"<color=#c0ffc0>{cmdOnly}</color>";
                        else cmdOnly = $"<color=#808080>{cmdOnly}</color>";
                        cmd = $"{cmdOnly}<color=#c04040>{cmd.Substring(equalsIndex)}</color>";
                    }
                    else if (cmd == highlight) cmd = $"<color=#c0ffc0>{cmd}</color>";
                    if (bits.Length > 1 && string.IsNullOrEmpty(bits[1]) == false)
                        res = GUILayout.Button(new GUIContent(cmd, bits[1]), buttonStyle, GUILayout.Height(14));
                    else
                        res = GUILayout.Button(cmd, buttonStyle, GUILayout.Height(14));
                            
                    if (res)
                    {
                        if (m_command == bits[0])
                            runCommand = true;
                        else
                            m_command = bits[0];
                    }
                }
            }
            else
                GUILayout.Label("");
            GUILayout.EndScrollView();
            GUILayout.EndVertical();

            if (p == 1)
            {
                GUI.backgroundColor = GUI.color = pinClr;
                GUILayout.Label("Pinned", EditorStyles.boldLabel);
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Height(scrollPaneHeightDouble));
                m_dropdownScrollers[2] = EditorGUILayout.BeginScrollView(m_dropdownScrollers[2], GUILayout.ExpandHeight(true));
                if (DebugConsole.Me != null)
                {
                    var pinned = DebugConsole.Me.m_pinnedCommands;
                    foreach (var label in pinned)
                    {
                        EditorGUILayout.BeginHorizontal();
                        GUI.color = GUI.backgroundColor = new Color(1, .3f, .3f);//Color.red;
                        if (GUILayout.Button(new GUIContent("\u2716"/*"\u274C"*/, "Unpin this command"), EditorStyles.label, GUILayout.Width(10)))
                            DebugConsole.Me.TogglePinned(label);
                        GUI.backgroundColor = GUI.color = pinClr;
                        if (GUILayout.Button(label, EditorStyles.label, GUILayout.Width(150), GUILayout.Height(14)))
                        {
                            if (m_command == label)
                                runCommand = true;
                            else
                                m_command = label;
                        }
                        GUILayout.EndHorizontal();
                    }
                }
                GUILayout.EndScrollView();
                GUILayout.EndVertical();
            }
            GUILayout.EndVertical();
        }
        
        GUILayout.EndHorizontal();
        GUI.backgroundColor = Color.white;
        GUI.color = Color.white;
        
        if (runCommand && DebugConsole.Me != null)
        {
            DebugConsole.Me.ExecuteConsole(m_command);
            m_command = "";
        }

        if (string.IsNullOrEmpty(m_command) && GUI.GetNameOfFocusedControl() == c_commandField)
        {
            TextEditor editor = (TextEditor) GUIUtility.GetStateObject(typeof(TextEditor), GUIUtility.keyboardControl);
            if (editor != null)
                editor.cursorIndex = editor.selectIndex = 0;
        }

        CompleteTooltipChecks();

        if (m_focusCommandNextUpdate > 0)
        {
            if (e.type == EventType.Repaint)
                --m_focusCommandNextUpdate;
            GUI.FocusControl(c_commandField);
            Repaint();
        }
    }

    private void Init()
    {
        m_focusCommandNextUpdate = 5;
    }

    
    [MenuItem("Window/DebugConsole ^#d")]
    static void ShowWindow()
    {
        var window = GetWindow<DebugConsoleWindow>();
        window.titleContent = new GUIContent("Debug Console");
        window.minSize = new Vector2(300, 80);
        window.maxSize = new Vector2(800, 800);
        window.Init();
        window.Show();
    }
}
