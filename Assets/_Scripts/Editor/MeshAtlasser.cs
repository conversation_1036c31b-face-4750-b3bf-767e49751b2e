#define STAMPTEXTURE
//#define DEBUGPOLYS

using System;
using System.Collections.Generic;
using System.IO;
using TMPro;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using static KitBashOptimiseManager;
using Object = UnityEngine.Object;

public partial class MeshAtlasser
{
    private static readonly Type[] c_BlacklistedComponents = { typeof(TextMeshPro) };
    
    private const string c_Albedo = "Albedo";
    private const string c_Normal = "Normal";
    private const string c_Tint = "Tint";
    private const string c_Mask = "Mask";
    private const string c_Emission = "Emission";
    private const string c_Control = "Control";

    private static MeshAtlasser Me;

    private string matFolder;
    private string meshFolder;
    private string atlasFolder;
    private string atlasName;
    private Dictionary<MeshFilter, GameObject> m_mFParents = new();

    private class MapType
    {
        public string TypeName;
        private readonly List<string> m_possibleNames;
        private readonly Color m_defaultColour;
        private readonly bool m_linear;

        private Dictionary<Shader, string> m_mapNames;

        public MapType(Color _default, bool _linear, string _mainName, params string[] _names)
        {
            TypeName = _mainName;

            m_defaultColour = _default;
            m_linear = _linear;
            m_possibleNames = new List<string>(_names);
            m_mapNames = new Dictionary<Shader, string>();
        }

        public bool Check(Shader _shader, string _texName)
        {
            if (!m_possibleNames.Contains(_texName))
                return false;
            if (m_mapNames.TryGetValue(_shader, out var oldName))
            {
                Debug.LogError($"Shader {_shader} has multiple {TypeName} maps: {oldName} and {_texName}. Unsure which to use...", _shader);
                return true;
            }
            m_mapNames.Add(_shader, _texName);
            return true;
        }

        public string CheckTex(string _texName)
        {
            foreach (var alias in m_possibleNames)
            {
                if (_texName.Contains(alias))
                    return alias;
            }

            return "";
        }

        public Texture2D Get(Material _mat)
        {
            // Add safety check for shader mapping
            if (!m_mapNames.TryGetValue(_mat.shader, out var propertyName))
            {
                Debug.LogWarning($"Shader {_mat.shader.name} not found in map names for {TypeName}. Creating default texture.");
                var defaultTex = new Texture2D(1, 1, TextureFormat.RGBA32, false, m_linear);
                defaultTex.SetPixel(0, 0, m_defaultColour);
                defaultTex.Apply();
                return defaultTex;
            }

            var tex = _mat.GetTexture(propertyName) as Texture2D;
            if (tex == null)
            {
                tex = new Texture2D(1, 1, TextureFormat.RGBA32, false, m_linear);
                tex.SetPixel(0, 0, m_defaultColour);
                tex.Apply();
            }
            return tex;
        }

        public void Set(Material _mat, Texture2D _tex)
        {
            if (m_mapNames.TryGetValue(_mat.shader, out var propertyName))
            {
                _mat.SetTexture(propertyName, _tex);
            }
            else
            {
                Debug.LogWarning($"Cannot set texture for shader {_mat.shader.name} - property name not found for {TypeName}");
            }
        }
    }

    private MapType m_albedoMapType = new(Color.white, false, c_Albedo, "_MainTex", "_BaseMap");
    private MapType m_normalMapType = new(new Color(.5f, .5f, 1f, 1), true, c_Normal, "_BumpMap", "_NormalMap", "_PlantNormalMap");
    private MapType m_tintMapType = new(Color.white, true, c_Tint, "_TintMap");
    private MapType m_maskMapType = new(Color.white, true, c_Mask, "_MaskMap", "_Mask");
    private MapType m_emissionMapType = new(Color.black, true, c_Emission, "_EmissionMap", "_EmmisionMap");
    private MapType m_controlMapType = new(Color.white, true, c_Control, "_PlantControl");

    private class MapBundle
    {
        private struct ChannelInfo
        {
            public float4 colour;
            public float blend;
            public int smi;
            public int channel;
            public DecorationHolder dh;

            public ChannelInfo(Color _c, float _blend, int _smi, int _channel, DecorationHolder _dh = null)
            {
                colour = new(_c.r, _c.g, _c.b, _c.a);
                blend = _blend;
                smi = _smi;
                channel = _channel;
                dh = _dh;
            }
            
            public ChannelInfo(float4 _c, float _blend, int _smi, int _channel, DecorationHolder _dh = null)
            {
                colour = _c;
                blend = _blend;
                smi = _smi;
                channel = _channel;
                dh = _dh;
            }

            public static (float4, float) Average(List<ChannelInfo> _channels)
            {
                float4 totCol = float4.zero;
                float totBlend = 0;
                foreach (var channel in _channels)
                {
                    totCol += channel.colour * channel.blend;
                    totBlend += channel.blend;
                }
                var avgCol = totCol / totBlend;
                var avgBlend = totBlend;
                return (avgCol, avgBlend);
            }
        }
        
        private readonly string m_shaderName;

        private readonly MapType[] m_allMapTypes;
        private readonly bool[] m_hasMapTypes;
        private readonly List<Texture2D>[] m_allMaps;
        private readonly int m_numMapTypes;
        private readonly List<int> m_tintChannelsUsed;

        private readonly List<int2> m_sizes = new();

        private List<(MeshFilter, int)> m_subMeshes = new();
        private Dictionary<(MeshFilter, int), Material> subMats = new();
        private Dictionary<Material, int> m_matInds = new();
        private List<List<TriData>> m_allTriData = new();
        
        private Dictionary<DecorationHolder, int> m_dHInds = new();
        private Dictionary<int, int> m_subDHInds = new();
        private Dictionary<int, ChannelInfo> m_channelInfos = new();

        public MapBundle(Shader _shader, Hash128 _hash)
        {
            m_shaderName = $"{_shader.name.Split('/')[^1]}{_hash}";

            m_allMapTypes = new[]
            {
                Me.m_albedoMapType, Me.m_normalMapType, Me.m_tintMapType, Me.m_maskMapType, Me.m_emissionMapType,
                Me.m_controlMapType
            };
            m_numMapTypes = m_allMapTypes.Length;
            m_hasMapTypes = new bool[m_numMapTypes];
            m_allMaps = new List<Texture2D>[m_numMapTypes];

            CheckAllMaps(_shader);
            for (int i = 0; i < m_numMapTypes; ++i)
            {
                if (m_hasMapTypes[i])
                {
                    m_allMaps[i] = new List<Texture2D>();
                    if(m_allMapTypes[i] == Me.m_tintMapType)
                        m_tintChannelsUsed = new List<int>();
                }
            }
        }

        private void CheckAllMaps(Shader _shader)
        {
            var propertyCount = ShaderUtil.GetPropertyCount(_shader);
            for (int i = 0; i < propertyCount; ++i)
            {
                if (ShaderUtil.GetPropertyType(_shader, i) != ShaderUtil.ShaderPropertyType.TexEnv)
                    continue;
                var name = ShaderUtil.GetPropertyName(_shader, i);
                var found = false;
                for (int j = 0; j < m_numMapTypes; ++j)
                {
                    bool check = m_allMapTypes[j].Check(_shader, name);
                    if (!check)
                        continue;
                    m_hasMapTypes[j] = true;
                    found = true;
                    break;
                }

                if (!found)
                    Debug.LogWarning($"Shader {_shader.name} texture {name} not currently being atlassed");
            }
        }


        private void DoForAll(Action<MapType, List<Texture2D>, int> Action)
        {
            int mapIndex = 0;
            for (int i = 0; i < m_numMapTypes; ++i)
            {
                if (m_hasMapTypes[i])
                    Action(m_allMapTypes[i], m_allMaps[i], mapIndex++);
            }
        }

        public void Add(Material _mat, MeshFilter _mf, int _subMesh)
        {
            if (_mat.HasVector("_Tiling"))
            {
                var tiling = _mat.GetVector("_Tiling");
                if (!Mathf.Approximately(tiling.x, 1) || !Mathf.Approximately(tiling.y, 1))
                    return;
            }

            if (_mat.HasFloat("_Parallax"))
            {
                var parallax = _mat.GetFloat("_Parallax");
                if (parallax != 0)
                    return;
            }

            if (_mat.HasVector("_PlantRotate"))
            {
                return; //This is set dynamically so unfortunately this material can't be atlassed
            }

            var dh = _mf.GetComponentInParent<DecorationHolder>();
            var dhChannel = dh != null ? (int)dh.m_channel : -1;

            if (dh != null)
            {
                if (!m_dHInds.TryGetValue(dh, out var dhInd))
                {
                    dhInd = m_dHInds.Count;
                    m_dHInds.Add(dh, dhInd);
                }
                m_subDHInds.Add(m_subMeshes.Count, dhInd);
            }
            m_subMeshes.Add((_mf, _subMesh));
            subMats.Add((_mf, _subMesh), _mat);

            if (m_matInds.ContainsKey(_mat))
                return;
            
            if (_mat.HasProperty("_R_ON_OFF"))
            {
                for (int i = 0; i < 4; ++i)
                {
                    var col = _mat.GetColor(ColourName(i));
                    var blend = _mat.GetFloat(BlendName(i));
                    var isDHChannel = i == dhChannel;
                    if (!isDHChannel && (blend == 0 || col == Color.white))
                        continue;
                    var info = new ChannelInfo(col, blend, m_matInds.Count, i, i == dhChannel ? dh : null);
                    m_channelInfos.Add(4 * m_matInds.Count + i, info);
                }
            }

            m_matInds.Add(_mat, m_matInds.Count);

            if (m_tintChannelsUsed != null) //Then this shader has tint maps
            {
                DoForAll((type, maps, _) =>
                {
                    var tex = GetReadableTexture(type.Get(_mat), type.TypeName);
                    maps.Add(tex);
                    if (type == Me.m_tintMapType)
                    {
                        if (dh != null)
                            m_tintChannelsUsed.Add((int)dh.m_channel);
                        else
                            m_tintChannelsUsed.Add(0);
                    }
                });
            }
            else
                DoForAll((type, maps, _) => maps.Add(GetReadableTexture(type.Get(_mat), type.TypeName)));
        }

        public void ResizeAllMaps()
        {
            for (int i = 0; i < m_numMapTypes; ++i)
                m_hasMapTypes[i] = m_hasMapTypes[i] && m_allMaps[i].Count > 0;

            for (int i = 0; i < m_matInds.Count; ++i)
            {
                var newMaps = new List<Texture2D>();
                DoForAll((_, maps, _) => newMaps.Add(maps[i]));
                m_sizes.Add(ResizeToMaxSize(newMaps));
                DoForAll((_, maps, ind) => maps[i] = newMaps[ind]);
            }
        }

        private int2 ResizeToMaxSize(List<Texture2D> _textures)
        {
            var maxSize = new int2(1, 1);
            foreach (var tex in _textures)
                maxSize = math.max(maxSize, new int2(tex.width, tex.height));

            for (var index = 0; index < _textures.Count; index++)
            {
                var tex = _textures[index];
                _textures[index] = UpscaleTexture(tex, maxSize);
            }
            return maxSize;
        }

        public bool ProcessMeshes()
        {
            for (int i = 0; i < m_subMeshes.Count; ++i)
            {
                var subMesh = m_subMeshes[i];
                var (mf, subMeshIndex) = subMesh;
                var mesh = mf.sharedMesh;
                var tris = GetMeshData(mesh, subMeshIndex, m_sizes[m_matInds[subMats[subMesh]]], i, out bool _skip);
                if (_skip)
                    m_subMeshes.RemoveAt(i--);
                else
                    m_allTriData.Add(tris);
            }

            return m_allTriData.Count > 1;
        }

        private static int[] KMeansPartitioned(List<(float4 col, bool grp)> data, int k = 4)
        {
            var trueData = new List<float4>(data.Count);
            var falseData = new List<float4>(data.Count);
            foreach (var d in data)
            {
                if (d.grp)
                    trueData.Add(d.col);
                else
                    falseData.Add(d.col);
            }
            var bestWCSS = float.MaxValue;
            var bestMap = new int[data.Count];
            for (int i = math.max(1, k - falseData.Count); i <= math.min(k - 1, trueData.Count); ++i)
            {
                // k - i < falseCount -> i > k - falseCount
                // i < trueCount
                var trueMap = KMeans(trueData, out var trueCentroids, i);
                var trueWCSS = WCSS(trueData, trueCentroids, trueMap);
                var falseMap = KMeans(falseData, out var falseCentroids, k - i);
                var falseWCSS = WCSS(falseData, falseCentroids, falseMap);
                var wcss = trueWCSS + falseWCSS;
                if (wcss >= bestWCSS)
                    continue;
                bestWCSS = wcss;
                for (int t = 0, f = 0; t + f < data.Count;)
                    bestMap[t + f] = data[t + f].grp ? trueMap[t++] : falseMap[f++];
            }
            return bestMap;
        }
        
        private static int[] KMeans(List<float4> colours, out List<float4> centroids, int k = 4, int maxIterations = 100)
        {
            var n = colours.Count;
            centroids = new List<float4>();
            var shuffled = new List<float4>(colours);
            shuffled.Shuffle();
            centroids.AddRange(shuffled.GetRange(0, k));

            var labels = new int[n];

            for (int iter = 0; iter < maxIterations; iter++)
            {
                for (int i = 0; i < n; i++)
                {
                    float minDist = float.MaxValue;
                    int closest = 0;
                    for (int j = 0; j < k; j++)
                    {
                        float dist = math.lengthsq(colours[i] - centroids[j]);
                        if (dist < minDist)
                        {
                            minDist = dist;
                            closest = j;
                        }
                    }
                    labels[i] = closest;
                }

                var newCentroids = new float4[k];
                int[] counts = new int[k];
                for (int i = 0; i < n; i++)
                {
                    newCentroids[labels[i]] += colours[i];
                    counts[labels[i]]++;
                }
                for (int j = 0; j < k; j++)
                {
                    if (counts[j] > 0)
                        centroids[j] = newCentroids[j] / counts[j];
                }
            }
            return labels;
        }
        
        private static float WCSS(List<float4> tints, List<float4> centroids, int[] assignments)
        {
            float wcss = 0;
            for(int i = 0; i < tints.Count; i++)
                wcss += math.lengthsq(tints[i] - centroids[assignments[i]]);
            return wcss;
        }

        public void GenerateAndApply(string _meshFolder)
        {
            var channelMap = MergeChannels();
            var matHashes = HashMats();

            var texDict = new Dictionary<(int, Hash128), List<TriData>>();
            var firstOccs = new Dictionary<(int, Hash128), (int, Material)>();
            for (int i = 0; i < m_subMeshes.Count; ++i)
            {
                var mat = subMats[m_subMeshes[i]];
                var hash = matHashes[mat];
                var dhInd = m_subDHInds.GetValueOrDefault(i, 0);
                var key = (dhInd, hash);
                if (!texDict.TryAdd(key, new List<TriData>(m_allTriData[i])))
                    texDict[key].AddRange(m_allTriData[i]);
                else
                    firstOccs.Add(key, (i, mat));
            }

            var keyList = new List<(int, Hash128)>();
            var submeshRects = new List<List<(int4, List<TriData>)>>();
            foreach (var (key, tris) in texDict)
            {
                submeshRects.Add(GetMinimalCoveringRects(tris));
                keyList.Add(key);
            }

            var (packedRects, packedTris) = BoxPack(submeshRects, out int size);
            if (size == -1)
            {
                Debug.LogError("Failed to create atlas");
                return;
            }

            var atlasses = CreateAtlasses(size);
#if STAMPTEXTURE
            for (int i = 0; i < keyList.Count; ++i)
            {
                var (_, mat) = firstOccs[keyList[i]];
                StampAllMaps(atlasses, packedRects[i], m_matInds[mat], channelMap);
            }
#endif
#if DEBUGPOLYS
            int count = 0;
            foreach (var tris in packedTris)
                count += tris.Length;
            var allTris = new TriData[count];
            int ind = 0;
            foreach (var tris in packedTris)
            {
                tris.CopyTo(allTris, ind);
                ind += tris.Length;
            }
            
            DrawTrianglesToTexture(atlasses[0], Color.red, allTris);
#endif
            SaveAllAtlasses(atlasses);

#if STAMPTEXTURE
            Material baseMat = null;
            foreach (var subTris in packedTris)
            {
                var sortedTris = new Dictionary<int, List<TriData>>();
                foreach (var tri in subTris)
                {
                    if (!sortedTris.TryAdd(tri.meshID, new() { tri }))
                        sortedTris[tri.meshID].Add(tri);
                }

                foreach (var (index, tris) in sortedTris)
                {
                    var (mF, subMeshIndex) = m_subMeshes[index];
                    var mesh = Object.Instantiate(mF.sharedMesh);
                    var renderer = mF.GetComponent<MeshRenderer>();
                    var mats = renderer.sharedMaterials;
                    var mat = mats[subMeshIndex];
                    RebuildMeshToAtlas(mesh, subMeshIndex, tris);
                    mF.mesh = mesh;

                    if (baseMat == null)
                    {
                        baseMat = new Material(mat);
                        FillInAtlasses(baseMat, atlasses);
                        SaveAsset(baseMat, Path.Combine(Me.matFolder, $"{m_shaderName}.mat"));
                    }
                    else
                    {
                        MaterialPropertyOverride.TryAdd(mat, baseMat, subMeshIndex, mF.gameObject);
                        var dh = mF.GetComponentInParent<DecorationHolder>();
                        if (dh != null && m_tintChannelsUsed != null)
                        {
                            var matInd = m_matInds[mat];

                            // Add bounds checking for tintChannelsUsed array access
                            if (matInd >= 0 && matInd < m_tintChannelsUsed.Count)
                            {
                                var oldChannel = (DecorationHolder.EChannel)m_tintChannelsUsed[matInd];
                                var oldColName = ColourName(oldChannel);
                                var oldBlendName = BlendName(oldChannel);

                                // Add bounds checking for channelMap access
                                int channelMapKey = matInd * 4 + (int)oldChannel;
                                if (channelMap.TryGetValue(channelMapKey, out var newChannelInt))
                                {
                                    var newChannel = (DecorationHolder.EChannel)newChannelInt;
                                    var newColName = ColourName(newChannel);
                                    var newBlendName = BlendName(newChannel);

                                    baseMat.SetColor(newColName, mat.GetColor(oldColName));
                                    baseMat.SetFloat(newBlendName, mat.GetFloat(oldBlendName));
                                    dh.m_channel = newChannel;
                                }
                                else
                                {
                                    Debug.LogWarning($"Failed to find channel mapping for material {mat.name}, matInd {matInd}, channel {oldChannel}. Tint properties may not be correctly applied.");
                                }
                            }
                            else
                            {
                                Debug.LogWarning($"Material index {matInd} is out of bounds for tintChannelsUsed array (size: {m_tintChannelsUsed.Count}). Material: {mat.name}");
                            }
                        }
                    }

                    mats[subMeshIndex] = baseMat;
                    renderer.sharedMaterials = mats;
                }
            }
#endif
        }

        private Dictionary<int, int> MergeChannels()
        {
            var dict = new Dictionary<int, int>();

            if (m_channelInfos.Count <= 4)
            {
                int i = 0;
                foreach (var (k, _) in m_channelInfos)
                    dict.Add(k, i++);
                return dict;
            }

            var data = new List<(float4, bool)>(m_channelInfos.Count);
            var keys = new List<int>(m_channelInfos.Keys);
            var vals = new List<ChannelInfo>(m_channelInfos.Values);
            foreach (var c in vals)
                data.Add((c.colour * c.blend, c.dh != null));
            
            var map = KMeansPartitioned(data);
            for (int i = 0; i < keys.Count; ++i)
                dict.Add(keys[i], map[i]);

            var channelGroups = new List<ChannelInfo>[] { new(), new(), new(), new() };
            for (int i = 0; i < m_channelInfos.Count; ++i)
                channelGroups[map[i]].Add(vals[i]);

            for (int channel = 0; channel < 4; ++channel)
            {
                var group = channelGroups[channel];
                if (group.Count == 0) continue;
                var (avgCol, avgBlend) = ChannelInfo.Average(group);
                foreach (var info in group)
                {
                    var copy = info;
                    copy.colour = avgCol;
                    copy.blend = avgBlend;
                    m_channelInfos[copy.smi * 4 + copy.channel] = copy;
                }
            }

            return dict;
        }

        private Dictionary<Material, Hash128> HashMats()
        {
            var matHashes = new Dictionary<Material, Hash128>();
            foreach (var (key, ind) in m_matInds)
            {
                var hash = new Hash128();
                DoForAll((_, maps, _) => hash.Append(maps[ind].imageContentsHash.ToString()));
                matHashes.Add(key, hash);
            }
            return matHashes;
        }

        private void StampAllMaps(List<Texture2D> _atlasses, int4x2[] _rects, int _matIndex,
            Dictionary<int, int> _channelMap)
        {
            DoForAll((type, maps, ind) =>
            {
                if (type == Me.m_tintMapType)
                {
                    var channelMap = new [] {-1, -1, -1, -1};
                    for (int i = 0; i < 4; ++i)
                    {
                        if (_channelMap.TryGetValue(_matIndex * 4 + i, out var channelToFill))
                            channelMap[i] = channelToFill;
                    }
                    CopyTextureChannels(maps[_matIndex], _atlasses[ind], _rects, channelMap);
                }
                else
                    CopyTexture(maps[_matIndex], _atlasses[ind], _rects);
            });
        }

        private List<Texture2D> CreateAtlasses(int _size)
        {
            var atlasses = new List<Texture2D>();
            DoForAll((type, _, _) =>
                atlasses.Add(new Texture2D(_size, _size, TextureFormat.RGBA32, false, type.TypeName == c_Normal)));
            return atlasses;
        }

        private void FillInAtlasses(Material _mat, List<Texture2D> _atlasses)
        {
            DoForAll((type, _, ind) => type.Set(_mat, _atlasses[ind]));
        }

        private void SaveAllAtlasses(List<Texture2D> _atlasses)
        {
            var paths = new List<string>();
            Directory.CreateDirectory(Me.atlasFolder);

            DoForAll((type, _, ind) => {
                var atlas = _atlasses[ind];
                var texPath = Path.Combine(Me.atlasFolder, $"{Me.atlasName}_{m_shaderName}_{type.TypeName}.png");
                paths.Add(texPath);

                atlas.wrapMode = TextureWrapMode.Clamp;
                atlas.Apply();
                File.WriteAllBytes(texPath, atlas.EncodeToPNG());
                Object.DestroyImmediate(atlas);
            });
            AssetDatabase.Refresh();

            for (int i = 0; i < paths.Count; i++)
            {
                var relativePath = GetRelativePath(paths[i]);
                var importer = (TextureImporter)AssetImporter.GetAtPath(relativePath);
                var type = m_allMapTypes[i];

                bool needsReimport = importer.wrapMode != TextureWrapMode.Clamp ||
                                     importer.sRGBTexture != (type.TypeName == c_Albedo) ||
                                     importer.maxTextureSize != 8192 ||
                                     importer.textureCompression != TextureImporterCompression.Compressed;

                if (!needsReimport) continue;
                
                TextureImporterSettings settings = new TextureImporterSettings();
                importer.ReadTextureSettings(settings);
                settings.wrapMode = TextureWrapMode.Clamp;
                settings.sRGBTexture = (type.TypeName == c_Albedo);
                importer.SetTextureSettings(settings);
                importer.maxTextureSize = 8192;
                importer.textureCompression = TextureImporterCompression.Compressed;
                importer.SaveAndReimport();
            }

            for (int i = 0; i < paths.Count; i++)
                _atlasses[i] = AssetDatabase.LoadAssetAtPath<Texture2D>(GetRelativePath(paths[i]));
        }
        
        private Texture2D GetReadableTexture(Texture2D _source, string _mapType)
        {
            if (_source == null) return null;
            if (_source.isReadable) return _source;

            var readWriteMode = _source.isDataSRGB ? RenderTextureReadWrite.sRGB : RenderTextureReadWrite.Linear;
            RenderTexture tmp =
                RenderTexture.GetTemporary(_source.width, _source.height, 0, RenderTextureFormat.ARGB32, readWriteMode);
            Graphics.Blit(_source, tmp);
            RenderTexture previous = RenderTexture.active;
            RenderTexture.active = tmp;
            Texture2D result = new Texture2D(_source.width, _source.height);
            result.ReadPixels(new Rect(0, 0, tmp.width, tmp.height), 0, 0);
            result.Apply();
            RenderTexture.active = previous;
            RenderTexture.ReleaseTemporary(tmp);
            if (_mapType == c_Normal)
                MakeNormalMapReadable(result);
            if (_mapType != c_Albedo && _source.isDataSRGB)
            {
                FromSRGB(result);
                Debug.LogError($"Map used as non-albedo {_source.name} is flagged as sRGB. If this is intended please speak to Cameron", _source);
            }
            else if (_mapType == c_Albedo && !_source.isDataSRGB)
            {
                ToSRGB(result);
                Debug.LogError($"Map used as albedo {_source.name} is flagged as linear. If this is intended please speak to Cameron", _source);
            }
            CheckTexName(_source, _mapType);
            return result;
        }
    
        private void CheckTexName(Texture2D _tex, string _type)
        {
            var texName = _tex.name;
            foreach (var type in m_allMapTypes)
            {
                var typeName = type.TypeName;
                if (typeName == _type)
                    continue;
                
                var str = type.CheckTex(texName);
                if (str == "")
                    continue;
                Debug.LogError($"Texture {texName} suggests it is a {typeName} type map but is being used as a {_type} type map!", _tex);
                break;
            }
        }
    }

    public static void Start(List<GameObject> _objs, string _matFolder, string _meshFolder, string _atlasFolder,
        string _atlasName = "Atlas")
    {
        Me = new MeshAtlasser
        {
            matFolder = _matFolder,
            meshFolder = _meshFolder,
            atlasFolder = _atlasFolder,
            atlasName = _atlasName
        };

        Me.AtlasPrefabs(_objs);
    }

    private void AtlasPrefabs(List<GameObject> objs)
    {
        var shaderMaps = new Dictionary<(Shader s, Hash128 h), MapBundle>();
        foreach (var obj in objs)
        {
            foreach (var meshFilter in obj.GetComponentsInChildren<MeshFilter>())
            {
                var mesh = meshFilter.sharedMesh;
                if (mesh == null)
                {
                    Debug.LogWarning($"{obj} has null mesh on {meshFilter}", obj);
                    continue;
                }
                
                bool skip = false;
                foreach (var comp in c_BlacklistedComponents)
                {
                    if (meshFilter.GetComponent(comp) != null)
                    {
                        Debug.Log($"Not batching {meshFilter.gameObject.name} as it has blacklisted component {comp.Name}");
                        skip = true;
                        break;
                    }
                }

                if (skip)
                    continue;

                Me.m_mFParents.Add(meshFilter, obj);
                var meshRenderer = meshFilter.GetComponent<MeshRenderer>();
                if (meshRenderer == null)
                {
                    Debug.LogWarning($"{obj} has null meshRenderer on {meshFilter.gameObject}", obj);
                    continue;
                }

                for (int i = 0; i < mesh.subMeshCount; ++i)
                {
                    var mat = meshRenderer.sharedMaterials[i];
                    if (mat == null)
                    {
                        Debug.LogWarning($"{obj} has null mat on {meshRenderer} submesh {i}", obj);
                        continue;
                    }

                    var shader = mat.shader;
                    var hash = new Hash128();
                    foreach (var shaderKeyword in mat.shaderKeywords)
                        hash.Append(shaderKeyword);
                    var key = (shader, hash);
                    if (!shaderMaps.TryGetValue(key, out var bundle))
                    {
                        bundle = new MapBundle(shader, hash);
                        shaderMaps.Add(key, bundle);
                    }

                    bundle.Add(mat, meshFilter, i);
                }
            }
        }

        foreach (var (_, bundle) in shaderMaps)
        {
            bundle.ResizeAllMaps();
            if (bundle.ProcessMeshes())
                bundle.GenerateAndApply(meshFolder);
        }
    }

    public enum FreeRectChoiceHeuristic
    {
        BestShortSideFit,
        BestLongSideFit,
        BestAreaFit,
        BottomLeftRule
    }

    [BurstCompile]
    public struct MaxRectsBinPack
    {
        public NativeList<int4> freeRectangles;

        public MaxRectsBinPack(int width, int height, Allocator allocator)
        {
            freeRectangles = new NativeList<int4>(allocator);
            freeRectangles.Add(new int4(0, 0, width - 1, height - 1));
        }

        public void Dispose()
        {
            if (freeRectangles.IsCreated)
                freeRectangles.Dispose();
        }

        public bool Insert(int width, int height, FreeRectChoiceHeuristic method, out int4 result)
        {
            int bestScore1 = int.MaxValue;
            int bestScore2 = int.MaxValue;
            int bestRectIndex = -1;
            result = 0;

            for (int i = 0; i < freeRectangles.Length; i++)
            {
                int currentWidth = freeRectangles[i].z - freeRectangles[i].x;
                int currentHeight = freeRectangles[i].w - freeRectangles[i].y;
                if (currentWidth >= width && currentHeight >= height)
                {
                    int score1 = 0, score2 = 0;
                    switch (method)
                    {
                        case FreeRectChoiceHeuristic.BestShortSideFit:
                        {
                            int leftoverHoriz = math.abs(currentWidth - width);
                            int leftoverVert = math.abs(currentHeight - height);
                            score1 = math.min(leftoverHoriz, leftoverVert);
                            score2 = math.max(leftoverHoriz, leftoverVert);
                            break;
                        }
                        case FreeRectChoiceHeuristic.BestLongSideFit:
                        {
                            int leftoverHoriz = math.abs(currentWidth - width);
                            int leftoverVert = math.abs(currentHeight - height);
                            score1 = math.max(leftoverHoriz, leftoverVert);
                            score2 = math.min(leftoverHoriz, leftoverVert);
                            break;
                        }
                        case FreeRectChoiceHeuristic.BestAreaFit:
                        {
                            score1 = (currentWidth * currentHeight) - (width * height);
                            score2 = 0;
                            break;
                        }
                        case FreeRectChoiceHeuristic.BottomLeftRule:
                        {
                            score1 = freeRectangles[i].y + height;
                            score2 = freeRectangles[i].x;
                            break;
                        }
                    }

                    if (score1 < bestScore1 || (score1 == bestScore1 && score2 < bestScore2))
                    {
                        result = new int4(freeRectangles[i].x, freeRectangles[i].y, freeRectangles[i].x + width,
                            freeRectangles[i].y + height);
                        bestScore1 = score1;
                        bestScore2 = score2;
                        bestRectIndex = i;
                    }
                }
            }

            if (bestRectIndex == -1)
                return false;

            PlaceRect(result);
            return true;
        }

        private void PlaceRect(int4 rect)
        {
            int count = freeRectangles.Length;
            for (int i = 0; i < count; i++)
            {
                if (SplitFreeNode(i, rect))
                {
                    freeRectangles[i--] = freeRectangles[^1];
                    freeRectangles.RemoveAt(freeRectangles.Length - 1);
                    count = freeRectangles.Length;
                }
            }
            PruneFreeList();
        }

        private bool SplitFreeNode(int index, int4 usedNode)
        {
            int4 freeNode = freeRectangles[index];
            if (usedNode.x >= freeNode.z || usedNode.z <= freeNode.x ||
                usedNode.y >= freeNode.w || usedNode.w <= freeNode.y)
                return false;

            if (usedNode.x < freeNode.z && usedNode.z > freeNode.x)
            {
                if (usedNode.y > freeNode.y && usedNode.y < freeNode.w)
                    freeRectangles.Add(new int4(freeNode.x, freeNode.y, freeNode.z, usedNode.y));

                if (usedNode.w < freeNode.w)
                    freeRectangles.Add(new int4(freeNode.x, usedNode.w, freeNode.z, freeNode.w));
            }

            if (usedNode.y < freeNode.w && usedNode.w > freeNode.y)
            {
                if (usedNode.x > freeNode.x && usedNode.x < freeNode.z)
                    freeRectangles.Add(new int4(freeNode.x, freeNode.y, usedNode.x, freeNode.w));

                if (usedNode.z < freeNode.z)
                    freeRectangles.Add(new int4(usedNode.z, freeNode.y, freeNode.z, freeNode.w));
            }

            return true;
        }

        private void PruneFreeList()
        {
            for (int i = 0; i < freeRectangles.Length; i++)
            {
                for (int j = i + 1; j < freeRectangles.Length; j++)
                {
                    if (IsContainedIn(freeRectangles[i], freeRectangles[j]))
                    {
                        freeRectangles[i] = freeRectangles[freeRectangles.Length - 1];
                        freeRectangles.RemoveAt(freeRectangles.Length - 1);
                        i--;
                        break;
                    }

                    if (IsContainedIn(freeRectangles[j], freeRectangles[i]))
                    {
                        freeRectangles[j] = freeRectangles[freeRectangles.Length - 1];
                        freeRectangles.RemoveAt(freeRectangles.Length - 1);
                        j--;
                    }
                }
            }
        }

        private bool IsContainedIn(int4 a, int4 b)
        {
            return a.x >= b.x && a.y >= b.y && a.z <= b.z && a.w <= b.w;
        }
    }
    
    public class RectItem
    {
        public int SubmeshIndex;
        public int4 OriginalRect;
        public List<TriData> Triangles;
    }

    private static (int4x2[][], TriData[][]) BoxPack(List<List<(int4, List<TriData>)>> subMeshRects, out int size)
    {
        int subMeshCount = subMeshRects.Count;
        int totalArea = 0;
        List<RectItem> items = new List<RectItem>();

        for (int i = 0; i < subMeshCount; i++)
        {
            foreach (var (rect, tris) in subMeshRects[i])
            {
                items.Add(new RectItem { SubmeshIndex = i, OriginalRect = rect, Triangles = tris });
                totalArea += AreaOf(rect);
            }
        }

        items.Sort((a, b) => AreaOf(b.OriginalRect).CompareTo(AreaOf(a.OriginalRect)));

        for (size = 256; size <= 16384; size <<= 1)
        {
            if (size * size < totalArea)
                continue;

            var inputRects = new List<int4>(items.Count);
            foreach (var t in items)
                inputRects.Add(t.OriginalRect);

            var outputRects = RunBoxPackJob(inputRects, size, FreeRectChoiceHeuristic.BestShortSideFit);
            bool failed = outputRects.Count < inputRects.Count;

            if (!failed)
            {
                int4x2[][] outRects = new int4x2[subMeshCount][];
                TriData[][] outTris = new TriData[subMeshCount][];
                List<List<int4x2>> rectsList = new List<List<int4x2>>();
                List<List<TriData>> trisList = new List<List<TriData>>();
                for (int i = 0; i < subMeshCount; i++)
                {
                    rectsList.Add(new List<int4x2>());
                    trisList.Add(new List<TriData>());
                }

                for (int i = 0; i < items.Count; i++)
                {
                    RectItem item = items[i];
                    int4 origRect = item.OriginalRect;
                    int4 newBox = outputRects[i];

                    foreach (var tri in item.Triangles)
                    {
                        tri.TransformUVs(origRect, newBox, size);
                        trisList[item.SubmeshIndex].Add(tri);
                    }
                    rectsList[item.SubmeshIndex].Add(new int4x2(origRect, newBox));
                }
                for (int i = 0; i < subMeshCount; i++)
                {
                    outRects[i] = rectsList[i].ToArray();
                    outTris[i] = trisList[i].ToArray();
                }
                return (outRects, outTris);
            }
        }
        size = -1;
        return (null, null);
    }

    private static float Efficiency(int4 a, int4 b) => AreaOf(a) + AreaOf(b) - AreaOf(TotalBounds(a, b));
    private static int WidthOf(int4 a) => a.z - a.x;
    private static int HeightOf(int4 a) => a.w - a.y;
    private static int AreaOf(int4 a) => WidthOf(a) * HeightOf(a);
    private static int4 TotalBounds(int4 a, int4 b) => new(math.min(a.xy, b.xy), math.max(a.zw, b.zw));

    [BurstCompile]
    public struct TriData
    {
        const int c_borderSize = 4;

        public int triID;
        public int meshID;

        private int2 size;

        private float2 vert1;
        public float2 Vert1 => vert1 / size; //privates are in pixel space, getters return in uv space
        private float2 vert2;
        public float2 Vert2 => vert2 / size;
        private float2 vert3;
        public float2 Vert3 => vert3 / size;
        public int2 GetMinPixel => math.max((int2)math.min(vert1, math.min(vert2, vert3)) - c_borderSize, 0);
        public int2 GetMaxPixel => math.min((int2)math.max(vert1, math.max(vert2, vert3)) + c_borderSize, size - 1);

        public TriData(float2 uv0, float2 uv1, float2 uv2, int tri, int mesh, int2 texSize)
        {
            size = texSize;
            triID = tri;
            meshID = mesh;
            vert1 = uv0 * size;
            vert2 = uv1 * size;
            vert3 = uv2 * size;
        }

        public void TransformUVs(int4 oldBox, int4 newBox, int newMatSize)
        {
            size = newMatSize;

            var oldPos = (float2)oldBox.xy;
            var newPos = (float2)newBox.xy;
            var translation = newPos - oldPos;
            vert1 += translation;
            vert2 += translation;
            vert3 += translation;

            // Original condition was checking if widths are the same, but this is backwards
            // The texture gets rotated when widths are DIFFERENT (flipped = oldWidth != newWidth)
            // So we should rotate UVs when widths are DIFFERENT, not when they're the same
            if (newBox.z - newBox.x != oldBox.z - oldBox.x)
            {
                // Texture was rotated 90 degrees in the atlas, so UVs need to be rotated to match
                vert1 = (vert1 - newPos).yx + newPos;
                vert2 = (vert2 - newPos).yx + newPos;
                vert3 = (vert3 - newPos).yx + newPos;
            }
        }
    }
}
