using UnityEngine;
using UnityEditor;
public class MenuItemHelper : EditorWindow
{
	[MenuItem("GameObject/Focus In-Game Camera on Scene Selection")]
	static void ViewWithInGameCamera()
	{
		if (Application.isPlaying && GameManager.Me != null)
		{
			//EditorApplication.ExecuteMenuItem("Window/Hierarchy")
			var sel = Selection.activeGameObject;
			if (sel != null)
			{
				var pos = sel.transform.position.GroundPosition();
				if (pos.y < GlobalData.c_seaLevel)
				{
					pos.y = GlobalData.c_seaLevel;
				}
				GameManager.Me.FocusCamera(pos);
			}
		}
	}
}
