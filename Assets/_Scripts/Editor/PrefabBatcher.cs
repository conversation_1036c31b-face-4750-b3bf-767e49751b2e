using System;
using System.Collections.Generic;
using System.IO;
using JBooth.MicroSplat;
using TMPro;
using UnityEngine;
using UnityEditor;
using UnityEditor.Animations;
using Object = UnityEngine.Object;
using PropertyOverride = MaterialPropertyOverride.PropertyOverride;
using FM = MeshUtils.FlexibleMesh;
using static KitBashOptimiseManager;

public class PrefabBatcher
{
    private static readonly Type[] c_BlacklistedComponents = { typeof(TextMeshPro) };
    
    private struct DictList<T>
    {
        public List<T> Contents;
        
        public DictList(List<T> _contents)
        {
            Contents = _contents;
        }
        
        public override int GetHashCode()
        {
            int hash = 0;
            foreach (var item in Contents)
                hash ^= item.GetHashCode();
            return hash;
        }

        public override bool Equals(object obj)
        {
            if (obj is not DictList<T> other || other.Contents.Count != Contents.Count)
                return false;
            foreach (var item in Contents)
            {
                if (!Contents.Contains(item))
                    return false;
            }
            return true;
        }
        
        public static bool operator ==(DictList<T> _a, DictList<T> _b) => _a.Equals(_b);
        public static bool operator !=(DictList<T> _a, DictList<T> _b) => !_a.Equals(_b);
    }
    
    private struct MatInstance
    {
        private Material m_baseMat; public Material BaseMat => m_baseMat;
        private bool m_hasOverride;
        private List<PropertyOverride> m_overrides;
        private DictList<int> m_batchID;

        public MatInstance(MeshRenderer _mr, int _sMI)
        {
            m_baseMat = _mr.sharedMaterials[_sMI];
            var mpo = _mr.GetComponents<MaterialPropertyOverride>().Find(t => t.m_subMeshIndex == _sMI);
            m_hasOverride = mpo != null;
            m_overrides = mpo?.m_materialPropertyOverrides;
            m_batchID = new DictList<int>(new());
        }

        public void ClearMPO()
        {
            m_hasOverride = false;
            m_overrides = null;
        }

        public void AddBatchId(int groupID)
        {
            var intToUse = groupID >> 5; // div 32
            while (intToUse >= m_batchID.Contents.Count)
                m_batchID.Contents.Add(0);
            m_batchID.Contents[intToUse] |= 1 << (groupID & 31);
        }
        
        public void RemoveBatchID(int groupID)
        {
            var intToUse = groupID >> 5; // div 32
            if (intToUse >= m_batchID.Contents.Count)
                return;
            m_batchID.Contents[intToUse] &= ~(1 << (groupID & 31));
        }

        public void ClearBatchID()
        {
            m_batchID.Contents = new();
        }

        public override bool Equals(object obj)
        {
            var other = (MatInstance)obj;
            return Equivalent(this, other);
        }

        private static bool Equivalent(MatInstance _a, MatInstance _b)
        {
            if (_a.m_baseMat != _b.m_baseMat)
                return false;
            if (_a.m_batchID != _b.m_batchID)
                return false;
            if (_a.m_overrides == null)
                return _b.m_overrides == null;
            if (_b.m_overrides == null)
                return false;
            if (_a.m_overrides.Count != _b.m_overrides.Count)
                return false;
            for (int i = 0; i < _a.m_overrides.Count; ++i)
            {
                var overrideA = _a.m_overrides[i];
                var overrideB = _b.m_overrides[i];
                if (overrideA.m_name != overrideB.m_name)
                    return false;
                if (!PropertyOverride.IsEq(overrideA.m_overrideC, overrideB.m_overrideC))
                    return false;
                if (!PropertyOverride.IsEq(overrideA.m_overrideF, overrideB.m_overrideF))
                    return false;
                if (overrideA.m_overrideI != overrideB.m_overrideI)
                    return false;
                if (!PropertyOverride.IsEq(overrideA.m_overrideV, overrideB.m_overrideV))
                    return false;
            }
            return true;
        }

        public override int GetHashCode()
        {
            int hash = m_baseMat.GetHashCode();
            hash ^= m_batchID.GetHashCode();
            if (m_overrides != null)
            {
                foreach (var prop in m_overrides)
                {
                    int propID = prop.m_name.GetHashCode();
                    hash ^= propID * GetVectorHashCode(prop.m_overrideC);
                    hash ^= propID * GetFloatHashCode(prop.m_overrideF);
                    hash ^= propID * prop.m_overrideI;
                    hash ^= propID * GetVectorHashCode(prop.m_overrideV);
                }
            }
            return hash;
        }

        private int GetVectorHashCode(Vector4 _v)
        {
            return GetFloatHashCode(_v.x) ^ GetFloatHashCode(_v.y) << 2 ^ GetFloatHashCode(_v.z) >> 2 ^ GetFloatHashCode(_v.w) >> 1;
        }

        private int GetFloatHashCode(float _f)
        {
            if (float.IsNaN(_f))
                return float.NaN.GetHashCode();
            if (float.IsPositiveInfinity(_f))
                return float.PositiveInfinity.GetHashCode();
            if (float.IsNegativeInfinity(_f))
                return float.NegativeInfinity.GetHashCode();
            return _f.GetHashCode();
        }

        public void RecreateOverride(GameObject _holder, int _subMeshIndex)
        {
            if (m_hasOverride)
            {
                var mpo = _holder.AddComponent<MaterialPropertyOverride>();
                mpo.m_subMeshIndex = _subMeshIndex;
                mpo.m_materialPropertyOverrides = m_overrides;
            }
        }
    }

    public static Dictionary<GameObject, PrefabBatcher> Batchers;

    private int OriginalRends;
    private int NewRends;
    private Dictionary<Component, List<List<Transform>>> Partitions;
    private List<(MatInstance, Transform)> OriginalMatInstances;
    private Dictionary<DictList<Transform>, int> GroupIDs;
    private int NumShaders;
    private int NumMaterials;
    
    private KitBashOptimiseManager m_manager;

    public static (int, int) GetRendererCount(GameObject _prefab)
    {
        var batcher = Batchers[_prefab];
        return (batcher.OriginalRends, batcher.NewRends);
    }

    public static void GetStatReport(GameObject _prefab, ReportHelper _rep)
    {
        var batcher = Batchers[_prefab];
        _rep.AppendLine($"Original MeshRenderers: {batcher.OriginalRends}");
        _rep.AppendLine($"New MeshRenderers: {batcher.NewRends}");
        _rep.BulletLevel++;
        _rep.AppendLine($"Number of shaders (incl. variants): {batcher.NumShaders}");
        _rep.AppendLine($"Number of materials: {batcher.NumMaterials}");
        _rep.AppendLine($"Number of components that move or disable renderers: {batcher.Partitions.Count}");
        _rep.BulletLevel--;
    }

    public static void GetDetailedStatReport(GameObject _prefab, ReportHelper _rep)
    {
        var batcher = Batchers[_prefab];
        var componentSplits = batcher.CalculateSplits(batcher.OriginalMatInstances, batcher.GroupIDs);
        if (componentSplits.Count > 0)
        {
            _rep.AppendLine("Components that split up renderers within the batch (these do not necessarily add up):");
            componentSplits.Sort((x, y) => y.Item2.CompareTo(x.Item2));
            _rep.BulletLevel++;
            foreach (var (component, direct, indirect) in componentSplits)
            {
                //Currently not using indirect as it doesn't really make much sense
                _rep.AppendLine(
                    $"{component.GetType()} \"{component.gameObject.name}\" directly resulted in {direct} extra renderer{(direct > 1 ? "s" : "")}.");
            }

            _rep.BulletLevel--;
        }
    }

    public static void Run(GameObject _prefab, KitBashOptimiseManager _manager)
    {
        var batcher = new PrefabBatcher();
        Batchers.Add(_prefab, batcher);
        
        batcher.m_manager = _manager;
        batcher.Partitions = new();
        batcher.BatchPrefab(_prefab);
    }

    public void BatchPrefab(GameObject _prefab)
    {
        var meshPath = m_manager.GetMeshPath(_prefab);
        if (File.Exists(meshPath))
            File.Delete(meshPath);

        OriginalMatInstances = new();
        var matInstances = GetMatInstances(_prefab, out NumShaders, out NumMaterials);

        GroupIDs = new Dictionary<DictList<Transform>, int>();
        
        var animators = _prefab.GetComponentsInChildren<Animator>(true);
        foreach (var animator in animators)
        {
            var groups = FindTransformsAffectedByController(animator.gameObject).ConvertAll(x => new List<Transform> { x });
            Partitions.Add(animator, new List<List<Transform>>());
            foreach (var group in groups)
                AddGroup(animator, group, GroupIDs, matInstances);
        }
        
        var blendables = _prefab.GetComponentsInChildren<MicroSplatBlendableObject>(true);
        foreach (var blendable in blendables)
        {
            Partitions.Add(blendable, new List<List<Transform>>());
            AddGroup(blendable, new() { blendable.transform }, GroupIDs, matInstances);
        }

        foreach (var excluder in _prefab.GetComponentsInChildren<IBatchPartitioner>(true))
        {
            var groups = excluder.GetExcludedTransforms();
            var component = excluder.Component();
            Partitions.Add(component, new List<List<Transform>>());
            foreach (var group in groups)
                AddGroup(component, group, GroupIDs, matInstances);
        }

        var matCounts = CountMats(matInstances);

        var (materialMeshMap, dhMeshes) = CollectMatMeshes(meshPath, matInstances, matCounts);

        CreateNewMeshes(materialMeshMap, dhMeshes, meshPath);
    }

    private bool GetAllValidSubMeshes(Renderer _renderer, out List<int> _validSubmeshes)
    {
        _validSubmeshes = new();
        foreach (var comp in c_BlacklistedComponents)
        {
            if (_renderer.GetComponent(comp) != null)
            {
                Debug.Log($"Not batching {_renderer} as it has blacklisted component {comp.Name}");
                return false;
            }
        }
        
        var filter = _renderer.GetComponent<MeshFilter>();
        var mesh = filter?.sharedMesh;
        if (mesh == null)
        {
            Debug.LogWarning($"{_renderer} has null mesh!");
            return false;
        }

        if (mesh.uv.Length < mesh.vertexCount)
        {
            Debug.Log($"{_renderer} has mesh {mesh} with no UVs, skipping");
            return false;
        }
        
        for (int j = 0; j < mesh.subMeshCount; ++j)
        {
            var mat = _renderer.sharedMaterials[j];
            if (mat == null)
            {
                Debug.LogWarning($"{_renderer} has null mat on submesh {j}");
                continue;
            }
            _validSubmeshes.Add(j);
        }
        return true;
    }

    private Dictionary<(MeshRenderer, int), MatInstance> GetMatInstances(GameObject _prefab, out int _shaders, out int _mats)
    {
        var shaders = new HashSet<Material>();
        var mats = new HashSet<MatInstance>();
        
        var matInstances = new Dictionary<(MeshRenderer, int), MatInstance>();
        var rends = _prefab.GetComponentsInChildren<MeshRenderer>(true);
        NewRends = OriginalRends = rends.Length;
        foreach (var mr in rends)
        {
            if (GetAllValidSubMeshes(mr, out var smIs))
            {
                foreach (var j in smIs)
                {
                    var mat = mr.sharedMaterials[j];
                    shaders.Add(mat);
                    var matInst = new MatInstance(mr, j);
                    mats.Add(matInst);
                    OriginalMatInstances.Add((matInst, mr.transform));
                    matInstances.Add((mr, j), matInst);
                }
            }
        }
        _shaders = shaders.Count;
        _mats = mats.Count;
        return matInstances;
    }

    private void AddGroup(Component _excluder, List<Transform> _transforms, 
        Dictionary<DictList<Transform>, int> groupIDs, Dictionary<(MeshRenderer, int), MatInstance> matInstances)
    {
        _transforms = _transforms.FindAll(x => x != null);
        if (_transforms.Count == 0)
            return;
        var dictList = new DictList<Transform>(_transforms);
        if (groupIDs.ContainsKey(dictList))
            return;
        Partitions[_excluder].Add(_transforms);
        var key = groupIDs.Count;
        groupIDs.Add(dictList, key);
        foreach (var transform in _transforms)
        {
            var rends = transform.GetComponentsInChildren<MeshRenderer>(true);
            foreach (var rend in rends)
            {
                if (GetAllValidSubMeshes(rend, out var smIs))
                {
                    foreach (var smI in smIs)
                        matInstances[(rend, smI)].AddBatchId(key);
                }
            }
        }
    }

    private Dictionary<MatInstance, int> CountMats(Dictionary<(MeshRenderer, int), MatInstance> _matInsts)
    {
        var matCounts = new Dictionary<MatInstance, int>();
        foreach (var (_, matInst) in _matInsts)
        {
            if (!matCounts.TryAdd(matInst, 1))
                matCounts[matInst]++;
        }
        return matCounts;
    }

    private (Dictionary<MatInstance, (Transform, FM)>, Dictionary<FM, List<DecorationHolder>>) CollectMatMeshes(
        string _meshPath, Dictionary<(MeshRenderer, int), MatInstance> _matInsts, Dictionary<MatInstance, int> _matCounts)
    {
        var materialMeshMap = new Dictionary<MatInstance, (Transform, FM)>();
        var dhMeshes = new Dictionary<FM, List<DecorationHolder>>();
        foreach (var ((mr, _), matInstance) in _matInsts)
        {
            if (mr == null)
                continue;
            if (_matCounts[matInstance] <= 1)
                continue;
            var mf = mr.GetComponent<MeshFilter>();
            var mesh = mf.sharedMesh;
            var dh = mr.GetComponentInParent<DecorationHolder>();
            for (int j = 0; j < mesh.subMeshCount; ++j)
            {
                var mat = mr.sharedMaterials[j];
                if (mat == null)
                    continue;
                var matInst = _matInsts[(mr, j)];
                materialMeshMap.TryAdd(matInst, (mr.transform, new FM()));
                var (target, fM) = materialMeshMap[matInst];
                var matrix = target.worldToLocalMatrix * mr.transform.localToWorldMatrix;
                fM.AddMesh(mesh, matrix, false);
                if (dh == null) 
                    continue;
                if (!dhMeshes.TryGetValue(fM, out var list))
                {
                    list = new List<DecorationHolder>();
                    dhMeshes.Add(fM, list);
                }
                list.AddUnique(dh);
            }

            NewRends--;
            foreach (var mpo in mr.GetComponents<MaterialPropertyOverride>())
                Object.DestroyImmediate(mpo);
            Object.DestroyImmediate(mr);
            Object.DestroyImmediate(mf);
        }
        foreach (var ((mr, _), _) in _matInsts) //Make sure stragglers are saved
        {
            if (mr == null)
                continue;
            var mf = mr.GetComponent<MeshFilter>();
            var mesh = mf.sharedMesh;
            var meshPath = Path.Combine(_meshPath, $"Mesh{mf.GetInstanceID()}.asset");
            if (!AssetDatabase.Contains(mesh))
                SaveAsset(mesh, meshPath);
        }
        return (materialMeshMap, dhMeshes);
    }

    private void CreateNewMeshes(Dictionary<MatInstance, (Transform, FM)> _materialMeshMap,
        Dictionary<FM, List<DecorationHolder>> _dhMeshMap, string _meshPath)
    {
        foreach (var (mI, (xForm, fM)) in _materialMeshMap)
        {
            var mat = mI.BaseMat;
            var pathSafeName = string.Join("_", mat.name.Split(Path.GetInvalidPathChars()));

            var holder = xForm.gameObject;
            if (holder.GetComponent<MeshRenderer>() != null)
            {
                holder = new GameObject($"Combined mesh: {pathSafeName}");
                holder.transform.SetParent(xForm);
            }

            fM.Optimise();
            var mesh = fM.Mesh();

            NewRends++;
            var mf = holder.AddComponent<MeshFilter>();

            var meshPath = Path.Combine(_meshPath, $"Mesh{mf.GetInstanceID()}.asset");
            SaveAsset(mesh, meshPath);
            mf.sharedMesh = mesh;
            var mr = holder.AddComponent<MeshRenderer>();
            mr.sharedMaterial = mat;
            mI.RecreateOverride(holder, 0);
            if (!_dhMeshMap.TryGetValue(fM, out var dhs)) 
                continue;
            var myDH = mr.GetComponentInParent<DecorationHolder>();
            foreach (var dh in dhs)
            {
                if (dh == myDH)
                    continue;
                dh.m_externalMeshRenderers.AddUnique(mr);
            }
        }
    }

    private List<(Component, int, int)> CalculateSplits(List<(MatInstance, Transform)> _matInsts, Dictionary<DictList<Transform>, int> _groupIDs)
    {
        var current = new HashSet<MatInstance>(_matInsts.ConvertAll(x => x.Item1)).Count;
        var partitionsDirect = new List<(Component, int, int)>();
        foreach (var (component, groups) in Partitions)
        {
            var matInstsCopy = new List<(MatInstance, Transform)>(_matInsts);
            foreach (var group in groups)
            {
                var groupID = _groupIDs[new DictList<Transform>(group)];
                for (int i = 0; i < matInstsCopy.Count; ++i)
                {
                    var (matInst, t) = matInstsCopy[i];
                    matInst.RemoveBatchID(groupID);
                    matInstsCopy[i] = (matInst, t);
                }
            }
            var direct = current - new HashSet<MatInstance>(matInstsCopy.ConvertAll(x => x.Item1)).Count;
            
            for (int i = 0; i < matInstsCopy.Count; ++i)
            {
                var (matInst, t) = matInstsCopy[i];
                matInst.ClearBatchID();
                matInstsCopy[i] = (matInst, t);
            }
            var min = new HashSet<MatInstance>(matInstsCopy.ConvertAll(x => x.Item1)).Count;
            foreach (var group in groups)
            {
                var groupID = _groupIDs[new DictList<Transform>(group)];
                foreach (var (matInst, t) in matInstsCopy)
                {
                    foreach (var transform in group)
                    {
                        if (!t.IsChildOf(transform)) 
                            continue;
                        matInst.AddBatchId(groupID);
                        break;
                    }
                }
            }
            var indirect = new HashSet<MatInstance>(matInstsCopy.ConvertAll(x => x.Item1)).Count - min;
            if (direct > 0)
                partitionsDirect.Add((component, direct, indirect));
        }
        return partitionsDirect;
    }
    
    public static List<Transform> FindTransformsAffectedByController(GameObject _anim)
    {
        var aC = _anim.GetComponent<Animator>().runtimeAnimatorController as AnimatorController;
        if (aC == null)
        {
            Debug.LogWarning($"Animator on {_anim} has no controller!");
            return new();
        }

        var transforms = new HashSet<Transform>();
        foreach (AnimationClip clip in GetAllClips(aC))
            ProcessClip(clip, _anim.transform, transforms);
        return new List<Transform>(transforms);
    }

    private static List<AnimationClip> GetAllClips(AnimatorController controller)
    {
        var clips = new List<AnimationClip>();
        foreach (var layer in controller.layers)
            TraverseStateMachine(layer.stateMachine, clips);
        return clips;
    }

    private static void TraverseStateMachine(AnimatorStateMachine stateMachine, List<AnimationClip> clips)
    {
        foreach (var state in stateMachine.states)
            ProcessMotion(state.state.motion, clips);
        foreach (var subStateMachine in stateMachine.stateMachines)
            TraverseStateMachine(subStateMachine.stateMachine, clips);
    }

    private static void ProcessMotion(Motion motion, List<AnimationClip> clips)
    {
        if (motion is AnimationClip clip)
        {
            if (!clips.Contains(clip)) 
                clips.Add(clip);
        }
        else if (motion is BlendTree blendTree)
        {
            foreach (ChildMotion child in blendTree.children)
                ProcessMotion(child.motion, clips);
        }
    }

    private static void ProcessClip(AnimationClip clip, Transform root, HashSet<Transform> transforms)
    {
        foreach (var binding in AnimationUtility.GetCurveBindings(clip))
        {
            if (binding.type == typeof(Transform))
            {
                Transform target = FindTransform(root, binding.path);
                if (target != null) transforms.Add(target);
            }
        }
    }

    private static Transform FindTransform(Transform root, string path)
    {
        if (string.IsNullOrEmpty(path)) 
            return root;
        Transform result = root.Find(path);
        if (result == null)
            Debug.LogWarning($"Animator \"{root.name}\" references non-existent transform: {path}");
        return result;
    }
}