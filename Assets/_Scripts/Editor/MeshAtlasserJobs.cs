using System;
using System.Collections.Generic;
using System.Linq;
using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Rendering;
using Random = Unity.Mathematics.Random;

public partial class MeshAtlasser
{
    private static List<TriData> GetMeshData(Mesh _mesh, int _subMeshIndex, int2 _size, int _meshID, out bool _skip)
    {
        if (_mesh.uv.Length == 0)
        {
            _skip = true;
            return new List<TriData>();
        }
        var meshJob = new MeshDataGetter(_mesh, _subMeshIndex, _size, _meshID);
        meshJob.Schedule().Complete();
        var output = new List<TriData>();
        meshJob.FillOutput(output, out _skip);
        return output;
    }

    [BurstCompile]
    private struct MeshDataGetter : IJob
    {
        private NativeArray<bool> m_hadBadUVs;
        [ReadOnly] private NativeArray<int> m_inds;
        [ReadOnly] private int m_subMeshIndex;
        [ReadOnly] private NativeArray<float2> m_uvs;
        [ReadOnly] private int2 m_texSize;
        [ReadOnly] private int m_meshID;
        
        [WriteOnly] private NativeArray<TriData> m_triData;
        
        public MeshDataGetter(Mesh mesh, int subMeshIndex, int2 texSize, int meshID)
        {
            m_subMeshIndex = subMeshIndex;
            m_texSize = texSize;
            m_meshID = meshID;
            
            var uvArray = mesh.uv;
            var indsArray = mesh.GetIndices(m_subMeshIndex);
            
            m_uvs = new NativeArray<Vector2>(uvArray, Allocator.TempJob).Reinterpret<float2>();
            
            m_triData = new NativeArray<TriData>(indsArray.Length / 3, Allocator.TempJob);
            
            m_inds = new NativeArray<int>(indsArray, Allocator.TempJob);
            
            m_hadBadUVs = new NativeArray<bool>(1, Allocator.TempJob);
        }

        public void Execute()
        {
            bool hasBadUVs = false;
            for (int i = 0; i < m_triData.Length; ++i)
            {
                var i0 = m_inds[i * 3 + 0];
                var i1 = m_inds[i * 3 + 1];
                var i2 = m_inds[i * 3 + 2];
                hasBadUVs |= m_uvs[i0].x < 0 || m_uvs[i0].y < 0 || m_uvs[i0].x > 1 || m_uvs[i0].y > 1;
                hasBadUVs |= m_uvs[i1].x < 0 || m_uvs[i1].y < 0 || m_uvs[i1].x > 1 || m_uvs[i1].y > 1;
                hasBadUVs |= m_uvs[i2].x < 0 || m_uvs[i2].y < 0 || m_uvs[i2].x > 1 || m_uvs[i2].y > 1;
                m_triData[i] = new TriData(m_uvs[i0], m_uvs[i1], m_uvs[i2], i, m_meshID, m_texSize);
            }
            m_hadBadUVs[0] = hasBadUVs;
        }

        public void FillOutput(List<TriData> _output, out bool _badUVs)
        {
            var oldLength = _output.Count;
            var length = oldLength + m_triData.Length;
            _output.Capacity = length;
            var arr = _output.GetBackingArray();
            NativeArray<TriData>.Copy(m_triData, 0, arr, oldLength, m_triData.Length);
            _output.SetContent(arr);
            _badUVs = m_hadBadUVs[0];
            
            m_triData.Dispose();
            m_uvs.Dispose();
            m_inds.Dispose();
            m_hadBadUVs.Dispose();
        }
    }

    private static Texture2D UpscaleTexture(Texture2D _tex, int2 _targetSize)
    {
        var upscale = _targetSize / new int2(_tex.width, _tex.height);
        if (math.all(upscale == 1))
            return _tex;
        var newTex = new Texture2D(_targetSize.x, _targetSize.y, TextureFormat.RGBA32, false);
        var textureJob = new UpscaleTextureJob(_tex, newTex, upscale);
        textureJob.Schedule(_targetSize.x * _targetSize.y, 64).Complete();
        newTex.Apply();
        UnityEngine.Object.DestroyImmediate(_tex);
        return newTex;
    }

    [BurstCompile]
    private struct UpscaleTextureJob : IJobParallelFor
    {
        [ReadOnly] private NativeArray<uint> source;
        [ReadOnly] private int sourceWidth;
        [ReadOnly] private int2 scale;
        
        [WriteOnly] private NativeArray<uint> dest;
        [ReadOnly] private int destWidth;
        
        public UpscaleTextureJob(Texture2D _source, Texture2D _dest, int2 _scale)
        {
            source = _source.GetRawTextureData<uint>();
            sourceWidth = _source.width;
            scale = _scale;
            dest = _dest.GetRawTextureData<uint>();
            destWidth = _dest.width;
        }

        public void Execute(int i)
        {
            var coord = new int2(i % destWidth, i / destWidth);
            var oldCoord = coord / scale;
            dest[i] = source[oldCoord.y * sourceWidth + oldCoord.x];
        }
    }

    private static void MakeNormalMapReadable(Texture2D _tex)
    {
        var job = new MakeNormalMapReadableJob(_tex);
        job.Schedule(_tex.width * _tex.height, 64).Complete();
        _tex.Apply();
    }
    
    [BurstCompile]
    private struct MakeNormalMapReadableJob : IJobParallelFor
    {
        private NativeArray<Color32> m_tex;
        
        public MakeNormalMapReadableJob(Texture2D _tex)
        {
            m_tex = _tex.GetRawTextureData<Color32>();
        }

        public void Execute(int i)
        {
            ref Color32 clr = ref m_tex.MutableRefAt(i);
            var nrm = new float3(clr.a / 127.5f - 1, clr.g / 127.5f - 1, 0);
            nrm.z = math.sqrt(1 - nrm.x * nrm.x - nrm.y * nrm.y);
            clr = new Color32((byte)(int)(nrm.x * 127.5f + 127.5f), (byte)(int)(nrm.y * 127.5f + 127.5f), (byte)(int)(nrm.z * 127.5f + 127.5f), 1);
        }
    }

    private static void ToSRGB(Texture2D _tex)
    {
        var job = new ConvertSRGBJob(_tex, false);
        job.Schedule(_tex.width * _tex.height, 64).Complete();
        _tex.Apply();
    }
    
    private static void FromSRGB(Texture2D _tex)
    {
        var job = new ConvertSRGBJob(_tex, true);
        job.Schedule(_tex.width * _tex.height, 64).Complete();
        _tex.Apply();
    }
    
    [BurstCompile]
    private struct ConvertSRGBJob : IJobParallelFor
    {
        [ReadOnly] private readonly float convPow;
        private NativeArray<Color32> m_tex;
        
        public ConvertSRGBJob(Texture2D _tex, bool _toLinear)
        {
            convPow = _toLinear ? 2.2f : 1f / 2.2f;
            m_tex = _tex.GetRawTextureData<Color32>();
        }

        public void Execute(int i)
        {
            ref Color32 clr = ref m_tex.MutableRefAt(i);
            var rgb = new float3(clr.r, clr.g, clr.b) / 255f;
            rgb = math.pow(rgb, convPow) * 255f;
            clr = new Color32((byte)rgb.x, (byte)rgb.y, (byte)rgb.z, clr.a);
        }
    }
    
    public static List<int4> RunBoxPackJob(List<int4> rects, int binSize, FreeRectChoiceHeuristic heuristic)
    {
        BoxPackJob job = new BoxPackJob(rects, binSize, heuristic);
        job.Schedule().Complete();
        return job.GetResults();
    }
    
    
    [BurstCompile]
    public struct BoxPackJob : IJob
    {
        private int binSize;
        private FreeRectChoiceHeuristic heuristic;
        
        [ReadOnly] private NativeArray<int4> inputRects;
        private NativeArray<int4> packedRects;
        private NativeArray<int> packedCount;

        public BoxPackJob(List<int4> _inputList, int _binSize, FreeRectChoiceHeuristic _heuristic)
        {
            binSize = _binSize;
            heuristic = _heuristic;
            inputRects = new NativeArray<int4>(_inputList.ToArray(), Allocator.TempJob);
            packedRects = new NativeArray<int4>(_inputList.Count, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
            packedCount = new NativeArray<int>(1, Allocator.TempJob);
        }
        
        public void Execute()
        {
            var packer = new MaxRectsBinPack(binSize, binSize, Allocator.Temp);
            int i;
            for (i = 0; i < inputRects.Length; ++i)
            {
                int width = inputRects[i].z - inputRects[i].x;
                int height = inputRects[i].w - inputRects[i].y;
                if (packer.Insert(width, height, heuristic, out var newBox))
                    packedRects[i] = newBox;
                else
                    break;
            }
            packedCount[0] = i;
            packer.Dispose();
        }
        
        public List<int4> GetResults()
        {
            int count = packedCount[0];
            var results = new List<int4>(count);
            for (int i = 0; i < count; i++)
                results.Add(packedRects[i]);
            
            packedRects.Dispose();
            inputRects.Dispose();
            packedCount.Dispose();
            return results;
        }
    }
    
    private static void DrawTrianglesToTexture(Texture2D _tex, Color _col, TriData[] _tris)
    {
        var job = new DrawTrianglesToTextureJob(_tex, _col, _tris);
        job.Schedule(_tris.Length, 64).Complete();
        job.Dispose();
    }

    [BurstCompile]
    private struct DrawTrianglesToTextureJob : IJobParallelFor
    {
        [ReadOnly] private NativeArray<TriData> m_tris;
        [NativeDisableParallelForRestriction] [WriteOnly] private NativeArray<Color32> m_output;
        [ReadOnly] private Color32 m_colour;
        [ReadOnly] private int2 m_size;
        
        public DrawTrianglesToTextureJob(Texture2D _tex, Color _col, TriData[] _tris)
        {
            m_tris = new NativeArray<TriData>(_tris, Allocator.TempJob);
            m_output = _tex.GetRawTextureData<Color32>();
            m_colour = _col;
            m_size = new (_tex.width, _tex.height);
        }

        public void Execute(int index)
        {
            var tri = m_tris[index];
            var v1 = (int2)(tri.Vert1 * m_size);
            var v2 = (int2)(tri.Vert2 * m_size);
            var v3 = (int2)(tri.Vert3 * m_size);
            
            DrawLine(v1, v2);
            DrawLine(v2, v3);
            DrawLine(v3, v1);
        }

        private void DrawLine(int2 start, int2 end)
        {
            int x0 = start.x, y0 = start.y;
            int x1 = end.x, y1 = end.y;

            int dx = math.abs(x1 - x0), dy = math.abs(y1 - y0);
            int sx = x0 < x1 ? 1 : -1, sy = y0 < y1 ? 1 : -1;
            int err = dx - dy;

            while (true)
            {
                m_output[x0 + m_size.x * y0] = m_colour;

                if (x0 == x1 && y0 == y1) break;
                int e2 = 2 * err;
                if (e2 > -dy)
                {
                    err -= dy;
                    x0 += sx;
                }

                if (e2 < dx)
                {
                    err += dx;
                    y0 += sy;
                }
            }
        }
        
        public void Dispose()
        {
            m_tris.Dispose();
        }
    }
    
    private static List<(int4, List<TriData>)> GetMinimalCoveringRects(List<TriData> _tris)
    {
        var job = new GetMinimalCoveringRectsJob(_tris);
        job.Schedule().Complete();
        return job.GetOutput();
    }

    [BurstCompile]
    private struct GetMinimalCoveringRectsJob : IJob
    {
        const float threshold = 0f;
        private struct NativeListMap<T1, T2> where T1 : unmanaged, IEquatable<T1> where T2 : unmanaged
        {
            private struct LinkNode
            {
                public T2 val;
                public int next;
            }
            
            private NativeList<LinkNode> m_nodes;
            private NativeHashMap<T1, int2> m_ends;
            
            public NativeListMap(int _capacity, Allocator _allocator)
            {
                m_nodes = new(_capacity, _allocator);
                m_ends = new(_capacity, _allocator);
            }
            
            public NativeListMap(NativeListMap<T1, T2> _copy, Allocator _allocator)
            {
                m_nodes = new(_copy.m_nodes.Length, _allocator);
                m_ends = new(_copy.m_ends.Count, _allocator);
                CopyFrom(_copy);
            }
            
            public void CopyFrom(NativeListMap<T1, T2> _copy)
            {
                m_nodes.Length = _copy.m_nodes.Length;
                NativeArray<LinkNode>.Copy(_copy.m_nodes.AsArray(), m_nodes.AsArray(), _copy.m_nodes.Length);
                m_ends.Clear();
                foreach (var pair in _copy.m_ends)
                    m_ends.Add(pair.Key, pair.Value);
            }

            public void Add(T1 _key, T2 _val)
            {
                LinkNode node = new LinkNode { val = _val, next = -1 };
                var ind = m_nodes.Length;
                m_nodes.Add(node);
                if (!m_ends.TryGetValue(_key, out var ends))
                {
                    m_ends.Add(_key, new int2(ind, ind));
                    return;
                }
                var tail = m_nodes[ends.y];
                tail.next = ind;
                m_nodes[ends.y] = tail;
                ends.y = ind;
                m_ends[_key] = ends;
            }

            public void Combine(T1 _key1, T1 _key2, T1 _newKey, out bool _addNew)
            {
                _addNew = !m_ends.ContainsKey(_newKey) || _key1.Equals(_newKey) || _key2.Equals(_newKey);
                Combine(_key1, _newKey);
                Combine(_key2, _newKey);
            }

            private void Combine(T1 _from, T1 _to)
            {
                if (_from.Equals(_to))
                    return;
                var fromEnds = m_ends[_from];
                m_ends.Remove(_from);
                if (!m_ends.ContainsKey(_to))
                {
                    m_ends[_to] = fromEnds;
                    return;
                }
                var toEnds = m_ends[_to];
                
                var tail = m_nodes[fromEnds.y];
                tail.next = toEnds.x;
                m_nodes[fromEnds.y] = tail;
                toEnds.x = fromEnds.x;
                m_ends[_to] = toEnds;
            }

            public List<(T1, List<T2>)> GetOutput()
            {
                var output = new List<(T1, List<T2>)>(m_ends.Count);
                foreach (var ends in m_ends)
                {
                    var node = m_nodes[ends.Value.x];
                    var list = new List<T2> {node.val};
                    while (node.next != -1)
                    {
                        node = m_nodes[node.next];
                        list.Add(node.val);
                    }
                    output.Add((ends.Key, list));
                }
                Dispose();
                return output;
            }

            public void Dispose()
            {
                m_nodes.Dispose();
                m_ends.Dispose();
            }
        }

        private NativeArray<TriData> m_tris;
        private NativeListMap<int4, TriData> m_bestTriContainers;
        private NativeHashSet<int4> m_allRects;
        private Random m_random;

        public GetMinimalCoveringRectsJob(List<TriData> _tris)
        {
            m_bestTriContainers = new(0, Allocator.TempJob);
            m_allRects = new(_tris.Count, Allocator.TempJob);
            
            m_tris = new NativeArray<TriData>(_tris.Count, Allocator.TempJob);
            NativeArray<TriData>.Copy(_tris.GetBackingArray(), 0, m_tris, 0, _tris.Count);
            
            m_random = new Random(1);
        }
        
        public void Execute()
        {
            NativeList<int4> rectsCopy = new(m_allRects.Count, Allocator.Temp);
            NativeListMap<int4, TriData> trisCopy = new(m_tris.Length, Allocator.Temp);
            NativeListMap<int4, TriData> originalTris = new(m_tris.Length, Allocator.Temp);
            Setup(originalTris);
            
            float bestScore = float.PositiveInfinity;
            for (int n = 0; n < 10; ++n)
            {
                trisCopy.CopyFrom(originalTris);
                ShuffleInto(m_allRects, rectsCopy);
                for (int i = 1; i < rectsCopy.Length; ++i)
                {
                    var rect = rectsCopy[i];
                    
                    for (int j = 0; j < i; ++j)
                    {
                        var otherRect = rectsCopy[j];
                        
                        var eff = Efficiency(rect, otherRect);
                        if (eff < threshold)
                            continue;

                        var newRect = TotalBounds(rect, otherRect);
                        trisCopy.Combine(rect, otherRect, newRect, out var addNew);
                        if (addNew)
                            rectsCopy[i] = newRect;
                        else
                            rectsCopy.RemoveAt(i);
                        
                        rectsCopy.RemoveAt(j);
                        
                        rect = rectsCopy[--i];
                        j = -1;
                    }
                }

                float score = 0;
                foreach (var rect in rectsCopy)
                    score += AreaOf(rect);
                if (score < bestScore)
                {
                    bestScore = score;
                    m_bestTriContainers.CopyFrom(trisCopy);
                }
            }
            
            rectsCopy.Dispose();
            trisCopy.Dispose();
            originalTris.Dispose();
        }

        private void Setup(NativeListMap<int4, TriData> _triContainers)
        {
            foreach (var tri in m_tris)
            {
                var rect = new int4(tri.GetMinPixel, tri.GetMaxPixel);
                _triContainers.Add(rect, tri);
                m_allRects.Add(rect);
            }
        }
        
        private void ShuffleInto<T>(NativeHashSet<T> toAdd, NativeList<T> into) where T : unmanaged, IEquatable<T>
        {
            into.Clear();
            foreach (var t in toAdd)
            {
                var ind = m_random.NextInt(into.Length + 1);
                into.InsertRange(ind, 1);
                into[ind] = t;
            }
        }
        
        public List<(int4, List<TriData>)> GetOutput()
        {
            var output = m_bestTriContainers.GetOutput();
            m_tris.Dispose();
            m_allRects.Dispose();
            return output;
        }
    }
    
    private static void CopyTexture(Texture2D _source, Texture2D _dest, int4x2[] _transformRects)
    {
        var job = new CopyTextureJob(_source, _dest, _transformRects, float4x4.identity);
        job.Schedule().Complete();
        job.Dispose();
    }
    
    private static void CopyTextureChannels(Texture2D _source, Texture2D _dest, int4x2[] _transformRects, int[] _channelMap)
    {
        var colMul = float4x4.zero;
        for (int i = 0; i < 4; ++i)
        {
            if (_channelMap[i] == -1)
                continue;
            colMul[_channelMap[i]][i] = 1f;
        }
        var job = new CopyTextureJob(_source, _dest, _transformRects, colMul);
        job.Schedule().Complete();
        job.Dispose();
    }
    
    [BurstCompile]
    private struct CopyTextureJob : IJob
    {
        [ReadOnly] private int sourceWidth;
        [ReadOnly] private int destWidth;
        [ReadOnly] private NativeArray<int4x2> transformRects;
        [ReadOnly] private float4x4 colMul;
        [ReadOnly] private NativeArray<Color32> source;
        
        [WriteOnly] NativeArray<Color32> dest;
        
        public CopyTextureJob(Texture2D _source, Texture2D _dest, int4x2[] _transformRects, float4x4 _colMul)
        {
            sourceWidth = _source.width;
            destWidth = _dest.width;
            transformRects = new NativeArray<int4x2>(_transformRects, Allocator.TempJob);
            colMul = _colMul;
            source = _source.GetRawTextureData<Color32>();
            dest = _dest.GetRawTextureData<Color32>();
        }

        public void Execute()
        {
            foreach (var rectPair in transformRects)
            {
                var oldRect = rectPair[0];
                var newRect = rectPair[1];
                var flipped = oldRect.z - oldRect.x != newRect.z - newRect.x;
                for (int x = 0; x <= oldRect.z - oldRect.x; ++x)
                {
                    for (int y = 0; y <= oldRect.w - oldRect.y; ++y)
                    {
                        var oldIndex = (oldRect.y + y) * sourceWidth + (oldRect.x + x);
                        var newIndex = flipped ? ((newRect.y + x) * destWidth + (newRect.x + y)) : ((newRect.y + y) * destWidth + (newRect.x + x));
                        if (newIndex < 0 || oldIndex < 0)
                            continue;
                        var oldCol = source[oldIndex];
                        var oldColFloat = new float4(oldCol.r, oldCol.g, oldCol.b, oldCol.a);
                        var newCol = math.mul(oldColFloat, colMul);
                        dest[newIndex] = new Color32((byte)newCol.x, (byte)newCol.y, (byte)newCol.z, (byte)newCol.w);
                    }
                }
            }
        }

        public void Dispose()
        {
            transformRects.Dispose();
        }
    }

    private static int GetChannelsUsed(Texture2D _tex)
    {
        var job = new CheckChannelsJob(_tex);
        job.Schedule().Complete();
        return job.GetOutput();
    }

    [BurstCompile]
    private struct CheckChannelsJob : IJob
    {
        [ReadOnly] private NativeArray<Color32> tex;
        
        [WriteOnly] NativeArray<bool> output;
        
        public CheckChannelsJob(Texture2D _tex)
        {
            tex = _tex.GetRawTextureData<Color32>();
            output = new NativeArray<bool>(4, Allocator.TempJob);
        }

        public void Execute()
        {
            foreach (var col in tex)
            {
                if (col.r > 0) //All white texture is counted as all red instead. Talk to cameron
                    output[0] = true;
                if (col.g is > 0 and < 255)
                    output[1] = true;
                if (col.b is > 0 and < 255)
                    output[2] = true;
                if (col.a is > 0 and < 255)
                    output[3] = true;
            }
        }

        public int GetOutput()
        {
            int i = 0;
            for (; i < 4; ++i)
            {
                if (!output[i])
                    break;
            }
            output.Dispose();
            return i;
        }
    }
    
    private static void RebuildMeshToAtlas(Mesh mesh, int subMeshIndex, List<TriData> tris)
    {
        Debug.Log($"RebuildMesh: mat={mat.name}, triCount={tris.Count}, firstTriMeshID={tris[0].meshID}");
        var job = new MeshRebuilderJob(mesh, subMeshIndex, tris);
        job.Schedule().Complete();
        job.GetOutput(mesh, subMeshIndex);
    }

    [BurstCompile]
    private struct MeshRebuilderJob : IJob
    {
        private struct VertDataHandler
        {
            private const int c_UV = sizeof(float) * 2;
            
            private int m_streamStride;
            private int m_uvBufferOffset;
            private NativeArray<float2> m_uvFerry;
            private NativeArray<byte> m_dataFerry;
            private NativeList<byte> m_vertBuffer;

            public VertDataHandler(Mesh _from)
            {
                m_uvBufferOffset = _from.GetVertexAttributeOffset(VertexAttribute.TexCoord0);
                m_streamStride = _from.GetVertexBufferStride(0);
                m_uvFerry = new NativeArray<float2>(1, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
                m_dataFerry = new NativeArray<byte>(m_streamStride, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
                
                var meshData = Mesh.AcquireReadOnlyMeshData(_from);
                var original = meshData[0].GetVertexData<byte>();
                m_vertBuffer = new NativeList<byte>(original.Length * 2, Allocator.TempJob);
                m_vertBuffer.AddRange(original);
                meshData.Dispose();
            }

            public int Add(int _indToCopy, float2 _newUV)
            {
                NativeArray<byte>.Copy(m_vertBuffer.AsArray(), _indToCopy * m_streamStride, m_dataFerry, 0, m_streamStride);
                m_uvFerry[0] = _newUV;
                NativeArray<byte>.Copy(m_uvFerry.Reinterpret<byte>(c_UV), 0, m_dataFerry, m_uvBufferOffset, c_UV);
                m_vertBuffer.AddRange(m_dataFerry);
                return (m_vertBuffer.Length / m_streamStride) - 1;
            }

            public void FillOutput(Mesh _into)
            {
                int numVerts = m_vertBuffer.Length / m_streamStride;
                _into.SetVertexBufferParams(numVerts, _into.GetVertexAttributes());
                _into.SetVertexBufferData(m_vertBuffer.AsArray(), 0, 0, m_vertBuffer.Length);
                _into.UploadMeshData(false);

                m_uvFerry.Dispose();
                m_dataFerry.Dispose();
                m_vertBuffer.Dispose();
            }
        }
        private struct vertUV : IEquatable<vertUV> 
        {
            float2 uv; 
            int ind;
            public vertUV(float2 _uv, int _ind)
            {
                uv = _uv;
                ind = _ind;
            }
            public bool Equals(vertUV other)
            {
                return math.lengthsq(uv - other.uv) < 0.0001f && ind == other.ind;
            }
            public override int GetHashCode()
            {
                return uv.GetHashCode() ^ ind.GetHashCode();
            }
        }
        private VertDataHandler m_vertHandler;
        private NativeHashMap<vertUV, int> m_vertUVToNewVert;
        private NativeArray<int> m_inds;
        
        [ReadOnly] private NativeArray<float2> m_uvs;
        [ReadOnly] private NativeArray<TriData> m_triData;
        
        public MeshRebuilderJob(Mesh _mesh, int _subMeshIndex, List<TriData> _tris)
        {
            m_vertHandler = new VertDataHandler(_mesh);
            
            m_vertUVToNewVert = new NativeHashMap<vertUV, int>(_tris.Count * 2, Allocator.TempJob);

            m_uvs = new NativeArray<Vector2>(_mesh.uv, Allocator.TempJob).Reinterpret<float2>();
            
            m_triData = new NativeArray<TriData>(_tris.Count, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
            NativeArray<TriData>.Copy(_tris.GetBackingArray(), 0, m_triData, 0, _tris.Count);
            
            m_inds = new NativeArray<int>(_mesh.GetIndices(_subMeshIndex), Allocator.TempJob);
        }
        
        public void Execute()
        {
            foreach (var ind in m_inds)
                m_vertUVToNewVert.TryAdd(new vertUV(m_uvs[ind], ind), ind);
            
            foreach (var tri in m_triData)
            {
                var i = tri.triID * 3;
                AddVertUV(tri.Vert1, i + 0);
                AddVertUV(tri.Vert2, i + 1);
                AddVertUV(tri.Vert3, i + 2);
            }
        }
        
        private void AddVertUV(float2 _uv, int _i)
        {
            var ind = m_inds[_i];
            if (!m_vertUVToNewVert.TryGetValue(new vertUV(_uv, ind), out var newInd))
            {
                newInd = m_vertHandler.Add(ind, _uv);
                m_vertUVToNewVert.Add(new vertUV(_uv, ind), newInd);
            }
            m_inds[_i] = newInd;
        }

        public void GetOutput(Mesh _mesh, int _subMeshIndex)
        {
            m_vertHandler.FillOutput(_mesh);
            _mesh.SetIndices(m_inds, MeshTopology.Triangles, _subMeshIndex);

            m_vertUVToNewVert.Dispose();
            m_uvs.Dispose();
            m_triData.Dispose();
            m_inds.Dispose();
        }
    }
}
