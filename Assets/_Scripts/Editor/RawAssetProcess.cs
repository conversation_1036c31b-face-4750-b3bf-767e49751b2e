using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using UnityEngine.Experimental.Rendering;

/*
 RawAssetProcess
 
 Generate atlassed textures and apply to models, aiming to minimise atlas count (for draw calls)
 
 TODO:
 * handle mismatched texture sizes (type-to-type and per-material)
 * out-by-one texture issue
 */
public class RawAssetProcess : MonoBehaviour
{
    [MenuItem("22Cans/Art/Process Raw Assets")]
    static void Process()
    {
        ProcessFoldersRecursively("Assets/RawAssets/");
    }

    static void ProcessFoldersRecursively(string _path)
    {
        var folders = Directory.GetDirectories(_path);
        var infoPath = _path + "/packDetails.txt";
        if (File.Exists(infoPath))
            ProcessFolder(infoPath);
        foreach (var folder in folders) ProcessFoldersRecursively(folder);
    }
    static void ProcessFolder(string _infoPath)
    {
        List<MaterialRegion> materialRegions = new();
        List<List<List<Poly>>> meshPolys = new();
        
        bool addSubfolders = false, clear = false;
        string outputFolder = null, prefabFolder = null, atlasName = null;
        var lines = File.ReadAllLines(_infoPath);
        foreach (var line in lines)
        {
            if (line.StartsWith('#')) continue;
            if (string.IsNullOrWhiteSpace(line)) continue;
            var bits = line.Split('=');
            var cmd = bits[0].ToLower();
            switch (cmd)
            {
                case "meshfolder":
                    outputFolder = bits[1];
                    if (outputFolder.EndsWith("\\") || outputFolder.EndsWith("/"))
                        outputFolder = outputFolder[0..^1];
                    break;
                case "prefabfolder":
                    prefabFolder = bits[1];
                    if (prefabFolder.EndsWith("\\") || prefabFolder.EndsWith("/"))
                        prefabFolder = prefabFolder[0..^1];
                    break;
                case "atlasname":
                    atlasName = bits[1];
                    break;
                case "subfolders":
                    addSubfolders = bits[1].ToLower().StartsWith("y");
                    break;
                case "clear":
                    clear = bits[1].ToLower().StartsWith("y");
                    break;
            }
        }
        if (outputFolder == null)
        {
            Debug.LogError($"Info file {_infoPath} doesn't contain MeshFolder= entry");
            return;
        }

        if (atlasName == null)
        {
            int lastSep = outputFolder.LastIndexOfAny(new char[] { '/', '\\' });
            atlasName = outputFolder[(lastSep+1)..];
        }
        
        var outputMaterialFolder = outputFolder;
        var outputTextureFolder = outputFolder;
        if (addSubfolders)
        {
            outputMaterialFolder += "/Materials";
            outputTextureFolder += "/Textures";
        }

        if (Directory.Exists(outputFolder) == false)
        {
            Directory.CreateDirectory(outputFolder);
        }
        else
        {
            if (clear)
            {
                Directory.Delete(outputFolder, true);
                Directory.CreateDirectory(outputFolder);
            }
            else
            {
                // check timestamp
            }
        }
        if (prefabFolder == null)
        {
            prefabFolder = outputFolder;
        }
        else if (Directory.Exists(prefabFolder) == false)
        {
            Directory.CreateDirectory(prefabFolder);
        }
        else
        {
            if (clear)
            {
                Directory.Delete(prefabFolder, true);
                Directory.CreateDirectory(prefabFolder);
            }
            else
            {
                // check timestamp
            }
        }
        if (Directory.Exists(outputTextureFolder) == false)
            Directory.CreateDirectory(outputTextureFolder);
        else if (clear)
        {
            Directory.Delete(outputTextureFolder, true);
            Directory.CreateDirectory(outputTextureFolder);
        }
        if (Directory.Exists(outputMaterialFolder) == false)
            Directory.CreateDirectory(outputMaterialFolder);
        else if (clear)
        {
            Directory.Delete(outputMaterialFolder, true);
            Directory.CreateDirectory(outputMaterialFolder);
        }


        var folder = Path.GetDirectoryName(_infoPath);
        var files = Directory.GetFiles(folder, "*.fbx");
        Material firstMat = null;
        for (int i = 0; i < files.Length; ++i)
        {
            var file = files[i];
            var obj = AssetDatabase.LoadAssetAtPath<GameObject>(file);
            var mr = obj.GetComponent<MeshRenderer>();
            var mf = obj.GetComponent<MeshFilter>();
            if (firstMat == null) firstMat = mr.sharedMaterials[0];
            var polys = ProcessMesh(mf.sharedMesh, mr.sharedMaterials, file, i, materialRegions);
            meshPolys.Add(polys);
        }
        MergeMaterialRegions(materialRegions);
        var atlasFile = $"{outputMaterialFolder}/{atlasName}.atlasMat";
        var atlasTexFile = $"{outputTextureFolder}/{atlasName}.atlasMat";
        var atlas = GenerateAtlas(materialRegions, firstMat, atlasFile, atlasTexFile, 4096, 4096, 4);
        for (int i = 0; i < files.Length; ++i)
        {
            var file = files[i];
            var obj = AssetDatabase.LoadAssetAtPath<GameObject>(file);
            var mf = obj.GetComponent<MeshFilter>();
            GenerateAtlasMesh(meshPolys[i], atlas, mf.sharedMesh, atlasFile + ".mat", files[i], outputFolder, prefabFolder, materialRegions);
        }
    }

    const string c_baseMapName = "_BaseMap";
    const string c_normalMapName = "_BumpMap";
    const string c_metalMapName = "_MetallicGlossMap";
    static void MergeMaterialRegions(List<MaterialRegion> _materialRegions)
    {
        for (int i = 0; i < _materialRegions.Count; ++i)
        {
            var mrI = _materialRegions[i];
            if (mrI.m_consumedBy != null) continue;
            var texI = mrI.m_material.GetTexture(c_baseMapName);
            for (int j = 0; j < _materialRegions.Count; ++j)
            {
                if (i == j) continue;
                var mrJ = _materialRegions[j];
                if (mrJ.m_consumedBy != null) continue;
                var texJ = mrJ.m_material.GetTexture(c_baseMapName);
                if (texI != texJ) continue;
                if (mrI.TryMerge(mrJ))
                    mrI.SetPixels(texI.width, texI.height);
            }
        }
    }

    public class Poly
    {
        public int m_v1, m_v2, m_v3;
        public Poly m_next = null, m_prev = null;
        public int m_id = 0, m_index = -1, m_materialRegion = -1;

        public static bool Nearly(Vector2 _a, Vector2 _b)
        {
            var d = _a - _b;
            const float c_epsilon = 1.0f / 1024.0f;
            return d.sqrMagnitude < c_epsilon * c_epsilon;
        }

        public bool Adjoins(Poly _other, Vector2[] _vs)
        {
            Vector2 t1 = _vs[m_v1], t2 = _vs[m_v2], t3 = _vs[m_v3];
            Vector2 o1 = _vs[_other.m_v1], o2 = _vs[_other.m_v2], o3 = _vs[_other.m_v3];
            bool m1 = Nearly(t1, o1) || Nearly(t1, o2) || Nearly(t1, o3);
            bool m2 = Nearly(t2, o1) || Nearly(t2, o2) || Nearly(t2, o3);
            bool m3 = Nearly(t3, o1) || Nearly(t3, o2) || Nearly(t3, o3);
            return m1 || m2 || m3;
            return (m1 && m2) || (m1 && m3) || (m2 && m3);
        }

        public string AdjoinsDebug(Poly _other, Vector2[] _vs)
        {
            Vector2 t1 = _vs[m_v1], t2 = _vs[m_v2], t3 = _vs[m_v3];
            Vector2 o1 = _vs[_other.m_v1], o2 = _vs[_other.m_v2], o3 = _vs[_other.m_v3];
            bool m1 = Nearly(t1, o1) || Nearly(t1, o2) || Nearly(t1, o3);
            bool m2 = Nearly(t2, o1) || Nearly(t2, o2) || Nearly(t2, o3);
            bool m3 = Nearly(t3, o1) || Nearly(t3, o2) || Nearly(t3, o3);
            if (m1) return $"t1 ({t1}) ({m_v1}) matches o1 ({o1 - t1}) ({_other.m_v1}) o2 ({o2 - t1}) ({_other.m_v2}) or o3 ({o3 - t1}) ({_other.m_v3})";
            if (m2) return $"t2 ({t2}) ({m_v2}) matches o1 ({o1 - t2}) ({_other.m_v1}) o2 ({o2 - t2}) ({_other.m_v2}) or o3 ({o3 - t2}) ({_other.m_v3})";
            if (m3) return $"t3 ({t3}) ({m_v3}) matches o1 ({o1 - t3}) ({_other.m_v1}) o2 ({o2 - t3}) ({_other.m_v2}) or o3 ({o3 - t3}) ({_other.m_v3})";
            return "-";
        }
    }

    static Poly FindStart(Poly _from)
    {
        while (_from.m_prev != null) _from = _from.m_prev;
        return _from;
    }
    static Poly FindEnd(Poly _from)
    {
        while (_from.m_next != null) _from = _from.m_next;
        return _from;
    }

    static bool IsLinkedTo(Poly _a, Poly _b)
    {
        var check = _b;
        while (check != null)
        {
            if (check == _a) return true;
            check = check.m_next;
        }
        check = _b;
        while (check != null)
        {
            if (check == _a) return true;
            check = check.m_prev;
        }
        return false;
    }

    static void CheckForLoop(Poly _from)
    {
        Poly check1 = _from, check2 = _from;
        while (check1.m_next != null && check2.m_next?.m_next != null)
        {
            check1 = check1.m_next;
            check2 = check2.m_next.m_next;
            if (check1 == check2)
            {
                Debug.LogError($"Loop detected, crashing out");
                check1 = null;
                break;
            }
        }
    }

    public class Region
    {
        public Vector2 m_min = Vector2.one * 1e23f, m_max = Vector2.one * -1e23f;
        public int m_ownerRegion = 0;
        public int m_materialRegion = 0;

        public static float Threshold(float _expanded)
        {
            _expanded -= 1;
            return (Mathf.Sqrt(_expanded) * 1.095f - _expanded) * 3.336f;
        }

        public bool Contains(Region _other)
        {
            var (contained, expanded) = ContainsFraction(_other);
            return contained > Threshold(expanded);
        }

        public (float, float) ContainsFraction(Region _other)
        {
            float tMinX = Mathf.Repeat(m_min.x, 1), tMinY = Mathf.Repeat(m_min.y, 1);
            float tMaxX = Mathf.Repeat(m_max.x, 1), tMaxY = Mathf.Repeat(m_max.y, 1);
            float oMinX = Mathf.Repeat(_other.m_min.x, 1), oMinY = Mathf.Repeat(_other.m_min.y, 1);
            float oMaxX = Mathf.Repeat(_other.m_max.x, 1), oMaxY = Mathf.Repeat(_other.m_max.y, 1);
            float cMinX = Mathf.Max(oMinX, tMinX), cMinY = Mathf.Max(oMinY, tMinY);
            float cMaxX = Mathf.Min(oMaxX, tMaxX), cMaxY = Mathf.Min(oMaxY, tMaxY);
            if (cMaxX < cMinX || cMaxY < cMinY) return (0, 0);
            float areaTotal = (oMaxX-oMinX) * (oMaxY-oMinY);
            float areaContained = (cMaxX-cMinX) * (cMaxY-cMinY);
            
            float eMinX = Mathf.Min(oMinX, tMinX), eMinY = Mathf.Min(oMinY, tMinY);
            float eMaxX = Mathf.Max(oMaxX, tMaxX), eMaxY = Mathf.Max(oMaxY, tMaxY);
            float areaParent = (tMaxX-tMinX) * (tMaxY-tMinY);
            float areaExpanded = (eMaxX-eMinX) * (eMaxY-eMinY);
            return (areaContained / areaTotal, areaExpanded / areaParent);
        }

        public bool Expand(Region _other)
        {
            float tMinX = Mathf.Repeat(m_min.x, 1), tMinY = Mathf.Repeat(m_min.y, 1);
            float tMaxX = Mathf.Repeat(m_max.x, 1), tMaxY = Mathf.Repeat(m_max.y, 1);
            float oMinX = Mathf.Repeat(_other.m_min.x, 1), oMinY = Mathf.Repeat(_other.m_min.y, 1);
            float oMaxX = Mathf.Repeat(_other.m_max.x, 1), oMaxY = Mathf.Repeat(_other.m_max.y, 1);
            bool res = false;
            if (oMinX < tMinX) { m_min.x += oMinX - tMinX; res = true; }
            if (oMinY < tMinY) { m_min.y += oMinY - tMinY; res = true; }
            if (oMaxX > tMaxX) { m_max.x += oMaxX - tMaxX; res = true; }
            if (oMaxY > tMaxY) { m_max.y += oMaxY - tMaxY; res = true; }
            return res;
        }

        public bool ContainsPoint01(float _x, float _y)
        {
            float tMinX = Mathf.Repeat(m_min.x, 1), tMinY = Mathf.Repeat(m_min.y, 1);
            float tMaxX = Mathf.Repeat(m_max.x, 1), tMaxY = Mathf.Repeat(m_max.y, 1);
            return _x >= tMinX && _x < tMaxX && _y >= tMinY && _y < tMaxY;
        }
    };

    static List<List<Poly>> ProcessMesh(Mesh _mesh, Material[] _materials, string _file, int _meshIndex, List<MaterialRegion> _materialRegions)
    {
        var subMeshes = _mesh.subMeshCount;
        if (subMeshes != _materials.Length)
        {
            Debug.LogError($"File {_file} - SubMeshes({subMeshes}) != materials({_materials.Length})");
            subMeshes = Mathf.Min(subMeshes, _materials.Length);
        }
        var uvs = _mesh.uv;

        var polyList = new List<List<Poly>>();

        int firstMaterialRegion = _materialRegions.Count;

        for (int i = 0; i < subMeshes; ++i)
        {
            const int c_uvSpace = 4096;
            byte[] uvBitmap = new byte[c_uvSpace * c_uvSpace];
            var inds = _mesh.GetIndices(i);
            var polys = new List<Poly>();
            polyList.Add(polys);
            // Generate a Poly representation for each triangle, and merge Polys into lists of touching triangles
            for (int j = 0; j < inds.Length; j += 3)
            {
                var poly = new Poly() {m_v1 = inds[j + 0], m_v2 = inds[j + 1], m_v3 = inds[j + 2], m_index = j / 3};
                polys.Add(poly);
                for (int k = 0; k < polys.Count - 1; ++k)
                {
                    if (poly.Adjoins(polys[k], uvs))
                    {
                        if (IsLinkedTo(poly, polys[k])) continue;
                        //if (polys.Count < 10) Debug.LogError($"poly {polys.Count-1} adjoins {k} - {poly.AdjoinsDebug(polys[k], uvs)}");
                        var polyEnd = FindEnd(poly);
                        var polyStart = FindStart(polys[k]);
                        polyEnd.m_next = polyStart;
                        polyStart.m_prev = polyEnd;
                        CheckForLoop(poly);
                    }
                }
            }
            // Assign IDs for each poly list
            int nextId = 0;
            for (int j = 0; j < polys.Count; ++j)
            {
                var poly = polys[j];
                if (poly.m_id != 0) continue;
                var thisId = ++nextId;
                poly.m_id = thisId;
                var polyNext = poly.m_next;
                while (polyNext != null)
                {
                    if (polyNext.m_id == 0) polyNext.m_id = thisId;
                    else if (polyNext.m_id == thisId) break; // cycle
                    else
                    {
                        Debug.LogError($"Error in poly join (n) {polys.IndexOf(poly)}x{polys.IndexOf(polyNext)} - setting {thisId} found {polyNext.m_id}");
                        break;
                    }
                    polyNext = polyNext.m_next;
                }
                polyNext = poly.m_prev;
                while (polyNext != null)
                {
                    if (polyNext.m_id == 0) polyNext.m_id = thisId;
                    else if (polyNext.m_id == thisId) break; // cycle
                    else
                    {
                        Debug.LogError($"Error in poly join (p) {polys.IndexOf(poly)}x{polys.IndexOf(polyNext)} - setting {thisId} found {polyNext.m_id}");
                        break;
                    }
                    polyNext = polyNext.m_prev;
                }
            }
            // Calculate extents in UV space of each poly list
            //Debug.LogError($"Material {i} num IDs {nextId}");
            var regions = new Region[nextId + 1];
            for (int k = 0; k < nextId; ++k)
            {
                var region = new Region();
                int id = k + 1;
                regions[id] = region;
                for (int j = 0; j < polys.Count; ++j)
                {
                    if (polys[j].m_id == id)
                    {
                        var poly = polys[j];
                        while (poly.m_prev != null) poly = poly.m_prev;
                        Vector2 min = Vector2.one * 1e23f, max = Vector2.one * -1e23f;
                        while (poly != null)
                        {
                            min = Vector2.Min(min, uvs[poly.m_v1]);
                            min = Vector2.Min(min, uvs[poly.m_v2]);
                            min = Vector2.Min(min, uvs[poly.m_v3]);
                            max = Vector2.Max(max, uvs[poly.m_v1]);
                            max = Vector2.Max(max, uvs[poly.m_v2]);
                            max = Vector2.Max(max, uvs[poly.m_v3]);
                            poly = poly.m_next;
                        }
                        region.m_min = min;
                        region.m_max = max;
                        break;
                    }
                }
            }
            // Calculate UV overlaps and coalesce UV spaces
            bool repeat = true;
            while (repeat)
            {
                repeat = false;
                //Debug.LogError($"Check contained");
                for (int j = 0; j < nextId; ++j)
                {
                    var regionJ = regions[j + 1];
                    if (regionJ.m_ownerRegion != 0) continue;
                    float bestContainerFraction = 0, bestContainerExpanded = 0;
                    int bestContainer = 0;
                    for (int k = 0; k < nextId; ++k)
                    {
                        if (j == k) continue;
                        var regionK = regions[k + 1];
                        if (regionK.m_ownerRegion == 0)
                        {
                            var (fraction, expanded) = regionK.ContainsFraction(regionJ);
                            if (fraction > bestContainerFraction)
                            {
                                bestContainer = k + 1;
                                bestContainerFraction = fraction;
                                bestContainerExpanded = expanded;
                            }
                        }
                    }
                    if (bestContainerFraction > Region.Threshold(bestContainerExpanded))
                    {
                        regionJ.m_ownerRegion = bestContainer;
                        repeat |= regions[bestContainer].Expand(regionJ);
                    }
                    //Debug.LogError($"ID {j + 1} range {regionJ.m_min}..{regionJ.m_max} - owner:{regionJ.m_ownerRegion} - best container:{bestContainer} {bestContainerFraction} {bestContainerExpanded}");
                }
            }
            // Calculate ultimate owner region for each region
            for (int j = 0; j < nextId; ++j)
            {
                var regionJ = regions[j + 1];
                if (regionJ.m_ownerRegion != 0)
                    while (regions[regionJ.m_ownerRegion].m_ownerRegion != 0)
                        regionJ.m_ownerRegion = regions[regionJ.m_ownerRegion].m_ownerRegion;
            }
            // Calculate list of all ultimate owner (parent) regions
            List<int> parentRegions = new();
            for (int j = 0; j < nextId; ++j)
            {
                var regionJ = regions[j + 1];
                if (regionJ.m_ownerRegion == 0)
                {
                    //Debug.LogError($"ID {j + 1} range {regionJ.m_min}..{regionJ.m_max} - {regions[1].ContainsFraction(regionJ)}");
                    parentRegions.Add(j + 1);
                }
            }

            // Add a MaterialRegion entry for each parent region
            for (int j = 0; j < parentRegions.Count; ++j)
            {
                var region = regions[parentRegions[j]];
                var mr = new MaterialRegion() {
                    m_minUV = region.m_min,
                    m_maxUV = region.m_max,
                    m_region = parentRegions[j],
                    m_meshIndex = _meshIndex,
                    m_materialIndex = i,
                    m_material = _materials[i],
                    m_originalIndex = _materialRegions.Count,
                };
                _materialRegions.Add(mr);
                region.m_materialRegion = _materialRegions.Count - 1;
            }
            // Look up the MaterialRegion for each poly
            for (int j = 0; j < polys.Count; ++j)
            {
                var poly = polys[j];
                int id = poly.m_id;
                if (regions[id].m_ownerRegion != 0) id = regions[id].m_ownerRegion;
                poly.m_materialRegion = regions[id].m_materialRegion;
            }

            // DEBUG: draw the UVs into a debug map
            if (false)
            {
                for (int j = 0; j < polys.Count; ++j)
                {
                    var poly = polys[j];
                    int i0 = poly.m_v1, i1 = poly.m_v2, i2 = poly.m_v3;
                    int id = poly.m_id;
                    if (regions[id].m_ownerRegion != 0) id = regions[id].m_ownerRegion;
                    //id = ((poly.m_id - 1) % 6) + 1;
                    DrawUVs(uvs[i0], uvs[i1], uvs[i2], id, uvBitmap, c_uvSpace, c_uvSpace);
                }
                var colours = new Color32[c_uvSpace * c_uvSpace];
                for (int j = 0; j < c_uvSpace * c_uvSpace; ++j)
                {
                    var v = uvBitmap[j];
                    int intensity = 0;
                    if (v > 0)
                    {
                        for (int k = 0; k < parentRegions.Count; ++k)
                        {
                            if (parentRegions[k] == v)
                            {
                                intensity = k * 255 / parentRegions.Count;
                                break;
                            }
                        }
                    }
                    //int intensity = v * 255 / nextId;
                    int r = intensity; //(v & 1) * 255;
                    int g = 255 - intensity; //((v >> 1) & 1) * 255;
                    int b = 0; //((v >> 2) & 1) * 255;
                    /*for (int k = 0; k < parentRegions.Count; ++k)
                    {
                        var region = regions[parentRegions[k]];
                        if (region.ContainsPoint01((j % c_uvSpace) / (float)c_uvSpace, (j / c_uvSpace) / (float)c_uvSpace))
                            b = 255;
                    }*/
                    if (v == 0) r = g = 0;
                    var c = new Color32((byte) r, (byte) g, (byte) b, (byte) 255);
                    colours[j] = c;
                }
                var tex = new Texture2D(c_uvSpace, c_uvSpace);
                tex.SetPixels32(colours);
                tex.Apply(false, false);
                var dump = $"{_file}.{i + 1}.png";
                File.WriteAllBytes(dump, tex.EncodeToPNG());
                AssetDatabase.ImportAsset(dump);
                DestroyImmediate(tex);
            }
            // /DEBUG
        }

        // Scan all MaterialRegions for all materials, generating PixelW/H and recording TintColour
        for (int i = firstMaterialRegion; i < _materialRegions.Count; ++i)
        {
            var mr = _materialRegions[i];
            var mat = _materials[mr.m_materialIndex];
            var mtex = mat.mainTexture;
            mr.SetPixels(mtex.width, mtex.height);
            var uvWindowCenter = (mr.m_minUV + mr.m_maxUV) * .5f;
            if (mat.HasProperty("_TintWindow1"))
            {
                for (int j = 0; j < 8; ++j)
                {
                    var window = mat.GetVector($"_TintWindow{j + 1}");
                    if (uvWindowCenter.x >= window.x && uvWindowCenter.x < window.z && uvWindowCenter.y >= window.y && uvWindowCenter.y < window.w)
                    {
                        mr.m_tintColour = mat.GetColor($"_TintColour{j + 1}");
                        break;
                    }
                }
            }
        }
        return polyList;
    }

    static Atlas GenerateAtlas(List<MaterialRegion> _materialRegions, int _baseW, int _baseH, int _padding)
    {
        var atlas = new Atlas(_baseW, _baseH, _padding);
        for (int i = 0; i < _materialRegions.Count; ++i)
        {
            var mr = _materialRegions[i];
            if (mr.m_consumedBy != null)
            {
                //Debug.LogError($"MR {i + 1} consumed by {mr.m_consumedBy.m_minUV}..{mr.m_consumedBy.m_maxUV} - {mr.m_consumedBy.m_pixelW}x{mr.m_consumedBy.m_pixelH}");
                continue;
            }
            //Debug.LogError($"MR {i + 1} mat {mr.m_materialIndex + 1} rgn {mr.m_region} size {mr.m_pixelW}x{mr.m_pixelH}");
            mr.m_atlasRegion = atlas.AddRegion(mr.m_pixelW, mr.m_pixelH);
        }
        // Optimise empty space
        atlas.Optimise();
        return atlas;
    }

    static Atlas GenerateAtlas(List<MaterialRegion> _materialRegions, Material _sourceMaterial, string _matFile, string _texFile, int _baseW, int _baseH, int _padding)
    {
        // Sort MaterialRegions by pixel area
        _materialRegions.Sort((_a, _b) => { return _b.m_pixelW * _b.m_pixelH - _a.m_pixelW * _a.m_pixelH; });
        
        // Create a new Atlas instance and populate it with all MaterialRegions
        int bestW = _baseW, bestH = _baseH;
        var atlas = GenerateAtlas(_materialRegions, bestW, bestH, _padding);
        if (atlas.TotalUsed() <= atlas.TotalArea() / 2)
        {
            var atlasW2 = GenerateAtlas(_materialRegions, _baseW/2, _baseH, _padding);
            //Debug.LogError($"AtlasW2 usage {atlasW2.TotalUsed()} / {atlasW2.TotalArea()} {100L * atlasW2.TotalUsed() / atlasW2.TotalArea()}% errors:{atlasW2.m_errors}");
            if (atlasW2.m_errors == 0)
                bestW /= 2;
            else
            {
                var atlasH2 = GenerateAtlas(_materialRegions, _baseW, _baseH / 2, _padding);
                //Debug.LogError($"AtlasH2 usage {atlasH2.TotalUsed()} / {atlasH2.TotalArea()} {100L * atlasH2.TotalUsed() / atlasH2.TotalArea()}% errors:{atlasH2.m_errors}");
                if (atlasH2.m_errors == 0)
                    bestH /= 2;
            }
        }
        atlas = GenerateAtlas(_materialRegions, bestW, bestH, _padding);

        // Generate a new material based off the source material (first material used?)
        var atlasMatFile = $"{_matFile}.mat";
        var material = new Material(_sourceMaterial);
        
        // Generate an atlas texture for each type of texture
        GenerateTextureAtlas(atlas, c_baseMapName, "Albedo", _materialRegions, material, _texFile);
        GenerateTextureAtlas(atlas, c_metalMapName, "Mask", _materialRegions, material, _texFile);
        GenerateTextureAtlas(atlas, c_normalMapName, "Normal", _materialRegions, material, _texFile);
        
        AssetDatabase.CreateAsset(material, atlasMatFile);
        AssetDatabase.ImportAsset(atlasMatFile);
        
        Debug.LogError($"Atlas usage {atlas.TotalUsed()} / {atlas.TotalArea()} {100L * atlas.TotalUsed() / atlas.TotalArea()}% err:{atlas.m_errors}");
        
        return atlas;
    }

    static void GenerateAtlasMesh(List<List<Poly>> _polyList, Atlas _atlas, Mesh _mesh, string _atlasMatFile, string _file, string _assetPath, string _prefabPath, List<MaterialRegion> _materialRegions)
    {
        var uvs = _mesh.uv;
        
        var fileName = Path.GetFileName(_file);
        
        // Generate a new single material model with the UV co-ordinates adjusted to match the atlas
        // First, create a lookup from vertex index to MaterialRegion
        var materialRegionLookup = new int[_materialRegions.Count];
        for (int i = 0; i < _materialRegions.Count; ++i) materialRegionLookup[_materialRegions[i].m_originalIndex] = i;
        var lookup = new int[uvs.Length];
        for (int i = 0; i < _polyList.Count; ++i)
        {
            var polys = _polyList[i];
            for (int j = 0; j < polys.Count; ++j)
            {
                var poly = polys[j];
                int newId = materialRegionLookup[poly.m_materialRegion];
                lookup[poly.m_v1] = newId;
                lookup[poly.m_v2] = newId;
                lookup[poly.m_v3] = newId;
            }
        }
        // For each vertex, look up the material index and remap the UV co-ordinate based on the atlas region
        for (int i = 0; i < uvs.Length; ++i)
        {
            int regionId = lookup[i];
            var mr = _materialRegions[regionId];
            while (mr.m_consumedBy != null) mr = mr.m_consumedBy;
            Vector2 inUVMin = mr.UVmin01, inUVMax = mr.UVmax01;
            Vector2 uv = uvs[i];
            uv.x = Mathf.Repeat(uv.x, 1); uv.y = Mathf.Repeat(uv.y, 1);
            uv.x = (uv.x - inUVMin.x) / (inUVMax.x - inUVMin.x);
            uv.y = (uv.y - inUVMin.y) / (inUVMax.y - inUVMin.y);
            if (uv.x < 0 || uv.x > 1 || uv.y < 0 || uv.y > 1)
                Debug.LogError($"UV out of range of window {uv.x},{uv.y} - {uvs[i].x},{uvs[i].y} - {inUVMin}-{inUVMax}");
            uvs[i] = _atlas.Remap(uv, mr.m_atlasRegion);
        }
        // Add all indices to one list
        List<int> newInds = new();
        for (int i = 0; i < _mesh.subMeshCount; ++i)
        {
            var inds = _mesh.GetIndices(i);
            for (int j = 0; j < inds.Length; ++j)
            {
                newInds.Add(inds[j]);
            }
        }
        // Create the new mesh and export
        var newMesh = new Mesh();
        newMesh.vertices = _mesh.vertices;
        newMesh.uv = uvs;
        newMesh.normals = _mesh.normals;
        newMesh.tangents = _mesh.tangents;
        newMesh.uv2 = _mesh.uv2;
        newMesh.colors = _mesh.colors;
        newMesh.SetIndices(newInds.ToArray(), MeshTopology.Triangles, 0);
        newMesh.UploadMeshData(false);
        var atlasFilterFile = $"{_assetPath}/{fileName}.asset";
        AssetDatabase.CreateAsset(newMesh, atlasFilterFile);
        AssetDatabase.ImportAsset(atlasFilterFile);
        
        var go = new GameObject(_file);
        var rnd = go.AddComponent<MeshRenderer>();
        var flt = go.AddComponent<MeshFilter>();
        rnd.material = AssetDatabase.LoadAssetAtPath<Material>(_atlasMatFile);
        flt.mesh = AssetDatabase.LoadAssetAtPath<Mesh>(atlasFilterFile);

        var atlasModelFile = $"{_prefabPath}/{fileName}.prefab";
        PrefabUtility.SaveAsPrefabAsset(go, atlasModelFile);
        AssetDatabase.ImportAsset(atlasModelFile);
        
        DestroyImmediate(go);
    }

    static void GenerateTextureAtlas(Atlas _atlas, string _id, string _label, List<MaterialRegion> _materialRegions, Material _outMaterial, string _file, bool _debug = false)
    {
        var atlasTex = new Texture2D(_atlas.m_w, _atlas.m_h);
        var fill = new Color32[_atlas.m_w * _atlas.m_h];
        Color32 fillColour = new Color32(0, 255, 255, 255);
        for (int i = 0; i < _atlas.m_w * _atlas.m_h; ++i)
            fill[i] = fillColour;
        atlasTex.SetPixels32(fill);
        for (int i = 0; i < _materialRegions.Count; ++i)
        {
            var mr = _materialRegions[i];
            if (mr.m_consumedBy != null) continue;
            var atlasRegion = _atlas.m_usedRegions[mr.m_atlasRegion];
            var mtex = mr.m_material.GetTexture(_id) as Texture2D;
            if (mtex == null) continue;
            mtex = GetReadableTexture(mtex);
            int mw, mh;
            if (mtex == null)
            {
                mw = 1024; mh = 1024;
            }
            else
            {
                mw = mtex.width; mh = mtex.height;
            }
            int minPixX = Mathf.FloorToInt(mr.Umin01 * mw), minPixY = Mathf.FloorToInt(mr.Vmin01 * mh);
            int maxPixX = Mathf.CeilToInt(mr.Umax01 * mw), maxPixY = Mathf.CeilToInt(mr.Vmax01 * mh);
            int pixW = maxPixX - minPixX;
            int pixH = maxPixY - minPixY;
            Color[] pixels;
            if (mtex == null)
            {
                pixels = new Color[pixW * pixH];
                for (int p = 0; p < pixW * pixH; ++p) pixels[p] = Color.white;
            }
            else
            {
                pixels = mtex.GetPixels(minPixX, minPixY, pixW, pixH);
            }
            if (_debug)
            {
                Color debugPixelColour = Color.HSVToRGB((float) i / (float) _materialRegions.Count, 1, 1); //Color.Lerp(Color.red, Color.green, (float)i / (float)_materialRegions.Count);
                for (int p = 0; p < pixels.Length; ++p)
                    pixels[p] = debugPixelColour;
            }
            atlasTex.SetPixels(atlasRegion.m_x + _atlas.m_padding, atlasRegion.m_y + _atlas.m_padding, pixW, pixH, pixels);
            var debugColour = Color.magenta;
            const float showDebug = 0;
            for (int j = 1; j <= _atlas.m_padding; ++j)
            {
                for (int k = 0; k < pixW; ++k)
                {
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding + k, atlasRegion.m_y + _atlas.m_padding - j, pixels[k + 0 * pixW] * (1-showDebug) + debugColour * showDebug);
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding + k, atlasRegion.m_y + _atlas.m_padding + pixH + j - 1, pixels[k + (pixH - 1) * pixW] * (1 - showDebug) + debugColour * showDebug);
                }
                for (int k = 0; k < pixH; ++k)
                {
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding - j, atlasRegion.m_y + _atlas.m_padding + k, pixels[0 + k * pixW] * (1 - showDebug) + debugColour * showDebug);
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding + pixW + j - 1, atlasRegion.m_y + _atlas.m_padding + k, pixels[(pixW - 1) + k * pixW] * (1 - showDebug) + debugColour * showDebug);
                }
                for (int k = 1; k <= _atlas.m_padding; ++k)
                {
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding - j, atlasRegion.m_y + _atlas.m_padding - k, pixels[0 + 0 * pixW] * (1 - showDebug) + debugColour * showDebug);
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding + pixW + j - 1, atlasRegion.m_y + _atlas.m_padding - k, pixels[(pixW - 1) + 0 * pixW] * (1 - showDebug) + debugColour * showDebug);
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding - j, atlasRegion.m_y + _atlas.m_padding + pixH + k - 1, pixels[0 + (pixH - 1) * pixW] * (1 - showDebug) + debugColour * showDebug);
                    atlasTex.SetPixel(atlasRegion.m_x + _atlas.m_padding + pixW + j - 1, atlasRegion.m_y + _atlas.m_padding + pixH + k - 1, pixels[(pixW - 1) + (pixH - 1) * pixW] * (1 - showDebug) + debugColour * showDebug);
                }
            }
        }
        atlasTex.Apply(false, false);
        var atlasTexFile = $"{_file}.{_label}.png";
        File.WriteAllBytes(atlasTexFile, atlasTex.EncodeToPNG());
        AssetDatabase.ImportAsset(atlasTexFile);
        //DebugReadableTextureCopies(_file);
        DestroyReadableTextureCopies();

        _outMaterial.SetTexture(_id, AssetDatabase.LoadAssetAtPath<Texture2D>(atlasTexFile));
    }

    static Dictionary<Texture2D, Texture2D> s_readableTextures = new();

    public static void DebugReadableTextureCopies(string _file)
    {
        foreach (var kvp in s_readableTextures)
        {
            var name = _file + "." + kvp.Key.name + ".readable.png";
            File.WriteAllBytes(name, kvp.Value.EncodeToPNG());
            AssetDatabase.ImportAsset(name);
        }
    }

    public static void DestroyReadableTextureCopies()
    {
        foreach (var kvp in s_readableTextures)
            DestroyImmediate(kvp.Value);
        s_readableTextures.Clear();
    }

    public static Texture2D GetReadableTexture(Texture2D source)
    {
        if (source == null) return null;
        if (s_readableTextures.TryGetValue(source, out var existing)) return existing;
        if (source.isReadable) return source;
        
        //Debug.LogError($"GRT {source.name} {source.format} {source.graphicsFormat} {source.isDataSRGB}");
        var readWriteMode = source.isDataSRGB ? RenderTextureReadWrite.sRGB : RenderTextureReadWrite.Linear;
        
        RenderTexture tmp = RenderTexture.GetTemporary(
            source.width,
            source.height,
            0,
            RenderTextureFormat.ARGB32,
            readWriteMode);

        Graphics.Blit(source, tmp);
        RenderTexture previous = RenderTexture.active;
        RenderTexture.active = tmp;
        Texture2D result = new Texture2D(source.width, source.height);
        result.ReadPixels(new Rect(0, 0, tmp.width, tmp.height), 0, 0);

        if (source.isDataSRGB == false && source.graphicsFormat == GraphicsFormat.RGBA_DXT5_UNorm)
        {
            var pixels = result.GetPixels();
            for (int j = 0; j < pixels.Length; ++j)
            {
                float x = pixels[j].a * 2 - 1, y = pixels[j].g * 2 - 1;
                float z = 1 - Mathf.Sqrt(x * x + y * y);
                pixels[j] = new Color(x * .5f + .5f, y * .5f + .5f, z * .5f + .5f, 1);
            }
            result.SetPixels(pixels);
        }
        
        result.Apply();
        RenderTexture.active = previous;
        RenderTexture.ReleaseTemporary(tmp);
        s_readableTextures[source] = result;
        return result;
    }
    
    public class MaterialRegion
    {
        public Vector2 m_minUV, m_maxUV;
        public float Umin01 => Mathf.Repeat(m_minUV.x, 1);
        public float Vmin01 => Mathf.Repeat(m_minUV.y, 1);
        public float Umax01 => Mathf.Repeat(m_maxUV.x, 1);
        public float Vmax01 => Mathf.Repeat(m_maxUV.y, 1);
        public Vector2 UVmin01 => new Vector2(Umin01, Vmin01);
        public Vector2 UVmax01 => new Vector2(Umax01, Vmax01);
        public int m_pixelW, m_pixelH;
        public int m_meshIndex;
        public int m_materialIndex;
        public Material m_material;
        public int m_region;
        public int m_atlasRegion;
        public int m_originalIndex;
        public Color m_tintColour;
        public MaterialRegion m_consumedBy = null;

        public bool TryMerge(MaterialRegion _other)
        {
            Vector2 thisUVMin = UVmin01, thisUVMax = UVmax01;
            Vector2 otherUVMin = _other.UVmin01, otherUVMax = _other.UVmax01;
            var thisArea = (thisUVMax.x - thisUVMin.x) * (thisUVMax.y - thisUVMin.y);
            var otherArea = (otherUVMax.x - otherUVMin.x) * (otherUVMax.y - otherUVMin.y);
            var cmin = Vector2.Max(thisUVMin, otherUVMin);
            var cmax = Vector2.Min(thisUVMax, otherUVMax);
            if (cmax.x < cmin.x || cmax.y < cmin.y) return false; // no overlap
            var ct = (cmax.x - cmin.x) * (cmax.y - cmin.y);
            var emin = Vector2.Min(thisUVMin, otherUVMin);
            var emax = Vector2.Max(thisUVMax, otherUVMax);
            var et = (emax.x - emin.x) * (emax.y - emin.y);

            //var cOfOT = ct / otherArea;
            var c = ct / thisArea;
            //var e = et / thisArea;
            //var cofe = ct / et; 
            //if (c > Region.Threshold(e)) return false;
            //if (c < .5f) return false;
            if (et - thisArea > otherArea) return false;
            // reasonable overlap, merge other into this
            if (emin.x < thisUVMin.x) m_minUV.x += emin.x - thisUVMin.x;
            if (emax.x > thisUVMax.x) m_maxUV.x += emax.x - thisUVMax.x;
            if (emin.y < thisUVMin.y) m_minUV.y += emin.y - thisUVMin.y;
            if (emax.y > thisUVMax.y) m_maxUV.y += emax.y - thisUVMax.y;
            _other.m_consumedBy = this;
            return true;
        }

        public void SetPixels(int _texw, int _texh)
        {
            int uvW = (int) ((m_maxUV.x - m_minUV.x) * _texw);
            int uvH = (int) ((m_maxUV.y - m_minUV.y) * _texh);
            m_pixelW = uvW;
            m_pixelH = uvH;
        }
    };

    public class Atlas
    {
        public class AtlasRegion
        {
            public int m_x, m_y, m_w, m_h;

            public float FitScore(int _w, int _h)
            {
                if (_w > m_w || _h > m_h) return 0;
                return _w * _h / (float)(m_w * m_h);
            }

            public Vector2 Remap(Vector2 _uv, float _padding, float _atlasW, float _atlasH)
            {
                float ustart = (m_x + _padding) / _atlasW, vstart = (m_y + _padding) / _atlasH;
                float usize = (m_w - _padding * 2) / _atlasW, vsize = (m_h - _padding * 2) / _atlasH;
                return new Vector2(ustart + _uv.x * usize, vstart + _uv.y * vsize);
            }

            public bool Combine(AtlasRegion _other)
            {
                if (m_x == _other.m_x && m_w == _other.m_w)
                {
                    if (m_y == _other.m_y + _other.m_h)
                    {
                        m_y = _other.m_y;
                        m_h += _other.m_h;
                        return true;
                    }
                    else if (m_y + m_h == _other.m_y)
                    {
                        m_h += _other.m_h;
                        return true;
                    }
                }
                if (m_y == _other.m_y && m_h == _other.m_h)
                {
                    if (m_x == _other.m_x + _other.m_w)
                    {
                        m_x = _other.m_x;
                        m_w += _other.m_w;
                        return true;
                    }
                    else if (m_x + m_w == _other.m_x)
                    {
                        m_w += _other.m_w;
                        return true;
                    }
                }
                return false;
            }
        }
        public List<AtlasRegion> m_freeRegions = new();
        public List<AtlasRegion> m_usedRegions = new();
        public int m_w, m_h, m_padding, m_errors = 0;
        public Atlas(int _w, int _h, int _padding)
        {
            m_w = _w; m_h = _h;
            m_freeRegions.Add(new AtlasRegion() { m_x = 0, m_y = 0, m_w = _w, m_h = _h });
            m_padding = _padding;
        }

        public Vector2 Remap(Vector2 _uv, int _region)
        {
            return m_usedRegions[_region].Remap(_uv, m_padding, m_w, m_h);
        }

        public int AddRegion(int _w, int _h)
        {
            _w += m_padding * 2;
            _h += m_padding * 2;
            int bestFit = -1;
            float bestFitScore = 0;
            for (int i = 0; i < m_freeRegions.Count; ++i)
            {
                float fitScore = m_freeRegions[i].FitScore(_w, _h);
                if (fitScore > bestFitScore)
                {
                    bestFitScore = fitScore;
                    bestFit = i;
                }
            }
            if (bestFit == -1)
            {
                ++m_errors;
                //Debug.LogError($"Unable to fit region {_w-m_padding*2}x{_h-m_padding*2}");
                return 0;
            }
            var alloc = m_freeRegions[bestFit];
            var newRegion = new AtlasRegion() { m_x = alloc.m_x, m_y = alloc.m_y, m_w = _w, m_h = _h };
            AtlasRegion newEmpty = null;
            int excessW = alloc.m_w - _w, excessH = alloc.m_h - _h;
            if (excessW > excessH)
            {
                // first split on w
                newEmpty = new AtlasRegion() { m_x = alloc.m_x, m_y = alloc.m_y + _h, m_w = _w, m_h = alloc.m_h - _h };
                alloc.m_x += _w; alloc.m_w -= _w;
            }
            else
            {
                // first split on h
                newEmpty = new AtlasRegion() { m_x = alloc.m_x + _w, m_y = alloc.m_y, m_w = alloc.m_w - _w, m_h = _h };
                alloc.m_y += _h; alloc.m_h -= _h;
            }
            m_freeRegions.Add(newEmpty);
            m_usedRegions.Add(newRegion);
            CombineFreeRegions();
            return m_usedRegions.Count - 1;
        }

        void CombineFreeRegions()
        {
            for (int i = 0; i < m_freeRegions.Count; ++i)
            {
                var ri = m_freeRegions[i];
                for (int j = 0; j < i; ++j)
                {
                    var rj = m_freeRegions[j];
                    if (ri.Combine(rj))
                    {
                        --i;
                        break;
                    }
                }
            }
        }

        public long TotalUsed()
        {
            long total = 0;
            for (int i = 0; i < m_usedRegions.Count; ++i)
                total += m_usedRegions[i].m_w * m_usedRegions[i].m_h;
            return total;
        }
        public long TotalArea() => m_w * m_h;

        public void Optimise()
        {
            // minimise the final atlas size by power-of-two cuts
            // could create a clever and subtle routine to coalesce free regions into blocks
            // but much simpler to create a bitmap and look for blocks
            var freeArea = new byte[m_w * m_h];
            int stride = m_w;
            for (int i = 0; i < m_freeRegions.Count; ++i)
            {
                var region = m_freeRegions[i];
                for (int y = 0; y < region.m_h; ++y)
                    for (int x = 0; x < region.m_w; ++x)
                        freeArea[(region.m_x+x)+stride*(region.m_y+y)] = 1;
            }
            bool keepGoing = true;
            while (keepGoing)
            {
                keepGoing = false;
                bool found = true;
                for (int i = m_w / 2; found && i < m_w; ++i)
                    for (int j = 0; found && j < m_h; ++j)
                        if (freeArea[i + stride * j] == 0)
                            found = false;
                if (found)
                {
                    m_w /= 2;
                    keepGoing = true;
                }
                else
                {
                    found = true;
                    for (int i = m_h / 2; found && i < m_h; ++i)
                        for (int j = 0; found && j < m_w; ++j)
                            if (freeArea[j + stride * i] == 0)
                                found = false;
                    if (found)
                    {
                        m_h /= 2;
                        keepGoing = true;
                    }
                }
            }
        }

        public static int RoundDownToPo2(int _n)
        {
            int pn = _n;
            while (_n != 0)
            {
                pn = _n;
                _n &= _n - 1;
            }
            return pn;
        }
    }

    static Vector2 PrepUV(Vector2 _uv, int _w, int _h)
    {
        _uv.x = Mathf.Repeat(_uv.x, 1) * _w;
        _uv.y = Mathf.Repeat(_uv.y, 1) * _h;
        return _uv;
    }

    static void DrawUVs(Vector2 _v0, Vector2 _v1, Vector2 _v2, int _index, byte[] _bitmap, int _w, int _h)
    {
        _v0 = PrepUV(_v0, _w, _h);
        _v1 = PrepUV(_v1, _w, _h);
        _v2 = PrepUV(_v2, _w, _h);
        Vector2 vMin = _v0, vMax = _v0;
        if (_v1.y < vMin.y) vMin = _v1;
        if (_v2.y < vMin.y) vMin = _v2;
        if (_v1.y > vMax.y) vMax = _v1;
        if (_v2.y > vMax.y) vMax = _v2;
        var vMid = _v0 + _v1 + _v2 - vMin - vMax;
        Vector2 v1 = vMin, v2 = vMin;
        if (vMid.y - vMin.y >= 1)
        {
            for (int y = (int) vMin.y; y <= (int) vMid.y; ++y)
            {
                if (y < 0) continue;
                if (y > _h) break;
                int x1 = (int) (vMin.x + (vMid.x - vMin.x) * (y - vMin.y) / (vMid.y - vMin.y));
                int x2 = (int) (vMin.x + (vMax.x - vMin.x) * (y - vMin.y) / (vMax.y - vMin.y));
                DrawSpan(_bitmap, _w, y, x1, x2, _index);
            }
        }
        for (int y = (int) vMid.y + 1; y <= (int) vMax.y; ++y)
        {
            if (y < 0) continue;
            if (y > _h) break;
            int x1 = (int) (vMid.x + (vMax.x - vMid.x) * (y - vMid.y) / (vMax.y - vMid.y));
            int x2 = (int) (vMin.x + (vMax.x - vMin.x) * (y - vMin.y) / (vMax.y - vMin.y));
            DrawSpan(_bitmap, _w, y, x1, x2, _index);
        }
    }

    static void DrawSpan(byte[] _bitmap, int _w, int _y, int _x1, int _x2, int _value)
    {
        int minX = Mathf.Min(_x1, _x2), maxX = _x1 + _x2 - minX;
        minX = Mathf.Max(0, minX);
        maxX = Mathf.Min(_w - 1, maxX);
        for (int x = minX; x <= maxX; ++x)
            _bitmap[x + _y * _w] = (byte) _value;
    }
}
