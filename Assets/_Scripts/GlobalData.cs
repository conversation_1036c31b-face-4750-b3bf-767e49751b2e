#define USE_TERRAIN_TEXTURE_FOR_GRASS
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using UnityEngine.Rendering;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using Unity.Burst;
using Unity.Collections.LowLevel.Unsafe;
using UnityEngine.Serialization;
using Random = Unity.Mathematics.Random;

#if UNITY_EDITOR
using UnityEditor;
#endif

public partial class GlobalData : MonoSingleton<GlobalData> {
	public static string CurrencySymbol = "<sprite=0>";
	public static string GemsSymbol = "<sprite=3>";
	public static string TicketsSymbol = "<sprite=4>";
	public static string CarryBoxAnim = "CarryBox";
	public static string CarryProductAnim = "CarryBoxHigh";
	
	public const string c_productId = "Product";
	public const string c_productDesignId = "ProductDesign";
	public const string c_productDesignTutorialId = "ProductTutorial";
	public const string c_avatarId = "Avatar";
	public const string c_designCompetitionId = "DesignCompetition";

	public const string c_avatarRootName = "Armature";
	
	protected override void _OnDestroy() {
		Debug.Log($"Shutting down");
		DisableAllManagers();
		DisposeAll();
	}
	public void DisposeAll() {
		if (m_heights.Length > 0)
			m_heights.Dispose();
		if (m_heightsOriginal.Length > 0)
			m_heightsOriginal.Dispose();
		if (m_navGrid.Length > 0)
			m_navGrid.Dispose();
		if (m_navGridLowRes.Length > 0)
			m_navGridLowRes.Dispose();
		if (m_waterPresence.Length > 0)
			m_waterPresence.Dispose();
		if (m_navGenerationFrameArray.Length > 0)
			m_navGenerationFrameArray.Dispose();
	}
	void OnDisable() {
		DisableAllManagers();
		KillAllNavJobs();
	}
	void DisableAllManagers() {
	}
	
	public Vector3 DragOffset() { return Vector3.zero; }

	[System.Serializable]
	public class PollutionColorSet
	{
		public Color m_buildingColor;
		public Color m_smokeColor;
		public Color m_fogPlaneColor;
		public Color m_orderCardColor;
		public string m_colourName;
		public PollutionColorSet(float _r, float _g, float _b, string _colourName)
		{
			Color color = new Color(_r, _g, _b);
			m_buildingColor = color;
			m_smokeColor = color;
			m_fogPlaneColor = color;
			m_orderCardColor = color;
			m_colourName = _colourName;
		}
		
		public PollutionColorSet(Color _color, string _colourName) : this(_color.r, _color.g, _color.b, _colourName)
		{
		}
	}	

	public AnimationClip m_defaultAnimationClip;
	public Material m_landscapeMaterial, m_seaMaterial;
	public Material m_testGridMaterial, m_testGridMaterial2;
	public GameObject m_characterPrefab;
	public GameObject m_touristPrefab;
	public GameObject[] m_characterContainerPrefabs;
	public GameObject m_undergroundManagerPrefab;
	public Transform m_centreGUI;
	public Transform m_characterHolder;
	public Transform m_buildingHolder;
	public Transform m_spawnPointHolder;
	public Transform m_questSpawnPointHolder;
	public Transform m_pickupsHolder;
	public Transform m_namedPointHolder;
	public GameObject m_terrainSelectionCirclePrefab;
	public Transform m_terrainCircleSelectionHolder;
	public GameObject m_psysOutlinePrefab;
	public GameObject m_roadSegmentEditPrefab;
	public GameObject m_roadSegmentEndpointEditPrefab;
	public GameObject m_factoryCranePrefab;
	public GameObject m_spawnPointPrefab;
	public GameObject m_plantPartialDestroyFX;
	public GameObject m_plantFinalDestroyFX;
	public TintParameters[] m_tintParameters;
	public Material m_roadSegmentEndpointEditMaterialUnselected;
	public Material m_roadSegmentEndpointEditMaterialSelected;
	public Material m_roadSegmentEndpointEditMaterialPlayerPath;
	public Material m_positiveSelectionMaterial;
	public Material m_negativeSelectionMaterial;
	public Material m_conveyorMaterial;
	public Material m_plotMaterial;
	public Material m_plotUnselectedMaterial;
	public Material m_terrainCircleProjectionMaterial;
	public Material m_throwIndicatorMaterial;
	public Material m_throwIndicatorTargetMaterial;
	public Material m_areaOfEffectMaterial;
	public bool m_roadEditMode = false;
	public Shader m_CSGCuboidShader;
	public Gradient m_skinColors;
	public Gradient m_hairColors;
	public Sprite m_pinSprite;
	public Sprite m_currencySpriteCash;
	public Sprite m_currencySpriteRoyalFavours;
	public Sprite m_currencySpriteLordsFavours;
	public Sprite m_currencySpritePeoplesFavours;
	public Sprite m_currencySpriteMysticFavours;
	public float m_clothingColorBlendAmount = .5f;
	public Color[] m_townSatisfactionColours;
	public Color[] m_rarityColours = new Color[] {
		Color.white, new Color(0,.62f,.52f), new Color(1,0,.54f), new Color(.86f,.62f,0),
	};
	public Color DesignPodiumPositionColourMe = new Color(.25f, .25f, .25f, 1);
	public Color DesignPodiumPositionColourOther = new Color(.75f, .4f, 0, 1);
	public Color m_buildBarBusinessColour = new Color(0.827451f, 0.47058824f, 0.28235295f, 1);
	public Color m_buildBarCivicColour = new Color(0.22917409f, 0.2826116f, 0.4716981f, 1);
	public Color m_buildBarDecorationColour = new Color(0.44313726f, 0.61960787f, 0.46666667f, 1);
	public Color m_buildBarRoadsColour = new Color(0.41568628f, 0.5529412f, 0.6431373f, 1);
	public Color m_buildBarLockedColour = new Color(0.47058824f, 0.47058824f, 0.47058824f, 0.5019608f);
	public Color m_buildingHighlightColour = new Color(0.7f,0.8f,1.0f, 1.0f);
	public Color m_designTableBezierColour = Color.clear;
	public Color m_designTableXrayColour = new Color(0,0,0,.25f);
	public Color m_buildingPlaceBezierColour = Color.clear;
	public Color m_personBezierColour = Color.clear;
	public Color m_miscPositiveBezierColour = Color.green;
	public Color m_miscNegativeBezierColour = Color.red;
	public Color m_UIBezierColour = Color.clear;
	[ColorUsage(false, true)] public Color m_buildingTint1 = Color.white;
	[ColorUsage(false, true)] public Color m_buildingTint2 = Color.white;
	public float m_buildingTintAmount = 0;
	public Color m_matterBezierColour = Color.clear;
	public Color m_moneyBezierColour = Color.clear;
	public Color m_productBezierColour = Color.clear;
	public Color m_metalMaterialBezierColour = Color.clear;
	public Color m_clayMaterialBezierColour = Color.clear;
	public Color m_coalMaterialBezierColour = Color.clear;
	public Color m_clothMaterialBezierColour = Color.clear;
	public Color m_woodMaterialBezierColour = Color.clear;
	public Color m_mineralMaterialBezierColour = Color.clear;
	public Color m_produceMaterialBezierColour = Color.clear;	
	public PollutionColorSet[] m_pollutionColours;
	public TMPro.TMP_FontAsset m_mainFont;
	//public NGBoat m_tugBoatPrefab = null;
	//public NGBoat m_bargePrefab = null;
	public NGBusinessDecisionCard m_decisionCardPrefab;
	public TooPoorIndicator m_tooPoorDialogPrefab;
	public GameObject m_managedBlockPrefab;
	public GameObject m_snapPointPrefab;
	public Material m_snapPointMaterialUnselected;
	public Material m_snapPointMaterialSelected;
	public GameObject m_obstacleMarkerRoadPrefab;
	public GameObject m_obstacleMarkerBuildingPrefab;
	public GameObject m_plotPrefab;
	public Material m_plotMaterialUnselected;
	public Material m_plotMaterialSelected;
	public GameObject m_pickupLinePrefab;
	public GameObject[] m_factoryPrefab;
	public GameObject m_spinner3DPrefab;
	public GameObject m_hapticExplorerPrefab;
	public Sprite m_avatar;
	public PlayerDetailsGUI m_playerDetailsGUIPrefab;
	public Dictionary<GameState_Design,Sprite> m_designSprites = new ();
	public List<BuildingNav> m_buildings = new List<BuildingNav>();
	public PickupGUI m_pickupGUIPrefab;
	public GameObject m_bankPopupBoardPrefab;
	public PhysicsMaterial m_characterRagdollPhysics;
	private Texture2D m_clearTexture; public Texture2D ClearTexture => m_clearTexture;
	private Sprite m_clearSprite; public Sprite ClearSprite => m_clearSprite;
	public float m_tabletPreferredHeightOverride = 100;
	public float m_phonePreferredHeightOverride = 80;

	public float m_heldItemScale = 0.1f;
	
	public int m_splatPathToDoor = -1;
	public int m_splatUnderBuilding = -1;
	public int m_splatUnderBuildingInner = -1;
	public float m_splatMargin = .5f;
	
	public Material m_cloudPostProcessMaterial;

	public bool m_drawAllNavPaths = false;
	
	[Range(0f,1f)] public float m_orderCardFactionColourSaturation = 0.5f;
	[Range(0f,1f)] public float m_orderCardFactionColourValue = 0.5f;

	public bool m_forceWorkersToTavern = false;
	
#if UNITY_EDITOR
	int m_lastBuildingTintValidation;
	void OnValidate() {
		UnityEditor.EditorPrefs.SetBool( "kAutoRefresh", true);

		if (Application.isPlaying && NGManager.Me != null && NGManager.Me.m_maBuildings != null)
		{
			int validation = 987;
			for (int i = 0; i < m_tintParameters.Length; ++i)
				validation = (validation * 77) + m_tintParameters[i].GetValidationCode();
			if (validation != m_lastBuildingTintValidation)
			{
				m_lastBuildingTintValidation = validation;
				m_tintParametersLookup = null;
				foreach (var ng in NGManager.Me.m_maBuildings)
					MaterialSetColour.RandomiseAllMaterials(ng.Visuals.gameObject);
			}
		}
	}
#endif

	public Color PrepareClothingColour(Color _c)
	{
		var tintAmount = UnityEngine.Random.Range(0, 1);
		tintAmount = 1 - tintAmount * tintAmount; // bias towards full
		//_c.a = tintAmount * GlobalData.Me.m_clothingColorBlendAmount;
		return _c;
	}

	[Serializable]
	public class TintParameters
	{
		public string m_name;
		public FlexibleGradient m_colours = new();
		public float m_amount;
		public float m_perObjectRandomness;
		private int m_seed;
		
		public int GetValidationCode()
		{
			var g = m_colours.Hash();
			var a = math.asint(m_amount);
			var b = math.asint(m_perObjectRandomness);
			return g * 1237 + a * 2377 + b * 7337;
		}

		public (Color, float) Generate(int _seed, int _subseed)
		{
			var seed = (uint)(_seed + m_seed);
			Utility.XorShift(ref seed);
			var rndv = Utility.XorShift01(ref seed);
			seed += (uint)_subseed;
			rndv += Utility.XorShift01(ref seed) * m_perObjectRandomness;
			rndv = math.frac(rndv);
			var c = m_colours.Evaluate(rndv);
			return (c, m_amount);
		}
		public TintParameters Prepare()
		{
			m_name = m_name.ToLower();
			m_seed = m_name.GetHashCode();
			return this;
		}
	}

	private Dictionary<string, TintParameters> m_tintParametersLookup;

	public TintParameters GetTintParameters(string _id)
	{
		if (m_tintParametersLookup == null || m_tintParametersLookup.Count != m_tintParameters.Length)
		{
			m_tintParametersLookup = new Dictionary<string, TintParameters>();
			foreach (var tp in m_tintParameters)
				m_tintParametersLookup[tp.m_name.ToLower()] = tp;
		}
		_id = _id.ToLower();
		if (m_tintParametersLookup.TryGetValue(_id, out var t))
			return t;
		return null;
	}

	public Color GetSkinColour(float _f) {
		return m_skinColors.Evaluate(_f);
	}
	public Color GetHairColour(float _f) {
		return m_hairColors.Evaluate(_f);
	}
	
	static readonly string[] DEBUG_M_NAMES =
	{
		"Gary",
		"Richard",
		"Peter",
		"Paul",
		"Ben",
		"Milo",
		"Dimitri",
		"David",
		"John",
		"Alessandro",
		"Jody",
		"Allesandro",
	};
	static readonly string[] DEBUG_F_NAMES =
	{
		"Francesca",
		"Nikki",
		"Jen",
		"Annah",
		"Thea",
		"Alice",
		"Iva",
		"Lenja",
		"Chelsea",
		"Michaela"
	};
	public static string GetRandomName(bool _isMale = true)
	{
		if(_isMale) return DEBUG_M_NAMES.PickRandom();
		return DEBUG_F_NAMES.PickRandom();
	}

	[Header("SalesGame")]
	//public PlayerController m_salesSelectedSalesman; public PlayerController SelectedSalesman { get { return m_salesSelectedSalesman; } set { m_salesSelectedSalesman = value; }} 
	//public HoverLocation m_salesSelectedLocation; public HoverLocation SelectedLocation {get { return m_salesSelectedLocation; } set { m_salesSelectedLocation = value; }}
	
	private Vector4 m_gizmoTB;
	public bool CheckTerrainBlocker(float _x1, float _y1, float _x2, float _y2, bool _debug = false) {
		if (_debug) m_gizmoTB = new Vector4(_x1, _y1, _x2, _y2);
		var job = new CheckTerrainBlockersJob(Mathf.Clamp(TerrainX(_x1), 0, c_heightmapW),
											  Mathf.Clamp(TerrainZ(_y1), 0, c_heightmapW),
											  Mathf.Clamp(TerrainX(_x2), 0, c_heightmapW),
											  Mathf.Clamp(TerrainZ(_y2), 0, c_heightmapW));
		job.Schedule().Complete();
		return job.GetOutput();
	}

	public NativeArray<Vector3> CheckTerrainBlockerBatch(NativeArray<Vector3> _inPositions, float _radius, bool _isWaterSurface, float _randomness)
	{
		var job = new CheckTerrainBlockersBatchJob(_inPositions, _radius, _isWaterSurface, _randomness);
		job.Schedule().Complete();
		return job.GetOutput();
	}

	public enum AllowNavType
	{
		None = 0,
		AnyLowNav,
		AnyNav,
		AnyPerfectNav,
	};

	public long[] CheckNavTerrainBlockerAndDistrictAligned(Vector3 _pos, Vector3 _side, Vector3 _fwd, int _width, int _depth, float _step, bool _ignoreDistricts = false, bool _ignoreWater = false, AllowNavType _allowNavType = AllowNavType.None, int _maxBlockers = 1)
	{
		var job = new CheckNavTerrainBlockersAndDistrictsAlignedJob(_pos, _side, _fwd, _width, _depth, _step, m_heights, _ignoreDistricts || NGManager.Me.m_placeBuildingsAnywhere, DistrictManager.Me.m_textureData, m_navGrid, _ignoreWater, _allowNavType, _maxBlockers);
		job.Schedule().Complete();
		return job.GetBlockers();
	}

	public bool CheckTerrainBlocker(float _x, float _z)
	{
		var x = Mathf.Clamp(TerrainX(_x), 0, c_heightmapW);
		var z = Mathf.Clamp(TerrainZ(_z), 0, c_heightmapW);
		return ((m_waterPresence[(x >> 5) + z * ((c_heightmapW + 31) >> 5)] >> (x & 31)) & 1) != 0;
	}

	[BurstCompile]
	public struct CheckTerrainBlockersJob : IJob
	{
		[ReadOnly] public int m_minX;
		[ReadOnly] public int m_minY;
		[ReadOnly] public int m_maxX;
		[ReadOnly] public int m_maxY;
		[ReadOnly] public int m_width;

		[ReadOnly] public NativeArray<UInt32> m_waterPresence;

		public NativeArray<bool> m_found;
		public CheckTerrainBlockersJob(int _x1, int _y1, int _x2, int _y2) {
			m_minX = _x1;
			m_minY = _y1;
			m_maxX = _x2;
			m_maxY = _y2;
			m_width = c_heightmapW;
			m_waterPresence = Me.m_waterPresence;
			m_found = new NativeArray<bool>(1, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		}

		public void Execute()
		{
			int stride = (m_width + 31) / 32;
			var bitmap = m_waterPresence;

			int minX = m_minX, maxX = m_maxX;
			int minY = m_minY, maxY = m_maxY;

			int startIndex = minX >> 5;
			int endIndex = maxX >> 5;

			uint startMask = uint.MaxValue << (minX & 31);
			uint endMask = uint.MaxValue >> (31 - (maxX & 31));

			m_found[0] = false;
			if (startIndex == endIndex)
			{
				uint mask = startMask & endMask;
				for (int y = minY, yIndex = minY * stride; y <= maxY; ++y, yIndex += stride)
				{
					if ((bitmap[startIndex + yIndex] & mask) != 0)
					{
						m_found[0] = true;
						return;
					}
				}
			}
			else
			{
				for (int y = minY, yIndex = minY * stride; y <= maxY; ++y, yIndex += stride)
				{

					if ((bitmap[startIndex + yIndex] & startMask) != 0)
					{
						m_found[0] = true;
						return;
					}
					for (int i = startIndex + 1; i < endIndex; i++)
					{
						if (bitmap[i + yIndex] != 0)
						{
							m_found[0] = true;
							return;
						}
					}
					if ((bitmap[endIndex + yIndex] & endMask) != 0)
					{
						m_found[0] = true;
						return;
					}
				}
			}
		}
		
		public bool GetOutput()
		{
			var output = m_found[0];
			m_found.Dispose();
			return output;
		}
	}

	[BurstCompile]
	public struct CheckTerrainBlockersBatchJob : IJob
	{
		[ReadOnly] public NativeArray<Vector3> m_inPositions;
		[ReadOnly] public float m_radius;
		[ReadOnly] public bool m_isWaterSurface;
		[ReadOnly] public float m_randomness;
		[ReadOnly] public int m_width;
		[ReadOnly] public int m_terrainOriginX;
		[ReadOnly] public int m_terrainOriginZ;
		[ReadOnly] public float m_terrainXZScale;

		[ReadOnly] public NativeArray<UInt32> m_waterPresence;

		public NativeArray<Vector3> m_outPositions;
		public CheckTerrainBlockersBatchJob(NativeArray<Vector3> _inPositions, float _radius, bool _isWaterSurface, float _randomness)
		{
			m_inPositions = _inPositions;
			m_radius = _radius;
			m_isWaterSurface = _isWaterSurface;
			m_randomness = _randomness;

			m_terrainOriginX = c_terrainOriginX;
			m_terrainOriginZ = c_terrainOriginZ;
			m_terrainXZScale = c_terrainXZScale;

			m_width = c_heightmapW;
			m_waterPresence = Me.m_waterPresence;
			m_outPositions = new NativeArray<Vector3>(_inPositions.Length, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		}

		int TerrainX(float _worldX) => (int)((_worldX - m_terrainOriginX) * m_terrainXZScale);
		int TerrainZ(float _worldZ) => (int)((_worldZ - m_terrainOriginZ) * m_terrainXZScale);

		public void Execute()
		{
			uint seedBase = 0x1234;
			for (int i=0; i<m_inPositions.Length; i++)
			{
				float3 pos = m_inPositions[i];
				uint seed = (uint) (pos.y * 100000 + seedBase);
				pos.x += (Utility.XorShift01(ref seed) * 2 - 1) * m_randomness;
				pos.z += (Utility.XorShift01(ref seed) * 2 - 1) * m_randomness;
				pos.y = 0;
				if (!m_isWaterSurface)
				{
					float x1 = pos.x - m_radius;
					float y1 = pos.y - m_radius;
					float x2 = pos.x + m_radius;
					float y2 = pos.y + m_radius;

					int minX = Mathf.Clamp(TerrainX(x1), 0, m_width);
					int minY = Mathf.Clamp(TerrainZ(y1), 0, m_width);
					int maxX = Mathf.Clamp(TerrainX(x2), 0, m_width);
					int maxY = Mathf.Clamp(TerrainZ(y2), 0, m_width);

					int stride = (m_width + 31) / 32;
					var bitmap = m_waterPresence;

					int startIndex = minX >> 5;
					int endIndex = maxX >> 5;

					uint startMask = uint.MaxValue << (minX & 31);
					uint endMask = uint.MaxValue >> (31 - (maxX & 31));

					if (startIndex == endIndex)
					{
						uint mask = startMask & endMask;
						for (int y = minY, yIndex = minY * stride; y <= maxY; ++y, yIndex += stride)
						{
							if ((bitmap[startIndex + yIndex] & mask) != 0)
							{
								pos.y = 1;
								break;;
							}
						}
					}
					else
					{
						for (int y = minY, yIndex = minY * stride; y <= maxY; ++y, yIndex += stride)
						{

							if ((bitmap[startIndex + yIndex] & startMask) != 0)
							{
								pos.y = 1;
								break;
							}
							for (int j = startIndex + 1; j < endIndex; j++)
							{
								if (bitmap[j + yIndex] != 0)
								{
									pos.y = 1;
									break;
								}
							}
							if ((bitmap[endIndex + yIndex] & endMask) != 0)
							{
								pos.y = 1;
								break;
							}
						}
					}
				}
				m_outPositions[i] = pos;
			}
		}
		
		public NativeArray<Vector3> GetOutput()
		{
			return m_outPositions;
		}
	}

	static bool s_haveShownDistrictSizeMismatchError = false;

	public const int c_districtMapRes = 512; // keep in line with any changes to the district map

	private static DebugConsole.Command s_districtDebugCmd = new DebugConsole.Command("districtdebug", _s => Me.ShowDistrictDebug(int.TryParse(_s, out var n) ? n : 50)); 
	private GameObject m_districtDebug;
	private void ShowDistrictDebug(int _size = 50)
	{
		var data = DistrictManager.Me.m_textureData;
		Debug.LogError($"ShowDistrictDebug - data size {data.Length} size {c_districtMapRes} = exp {c_districtMapRes * c_districtMapRes * 2}"); 
		var png = DistrictManager.Me.m_texture.EncodeToPNG();
		if (png != null)
			System.IO.File.WriteAllBytes($"{Application.persistentDataPath}/districtFilter.png", png);
		else
			Debug.LogError($"Failed to encode png1");
		var tex = new Texture2D(c_districtMapRes, c_districtMapRes, TextureFormat.RG16, false);
		tex.SetPixelData(data, 0);
		tex.Apply(false, false);
		png = tex.EncodeToPNG();
		if (png != null)
			System.IO.File.WriteAllBytes($"{Application.persistentDataPath}/districtFilter2.png", png);
		else
			Debug.LogError($"Failed to encode png2");
		
		if (m_districtDebug != null) Destroy(m_districtDebug);
		m_districtDebug = new GameObject("DistrictDebug");
		GameManager.Me.RaycastAtPoint(new Vector3(Screen.width / 2, Screen.height / 2, 0), out var hit, GameManager.c_layerTerrainBit);
		var pos = hit.point;
		int div = c_heightmapW / c_districtMapRes;
		var step = div / c_terrainXZScale;
		for (int dz = -_size; dz <= _size; ++dz)
		{
			var pz = pos.z + dz;
			var z = TerrainZ(pz);
			if (z < 0 || z >= c_heightmapW) continue;
			for (int dx = -_size; dx <= _size; ++dx)
			{
				var px = pos.x + dx;
				var x = TerrainX(px);
				if (x < 0 || x >= c_heightmapW) continue;
				var index = (x / div) + c_districtMapRes * (z / div);
				var red = data[index * 2 + 0];
				var green = data[index * 2 + 1];
				var o = GameObject.CreatePrimitive(PrimitiveType.Cube);
				o.name = $"D{x},{z}";
				o.transform.SetParent(m_districtDebug.transform);
				o.transform.position = (new Vector3(px, 0, pz)).GroundPosition(.5f);
				o.transform.localScale = new Vector3(step * .8f, 1, step * .8f);
				var rnd = o.GetComponent<Renderer>();
				rnd.material = m_roadSegmentEndpointEditMaterialUnselected;
				rnd.material.SetFloat("_IgnoreDistrictFilter", 1);
				rnd.material.SetColor("_R", new Color(red / 255f, green / 255f, 0));
			}
		}
	}

	[BurstCompile]
	public struct CheckNavTerrainBlockersAndDistrictsAlignedJob : IJob
	{
		[ReadOnly] public NativeArray<UInt32> m_waterPresence;
		[ReadOnly] public bool m_ignoreDistricts;
		[ReadOnly] public bool m_ignoreWater;
		[ReadOnly] public AllowNavType m_allowNavType;
		[ReadOnly] public NativeArray<byte> m_districtData;
		[ReadOnly] public NativeArray<byte> m_navData;
		[ReadOnly] public int m_width;
		[ReadOnly] public int m_depth;
		[ReadOnly] public float m_step;
		[ReadOnly] public float3 m_pos;
		[ReadOnly] public float3 m_side;
		[ReadOnly] public float3 m_fwd;
		
		public NativeArray<int> m_result;
		public NativeArray<long> m_blockers;
		
		public CheckNavTerrainBlockersAndDistrictsAlignedJob(float3 _pos, float3 _side, float3 _fwd, int _width, int _depth, float _step, NativeArray<float> _heights, bool _ignoreDistricts, NativeArray<byte> _districtData, NativeArray<byte> _navData, bool _ignoreWater, AllowNavType _allowNavType, int _maxBlockers = 1)
		{
			m_pos = _pos;
			m_side = _side;
			m_fwd = _fwd;
			m_width = _width;
			m_depth = _depth;
			m_step = _step;
			m_waterPresence = Me.m_waterPresence;
			m_ignoreDistricts = _ignoreDistricts;
			m_ignoreWater = _ignoreWater;
			m_allowNavType = _allowNavType;
			m_districtData = _districtData;
			m_navData = _navData;
			m_result = new NativeArray<int>(2, Allocator.TempJob, NativeArrayOptions.ClearMemory);
			m_blockers = new NativeArray<long>(_maxBlockers, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);

#if UNITY_EDITOR
			if (s_haveShownDistrictSizeMismatchError == false && _districtData.Length != c_districtMapRes * c_districtMapRes * 2)
			{
				s_haveShownDistrictSizeMismatchError = true;
				Debug.LogError($"District data size mismatch - constant is {c_districtMapRes} should be {Mathf.Sqrt(_districtData.Length / 2)}");
			}
#endif
		}

		bool CheckNav(byte _value)
		{
			if (m_allowNavType == AllowNavType.AnyLowNav)
				return IsLowNavable(_value);
			if (m_allowNavType == AllowNavType.AnyNav)
				return IsNavable(_value);
			if (m_allowNavType == AllowNavType.AnyPerfectNav)
				return IsPerfectNavable(_value);
			return IsNavBuildable(_value);
		}

		public int CheckNavTerrainBlockerAndDistrict(float _x, float _z)
		{
			var x = math.clamp(TerrainX(_x), 0, c_heightmapW -1);
			var z = math.clamp(TerrainZ(_z), 0, c_heightmapW -1);
			int div = c_heightmapW / c_districtMapRes;
			if (m_ignoreDistricts == false && m_districtData[((x / div) + c_districtMapRes * (z / div)) * 2 + 1] < 128) return 1;
			int index = x + z * c_heightmapW;
			if (CheckNav(m_navData[index]) == false) return 2;
			if (m_ignoreWater == false)
				return (int)((m_waterPresence[(x >> 5) + z * ((c_heightmapW + 31) >> 5)] >> (x & 31)) & 1) * 3;
			return 0;
		}

		public int V2I(float3 _v) { return TerrainXr(_v.x) + (TerrainZr(_v.z) << c_heightmapWShift); }

		public void Execute()
		{
			const float c_blockerBitmapScale = .5f;
			int blockerBMW = (int)(m_width * c_blockerBitmapScale), blockerBMH = (int)(m_depth * c_blockerBitmapScale);
			var blockerBMSize = (blockerBMW * blockerBMH + 31) / 32;
			var blockerBitmap = new NativeArray<uint>(blockerBMSize, Allocator.Temp);
			
			for (float y = 1; y < m_depth; y += m_step)
			{
				for (float x = 0; x < m_width; x += m_step)
				{
					float3 p = m_pos + m_side * x + m_fwd * y;
					var res = CheckNavTerrainBlockerAndDistrict(p.x, p.z);
					if (res != 0)
					{
						m_result[0] = res;
						if (m_result[1] < m_blockers.Length)
						{
							bool allowAdd = true;
							if (blockerBMSize > 0)
							{
								int bIndex = (int)(x * c_blockerBitmapScale) + (int)(y * c_blockerBitmapScale) * blockerBMW;
								if ((blockerBitmap[bIndex >> 5] & (uint) (1 << (bIndex & 31))) == 0)
									blockerBitmap[bIndex >> 5] |= (uint) (1 << (bIndex & 31));
								else
									allowAdd = false;
							}
							if (allowAdd)
								m_blockers[m_result[1]++] = (long) V2I(p) + ((long) res << 32);
						}
						else
						{
							x = m_width;
							y = m_depth;
						}
					}
				}
			}
		}

		public long[] GetBlockers()
		{
			var res = new long[m_result[1]];
			for (int i = 0; i < res.Length; ++i)
				res[i] = m_blockers[i];
			m_result.Dispose();
			m_blockers.Dispose();
			return res;
		}

		public bool GetOutput()
		{
			bool res = m_result[0] != 0;
			m_result.Dispose();
			m_blockers.Dispose();
			return res;
		}
	}


#if UNITY_EDITOR
	[UnityEditor.MenuItem("22Cans/Art/Terrain/Smooth Terrain")]
	private static void SmoothTerrain()
	{
		var terrain = Terrain.activeTerrain;
		var td = terrain.terrainData;
		int res = td.heightmapResolution;
		var heights = td.GetHeights(0,0,res, res);
		var smoothHeights = new float[res,res];
		const int c_kernelHalfSize = 2;
		float[] kernel = new float[] { 1, 1, 1, 1, 1, };
		for (int y = 0; y < res; ++y)
		{
			for (int x = 0; x < res; ++x)
			{
				float f = 0;
				for (int yy = -c_kernelHalfSize; yy <= c_kernelHalfSize; ++yy)
				{
					for (int xx = -c_kernelHalfSize; xx <= c_kernelHalfSize; ++xx)
					{
						int ix = Mathf.Clamp(x + xx, 0, res-1);
						int iy = Mathf.Clamp(y + yy, 0, res-1);
						f += heights[iy, ix] * kernel[xx + c_kernelHalfSize] * kernel[yy + c_kernelHalfSize];
					}
				}
				f /= (c_kernelHalfSize * 2 + 1) * (c_kernelHalfSize * 2 + 1);
				smoothHeights[y, x] = f;
			}
		}
		td.SetHeights(0,0,smoothHeights);
	}

	[UnityEditor.MenuItem("22Cans/Art/Terrain/Double Terrain")]
	private static void DoubleTerrain()
	{
		var terrain = Terrain.activeTerrain;
		var td = terrain.terrainData;
		int res = td.heightmapResolution;
		if (UnityEditor.EditorUtility.DisplayDialog("Double terrain size", $"Are you sure you want to double the terrain size to {res * 2}?", "Yes", "No") == false)
		{
			return;
		}
		var heights = td.GetHeights(0, 0, res, res);
		var splats = td.GetAlphamaps(0, 0, res, res);
		var splatCount = splats.GetLength(2);
		var newHeights = new float[res * 2 - 1, res * 2 - 1];
		var newSplats = new float[res * 2 - 1, res * 2 - 1, splatCount];
		for (int z = 0; z < res * 2 - 1; ++z)
		{
			int cz = Mathf.Min(z, res-1);
			for (int x = 0; x < res * 2 - 1; ++x)
			{
				int cx = Mathf.Min(x, res - 1);
				newHeights[z, x] = heights[cz, cx];
				for (int i = 0; i < splatCount; ++i)
				{
					newSplats[z, x, i] = splats[cz, cx, i];
				}
			}
		}
		td.heightmapResolution = res * 2 - 1;
		td.alphamapResolution = res * 2 - 1;
		td.SetHeights(0, 0, newHeights);
		td.SetAlphamaps(0, 0, newSplats);
	}

	public class OffsetTerrainWindow : UnityEditor.EditorWindow
	{
		int m_offsetX = 0, m_offsetZ = 0;

		void OnGUI()
		{
			m_offsetX = UnityEditor.EditorGUILayout.IntField("X Offset", m_offsetX);
			m_offsetZ = UnityEditor.EditorGUILayout.IntField("Z Offset", m_offsetZ);

			if (GUILayout.Button("Offset!"))
			{
				var terrain = Terrain.activeTerrain;
				var td = terrain.terrainData;
				int res = td.heightmapResolution;
				var heights = td.GetHeights(0, 0, res, res);
				var newHeights = new float[res, res];
				for (int z = 0; z < res; ++z)
				{
					for (int x = 0; x < res; ++x)
					{
						int ox = Math.Clamp(x - m_offsetX, 0, res-1);
						int oz = Math.Clamp(z - m_offsetZ, 0, res - 1);
						newHeights[z, x] = heights[oz, ox];
					}
				}
				td.SetHeights(0, 0, newHeights);
				Close();
			}

			if (GUILayout.Button("Cancel"))
			{
				Close();
			}
		}

		[UnityEditor.MenuItem("22Cans/Art/Terrain/Offset Terrain")]
		static void OffsetTerrain()
		{
			OffsetTerrainWindow window = new OffsetTerrainWindow();
			window.ShowUtility();
		}
	}
#endif

	void CheckTerrainValues(Terrain _terrain)
	{
		var td = _terrain.terrainData;
		if (td.heightmapScale.y.Nearly(HFtoH) == false)
			Debug.LogError($"TERRAIN CONST ERROR: HFtoH ({HFtoH}) should match the terrain heightmapScale.y ({td.heightmapScale.y})");
		if ((int) _terrain.gameObject.transform.position.x != c_terrainOriginX)
			Debug.LogError($"TERRAIN CONST ERROR: c_terrainOriginX ({c_terrainOriginX}) should match the terrain position.x ({_terrain.gameObject.transform.position.x})");
		if ((int) _terrain.gameObject.transform.position.z != c_terrainOriginZ)
			Debug.LogError($"TERRAIN CONST ERROR: c_terrainOriginZ ({c_terrainOriginZ}) should match the terrain position.z ({_terrain.gameObject.transform.position.z})");
		if (td.heightmapResolution != c_heightmapW + 1 || td.heightmapResolution != c_heightmapH + 1)
			Debug.LogError($"TERRAIN CONST ERROR: c_heightmapW/H ({c_heightmapW},{c_heightmapH}) should match the terrain heightmapResolution - 1 ({td.heightmapResolution - 1})");
		var scaleX = 1.0f / td.heightmapScale.x;
		var scaleZ = 1.0f / td.heightmapScale.z;
		if (scaleX.Nearly(c_terrainXZScale) == false || scaleZ.Nearly(c_terrainXZScale) == false)
			Debug.LogError($"TERRAIN CONST ERROR: c_terrainXZScale ({c_terrainXZScale}) should match the terrain heightmapResolution / heightmapScale.x/z ({scaleX},{scaleZ})");
	}

	[BurstCompile]
	public struct ConvertHeightmapJob : IJob
	{
		[ReadOnly] public float m_seaLevelHF;
		public NativeArray<float> m_heights;
		[WriteOnly] public NativeArray<float> m_heightsOriginal;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		[WriteOnly] public NativeArray<UInt16> m_heights16;
#endif
		[WriteOnly] public NativeArray<UInt32> m_waterPresence;
		[WriteOnly] public NativeArray<UInt32> m_initialWaterPresence;

		
		public ConvertHeightmapJob(float _seaLevelHF)
		{
			m_seaLevelHF = _seaLevelHF;
			m_heights = Me.m_heights;
			m_heightsOriginal = Me.m_heightsOriginal;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
			m_heights16 = Me.m_legacyHeightsPixels;
#endif
			m_waterPresence = Me.m_waterPresence;
			m_initialWaterPresence = Me.m_initialWaterPresence;
		}

		public void Execute()
		{
			int w = c_heightmapW, h = c_heightmapH;
			UInt32 sea = 0;
			for (int y = 0; y < h; ++y)
			{
				for (int x = 0; x < w; ++x)
				{
					int index = x + y * w;
					var v = m_heights[index];
					var vH = v * HFtoH;
					var v16 = (UInt16)(v * HFtoPixel);
					m_heights[index] = vH;
					m_heightsOriginal[index] = vH;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
					m_heights16[index] = v16;
#endif
					
					int bitIndex = x & 31;
					bool isSea = v <= m_seaLevelHF;
					if (isSea)
						sea |= 1u << bitIndex;
					if (bitIndex == 31)
					{
						var intIndex = (x >> 5) + y * ((w + 31) / 32);
						m_waterPresence[intIndex] = sea;
						m_initialWaterPresence[intIndex] = sea;
						sea = 0;
					}
				}
			}
		}
	}
	public Material[] m_heightMapMaterials;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
	public Texture2D m_legacyHeights;
	public NativeArray<UInt16> m_legacyHeightsPixels;
#endif
	public NativeArray<UInt32> m_waterPresence;
	public NativeArray<UInt32> m_initialWaterPresence;
	private NativeArray<float> m_heights;
	private NativeArray<float> m_heightsOriginal;
	public ref NativeArray<float> Heights => ref m_heights;
	public bool TerrainReady { get; set; } = false;
	public string TerrainAsset => "v1";

	public void InitialiseTerrainData()
	{
		ChooseGameAssets();
		BackupTerrainData();
		TerrainReady = true;
		var terrain = m_terrain;
		var td = terrain.terrainData;
		CheckTerrainValues(terrain);
		CreateTerrainTexture(td);
		InitialiseDirectionData();
	}
	
	public const float c_seaLevel = 90f;
	void CreateTerrainTexture(TerrainData _td)
	{
		m_heights = new NativeArray<float>(c_heightmapW * c_heightmapH, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
		m_heightsOriginal = new NativeArray<float>(c_heightmapW * c_heightmapH, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
		m_waterPresence = new NativeArray<UInt32>(((c_heightmapW + 31) / 32) * c_heightmapH, Allocator.Persistent, NativeArrayOptions.ClearMemory);
		m_initialWaterPresence = new NativeArray<UInt32>(((c_heightmapW + 31) / 32) * c_heightmapH, Allocator.Persistent, NativeArrayOptions.ClearMemory);
#if !USE_TERRAIN_TEXTURE_FOR_GRASS		
		m_legacyHeights = new Texture2D(c_heightmapW, c_heightmapH, TextureFormat.R16, false, true);
		m_legacyHeightsPixels = m_legacyHeights.GetRawTextureData<UInt16>();
#endif
		
		var heights = _td.GetHeights(0, 0, c_heightmapW, c_heightmapH);
		UnsafeUtilities.Copy(heights, m_heights, c_heightmapW * c_heightmapH);
		const float c_seaLevelHF = c_seaLevel * HtoHF;
		var job = new ConvertHeightmapJob(c_seaLevelHF);
		job.Schedule().Complete();

#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		m_legacyHeights.Apply(false, false);
		Shader.SetGlobalTexture("_HeightMap", m_legacyHeights);
		Shader.SetGlobalTexture("_HeightMapV", m_legacyHeights);
#endif
		Vector3 texFormatDot = Vector3.right * HFtoH;
		Shader.SetGlobalVector("_HeightmapDot", texFormatDot);
		float terrainScaleX = c_terrainXZScale / c_heightmapW, terrainScaleZ = c_terrainXZScale / c_heightmapH;
		Shader.SetGlobalVector("_HeightmapToUV", new Vector4(terrainScaleX, terrainScaleZ,  -c_terrainOriginX * terrainScaleX, -c_terrainOriginZ * terrainScaleZ));
	}
	
	public void EditorInit()
	{
		CreateTerrainTexture(Terrain.activeTerrain.terrainData);
	}

	void LateAwake() {
		InitialiseTerrainData();
	}
	void Start() {
		LateAwake();
		Initialise();
	}

	float m_cloudPostProcessInitial = -1;
	float m_cloudPostProcessLevel = 1;
	float m_cloudPostProcessLevelTarget = 1;

	public void SetCloudPostProcessOn(bool _on)
	{
		m_cloudPostProcessLevelTarget = _on ? 1 : 0;
	}

	public float m_timeQuant = 0;
	public bool m_timeQuantIsMoveFrame = true;
	private int m_timeQuantLastMoveFrame;
	public bool UsingTimeQuant => m_timeQuant > .00001f;
	void SetTimeQ()
	{
		float timeQ = UsingTimeQuant ? Mathf.Floor(Time.time * m_timeQuant) / m_timeQuant : Time.time;
		Shader.SetGlobalVector("_TimeQ", new Vector4(timeQ * .05f, timeQ, timeQ * 2, timeQ * 3));

		int referenceFrameRate = 60;
		int framesPerUpdate = UsingTimeQuant ? 1 : (int)(60 / m_timeQuant);
		int frame = (int)(Time.time * referenceFrameRate);
		m_timeQuantIsMoveFrame = frame / framesPerUpdate != m_timeQuantLastMoveFrame / framesPerUpdate;
		if (m_timeQuantIsMoveFrame) m_timeQuantLastMoveFrame = frame;
	}

	void SetGlobalShaderParameters()
	{
#if USE_TERRAIN_TEXTURE_FOR_GRASS
		var terrainTexture = GlobalData.Me.m_terrainData.heightmapTexture;
		Shader.SetGlobalTexture("_HeightMap", terrainTexture);
		Shader.SetGlobalTexture("_HeightMapV", terrainTexture);
		Vector3 texFormatDot = Vector3.right * (HFtoH * 2); // terrainData texture is half value of our version
		Shader.SetGlobalVector("_HeightmapDot", texFormatDot);
#endif
	}

	private List<System.Action> m_registeredUpdaters = new List<System.Action>();
	void RunRegisteredUpdaters()
	{
		for (int i = m_registeredUpdaters.Count - 1; i >= 0; i--)
			m_registeredUpdaters[i]();
	}
	public void RegisterUpdater(System.Action _cb) => m_registeredUpdaters.Add(_cb);
	public void UnregisterUpdater(System.Action _cb) => m_registeredUpdaters.Remove(_cb);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	int m_lastFixedUpdateFrame = -1;
	double m_physicsTimeStart;
	float m_lastPhysicsTime, m_smoothPhysicsTime;
	float[] m_physicsTimes = new float[16];
	int m_physicsTimeNext = 0;
	void FixedUpdate()
	{
		if (m_lastFixedUpdateFrame == Time.frameCount) return;
		m_lastFixedUpdateFrame = Time.frameCount;
		m_physicsTimeStart = Time.realtimeSinceStartupAsDouble;
	}

	void CheckPhysicsTime()
	{
		if (m_lastFixedUpdateFrame == Time.frameCount)
			m_lastPhysicsTime = (float)((Time.realtimeSinceStartupAsDouble - m_physicsTimeStart) * 1000);
		else
			m_lastPhysicsTime = 0;
		m_physicsTimes[m_physicsTimeNext & 15] = m_lastPhysicsTime;
		++m_physicsTimeNext;
		m_smoothPhysicsTime = 0;
		for (int i = 0; i < 16; ++i)
			m_smoothPhysicsTime += m_physicsTimes[i];
		m_smoothPhysicsTime *= (1.0f / 16.0f);
	}
	
	public string PhysicsTimeString => $"<size=24>   PhysMS:{m_lastPhysicsTime,4:0.0}ms SmPhysMS:{m_smoothPhysicsTime:0.0}ms</size>";
#else
	void CheckPhysicsTime() {}
	public string PhysicsTimeString => "";
#endif
	
	private static string CostsToString(float[] _costs)
	{
		var s = "";
		for (int i = 0; i < _costs.Length; ++i) s += $"[{i}:{_costs[i]:0.0}] ";
		return s;
	}
	
	string m_windowName;
	private int m_lastNavComplexity = 0, m_lastNavComplexityQuant = 0; public int LastNavComplexity => m_lastNavComplexity;
	void Update()
	{
		CheckPhysicsTime();
		
		RunRegisteredUpdaters();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		SetGlobalShaderParameters();
#endif
		SetTimeQ();
		const string c_cloudSTy = "_CloudSTy";
		if (m_cloudPostProcessInitial < 0)
			m_cloudPostProcessInitial = m_cloudPostProcessMaterial.GetFloat(c_cloudSTy);
		if (m_cloudPostProcessLevel.Nearly(m_cloudPostProcessLevelTarget) == false)
		{
			m_cloudPostProcessLevel = Mathf.Lerp(m_cloudPostProcessLevel, m_cloudPostProcessLevelTarget, .2f);
			m_cloudPostProcessMaterial.SetFloat(c_cloudSTy, Mathf.Lerp(1, m_cloudPostProcessInitial, m_cloudPostProcessLevel));
		}

		CheckWaitingFindPathQueue();

		for(int i = m_navJobs.Count - 1; i >= 0; i--)
		{
			if(m_navJobs[i].handle.IsCompleted)
			{
				ActiveJob completedJob = m_navJobs[i];
				m_navJobs.RemoveAt(i);
				completedJob.handle.Complete();
				(m_lastNavComplexityQuant, m_lastNavComplexity) = completedJob.job.GetStats();

				var requestDuration = Time.realtimeSinceStartup - completedJob.startTime;
				if (requestDuration > 5)
					Debug.Log($"NavJob took {requestDuration:0.0}s to complete - from {completedJob.from} to {completedJob.to} - depth {m_lastNavComplexityQuant} {m_lastNavComplexity} - costs {CostsToString(completedJob.costs)}");
				
				var res = completedJob.job.GetResult();
				var output = completedJob.job.GetOutput();
				if(CompleteWaitingFindPath(completedJob.callerID) && m_navGenerationFrame == completedJob.frame)
				{
					completedJob?.cb(output, res);
				}
				else
				{
					//Debug.LogError(
					//	$"GlobalData navJobs - update - NavGenFrame out of sync on job completion. m_navGenerationFrame == _gen {m_navGenerationFrame} == {completedJob.frame}");
				}
			}
		}
	}

	public void Initialise() {
		TerrainManager.Me.Load();
		UpdateNavGrid();
		TerrainManager.Me.PostLoad();
		
		m_clearTexture = new Texture2D(8,8);
		var clrs = new Color32[8*8];
		Color32 clear = new Color32(255,255,255,0);
		for (int i = 0; i < 8*8; ++i) clrs[i] = clear;
		m_clearTexture.SetPixels32(clrs);
		m_clearTexture.Apply(true, true);
		m_clearSprite = Sprite.Create(m_clearTexture, new Rect(0,0,8,8), new Vector2(4,4));
	}
	
	public void HideTerrain(bool _hide) => m_terrain.drawHeightmap = m_terrain.drawTreesAndFoliage = !_hide;
	public bool IsHidingTerrain() => m_terrain.drawHeightmap == false;

	public const int c_landscapeChangeChunkSize = 1024 / 64;
	public ulong[] m_landscapeChangedBm = new ulong[64];

	// There are three height systems in use in different places:
	// Unity TerrainData heights (HF) are 0..1 - terrainData.GetHeight is in this space
	// Real heights (H) are 0..TerrainData.heightmapScale.y in world units (meters) - GlobalData.m_heights is in this space
	// Pixels (Pixel) are ushort interpretations of HF (0..1 => 0..65535) - height texture data is in this space
	public const float HFtoPixel = 65535; // pixels are ushort so 1.0 => 65535
	public const float HFtoH = 3686.4f; // must match TerrainData.heightmapScale.y
	public const float PixelToHF = 1.0f / HFtoPixel;
	public const float PixelToH = PixelToHF * HFtoH;
	public const float HtoHF = 1.0f / HFtoH;
	public const float HtoPixel = HtoHF * HFtoPixel;
	//
	public const float c_terrainXZScale = 4; // heightfield cells per meter
	public const int c_terrainOriginX = -695; // must match the terrain position X 
	public const int c_terrainOriginZ = -285; // must match the terrain position Z
	//
	public const int c_heightmapW = 4096; // must match the terrain heightmap resolution - 1
	public const int c_heightmapH = 4096;
	public const int c_heightmapWShift = 12; // c_heightmapW == 1 << c_heightmapWShift
	public const int c_heightmapWMask = c_heightmapW - 1; // a mask to isolate the X part of a heightmap index
	//
	public const int c_maxSlopeEffectDistance = 4;

	bool CoordOnMap(int _x, int _y) { _x = TerrainX(_x); _y = TerrainZ(_y); return _x >= 0 && _y >= 0 && _x < c_heightmapW && _y < c_heightmapH; }
	bool CoordOnMapUnclamped(int _x, int _y) { return _x >= 0 && _y >= 0 && _x < c_heightmapW && _y < c_heightmapH; }
	void Clamp(ref int _x, ref int _y) { _x = TerrainX(_x); _y = TerrainZ(_y); if (_x < 0) _x = 0; else if (_x > c_heightmapW-1) _x = c_heightmapW-1; if (_y < 0) _y = 0; else if (_y > c_heightmapH-1) _y = c_heightmapH-1; }
	public float GetHeight(Vector3 _v) { return GetRealHeight(_v.x, _v.z); } // v in world units
	public float GetRawHeight(Vector3 _v) { return GetRawHeight((int)(_v.x / TerrainBlock.GlobalScale), (int)(_v.z / TerrainBlock.GlobalScale)); }
	public float GetOriginalHeight(Vector3 _v) { return GetOriginalHeight((int)(_v.x / TerrainBlock.GlobalScale), (int)(_v.z / TerrainBlock.GlobalScale)); }
	public float GetRawHeight(int _x, int _y) { Clamp(ref _x, ref _y); return m_heights[_x + _y * c_heightmapW]; }
	public float GetRawHeightUnclamped(int _x, int _y) => m_heights[_x + _y * c_heightmapW];
	public float GetOriginalHeight(int _x, int _y) { Clamp(ref _x, ref _y); return m_heightsOriginal[_x + _y * c_heightmapW]; }
	public float GetRealHeight(Vector3 _v, bool _originalHeight = false) { return GetRealHeight(_v.x, _v.z, _originalHeight); }
	public float GetRealOriginalHeight(Vector3 _v) { return GetRealHeight(_v.x, _v.z, true); }
	public float GetMaxSlope(int _x, int _z)
	{
		float hx = GetRawHeight(_x + 1, _z) - GetRawHeight(_x - 1, _z);
		float hz = GetRawHeight(_x, _z + 1) - GetRawHeight(_x, _z - 1);
		return Mathf.Max(Mathf.Abs(hx), Mathf.Abs(hz));
	}
	
	public Vector3 GetNormal(Vector3 _v)
	{
		float hx1 = GetRealHeight(_v.x + .25f, _v.z) - GetRealHeight(_v.x - .25f, _v.z);
		float hz1 = GetRealHeight(_v.x, _v.z + .25f) - GetRealHeight(_v.x, _v.z - .25f);
		return new Vector3(-hx1, .5f, -hz1).normalized;
	}

	public static float TerrainXf(float _worldX) => (_worldX - c_terrainOriginX) * c_terrainXZScale;
	public static float TerrainZf(float _worldZ) => (_worldZ - c_terrainOriginZ) * c_terrainXZScale;
	public static int TerrainX(float _worldX) => (int)TerrainXf(_worldX);
	public static int TerrainZ(float _worldZ) => (int)TerrainZf(_worldZ);
	public static int TerrainXr(float _worldX) => (int)(TerrainXf(_worldX) + .5f);
	public static int TerrainZr(float _worldZ) => (int)(TerrainZf(_worldZ) + .5f);
	public static int TerrainXc(float _worldX) => (int)math.ceil(TerrainXf(_worldX));
	public static int TerrainZc(float _worldZ) => (int)math.ceil(TerrainZf(_worldZ));
	public static int TerrainXclamped(float _worldX) => math.clamp(TerrainX(_worldX), 0, c_heightmapW - 1);
	public static int TerrainZclamped(float _worldZ) => math.clamp(TerrainZ(_worldZ), 0, c_heightmapH - 1);
	public static float FromTerrainX(float _terrainX) => _terrainX / c_terrainXZScale + c_terrainOriginX;
	public static float FromTerrainZ(float _terrainZ) => _terrainZ / c_terrainXZScale + c_terrainOriginZ;
	public static Vector2Int TerrainXZ(Vector3 _world) => new Vector2Int(TerrainX(_world.x), TerrainZ(_world.z));
	public static Vector2Int TerrainXZr(Vector3 _world) => new Vector2Int(TerrainXr(_world.x), TerrainZr(_world.z));

	public static Vector3 TerrainQuantize(Vector3 _v, float _offset = 0)
	{
		_v.x = FromTerrainX((int)(TerrainXf(_v.x) + _offset));
		_v.z = FromTerrainZ((int)(TerrainZf(_v.z) + _offset));
		return _v;
	}

	public static Vector3 FromTerrain(Vector3 _v)
	{
		_v.x = FromTerrainX(_v.x);
		_v.z = FromTerrainZ(_v.z);
		return _v;
	}

	
	public static readonly Vector3 c_terrainOrigin = new Vector3(c_terrainOriginX, 0, c_terrainOriginZ);
	public static readonly Vector3 c_terrainExtent = new Vector3(c_heightmapW / c_terrainXZScale, 0, c_heightmapH / c_terrainXZScale);
	public static Vector3 c_terrainMin => c_terrainOrigin;
	public static Vector3 c_terrainMax => c_terrainOrigin + c_terrainExtent;
	public static bool IsInTerrain(Vector3 _v) => _v.x >= c_terrainOrigin.x && _v.z >= c_terrainOrigin.z && _v.x < c_terrainOrigin.x + c_terrainExtent.x && _v.z < c_terrainOrigin.z + c_terrainExtent.z;
	public static Vector3 ClampToTerrain(Vector3 _v, Vector3 _terrainMin, Vector3 _terrainMax) => Vector3.Min(Vector3.Max(_v, _terrainMin + Vector3.one * .5f), _terrainMax + Vector3.one * -.5f);
	
	public float GetRealHeight(float fx, float fy, bool _originalHeights = false) {
		if (GameManager.Me.IsInPlayground) return GameManager.c_playgroundHeight;

		float originX = c_terrainOriginX, originZ = c_terrainOriginZ;
		int w = c_heightmapW, h = c_heightmapH;
		var heights = _originalHeights ? m_heightsOriginal : m_heights;
		var subNav = GameManager.Me.m_state.m_subSceneState.Current;
		if (subNav != null && subNav.ContainsPoint(fx, fy))
		{
			originX = subNav.SubNav.m_subNav.m_origin.x;
			originZ = subNav.SubNav.m_subNav.m_origin.z;
			w = subNav.SubNav.m_subNav.m_width;
			h = subNav.SubNav.m_subNav.m_height;
			heights = subNav.SubNav.m_subNav.m_heightGrid;
		}
		fx /= TerrainBlock.GlobalScale; fy /= TerrainBlock.GlobalScale;
		fx -= originX; fy -= originZ;
		fx *= c_terrainXZScale; fy *= c_terrainXZScale;
		int ix0 = (int)fx;
		int iy0 = (int)fy;
		int ix1 = ix0 + 1;
		int iy1 = iy0 + 1;
		float xCorDist = fx - ix0;
		float yCorDist = fy - iy0;

		bool InRange(int _x, int _y) => _x >= 0 && _y >= 0 && _x < w && _y < h;

		if (InRange(ix0,iy0) && InRange(ix1,iy1)) {
			var bs_nw = heights[ix0 + iy0 * w];
			var bs_ne = heights[ix1 + iy0 * w];
			var bs_sw = heights[ix0 + iy1 * w];
			var bs_se = heights[ix1 + iy1 * w];
			var bs_n = Mathf.Lerp(bs_nw, bs_ne, xCorDist);
			var bs_s = Mathf.Lerp(bs_sw, bs_se, xCorDist);
			return Mathf.Lerp(bs_n, bs_s, yCorDist);
		}
		return 0;
	}

	[BurstCompile]
	public struct CheckHeightsJob : IJob
	{
		[ReadOnly] int m_count;
		[ReadOnly] NativeArray<float> m_heights;
		NativeArray<float2> m_positions;
		NativeArray<float> m_outputs;

		public CheckHeightsJob(float2[] _positions, NativeArray<float> _heights)
		{
			m_count = _positions.Length;
			m_positions = new NativeArray<float2>(_positions, Allocator.TempJob);
			m_heights = _heights;
			m_outputs = new NativeArray<float>(m_count, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		}

		public void Execute()
		{
			for (int i = 0; i < m_count; ++i)
			{
				float2 pos = m_positions[i];
				pos.x /= TerrainBlock.GlobalScale; pos.y /= TerrainBlock.GlobalScale;
				pos.x -= c_terrainOriginX; pos.y -= c_terrainOriginZ;
				pos.x *= c_terrainXZScale; pos.y *= c_terrainXZScale;
				int ix0 = (int)pos.x;
				int iy0 = (int)pos.y;
				int ix1 = ix0 + 1;
				int iy1 = iy0 + 1;
				float xCorDist = pos.x - ix0;
				float yCorDist = pos.y - iy0;
				if (CoordOnMapUnclamped(ix0, iy0) && CoordOnMapUnclamped(ix1, iy1))
				{
					var bs_nw = m_heights[ix0 + iy0 * c_heightmapW];
					var bs_ne = m_heights[ix1 + iy0 * c_heightmapW];
					var bs_sw = m_heights[ix0 + iy1 * c_heightmapW];
					var bs_se = m_heights[ix1 + iy1 * c_heightmapW];
					var bs_n = Mathf.Lerp(bs_nw, bs_ne, xCorDist);
					var bs_s = Mathf.Lerp(bs_sw, bs_se, xCorDist);
					m_outputs[i] = Mathf.Lerp(bs_n, bs_s, yCorDist);
				}
				else
					m_outputs[i] = 0;
			}
		}

		bool CoordOnMapUnclamped(int _x, int _y) 
		{ 
			return _x >= 0 && _y >= 0 && _x < c_heightmapW && _y < c_heightmapH; 
		}

		public void GetOutput(ref float[] _out)
		{
			NativeArray<float>.Copy(m_outputs, _out);

			m_outputs.Dispose();
			m_positions.Dispose();
		}
	}

	public bool SetRealHeight(Vector3 _p, float _height, bool _apply = true)
	{
		float texValue = _height * HtoHF;
		int ix = TerrainX(_p.x), iy = TerrainZ(_p.z);
		int index = ix + iy * c_heightmapW;
		if (Mathf.Abs(m_heights[index] - _height) < .001f) return false;
		m_heights[index] = _height;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		m_legacyHeightsPixels[index] = (UInt16)(texValue * HFtoPixel);
		if (_apply) m_legacyHeights.Apply(false, false);
#endif
		return true;
	}

	public bool IsWaterAt(Vector3 _at)
	{
		int x = TerrainX(_at.x), z = TerrainZ(_at.z);
		if (x < 0 || z < 0 || x >= c_heightmapW || z >= c_heightmapH) return false;
		int index = x + z * c_heightmapW;
		return ((m_waterPresence[index >> 5] >> (x & 31)) & 1) != 0;
	}

	private static Dictionary<string, string> s_typeToSurfaceType = new();
	public static string GetTerrainSurfaceAtPoint(Vector3 _at)
	{
		var waterDepth = _at.y - c_seaLevel;
		if (waterDepth < -1) return "Surface_WaterDeep";
		if (waterDepth < 0) return "Surface_WaterShallow";
		if (Me.IsWaterAt(_at)) return "Surface_WaterShallow";
		int index = CameraRenderSettings.Me.GetStrongestSplatPoint(_at);
		var type = CameraRenderSettings.Me.m_splatAudioTypes[index];
		if (s_typeToSurfaceType.TryGetValue(type, out var surfaceType) == false)
			s_typeToSurfaceType[type] = surfaceType = $"Surface_{type}";
		return surfaceType;
	}

	public static string GetSurfaceTypeFromObject(GameObject _on, Vector3 _at)
	{
		if (_on.GetComponent<Terrain>() != null) return GetTerrainSurfaceAtPoint(_at);
		return SurfaceMaterial.GetSurfaceAudioSwitchFromObject(_on, "Surface_Mud"); 
	}
	
	public Terrain m_moaTerrain;
	public TerrainPopulation m_moaTerrainPopulation;
	//public DistrictSet m_moaDistricts;
	public GameObject m_moaVisuals;

	private void ChooseGameAssets()
	{
	}
	
	public Terrain m_terrain = null;
	public TerrainData m_terrainData = null;
	public TerrainData m_originalTerrainData = null;
	
	static DebugConsole.Command s_editterrain = new ("editterrain", _s => Me.m_terrain.terrainData = Me.m_originalTerrainData);

	private void BackupTerrainData()
	{
		if (m_terrain == null) m_terrain = Terrain.activeTerrain;
		if (m_terrain == null) return;
		var td = m_terrain.terrainData;
		var tdNew = new TerrainData();
		tdNew.terrainLayers = td.terrainLayers;
		tdNew.alphamapResolution = td.alphamapResolution;
		tdNew.heightmapResolution = td.heightmapResolution;
		tdNew.size = td.size;
		tdNew.SetHeights(0, 0, td.GetHeights(0, 0, td.heightmapResolution, td.heightmapResolution));
		tdNew.SetAlphamaps(0,0, td.GetAlphamaps(0, 0, td.alphamapResolution, td.alphamapResolution));
		tdNew.SyncHeightmap();
		if (!TerrainPopulation.CreateGameObjects)
		{
			tdNew.treePrototypes = td.treePrototypes;
			tdNew.SetTreeInstances(td.treeInstances, false);
		}
		m_terrain.terrainData = tdNew;
		m_terrainData = tdNew;
		m_originalTerrainData = td;
		m_terrain.GetComponent<TerrainCollider>().terrainData = tdNew;
		CameraRenderSettings.Me.LoadTerrainData();
	}

	int m_batchTerrainOperationsInProgress = 0;
	public bool BatchTerrainOperationsInProgress => m_batchTerrainOperationsInProgress > 0;
	
	public void BeginBatchTerrainOperations()
	{
		BeginDeferTerrainHeightsUpdate();
		++m_batchTerrainOperationsInProgress;
	}

	public void EndBatchTerrainOperations()
	{
		int tx = (int) m_deferTerrainHeightsUpdateRegion.x, tz = (int) m_deferTerrainHeightsUpdateRegion.y;
		int tx2 = (int) m_deferTerrainHeightsUpdateRegion.z, tz2 = (int) m_deferTerrainHeightsUpdateRegion.w;
		EndDeferTerrainHeightsUpdate();
		if (--m_batchTerrainOperationsInProgress == 0)
		{
			if (m_deferTerrainHeightsUpdateRegion.x < m_deferTerrainHeightsUpdateRegion.z && m_deferTerrainHeightsUpdateRegion.y < m_deferTerrainHeightsUpdateRegion.w)
			{
				SyncHeightmap(tx, tz, tx2 - tx, tz2 - tz);
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
				m_legacyHeights.Apply(false, false);
#endif
				OnTerrainHeightsUpdated?.Invoke();
			}
		}
	}

	public event Action OnTerrainHeightsUpdated;
	
	bool m_terrainHeightsUpdated = false;
	
	public void RestoreTerrainHeights(Vector3 _min, Vector3 _max)
	{
		int x = TerrainX(_min.x), z = TerrainZ(_min.z);
		int w = TerrainXc(_max.x) - x, h = TerrainZc(_max.z) - z;
		if (x < 0) { w += x; x = 0; }
		if (z < 0) { h += z; z = 0; }
		if (x + w > c_heightmapW) w = c_heightmapW - x;
		if (z + h > c_heightmapH) h = c_heightmapH - z;
		AddTerrainDirtyRegion(x, z, x+w, z+h);
		var originalHeights = m_originalTerrainData.GetHeights(x, z, w, h);
		var heightSteps = new float[w];
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		var heightPixels = new ushort[w];
#endif
		for (int iz = 0; iz < h; ++iz)
		{
			for (int ix = 0; ix < w; ++ix)
			{
				float hf = originalHeights[iz, ix];
				heightSteps[ix] = hf * HFtoH;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
				heightPixels[ix] = (UInt16) (hf * HFtoPixel);
#endif
			}
			NativeArray<float>.Copy(heightSteps, 0, m_heights, x + (z + iz) * c_heightmapW, w);
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
			NativeArray<UInt16>.Copy(heightPixels, 0, m_legacyHeightsPixels, x + (z + iz) * c_heightmapW, w);
#endif
		}

		m_terrainHeightsUpdated = true;
		if (m_batchTerrainOperationsInProgress == 0)
			m_terrainData.SetHeightsDelayLOD(x, z, originalHeights);
	}

	public void RestoreWaterPresence(Vector3 _min, Vector3 _max)
	{
		int x = TerrainX(_min.x), z = TerrainZ(_min.z);
		int w = TerrainXc(_max.x) - x, h = TerrainZc(_max.z) - z;
		if (x + w > c_heightmapW) w = c_heightmapW - x;
		if (z + h > c_heightmapH) h = c_heightmapH - z;

		if (x == 0 && z == 0 && w == c_heightmapW && h == c_heightmapH)
		{
			m_waterPresence.CopyFrom(m_initialWaterPresence);
			return;
		}
		new RestoreWaterPresenceJob(x, z, w, h, c_heightmapW).Schedule().Complete();
	}
		[BurstCompile]
	public struct RestoreWaterPresenceJob : IJob
	{
		NativeArray<UInt32> m_presence;
		NativeArray<UInt32> m_presenceInitial;
		[ReadOnly] int m_x;
		[ReadOnly] int m_z;
		[ReadOnly] int m_w;
		[ReadOnly] int m_h;
		[ReadOnly] int m_stride;

		public RestoreWaterPresenceJob(int _x, int _z, int _w, int _h, int _stride)
		{
			m_presence = Me.m_waterPresence;
			m_presenceInitial = Me.m_initialWaterPresence;
			m_x = _x;
			m_z = _z;
			m_w = _w;
			m_h = _h;
			m_stride = _stride;
		}

		public void Execute()
		{
			int stride = (m_stride + 31) / 32;

			int xIndexStart = m_x >> 5, xIndexEnd = (m_x + m_w - 1) >> 5;
			int xBitStart = m_x & 31, xBitEnd = (m_x + m_w - 1) & 31;
			UInt32 xMaskStart = ~((1U << xBitStart) - 1U), xMaskEnd = ~0U >> (31 - xBitEnd);
			if (xIndexStart == xIndexEnd)
			{
				xMaskStart &= xMaskEnd;
				for (int iz = 0; iz < m_h; ++iz)
				{
					int zIndex = (m_z + iz) * stride + xIndexStart;
					m_presence[zIndex] = (m_presence[zIndex] & ~xMaskStart) | (m_presenceInitial[zIndex] & xMaskStart);
				}
			}
			else
			{
				for (int iz = 0; iz < m_h; ++iz)
				{
					int zIndex = (m_z + iz) * stride;
					int zIndexStart = zIndex + xIndexStart;
					int zIndexEnd = zIndex + xIndexEnd;
					m_presence[zIndexStart] = (m_presence[zIndexStart] & ~xMaskStart) | (m_presenceInitial[zIndexStart] & xMaskStart);
					m_presence[zIndexEnd] = (m_presence[zIndexEnd] & ~xMaskEnd) | (m_presenceInitial[zIndexEnd] & xMaskEnd);
					for (int ix = xIndexStart + 1; ix < xIndexEnd; ++ix)
						m_presence[zIndex + ix] = m_presenceInitial[zIndex + ix];
				}
			}
		}
	}

	int m_deferTerrainHeightsUpdate = 0;
	Vector4 m_deferTerrainHeightsUpdateRegion;
	public void BeginDeferTerrainHeightsUpdate()
	{
		if (m_deferTerrainHeightsUpdate == 0)
			m_deferTerrainHeightsUpdateRegion = new Vector4(1e23f,1e23f, -1e23f,-1e23f);
		++m_deferTerrainHeightsUpdate;
	}

	public void AddTerrainDirtyRegion(int _x1, int _z1, int _x2, int _z2)
	{
		m_deferTerrainHeightsUpdateRegion.x = Mathf.Min(m_deferTerrainHeightsUpdateRegion.x, _x1);
		m_deferTerrainHeightsUpdateRegion.y = Mathf.Min(m_deferTerrainHeightsUpdateRegion.y, _z1);
		m_deferTerrainHeightsUpdateRegion.z = Mathf.Max(m_deferTerrainHeightsUpdateRegion.z, _x2);
		m_deferTerrainHeightsUpdateRegion.w = Mathf.Max(m_deferTerrainHeightsUpdateRegion.w, _z2);
	}

	private void SyncHeightmap(int _x, int _z, int _w, int _h)
	{
		m_terrainData.DirtyHeightmapRegion(new RectInt(_x, _z, _w, _h), TerrainHeightmapSyncControl.HeightAndLod);
		//m_terrainData.SyncHeightmap();
		SetGlobalShaderParameters();
	}

	public (Vector3, Vector3) GetTerrainDirtyRegion() => (new Vector3(m_deferTerrainHeightsUpdateRegion.x, 0, m_deferTerrainHeightsUpdateRegion.y), new Vector3(m_deferTerrainHeightsUpdateRegion.z, 0, m_deferTerrainHeightsUpdateRegion.w));

	public void EndDeferTerrainHeightsUpdate()
	{
		--m_deferTerrainHeightsUpdate;
		if (m_deferTerrainHeightsUpdate == 0)
		{
			if (m_deferTerrainHeightsUpdateRegion.x < m_deferTerrainHeightsUpdateRegion.z)
			{
				int tx = (int)m_deferTerrainHeightsUpdateRegion.x, tz = (int)m_deferTerrainHeightsUpdateRegion.y;
				int tx2 = (int)m_deferTerrainHeightsUpdateRegion.z, tz2 = (int)m_deferTerrainHeightsUpdateRegion.w;
				if (tx < 0) tx = 0; if (tz < 0) tz = 0;
				if (tx2 > c_heightmapW - 1) tx2 = c_heightmapW - 1; if (tz2 > c_heightmapH - 1) tz2 = c_heightmapH - 1;
				int w = tx2 + 1 - tx, h = tz2 + 1 - tz;
				var heights = new float[h, w];
				var row = new float[w];
				for (int z = 0; z < h; ++z)
				{
					NativeArray<float>.Copy(m_heights, tx + (tz + z) * c_heightmapW, row, 0, w);
					for (int x = 0; x < w; ++x)
					{
						heights[z, x] = row[x] * HtoHF;//m_heights[(tx + x) + (tz + z) * m_w] * HtoHF;
					}
				}
				m_terrainHeightsUpdated = true;
				m_terrainData.SetHeightsDelayLOD(tx, tz, heights);
				if (m_batchTerrainOperationsInProgress == 0)
					SyncHeightmap(tx, tz, w, h);

				int iMinX = (int)m_deferTerrainHeightsUpdateRegion.x;
				int iMinZ = (int)m_deferTerrainHeightsUpdateRegion.y;
				int iMaxX = (int)m_deferTerrainHeightsUpdateRegion.z;
				int iMaxZ = (int)m_deferTerrainHeightsUpdateRegion.w;
				iMinX -= c_maxSlopeEffectDistance;
				iMinZ -= c_maxSlopeEffectDistance;
				iMaxX += c_maxSlopeEffectDistance;
				iMaxZ += c_maxSlopeEffectDistance;
				var job = new GenNavGridOrFillFromSlopeJob(iMinX, iMinZ, iMaxX-iMinX, iMaxZ-iMinZ);
				job.Schedule().Complete();
			}
		}
	}

	public void SetWaterPresenceRadial(Vector3 _center, float _radius, Vector3 _dirtyMin, Vector3 _dirtyMax, float _onlyFillUnderDepth = 1e23f)
	{
		_radius *= c_terrainXZScale;

		var scaledCenter = (_center - c_terrainOrigin) * c_terrainXZScale;
		int ix = TerrainX(_center.x), iz = TerrainZ(_center.z);
		int iRad = Mathf.CeilToInt(_radius);

		int iMinX = ix - iRad, iMaxX = ix + iRad, iMinZ = iz - iRad, iMaxZ = iz + iRad;
		if (iMinX < _dirtyMin.x) iMinX = (int) _dirtyMin.x;
		if (iMaxX > _dirtyMax.x) iMaxX = (int) _dirtyMax.x;
		if (iMinZ < _dirtyMin.z) iMinZ = (int) _dirtyMin.z;
		if (iMaxZ > _dirtyMax.z) iMaxZ = (int) _dirtyMax.z;
		if (iMinX > iMaxX || iMinZ > iMaxZ)
			return;

		var job = new RadialWaterPresenceJob(scaledCenter, ix, iz, iRad, _dirtyMin, _dirtyMax, _onlyFillUnderDepth);
		job.Schedule().Complete();
	}

	public bool SetHeightsRadial(Vector3 _center, float _radiusInner, float _radiusOuter, float _height, Vector3 _dirtyMin, Vector3 _dirtyMax, bool _onlyRaise = false, bool _ignoreWaterPresence = false, float _smoothProfile = 1, bool _fillWithOriginal = false)
	{
		_radiusInner *= c_terrainXZScale;
		_radiusOuter *= c_terrainXZScale;
		var scaledCenter = (_center - c_terrainOrigin) * c_terrainXZScale;
		int ix = TerrainX(_center.x), iz = TerrainZ(_center.z);
		int iRad = Mathf.CeilToInt(_radiusOuter);

		int iMinX = ix - iRad, iMaxX = ix + iRad, iMinZ = iz - iRad, iMaxZ = iz + iRad;
		if (iMinX < _dirtyMin.x) iMinX = (int) _dirtyMin.x;
		if (iMaxX > _dirtyMax.x) iMaxX = (int) _dirtyMax.x;
		if (iMinZ < _dirtyMin.z) iMinZ = (int) _dirtyMin.z;
		if (iMaxZ > _dirtyMax.z) iMaxZ = (int) _dirtyMax.z;
		if (iMinX > iMaxX || iMinZ > iMaxZ)
			return false;
		
		bool hasChanged = false;
		float outerSqrd = _radiusOuter * _radiusOuter, innerSqrd = _radiusInner * _radiusInner;
		float dScale = 1.0f / (outerSqrd - innerSqrd);

		if (m_deferTerrainHeightsUpdate == 0)
		{
			float[,] heights = new float[iRad + iRad + 1, iRad + iRad + 1];
			var rowHeights = new float[iRad * 2 + 1];
			for (int z = -iRad; z <= iRad; ++z)
			{
				NativeArray<float>.Copy(m_heights, ix - iRad + (iz + z) * c_heightmapW, rowHeights, 0, iRad * 2 + 1);
				for (int x = -iRad; x <= iRad; ++x)
				{
					int lx = x + ix, lz = z + iz;
					float dx = scaledCenter.x - lx, dz = scaledCenter.z - lz;
					float intensity = (outerSqrd - (dx * dx + dz * dz)) * dScale;
					var current = rowHeights[x + iRad];
					if (intensity > 0)
					{
						intensity = Mathf.Min(1, intensity);
						//intensity = intensity * intensity * (3 - intensity - intensity);
						current = Mathf.Lerp(current, _height, intensity);
						if (rowHeights[x + iRad].Nearly(current) == false)
						{
							rowHeights[x + iRad] = current;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
							m_legacyHeightsPixels[lx + lz * c_heightmapW] = (UInt16) (current * HtoPixel);
#endif
							hasChanged = true;
						}
					}
					if (m_deferTerrainHeightsUpdate == 0) heights[z + iRad, x + iRad] = current * HtoHF;
				}
				NativeArray<float>.Copy(rowHeights, 0, m_heights, ix - iRad + (iz + z) * c_heightmapW, iRad * 2 + 1);
			}
			if (m_batchTerrainOperationsInProgress == 0)
			{
				m_terrainHeightsUpdated = true;
				m_terrainData.SetHeightsDelayLOD(ix - iRad, iz - iRad, heights);
				if (m_batchTerrainOperationsInProgress == 0)
					SyncHeightmap(ix - iRad, iz - iRad, iRad * 2 + 1, iRad * 2 + 1);

#if !USE_TERRAIN_TEXTURE_FOR_GRASS
				m_legacyHeights.Apply(false, false);
#endif
				//if (hasChanged) TerrainManager.Me.Regenerate(_center - Vector3.one * _radiusOuter, _center + Vector3.one * _radiusOuter);
			}
			else if (hasChanged)
			{
				AddTerrainDirtyRegion(ix - iRad, iz - iRad, ix + iRad, iz + iRad);
			}
		}
		else
		{
			var job = new RadialHeightsJob(scaledCenter, ix, iz, iRad, c_heightmapW, outerSqrd, dScale, _height, _dirtyMin, _dirtyMax, _onlyRaise, _ignoreWaterPresence, _smoothProfile, _fillWithOriginal);
			job.Schedule().Complete();
			AddTerrainDirtyRegion(iMinX, iMinZ, iMaxX, iMaxZ);
		}

		return hasChanged;
	}
	
	public bool SetManyHeightsRadial(NativeArray<Vector3> _jitteredPositions, NativeArray<Vector3> _unjitteredPositions, float _radiusInner, float _radiusOuter, NativeArray<float> _heights, float _heightAdjust, Vector3 _dirtyMin, Vector3 _dirtyMax, RadialHeightsBatchJobMode _mode, bool _onlyRaise = false, bool _ignoreWaterPresence = false, float _smoothProfile = 1, GlobalData.RavineData _ravineData = new GlobalData.RavineData(), GlobalData.IslandData _islandData = new GlobalData.IslandData())
	{
		_radiusInner *= c_terrainXZScale;
		_radiusOuter *= c_terrainXZScale;
		
		bool hasChanged = false;
		float outerSqrd = _radiusOuter * _radiusOuter, innerSqrd = _radiusInner * _radiusInner;
		float dScale = 1.0f / (outerSqrd - innerSqrd);

		var job = new RadialHeightsBatchJob(_jitteredPositions, _unjitteredPositions, _radiusInner, _radiusOuter, c_heightmapW, outerSqrd, dScale, _heights, _heightAdjust, _dirtyMin, _dirtyMax, _onlyRaise, _ignoreWaterPresence, _mode, _smoothProfile, _ravineData, _islandData);
		job.Schedule().Complete();
		job.End();
		return hasChanged;
	}

	public void AdjustHeightsRadial(Vector3 _center, float _radiusInner, float _radiusOuter, float _height, Vector3 _dirtyMin, Vector3 _dirtyMax)
	{
		_radiusInner *= c_terrainXZScale;
		_radiusOuter *= c_terrainXZScale;
		var scaledCenter = (_center - c_terrainOrigin) * c_terrainXZScale;
		int ix = TerrainX(_center.x), iz = TerrainZ(_center.z);
		int iRad = Mathf.CeilToInt(_radiusOuter);

		int iMinX = ix - iRad, iMaxX = ix + iRad, iMinZ = iz - iRad, iMaxZ = iz + iRad;
		if (iMinX < _dirtyMin.x) iMinX = (int) _dirtyMin.x;
		if (iMaxX > _dirtyMax.x) iMaxX = (int) _dirtyMax.x;
		if (iMinZ < _dirtyMin.z) iMinZ = (int) _dirtyMin.z;
		if (iMaxZ > _dirtyMax.z) iMaxZ = (int) _dirtyMax.z;
		if (iMinX > iMaxX || iMinZ > iMaxZ)
			return;
		
		float outerSqrd = _radiusOuter * _radiusOuter, innerSqrd = _radiusInner * _radiusInner;
		float dScale = 1.0f / (outerSqrd - innerSqrd);
		
		var job = new RadialAdjustHeightsJob(scaledCenter, ix, iz, iRad, c_heightmapW, outerSqrd, dScale, _height, _dirtyMin, _dirtyMax);
		job.Schedule().Complete();
		AddTerrainDirtyRegion(iMinX, iMinZ, iMaxX, iMaxZ);
	}
	
	public bool SmoothHeightsRadial(NativeArray<Vector3> _centerList, float _radiusInner, float _radiusOuter, Vector3 _dirtyMin, Vector3 _dirtyMax)
	{
		_radiusInner *= c_terrainXZScale;
		_radiusOuter *= c_terrainXZScale;
		
		bool hasChanged = false;
		
		var job = new RadialHeightsSmoothJob(_centerList, _radiusInner, _radiusOuter, c_heightmapW, _dirtyMin, _dirtyMax, c_terrainOrigin, c_terrainXZScale);
		job.Schedule().Complete();
		job.End();

		return hasChanged;
	}

	static NativeArray<byte> s_roadTypePrioritiesNativeArray;
	public static NativeArray<byte> RoadTypePriorities {
		get {
			if (s_roadTypePrioritiesNativeArray.IsCreated == false)
				s_roadTypePrioritiesNativeArray = new NativeArray<byte>(s_roadTypePriorities, Allocator.Persistent);
			return s_roadTypePrioritiesNativeArray;
		}
	}
	[BurstCompile]
	public struct RadialNavJob : IJob
	{
		NativeArray<byte> m_nav;
		[ReadOnly] int m_navStride;
		[ReadOnly] NativeArray<byte> m_roadTypePriorities;
		[ReadOnly] int m_sx, m_sz, m_w, m_h;
		[ReadOnly] float3 m_center;
		[ReadOnly] float m_radiusOuter;
		[ReadOnly] float m_ratioInner;
		[ReadOnly] byte m_typeInner, m_typeOuter;

		public RadialNavJob(NativeArray<byte> _nav, int _navStride, int _sx, int _sz, int _w, int _h, Vector3 _center, float _radiusOuter, float _ratioInner, byte _typeInner, byte _typeOuter)
		{
			m_roadTypePriorities = RoadTypePriorities;
			m_nav = _nav;
			m_navStride = _navStride;
			m_sx = _sx; m_sz = _sz; m_w = _w; m_h = _h;
			m_center = _center;
			m_radiusOuter = _radiusOuter; m_ratioInner = _ratioInner;
			m_typeInner = _typeInner; m_typeOuter = _typeOuter;
		}
		
		public void Execute()
		{
			float invRadOuterSqrd = 1.0f / (m_radiusOuter * m_radiusOuter);
			byte innerPriority = m_roadTypePriorities[m_typeInner], outerPriority = m_roadTypePriorities[m_typeOuter];
			for (int z = 0; z <= m_h; ++z)
			{
				for (int x = 0; x <= m_w; ++x)
				{
					int lx = x + m_sx, lz = z + m_sz;
					float dx = m_center.x - lx, dz = m_center.z - lz;
					float ratio = (dx * dx + dz * dz) * invRadOuterSqrd;
					if (ratio < 1)
					{
						int index = lx + lz * m_navStride;
						var existing = m_nav[index];
						m_nav[index] = GetNew(existing, m_typeInner, m_typeOuter, ratio < m_ratioInner, m_roadTypePriorities[existing], innerPriority, outerPriority);
					}
				}
			}
		}
	}

	[BurstCompile]
	public struct RadialNavBatchJob : IJob
	{
		[ReadOnly] float m_start, m_end, m_step;
		[ReadOnly] NativeArray<float> m_intersectionTs;
		[ReadOnly] NativeArray<float3> m_pathSmoothed;
		[ReadOnly] int m_pathSmoothedTrueLength;
		NativeArray<byte> m_nav;
		[ReadOnly] int m_navStride;
		[ReadOnly] NativeArray<byte> m_roadTypePriorities;
		[ReadOnly] int m_x, m_z, m_w, m_h;
		[ReadOnly] float m_radiusOuter;
		[ReadOnly] float m_radiusInner;
		[ReadOnly] byte m_typeInner, m_typeOuter;
		[ReadOnly] float m_terrainScale;
		[ReadOnly] bool m_areBridgeIntersections;

		public RadialNavBatchJob(float _start, float _end, float _step, NativeArray<float3> _pathSmoothed, int _pathSmoothedLength, NativeArray<float> _intersectionTs, NativeArray<byte> _nav, int _navStride, int _x, int _z, int _w, int _h, float _radiusOuter, float _radiusInner, byte _typeInner, byte _typeOuter, bool _areBridgeIntersections)
		{
			m_start = _start;
			m_end = _end;
			m_step = _step;
			m_intersectionTs = _intersectionTs;
			m_pathSmoothed = _pathSmoothed;
			// RW-10-DEC-24: The need for a separate length var is that m_pathSmoothed comes 
			// from NativeishList.GetBackingArray, which gives an array of the _capacity_ of the 
			// List, not the Length.
			m_pathSmoothedTrueLength = _pathSmoothedLength;
			
			m_areBridgeIntersections = _areBridgeIntersections;

			if (s_roadTypePrioritiesNativeArray.IsCreated == false)
				s_roadTypePrioritiesNativeArray = new NativeArray<byte>(s_roadTypePriorities, Allocator.Persistent);
			m_roadTypePriorities = s_roadTypePrioritiesNativeArray;
			m_nav = _nav;
			m_navStride = _navStride;
			m_x = _x; m_z = _z; m_w = _w; m_h = _h;
			m_radiusOuter = _radiusOuter; m_radiusInner = _radiusInner;
			m_typeInner = _typeInner; m_typeOuter = _typeOuter;

			m_terrainScale = c_terrainXZScale;
		}

		float3 CatmullRom(NativeArray<float3> _list, int _trueLength, float _t)
		{
			_t = PathManager.Path.SanitiseT(_t);
			_t *= (_trueLength - 4);
			int index = (int) _t;
			index = Mathf.Clamp(index, 0, (_trueLength - 4));
			_t -= index;
			return CatmullRom(_list, _trueLength, index + 1, _t);
		}

		float3 CatmullRom(NativeArray<float3> _list, int _trueLength, int _index0, float _t)
		{
			int countM1 = _trueLength - 1;
			if (_index0 < 0)
			{
				_index0 = 0;
				_t = 0;
			}
			if (_index0 > countM1)
			{
				_index0 = countM1;
			}
			float t1 = _t;
			float t2 = t1 * t1;
			float t3 = t2 * t1;
			int indexM1 = _index0 - 1;
			if (indexM1 < 0) indexM1 = 0;
			int indexP1 = _index0 + 1;
			if (indexP1 > countM1) indexP1 = countM1;
			int indexP2 = _index0 + 2;
			if (indexP2 > countM1) indexP2 = countM1;
			const float Tau = 0.5f;//PathSet.s_tension;
			float kTt1 = Tau * t1, kTt2 = Tau * t2, kTt3 = Tau * t3;
			float k3mTt2 = t2 + t2 + t2 - kTt2, k2mTt3 = t3 + t3 - kTt3; 
			float mM1 = kTt2 + kTt2 - kTt1 - kTt3;//(-t1 + 2 * t2 - t3) * Tau;
			float m0 = 1 - k3mTt2 + k2mTt3;//1 + (Tau - 3) * t2 + (2 - Tau) * t3;
			float mP1 = kTt1 + k3mTt2 - kTt2 - k2mTt3;//Tau * t1 + (3 - Tau * 2) * t2 + (Tau - 2) * t3;
			float mP2 = kTt3 - kTt2;//(-t2 + t3) * Tau;
			//return _list[indexM1] * mM1 + _list[_index0] * m0 + _list[indexP1] * mP1 + _list[indexP2] * mP2;
			var lM1 = _list[indexM1];
			var l0 = _list[_index0];
			var lP1 = _list[indexP1];
			var lP2 = _list[indexP2];
			var cr = new float3(lM1.x * mM1 + l0.x * m0 + lP1.x * mP1 + lP2.x * mP2,
								lM1.y * mM1 + l0.y * m0 + lP1.y * mP1 + lP2.y * mP2,
								lM1.z * mM1 + l0.z * m0 + lP1.z * mP1 + lP2.z * mP2);
			return cr;
		}
		
		public void Execute()
		{
			int intersectionsPassed = 0;
			float bridgeUntil = -1;
			for (float t=m_start; t<m_end; t+=m_step)
			{
				float t01 = Mathf.Clamp01(t);

				if (intersectionsPassed < m_intersectionTs.Length && t01 > m_intersectionTs[intersectionsPassed])
				{
					if ((intersectionsPassed++ & 1) == 0)
					{
						var end = m_intersectionTs[intersectionsPassed++];;
						if (m_areBridgeIntersections)
							bridgeUntil = end;
						else
							t = end;
					}
				}

				var center = CatmullRom(m_pathSmoothed, m_pathSmoothedTrueLength, t);

				var radiusOuter = m_radiusOuter;
				var radiusInner = m_radiusInner;
				var radiusMiddle = 0f;
				var isBridge = t <= bridgeUntil;
				var typeInner = m_typeInner;
				var typeOuter = isBridge ? (byte) NavCostTypes.BridgeEdge : m_typeOuter;
				var typeMiddle = m_typeInner;
				var typeReplacement = typeOuter;
				if (isBridge)
				{
					typeInner = m_typeOuter;
					typeReplacement = typeInner;
					if (radiusInner < .75f)
					{
						radiusInner = .75f;
					}
					else
					{
						radiusInner = 1f;
						radiusMiddle = .5f;
					}
				}
				if (isBridge && radiusOuter < radiusInner + .75f)
					radiusOuter = radiusInner + .75f;

				float scaledRadiusOuter = radiusOuter * m_terrainScale;
				float scaledRadiusInner = radiusInner * m_terrainScale;
				float scaledRadiusMiddle = radiusMiddle * m_terrainScale;
				float ratioInner = scaledRadiusInner * scaledRadiusInner / (scaledRadiusOuter * scaledRadiusOuter);
				float ratioMiddle = scaledRadiusMiddle * scaledRadiusMiddle / (scaledRadiusOuter * scaledRadiusOuter);

				int sx = TerrainXr(center.x - radiusOuter), sz = TerrainZr(center.z - radiusOuter);
				int ex = TerrainXr(center.x + radiusOuter), ez = TerrainZr(center.z + radiusOuter);
				sx = Mathf.Clamp(sx, m_x, m_x+m_w-1); ex = Mathf.Clamp(ex, m_x, m_x+m_w-1);
				sz = Mathf.Clamp(sz, m_z, m_z+m_h-1); ez = Mathf.Clamp(ez, m_z, m_z+m_h-1);
				center.x = TerrainXf(center.x); center.z = TerrainZf(center.z);
				int w = ex+1-sx;
				int h = ez+1-sz;
				
				float invRadOuterSqrd = 1.0f / (scaledRadiusOuter * scaledRadiusOuter);
				byte innerPriority = m_roadTypePriorities[typeInner], outerPriority = m_roadTypePriorities[typeOuter];
				for (int z = 0; z <= h; ++z)
				{
					for (int x = 0; x <= w; ++x)
					{
						int lx = x + sx, lz = z + sz;
						float dx = center.x - lx, dz = center.z - lz;
						float ratio = (dx * dx + dz * dz) * invRadOuterSqrd;
						if (ratio < 1)
						{
							int index = lx + lz * m_navStride;
							var existing = m_nav[index];
							var isMiddle = ratio < ratioMiddle;
							if (existing == m_typeOuter) existing = isMiddle ? m_typeInner : typeReplacement;
							m_nav[index] = GetNew(existing, isMiddle ? m_typeInner : typeInner, typeOuter, ratio < ratioInner, m_roadTypePriorities[existing], innerPriority, outerPriority);
						}
					}
				}
			}	
		}
	}
	
	[BurstCompile]
	public struct RadialHeightsJob : IJob
	{
		NativeArray<float> m_heights;
		NativeArray<float> m_heightsReference;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		NativeArray<UInt16> m_heightPixels;
#endif
		[ReadOnly] NativeArray<UInt32> m_waterPresence;
		[ReadOnly] int m_waterStride;
		[ReadOnly] int m_x;
		[ReadOnly] int m_z;
		[ReadOnly] int m_rad;
		[ReadOnly] int m_stride;
		[ReadOnly] float m_outerSqrd;
		[ReadOnly] float m_dScale;
		[ReadOnly] float m_height;
		[ReadOnly] float3 m_scaledCenter;
		[ReadOnly] float3 m_dirtyMin;
		[ReadOnly] float3 m_dirtyMax;
		[ReadOnly] bool m_onlyRaise;
		[ReadOnly] float m_smoothProfile;
		[ReadOnly] bool m_fillWithOriginal;

		public RadialHeightsJob(Vector3 _scaledCenter, int _x, int _z, int _rad, int _stride, float _outerSqrd, float _dScale, float _height, Vector3 _dirtyMin, Vector3 _dirtyMax, bool _onlyRaise, bool _ignoreWaterPresence, float _smoothProfile = 1, bool _fillWithOriginal = false)
		{
			m_heights = Me.m_heights;
			m_heightsReference = Me.m_heightsOriginal;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
			m_heightPixels = Me.m_legacyHeightsPixels;
#endif
			m_waterPresence = Me.m_waterPresence;
			m_waterStride = _ignoreWaterPresence ? 0 : c_heightmapW;
			m_x = _x;
			m_z = _z;
			m_rad = _rad;
			m_stride = _stride;
			m_scaledCenter = _scaledCenter;
			m_outerSqrd = _outerSqrd;
			m_dScale = _dScale;
			m_height = _height;
			m_dirtyMin = _dirtyMin;
			m_dirtyMax = _dirtyMax;
			m_onlyRaise = _onlyRaise;
			m_smoothProfile = _smoothProfile;
			m_fillWithOriginal = _fillWithOriginal;
		}


		float FastSqrt01(float number) => number * (2 - number);
		
		public void Execute()
		{
			var waterStride = (m_waterStride + 31) / 32;
			bool hasChanged = false;
			int dirtyMinX = (int)m_dirtyMin.x, dirtyMaxX = (int)m_dirtyMax.x, dirtyMinZ = (int)m_dirtyMin.z, dirtyMaxZ = (int)m_dirtyMax.z;
			for (int z = -m_rad; z <= m_rad; ++z)
			{
				int lz = z + m_z;
				if (lz < dirtyMinZ || lz > dirtyMaxZ) continue;
				float dz = m_scaledCenter.z - lz;
				float dzSqrd = dz * dz;
				int zWaterIndex = lz * waterStride;
				for (int x = -m_rad; x <= m_rad; ++x)
				{
					int lx = x + m_x;
					if (lx < dirtyMinX || lx > dirtyMaxX) continue;
					float dx = m_scaledCenter.x - lx;
					float intensity = (m_outerSqrd - (dx * dx + dzSqrd)) * m_dScale;
					if (intensity > 0)
					{
						if (m_smoothProfile > 0)
						{
							intensity = math.min(1, intensity * intensity * intensity);
							intensity = intensity * intensity * (3 - intensity - intensity);
						}
						else
						{
							intensity = math.min(1, intensity);
							//intensity = intensity * intensity * intensity * intensity * intensity * intensity * intensity * intensity;
						}
						int index = lx + lz * m_stride;
						var current = m_heights[index];
						var targetHeight = m_height;
						if (m_fillWithOriginal)
						{
							targetHeight = m_heightsReference[index];
							intensity = 1;
						}
						var target = Mathf.Lerp(current, targetHeight, intensity);
						if (current.Nearly(target) == false)
						{
							if (m_onlyRaise == false || current < target)
							{
								if (m_waterStride != 0 && (m_waterPresence[zWaterIndex + lx / 32] & (UInt32) (1 << (lx & 31))) != 0)
									continue;
								m_heights[index] = target;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
								m_heightPixels[index] = (UInt16) (target * HtoPixel);
#endif
								hasChanged = true;
							}
						}
					}
				}
			}
		}
	}

	public enum RadialHeightsBatchJobMode
	{
		NORMAL=0,
		WATERSURFACE,
		RAVINE,
		ISLAND
	};

	public struct RavineData
	{
		public float startFraction;
		public float endFraction;
		public float step;
	};

	public struct IslandData
	{
		public float m_heightMin;
		public float m_heightMax;
		public float m_random;
		public int m_inset;
	};

	[BurstCompile]
	public struct RadialHeightsBatchJob : IJob
	{
		NativeArray<float> m_heights;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		NativeArray<UInt16> m_heightPixels;
#endif
		[ReadOnly] NativeArray<UInt32> m_waterPresence;
		[ReadOnly] int m_waterStride;
		[ReadOnly] float m_radiusInner;
		[ReadOnly] float m_radiusOuter;
		[ReadOnly] int m_stride;
		[ReadOnly] float m_outerSqrd;
		[ReadOnly] float m_dScale;
		[ReadOnly] NativeArray<float> m_smoothValues;
		[ReadOnly] float m_heightAdjust;

		[ReadOnly] NativeArray<Vector3> m_jitteredPositions;
		[ReadOnly] NativeArray<Vector3> m_unjitteredPositions;
		[ReadOnly] float3 m_dirtyMin;
		[ReadOnly] float3 m_dirtyMax;
		[ReadOnly] Vector3 m_terrainOrigin;
		[ReadOnly] float m_terrainXZScale;
		[ReadOnly] bool m_onlyRaise;
		[ReadOnly] float m_smoothProfile;
		NativeArray<int> m_dirtyAreaAjustment;
		RadialHeightsBatchJobMode m_mode;
		[ReadOnly] RavineData m_ravineData;
		[ReadOnly] IslandData m_islandData;

		public RadialHeightsBatchJob(NativeArray<Vector3> _jitteredPositions, NativeArray<Vector3> _unjitteredPositions, float _radiusInner, float _radiusOuter, int _stride, float _outerSqrd, float _dScale, NativeArray<float> _smoothValues, float _heightAdjust, 
			Vector3 _dirtyMin, Vector3 _dirtyMax, bool _onlyRaise, bool _ignoreWaterPresence, RadialHeightsBatchJobMode _mode, float _smoothProfile = 1, RavineData _ravineData = new RavineData(), IslandData _islandData = new IslandData())
		{
			m_heights = Me.m_heights;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
			m_heightPixels = Me.m_legacyHeightsPixels;
#endif
			m_waterPresence = Me.m_waterPresence;
			m_waterStride = _ignoreWaterPresence ? 0 : c_heightmapW;
			m_radiusInner = _radiusInner;
			m_radiusOuter = _radiusOuter;
			m_stride = _stride;
			m_jitteredPositions = _jitteredPositions;
			m_unjitteredPositions = _unjitteredPositions;
			m_outerSqrd = _outerSqrd;
			m_dScale = _dScale;
			m_smoothValues = _smoothValues;
			m_heightAdjust = _heightAdjust;
			m_dirtyMin = _dirtyMin;
			m_dirtyMax = _dirtyMax;
			m_terrainOrigin = c_terrainOrigin;
			m_terrainXZScale = c_terrainXZScale;
			m_onlyRaise = _onlyRaise;
			m_smoothProfile = _smoothProfile;
			m_mode = _mode;
			m_ravineData = _ravineData;
			m_islandData = _islandData;

			// Idx 0 is if the dirty area needs to be adjusted at all.
			// Idx 1 & 2 are the min x and min z coords of the area adjustment.
			// Idx 3 & 4 are the max x and min z coords of the area adjustment.
			m_dirtyAreaAjustment = new NativeArray<int>(5, Allocator.Persistent, NativeArrayOptions.ClearMemory);
			m_dirtyAreaAjustment[0] = 0;
			m_dirtyAreaAjustment[1] = m_dirtyAreaAjustment[2] = int.MaxValue;
			m_dirtyAreaAjustment[3] = m_dirtyAreaAjustment[4] = int.MaxValue;
		}


		float FastSqrt01(float number) => number * (2 - number);
		
		public void Execute()
		{
			float outerSqrd = m_radiusOuter * m_radiusOuter, innerSqrd = m_radiusInner * m_radiusInner;
			float dScale = 1.0f / (outerSqrd - innerSqrd);
		
			NativeArray<Vector3> centerList;
			if (m_mode == RadialHeightsBatchJobMode.WATERSURFACE)
			{
				centerList = m_unjitteredPositions;
			}
			else
			{
				centerList = m_jitteredPositions;
			}

			int startValue = 0;
			int count = centerList.Length;

			if (m_mode == RadialHeightsBatchJobMode.ISLAND)
			{
				startValue = m_islandData.m_inset;
				count -= m_islandData.m_inset;
			}

			for (int i=startValue; i<count; i++)
			{
				Vector3 center = centerList[i];

				if (m_mode == RadialHeightsBatchJobMode.WATERSURFACE || center.y < 0.1f)
				{
					if (m_mode == RadialHeightsBatchJobMode.ISLAND)
					{
						Vector3 up = m_unjitteredPositions[i];
						center = Vector3.Lerp(up, center, m_islandData.m_random);
					}

					float thisInnerRadius = m_radiusInner;
					float thisOuterRadius = m_radiusOuter;
					if (m_mode == RadialHeightsBatchJobMode.RAVINE)
					{
						float mul = 1;
						float t = m_unjitteredPositions[i].y;
						if (t < m_ravineData.startFraction) mul = (t + m_ravineData.step) / m_ravineData.startFraction;
						else if (t > 1 - m_ravineData.endFraction) mul = (1 - t) / m_ravineData.endFraction;

						thisInnerRadius *= mul;
						innerSqrd = thisInnerRadius * thisInnerRadius;
						thisOuterRadius *= mul;
						outerSqrd = thisOuterRadius * thisOuterRadius;
					}

					var scaledCenter = (center - m_terrainOrigin) * m_terrainXZScale;
					int ix = TerrainX(center.x), iz = TerrainZ(center.z);
					int iRad = Mathf.CeilToInt(thisOuterRadius);

					int iMinX = ix - iRad, iMaxX = ix + iRad, iMinZ = iz - iRad, iMaxZ = iz + iRad;
					if (iMinX < m_dirtyMin.x) iMinX = (int) m_dirtyMin.x;
					if (iMaxX > m_dirtyMax.x) iMaxX = (int) m_dirtyMax.x;
					if (iMinZ < m_dirtyMin.z) iMinZ = (int) m_dirtyMin.z;
					if (iMaxZ > m_dirtyMax.z) iMaxZ = (int) m_dirtyMax.z;
					if (iMinX <= iMaxX && iMinZ <= iMaxZ)
					{
						var waterStride = (m_waterStride + 31) / 32;
						int dirtyMinX = (int)m_dirtyMin.x, dirtyMaxX = (int)m_dirtyMax.x, dirtyMinZ = (int)m_dirtyMin.z, dirtyMaxZ = (int)m_dirtyMax.z;
						for (int z = -iRad; z <= iRad; ++z)
						{
							int lz = z + iz;
							if (lz < dirtyMinZ || lz > dirtyMaxZ) continue;
							float dz = scaledCenter.z - lz;
							float dzSqrd = dz * dz;
							int zWaterIndex = lz * waterStride;
							for (int x = -iRad; x <= iRad; ++x)
							{
								int lx = x + ix;
								if (lx < dirtyMinX || lx > dirtyMaxX) continue;
								float dx = scaledCenter.x - lx;
								float intensity = (m_outerSqrd - (dx * dx + dzSqrd)) * m_dScale;
								if (intensity > 0)
								{
									if (m_smoothProfile > 0)
									{
										intensity = math.min(1, intensity * intensity * intensity);
										intensity = intensity * intensity * (3 - intensity - intensity);
									}
									else
									{
										intensity = math.min(1, intensity);
										//intensity = intensity * intensity * intensity * intensity * intensity * intensity * intensity * intensity;
									}
									int index = lx + lz * m_stride;
									var current = m_heights[index];
									float height = m_smoothValues[i]+m_heightAdjust;
									if (m_mode == RadialHeightsBatchJobMode.ISLAND)
									{
										Vector3 up = m_unjitteredPositions[i];
										int seedBase = 0x1717317;
										uint seed = (uint) (up.y * 100000 + seedBase);
                    var r = Utility.XorShift01(ref seed);
										height = m_smoothValues[i] + Mathf.Lerp(m_islandData.m_heightMin, m_islandData.m_heightMax, r);
									}
									var target = Mathf.Lerp(current, height, intensity);
									if (current.Nearly(target) == false)
									{
										if (m_onlyRaise == false || current < target)
										{
											if (m_waterStride != 0 && (m_waterPresence[zWaterIndex + lx / 32] & (UInt32) (1 << (lx & 31))) != 0)
												continue;
											m_heights[index] = target;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
											m_heightPixels[index] = (UInt16) (target * HtoPixel);
#endif
										}
									}
								}
							}
						}
						
						// Idx 0 is if the dirty area needs to be adjusted at all.
						// Idx 1 & 2 are the min x and min z coords of the area adjustment.
						// Idx 3 & 4 are the max x and min z coords of the area adjustment.
						m_dirtyAreaAjustment[0] = 1;
						m_dirtyAreaAjustment[1] = Math.Min(m_dirtyAreaAjustment[1], iMinX);
						m_dirtyAreaAjustment[2] = Math.Min(m_dirtyAreaAjustment[2], iMinZ);
						m_dirtyAreaAjustment[3] = Math.Min(m_dirtyAreaAjustment[3], iMaxX);
						m_dirtyAreaAjustment[4] = Math.Min(m_dirtyAreaAjustment[4], iMaxZ);
					}
				}
			}
		}

		public void End()
		{
			if (m_dirtyAreaAjustment[0] > 0)
			{
				GlobalData.Me.AddTerrainDirtyRegion(m_dirtyAreaAjustment[1], m_dirtyAreaAjustment[2], m_dirtyAreaAjustment[3], m_dirtyAreaAjustment[4]);
			}
			m_dirtyAreaAjustment.Dispose();
		}
	}
	[BurstCompile]
	public struct RadialAdjustHeightsJob : IJob
	{
		NativeArray<float> m_heights;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		NativeArray<UInt16> m_heightPixels;
#endif
		[ReadOnly] int m_x;
		[ReadOnly] int m_z;
		[ReadOnly] int m_rad;
		[ReadOnly] int m_stride;
		[ReadOnly] float m_outerSqrd;
		[ReadOnly] float m_dScale;
		[ReadOnly] float m_height;
		[ReadOnly] float3 m_scaledCenter;
		[ReadOnly] float3 m_dirtyMin;
		[ReadOnly] float3 m_dirtyMax;

		public RadialAdjustHeightsJob(Vector3 _scaledCenter, int _x, int _z, int _rad, int _stride, float _outerSqrd, float _dScale, float _height, Vector3 _dirtyMin, Vector3 _dirtyMax)
		{
			m_heights = Me.m_heights;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
			m_heightPixels = Me.m_legacyHeightsPixels;
#endif
			m_x = _x;
			m_z = _z;
			m_rad = _rad;
			m_stride = _stride;
			m_scaledCenter = _scaledCenter;
			m_outerSqrd = _outerSqrd;
			m_dScale = _dScale;
			m_height = _height;
			m_dirtyMin = _dirtyMin;
			m_dirtyMax = _dirtyMax;
		}

		public void Execute()
		{
			bool hasChanged = false;
			int dirtyMinX = (int)m_dirtyMin.x, dirtyMaxX = (int)m_dirtyMax.x, dirtyMinZ = (int)m_dirtyMin.z, dirtyMaxZ = (int)m_dirtyMax.z;
			for (int z = -m_rad; z <= m_rad; ++z)
			{
				int lz = z + m_z;
				if (lz < dirtyMinZ || lz > dirtyMaxZ) continue;
				float dz = m_scaledCenter.z - lz;
				float dzSqrd = dz * dz;
				for (int x = -m_rad; x <= m_rad; ++x)
				{
					int lx = x + m_x;
					if (lx < dirtyMinX || lx > dirtyMaxX) continue;
					float dx = m_scaledCenter.x - lx;
					float intensity = (m_outerSqrd - (dx * dx + dzSqrd)) * m_dScale;
					if (intensity > 0)
					{
						intensity = Mathf.Min(1, intensity * intensity * intensity);
						intensity = intensity * intensity * (3 - intensity - intensity);
						int index = lx + lz * m_stride;
						var current = m_heights[index];
						var target = current + m_height * intensity;
						if (current.Nearly(target) == false)
						{
							m_heights[index] = target;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
							m_heightPixels[index] = (UInt16) (target * HtoPixel);
#endif
							hasChanged = true;
						}
					}
				}
			}
		}
	}	
	[BurstCompile]
	public struct RadialHeightsSmoothJob : IJob
	{
		NativeArray<float> m_heights;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		NativeArray<UInt16> m_heightPixels;
#endif
		[ReadOnly] float m_radiusInner;
		[ReadOnly] float m_radiusOuter;
		[ReadOnly] int m_stride;
		[ReadOnly] NativeArray<Vector3> m_centerList;
		[ReadOnly] float3 m_dirtyMin;
		[ReadOnly] float3 m_dirtyMax;
		[ReadOnly] Vector3 m_terrainOrigin;
		[ReadOnly] float m_terrainXZScale;
		NativeArray<int> m_dirtyAreaAjustment;

		public RadialHeightsSmoothJob(NativeArray<Vector3> _centerList, float _radiusInner, float _radiusOuter, int _stride, Vector3 _dirtyMin, Vector3 _dirtyMax, Vector3 _terrainOrigin, float _terrainXZScale)
		{
			m_heights = Me.m_heights;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
			m_heightPixels = Me.m_legacyHeightsPixels;
#endif
			m_stride = _stride;
			m_centerList = _centerList;
			m_radiusInner = _radiusInner;
			m_radiusOuter = _radiusOuter;
			m_dirtyMin = _dirtyMin;
			m_dirtyMax = _dirtyMax;
			m_terrainOrigin = _terrainOrigin;
			m_terrainXZScale = _terrainXZScale;

			// Idx 0 is if the dirty area needs to be adjusted at all.
			// Idx 1 & 2 are the min x and min z coords of the area adjustment.
			// Idx 3 & 4 are the max x and min z coords of the area adjustment.
			m_dirtyAreaAjustment = new NativeArray<int>(5, Allocator.Persistent, NativeArrayOptions.ClearMemory);
			m_dirtyAreaAjustment[0] = 0;
			m_dirtyAreaAjustment[1] = m_dirtyAreaAjustment[2] = int.MaxValue;
			m_dirtyAreaAjustment[3] = m_dirtyAreaAjustment[4] = int.MaxValue;
		}

		public void Execute()
		{
			float outerSqrd = m_radiusOuter * m_radiusOuter, innerSqrd = m_radiusInner * m_radiusInner;
			float dScale = 1.0f / (outerSqrd - innerSqrd);
			for (int i=0; i<m_centerList.Length; i++)
			{
				Vector3 center = m_centerList[i];
				if (center.y < 0.1f)
				{
					var scaledCenter = (center - m_terrainOrigin) * m_terrainXZScale;
					int ix = TerrainX(center.x), iz = TerrainZ(center.z);
					int iRad = Mathf.CeilToInt(m_radiusOuter);
					
					int iMinX = ix - iRad, iMaxX = ix + iRad, iMinZ = iz - iRad, iMaxZ = iz + iRad;
					if (iMinX < m_dirtyMin.x) iMinX = (int) m_dirtyMin.x;
					if (iMaxX > m_dirtyMax.x) iMaxX = (int) m_dirtyMax.x;
					if (iMinZ < m_dirtyMin.z) iMinZ = (int) m_dirtyMin.z;
					if (iMaxZ > m_dirtyMax.z) iMaxZ = (int) m_dirtyMax.z;
					if (iMinX <= iMaxX && iMinZ <= iMaxZ)
					{
						bool hasChanged = false;
						int dirtyMinX = (int)m_dirtyMin.x, dirtyMaxX = (int)m_dirtyMax.x, dirtyMinZ = (int)m_dirtyMin.z, dirtyMaxZ = (int)m_dirtyMax.z;
						for (int z = -iRad; z <= iRad; ++z)
						{
							int lz = z + iz;
							if (lz < dirtyMinZ || lz > dirtyMaxZ) continue;
							float dz = scaledCenter.z - lz;
							float dzSqrd = dz * dz;
							for (int x = -iRad; x <= iRad; ++x)
							{
								int lx = x + ix;
								if (lx < dirtyMinX || lx > dirtyMaxX) continue;
								float dx = scaledCenter.x - lx;
								float intensity = (outerSqrd - (dx * dx + dzSqrd)) * dScale;
								if (intensity > 0)
								{
									int index = lx + lz * m_stride;
									intensity = Mathf.Min(1, intensity);
									float average = m_heights[index - m_stride - 1] + m_heights[index - m_stride] + m_heights[index - m_stride + 1] +
													m_heights[index - 1] + m_heights[index] + m_heights[index + 1] +
													m_heights[index + m_stride - 1] + m_heights[index + m_stride] + m_heights[index + m_stride + 1];
									/*float average = 0;
									int indexD = index - m_stride;
									for (int sz = -1; sz <= 1; ++sz)
									{
										for (int sx = -1; sx <= 1; ++sx)
											average += m_heights[indexD + sx];
										indexD += m_stride;
									}*/
									average *= 1.0f / 9.0f;
									var current = m_heights[index];
									current = Mathf.Lerp(current, average, intensity);
									if (m_heights[index].Nearly(current) == false)
									{
										m_heights[index] = current;
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
										m_heightPixels[index] = (UInt16) (current * HtoPixel);
#endif
										hasChanged = true;
									}
								}
							}
						}

						// Idx 0 is if the dirty area needs to be adjusted at all.
						// Idx 1 & 2 are the min x and min z coords of the area adjustment.
						// Idx 3 & 4 are the max x and min z coords of the area adjustment.
						m_dirtyAreaAjustment[0] = 1;
						m_dirtyAreaAjustment[1] = Math.Min(m_dirtyAreaAjustment[1], iMinX);
						m_dirtyAreaAjustment[2] = Math.Min(m_dirtyAreaAjustment[2], iMinZ);
						m_dirtyAreaAjustment[3] = Math.Min(m_dirtyAreaAjustment[3], iMaxX);
						m_dirtyAreaAjustment[4] = Math.Min(m_dirtyAreaAjustment[4], iMaxZ);
					}
				}
			}
		}

		public void End()
		{
			if (m_dirtyAreaAjustment[0] > 0)
			{
				GlobalData.Me.AddTerrainDirtyRegion(m_dirtyAreaAjustment[1], m_dirtyAreaAjustment[2], m_dirtyAreaAjustment[3], m_dirtyAreaAjustment[4]);
			}
			m_dirtyAreaAjustment.Dispose();
		}
	}
	
	[BurstCompile]
	public struct RadialWaterPresenceJob : IJob
	{
		NativeArray<UInt32> m_waterPresence;
		[ReadOnly] NativeArray<float> m_heights;
		[ReadOnly] float m_onlyFillUnderDepth;
		[ReadOnly] int m_x;
		[ReadOnly] int m_z;
		[ReadOnly] int m_rad;
		[ReadOnly] int m_stride;
		[ReadOnly] float3 m_scaledCenter;
		[ReadOnly] float3 m_dirtyMin;
		[ReadOnly] float3 m_dirtyMax;

		public RadialWaterPresenceJob(Vector3 _scaledCenter, int _x, int _z, int _rad, Vector3 _dirtyMin, Vector3 _dirtyMax, float _onlyFillUnderDepth)
		{
			m_waterPresence = Me.m_waterPresence;
			m_heights = Me.m_heights;
			m_onlyFillUnderDepth = _onlyFillUnderDepth;
			m_x = _x;
			m_z = _z;
			m_rad = _rad;
			m_stride = c_heightmapW;
			m_scaledCenter = _scaledCenter;
			m_dirtyMin = _dirtyMin;
			m_dirtyMax = _dirtyMax;
		}

		public void Execute()
		{
			var stride = (m_stride + 31) / 32;
			var radSqrd = m_rad * m_rad;
			bool hasChanged = false;
			for (int z = -m_rad; z <= m_rad; ++z)
			{
				int lz = z + m_z;
				if (lz < m_dirtyMin.z || lz > m_dirtyMax.z) continue;
				float dz = m_scaledCenter.z - lz;
				float dzSqrd = dz * dz;
				int zIndex = lz * stride;
				for (int x = -m_rad; x <= m_rad; ++x)
				{
					int lx = x + m_x;
					if (lx < m_dirtyMin.x || lx > m_dirtyMax.x) continue;
					float dx = m_scaledCenter.x - lx;
					if (dx * dx + dzSqrd < radSqrd)
					{
						int index = lx + lz * m_stride;
						if (m_heights[index] < m_onlyFillUnderDepth)
							m_waterPresence[zIndex + lx / 32] |= (UInt32) (1 << (lx & 31));
					}
				}
			}
		}
	}

	public bool SetHeights(Vector3 _min, Vector3 _max, float _height)
	{
		int w = (int)(_max.x - _min.x), h = (int)(_max.z - _min.z);  
		bool hasChanged = false;
		for (int z = 0; z <= h; ++z)
		{
			for (int x = 0; x <= w; ++x)
			{
				var p = _min; p.x += x; p.z += z;
				hasChanged |= SetRealHeight(p, _height, false);
			}
		}
		int ix = TerrainX(_min.x), iz = TerrainZ(_min.z);
		var heights = new float[h + 1, w + 1];
		float height01 = _height * HtoHF;
		for (int z = 0; z <= h; ++z)
			for (int x = 0; x <= w; ++x)
				heights[z, x] = height01;
		m_terrainHeightsUpdated = true;
		m_terrainData.SetHeightsDelayLOD(ix, iz, heights);
		SyncHeightmap(ix, iz, w + 1, h + 1);
		bool smoothEdges = false;
		if (smoothEdges)
		{
			for (int z = -1; z <= h+1; ++z)
			{
				float weight = (z == -1 || z == h + 1) ? .05f : .1f;
				var p = _min; p.x += -1; p.z += z;
				var oldH = GlobalData.Me.GetRealHeight(p);
				hasChanged |= GlobalData.Me.SetRealHeight(p, Mathf.Lerp(oldH, _height, weight), false);
				p = _min; p.x += w + 1; p.z += z;
				oldH = GlobalData.Me.GetRealHeight(p);
				hasChanged |= GlobalData.Me.SetRealHeight(p, Mathf.Lerp(oldH, _height, weight), false);
			}
			for (int x = -1; x <= w+1; ++x)
			{
				float weight = (x == -1 || x == h + 1) ? .1f : .25f;
				var p = _min; p.x += x; p.z += -1;
				var oldH = GlobalData.Me.GetRealHeight(p);
				hasChanged |= GlobalData.Me.SetRealHeight(p, Mathf.Lerp(oldH, _height, weight), false);
				p = _min; p.x += x; p.z += h + 1;
				oldH = GlobalData.Me.GetRealHeight(p);
				hasChanged |= GlobalData.Me.SetRealHeight(p, Mathf.Lerp(oldH, _height, weight), false);
			}
			_min.x -= TerrainBlock.GlobalScale; _min.z -= TerrainBlock.GlobalScale;
			_max.x += TerrainBlock.GlobalScale; _max.z += TerrainBlock.GlobalScale;
		}
#if !USE_TERRAIN_TEXTURE_FOR_GRASS
		m_legacyHeights.Apply(false, false);
#endif
		if (hasChanged) TerrainManager.Me.Regenerate(_min, _max);
		return hasChanged;
	}
	

	public enum Layers {
		//
		Sea = 4,
		UI = 5,
		//
		Land = 8,
		Roads = 9,
		Tree = 12,
		Rock = 13,
		Follower = 14,
		Abode = 15,
		TerrainMask = (1 << (int)Land) | (1 << (int)Sea),
	}

	//============ NAV =============

	int[] m_directionOffsets, m_directions;
	int[] m_directionOffsetsNav;
	float[] m_directionDistances;
	public void InitialiseDirectionData() {
		if (m_directionOffsets == null) {
			m_directionOffsets = new int[8];
			m_directionDistances = new float[8];
			m_directions = new int[8*2];
			m_directions[0*2+0] = 1; m_directions[0*2+1] = 0;  m_directions[1*2+0] = -1; m_directions[1*2+1] = 0;
			m_directions[2*2+0] = 0; m_directions[2*2+1] = 1;  m_directions[3*2+0] = 0; m_directions[3*2+1] = -1;
			m_directions[4*2+0] = 1; m_directions[4*2+1] = 1;  m_directions[5*2+0] = -1; m_directions[5*2+1] = 1;
			m_directions[6*2+0] = 1; m_directions[6*2+1] = -1;  m_directions[7*2+0] = -1; m_directions[7*2+1] = -1;
			m_directionOffsets[0] = 1; m_directionOffsets[1] = -1; m_directionOffsets[2] = c_heightmapW; m_directionOffsets[3] = -c_heightmapW;
			m_directionOffsets[4] = c_heightmapW + 1; m_directionOffsets[5] = c_heightmapW - 1; m_directionOffsets[6] = -c_heightmapW + 1; m_directionOffsets[7] = -c_heightmapW - 1;
			m_directionDistances[0] = m_directionDistances[1] = m_directionDistances[2] = m_directionDistances[3] = 1f;
			m_directionDistances[4] = m_directionDistances[5] = m_directionDistances[6] = m_directionDistances[7] = Mathf.Sqrt(2f);
		}
	}
	public void InitialiseNavDirectionData() {
		if (m_directionOffsetsNav == null) {
			m_directionOffsetsNav = new int[8];
			m_directionOffsetsNav[0] = 1; m_directionOffsetsNav[1] = -1; m_directionOffsetsNav[2] = m_navW; m_directionOffsetsNav[3] = -m_navW;
			m_directionOffsetsNav[4] = m_navW + 1; m_directionOffsetsNav[5] = m_navW - 1; m_directionOffsetsNav[6] = -m_navW + 1; m_directionOffsetsNav[7] = -m_navW - 1;
		}
	}

	static VertexAttributeDescriptor[] s_plotVertexDesc = new[] {
		new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float32, 3),
		new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float32, 2),
	};
	[System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
	public struct PlotVertex {
		float posx, posy, posz, tu, tv;
		public PlotVertex(Vector3 _pos, float _u, float _v) {
			posx = _pos.x; posy = _pos.y; posz = _pos.z; tu = _u; tv = _v;
		}
	}

	//============ NAV =============

	NativeArray<byte> m_navGrid; public NativeArray<byte> NavGrid => m_navGrid;

	public NavCostTypes GetNavAtPoint(Vector3 _pos)
	{
		int x = TerrainXr(_pos.x), z = TerrainZr(_pos.z);
		return (NavCostTypes)m_navGrid[x + z * m_navW];
	}

	public Vector3 GetClosestLegalCharacterDropPoint(Vector3 _pos, float _raise = 0)
	{
		var allowedBitmap = (1 << (int)NavCostTypes.Road) | (1 << (int)NavCostTypes.Pavement) | (1 << (int)NavCostTypes.OffRoad);
		int iTo = V2I(ref _pos);
		var atPoint = m_navGrid[iTo];
		if (((allowedBitmap >> atPoint) & 1) == 0)
		{
			// wrong type, find nearest valid type
			// this code copied from NavJob.CheckForDegenerateTarget; these should be combined (and this should be done under burst)
			int iReplace = -1, iReplaceDistSqrd = 40 * 40;
			for (int steps = 1; steps * steps < iReplaceDistSqrd; ++steps)
			{
				int iStepL = iTo - steps - (steps << m_navShift), iStepB = iTo - steps + (steps << m_navShift);
				int iStepR = iTo + steps + (steps << m_navShift), iStepT = iTo + steps - (steps << m_navShift);
				for (int step = 0, crossDist = steps; step < steps + steps; ++step, --crossDist)
				{
					int totalDistSqrd = steps * steps + crossDist * crossDist;
					if (totalDistSqrd < iReplaceDistSqrd)
					{
						if (((allowedBitmap >> m_navGrid[iStepL]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepL, totalDistSqrd);
						if (((allowedBitmap >> m_navGrid[iStepB]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepB, totalDistSqrd);
						if (((allowedBitmap >> m_navGrid[iStepR]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepR, totalDistSqrd);
						if (((allowedBitmap >> m_navGrid[iStepT]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepT, totalDistSqrd);
					}
					iStepL += m_navW; iStepB += 1; iStepR -= m_navW; iStepT -= 1;
				}
				if (iReplace != -1)
				{
					var newPos = I2V(iReplace).GroundPosition(_raise);
					newPos.y = Mathf.Max(newPos.y, _pos.y);
					return newPos;
				}
			}
			
		}
		return _pos;
	}

	bool NodeNavable(int _x, int _z) {
		// rocks
		return true;
	}
	bool IsValidTarget(int _x, int _z) {
		return true;
	}
	int m_navShift, m_navMask, m_navW; public int NavW => m_navW; public int NavShift => m_navShift;
	public void UpdateNavGrid() {
		UpdateNavGrid(0, 0, c_heightmapW, c_heightmapH);
	}

	private const int c_navShift = 0;
	public const int c_navScale = 1 << c_navShift;
	public void UpdateNavGrid(int _sx, int _sz, int _ex, int _ez) { // in heightmap units
		UpdateNavInternal(_sx*c_navScale-c_navScale, _sz*c_navScale-c_navScale, _ex*c_navScale+c_navScale, _ez*c_navScale+c_navScale);
	}
	private void UpdateNavInternal(int _sx, int _sz, int _ex, int _ez) { // in nav units
		m_navGenerationFrame = Time.frameCount;
		int navSize = c_heightmapW * c_navScale;
		if (_sx < 3) _sx = 3; if (_ex > navSize-3) _ex = navSize-3;
		if (_sz < 3) _sz = 3; if (_ez > navSize-3) _ez = navSize-3;
		if (m_navGrid.IsCreated == false) {
			m_navW = navSize;
			m_navShift = c_heightmapWShift + c_navShift; m_navMask = (c_heightmapWMask << c_navShift) | 1;
			m_navGrid = new NativeArray<byte>(navSize*navSize, Allocator.Persistent, NativeArrayOptions.ClearMemory);
			InitialiseNavDirectionData();
		}
	}

	NativeArray<int> m_navGenerationFrameArray = new (1 + c_findPathMaxInProgress, Allocator.Persistent);
	public int m_navGenerationFrame
	{
		get { return m_navGenerationFrameArray[0]; }
		set { m_navGenerationFrameArray[0] = value; }
	}

	int ChangeNavInProgressID(int _idFrom, int _idTo)
	{
		for (int i = 1; i <= c_findPathMaxInProgress; ++i)
		{
			if (m_navGenerationFrameArray[i] == _idFrom)
			{
				m_navGenerationFrameArray[i] = _idTo;
				return i;
			}
		}
		return 0;
	}

	void KillAllNavJobs()
	{
		int count = 0;
		for (int i = 1; i <= c_findPathMaxInProgress; ++i)
		{
			if (m_navGenerationFrameArray[i] != 0)
			{
				m_navGenerationFrameArray[i] = 0;
				++count;
			}
		}
#if UNITY_EDITOR
		if (count > 0) Debug.Log($"On shutdown killed {count} active nav {(count > 1 ? "jobs" : "job")}");
#endif
	}

	public int FillNavInProgressID(int _id) => ChangeNavInProgressID(0, _id);
	public int ClearNavInProgressID(int _id) => ChangeNavInProgressID(_id, 0);

	public void FillNavRectangle(Vector3 _worldCorner1, Vector3 _worldCorner2, int _value) {
		m_navGenerationFrame = Time.frameCount;
		int navSize = c_heightmapW * c_navScale;
		int sx = TerrainXr(_worldCorner1.x), sz = TerrainZr(_worldCorner1.z);
		int ex = TerrainXr(_worldCorner2.x), ez = TerrainZr(_worldCorner2.z);
		sx = Mathf.Clamp(sx, 0, navSize-1); ex = Mathf.Clamp(ex, 0, navSize-1);
		sz = Mathf.Clamp(sz, 0, navSize-1); ez = Mathf.Clamp(ez, 0, navSize-1);
		for (int z = sz; z <= ez; z ++) {
			for (int x = sx; x <= ex; x ++) {
				var vt = IsValidTarget(x, z);
				m_navGrid[x + z * navSize] = (byte)_value;
			}
		}
	}
	public void FillNavPath(Vector3 _center, float _radiusInner, float _radiusOuter, NavCostTypes _inner, NavCostTypes _outer, int _x = 0, int _z = 0, int _w = 1024, int _h = 1024) {
		byte typeInner = (byte)_inner, typeOuter = (byte)_outer;
		m_navGenerationFrame = Time.frameCount;
		int navSize = c_heightmapW * c_navScale;
		_x *= c_navScale; _z *= c_navScale; _w *= c_navScale; _h *= c_navScale;
		int sx = TerrainXr(_center.x - _radiusOuter), sz = TerrainZr(_center.z - _radiusOuter);
		int ex = TerrainXr(_center.x + _radiusOuter), ez = TerrainZr(_center.z + _radiusOuter);
		sx = Mathf.Clamp(sx, _x, _x+_w-1); ex = Mathf.Clamp(ex, _x, _x+_w-1);
		sz = Mathf.Clamp(sz, _z, _z+_h-1); ez = Mathf.Clamp(ez, _z, _z+_h-1);
		_center.x = TerrainXf(_center.x); _center.z = TerrainZf(_center.z);
		_radiusInner *= c_terrainXZScale; _radiusOuter *= c_terrainXZScale;
		var ratioInner = _radiusInner * _radiusInner / (_radiusOuter * _radiusOuter);

		if (true)
		{
			var job = new RadialNavJob(m_navGrid, navSize, sx, sz, ex+1-sx, ez+1-sz, _center, _radiusOuter, ratioInner, typeInner, typeOuter);
			job.Schedule().Complete();
		}
		else
		{
			byte innerPriority = s_roadTypePriorities[typeInner], outerPriority = s_roadTypePriorities[typeOuter];
			for (int z = sz; z <= ez; z++)
			{
				for (int x = sx; x <= ex; x++)
				{
					float dx = _center.x - x, dz = _center.z - z;
					float ratio = (dx * dx + dz * dz) / (_radiusOuter * _radiusOuter);
					if (ratio < 1)
					{
						int index = x + z * navSize;
						var existing = m_navGrid[index];
						m_navGrid[index] = GetNew(existing, typeInner, typeOuter, ratio < ratioInner, s_roadTypePriorities[existing], innerPriority, outerPriority);
					}
				}
			}
		}
	}

	public void FillNavPathBatch(float _start, float _end, float _step, NativeishList<float3> _pathSmoothed, List<float> _intersectionsTs, float _radiusInner, float _radiusOuter, NavCostTypes _inner, NavCostTypes _outer, int _x = 0, int _z = 0, int _w = 1024, int _h = 1024, bool _areBridgeIntersections = false)
	{
		byte typeInner = (byte)_inner, typeOuter = (byte)_outer;
		m_navGenerationFrame = Time.frameCount;
		int navSize = c_heightmapW * c_navScale;
		_x *= c_navScale; _z *= c_navScale; _w *= c_navScale; _h *= c_navScale;

		NativeArray<float> intersectionsArr = _intersectionsTs.ToNativeArray(Allocator.TempJob);
		var job = new RadialNavBatchJob(_start, _end, _step, _pathSmoothed.GetBackingArray(), _pathSmoothed.Length, intersectionsArr, m_navGrid, navSize, _x, _z, _w, _h, _radiusOuter, _radiusInner, typeInner, typeOuter, _areBridgeIntersections);
		job.Schedule().Complete();
		intersectionsArr.Dispose();
	}

	NativeArray<byte> m_navGridOriginal;
	bool m_navGridOriginalCreated = false;

	[BurstCompile]
	public struct GenNavGridOrFillFromSlopeJob : IJob
	{
		public NativeArray<float> m_heights;
		public NativeArray<byte> m_navGrid;
		private int m_x1, m_z1, m_w, m_h;
		private bool isGenNavGrid;
		
		public GenNavGridOrFillFromSlopeJob(int _x1, int _z1, int _w = 0, int _h = 0)
		{
			m_heights = Me.m_heights;
			m_navGrid = Me.m_navGridOriginal;
			if ((_w == 0) && (_h == 0))
			{
				m_x1 = 0; m_z1 = 0; m_w = c_heightmapW; m_h = c_heightmapH;
				isGenNavGrid = true;
			}
			else
			{
				m_x1 = _x1; m_z1 = _z1; m_w = _w; m_h = _h;
				isGenNavGrid = false;
			}
		}

		public void Execute()
		{
			const float slopeMaxAngleDeg = 45f;
			float slopeSteep = math.tan(math.radians(slopeMaxAngleDeg));
			float slopeSteepSquared = slopeSteep * slopeSteep;
			
			for (int z = m_z1; z < m_z1 + m_h; ++z)
			{
				for (int x = m_x1; x < m_x1 + m_w; ++x)
				{
					float steepnessSquared = CalculateSteepnessFromAverageHeight(x, z);
					bool isSlope = steepnessSquared > slopeSteepSquared;
					if (isGenNavGrid)
						m_navGrid[x + z * c_heightmapW] = isSlope ? (byte) NavCostTypes.Slope : (byte) NavCostTypes.OffRoad;
					else if (isSlope)
						m_navGrid[x + z * c_heightmapW] = (byte) NavCostTypes.Slope;
				}
			}
		}

		private float CalculateAverageSteepness(int x, int z)
		{
			float average = 0f;
			float count = 0f;
			int maxOffset = 3;
			for (int dx = -maxOffset; dx <= maxOffset; ++dx)
			{
				int nx = x + dx;
				for (int dz = -maxOffset; dz <= maxOffset; ++dz)
				{
					int nz = z + dz;

					if ((nx >= 0) && (nx < c_heightmapW) && (nz >= 0) && (nz < c_heightmapH))
					{
						average += CalculateSteepness(nx, nz);
						count += 1f;
					}
				}
			}

			return average / count;
		}

		private float CalculateSteepness(int x, int z)
		{
			int nx = math.max(x - 1, 0);
			int px = math.min(x + 1, c_heightmapW - 1);
			int nz = math.max(z - 1, 0);
			int pz = math.min(z + 1, c_heightmapH - 1);

			float dx = (m_heights[px + z * c_heightmapW] - m_heights[nx + z * c_heightmapW]) * 2f;
			float dz = (m_heights[x + pz * c_heightmapW] - m_heights[x + nz * c_heightmapW]) * 2f;

			return dx * dx + dz * dz;
		}

		private float CalculateSteepnessFromAverageHeight(int x, int z)
		{
			float left = 0f; float leftCount = 0f;
			float right = 0f; float rightCount = 0f;
			float down = 0f; float downCount = 0f;
			float up = 0f; float upCount = 0f;
			int maxOffset = 3;
			for (int dx = -maxOffset; dx <= maxOffset; ++dx)
			{
				int nx = x + dx;
				for (int dz = -maxOffset; dz <= maxOffset; ++dz)
				{
					int nz = z + dz;
					if ((nx >= 0) && (nx < c_heightmapW) && (nz >= 0) && (nz < c_heightmapH))
					{
						float height = m_heights[nx + nz * c_heightmapW];
						var sqrDiff = 0f;//dx*dx - dz*dz;
						if (dx <= 0 && sqrDiff >= 0)
						{
							left += height;
							leftCount += 1f;
						}
						if (dx >= 0 && sqrDiff >= 0)
						{
							right += height;
							rightCount += 1f;
						}
						if (dz <= 0 && sqrDiff <= 0)
						{
							down += height;
							downCount += 1f;
						}
						if (dz >= 0 && sqrDiff <= 0)
						{
							up += height;
							upCount += 1f;
						}
					}
				}
			}

			float normalX = ((right / rightCount) - (left / leftCount)) * 2f;
			float normalZ = ((up / upCount) - (down / downCount)) * 2f;

			return normalX * normalX + normalZ * normalZ;
		}
	}
	
	void CheckNavGridOriginal()
	{
		if (m_navGridOriginalCreated) return;
		m_navGridOriginalCreated = true;
		m_navGridOriginal = new NativeArray<byte>(c_heightmapW * c_heightmapH, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
		var job = new GenNavGridOrFillFromSlopeJob(0, 0);
		job.Schedule().Complete();
	}

	public void ClearNavRect(Vector3 _min, Vector3 _max)
	{
		CheckNavGridOriginal();
		
		m_navGenerationFrame = Time.frameCount;
		_min *= c_navScale; _max *= c_navScale;
		int navSize = c_heightmapW * c_navScale;
		int sx = TerrainXr(_min.x), sz = TerrainZr(_min.z);
		int ex = TerrainXr(_max.x), ez = TerrainZr(_max.z);
		sx = Mathf.Clamp(sx, 0, c_heightmapW - 1); ex = Mathf.Clamp(ex, 0, c_heightmapW - 1);
		sz = Mathf.Clamp(sz, 0, c_heightmapH - 1); ez = Mathf.Clamp(ez, 0, c_heightmapH - 1);
		
		int w = ex + 1 - sx;
		int h = ez + 1 - sz;
		int index = sz * navSize + sx;
		if (sx == 0 && sz == 0 && w == c_heightmapW && h == c_heightmapH)
		{
			NativeArray<byte>.Copy(m_navGridOriginal, 0, m_navGrid, 0, c_heightmapW * c_heightmapH);
		}
		else
		{
			for (int z = 0; z < h; ++z, index += navSize)
				NativeArray<byte>.Copy(m_navGridOriginal, index, m_navGrid, index, w);
		}
		//var row = new byte[w];
		//for (int x = 0; x < w; x++) row[x] = (byte)NavCostTypes.OffRoad;
		//for (int z = sz; z <= ez; z++) NativeArray<byte>.Copy(row, 0, m_navGrid, z * navSize + sx, w);
	}

	public static bool IsNavBuildable(byte _navType) => IsNavBuildable((NavCostTypes)_navType);
	public static bool IsLowNavable(byte _navType) => IsLowNavable((NavCostTypes)_navType);
	public static bool IsNavable(byte _navType) => IsNavable((NavCostTypes)_navType);
	public static bool IsPerfectNavable(byte _navType) => IsPerfectNavable((NavCostTypes)_navType);

	public static bool IsNavBuildable_Old(NavCostTypes _type)
	{
		switch (_type)
		{
			case NavCostTypes.OffRoad: // general countryside
			case NavCostTypes.PushThrough: // hedge-like
			case NavCostTypes.Slope: // steep slopes don't block buildings
			case NavCostTypes.LowNoNav: // nav blockers such as barrels
			//case NavCostTypes.NoNav: // fence-like // GL - 290524 - no longer allowing plots that would interrupt a fence
				return true;
			default:
				return false;
		}
	}

	public static bool IsNavBuildable(NavCostTypes _type)
	{
		// New building placement scheme
		switch (_type)
		{
			case NavCostTypes.OffRoad: // general countryside
			case NavCostTypes.PushThrough: // hedge-like
			case NavCostTypes.Pavement:
			case NavCostTypes.EnterBuilding:
			case NavCostTypes.LowNoNav: // nav blockers such as barrels
				return true;
			default:
				return false;
		}
	}
	
	public static bool IsLowNavable(NavCostTypes _type)
	{
		return _type != NavCostTypes.NoNav && _type != NavCostTypes.Building && _type != NavCostTypes.NoNavCanBreak; // exclude LowNoNav
	}
	
	public static bool IsPerfectNavable(NavCostTypes _type)
	{
        // exclude NoNav, Building, Gate, PushThrough, Slope, BuildingInner, LowNoNav, EnterBuilding,
		return _type == NavCostTypes.Road ||  _type == NavCostTypes.Pavement ||  _type == NavCostTypes.Rail ||  _type == NavCostTypes.OffRoad; // exclude LowNoNav
	}

	public static bool IsNavable(NavCostTypes _type)
	{
		return _type != NavCostTypes.NoNav && _type != NavCostTypes.LowNoNav && _type != NavCostTypes.Building && _type != NavCostTypes.NoNavCanBreak;
	}

	public static byte GetNew(byte existing, byte _inner, byte _outer, bool _isInner, byte _existingPriority, byte _innerPriority, byte _outerPriority)
	{
		if (existing == (_isInner ? _outer : _inner))
			return _inner;
		byte applying, applyingPriority;
		if (_isInner)
		{
			applying = _inner;
			applyingPriority = _innerPriority;
		}
		else
		{
			applying = _outer;
			applyingPriority = _outerPriority;
		}
		if (applyingPriority < _existingPriority)
			return applying;
		return existing;
	}

	public static float CalculateDistanceHeuristic(int _dx, int _dz)
	{
		//return math.sqrt(dx*dx+dz*dz); // euclidean distance, not a good fit since on a grid it will tend to under-estimate
		if (_dx < 0) _dx = -_dx;
		if (_dz < 0) _dz = -_dz;
		const float c_root2minus1 = .4142f;
		return _dx + _dz + math.min(_dx, _dz) * c_root2minus1; // good fit for a grid, matches the straight/diagonal nature of grid nav
		//return _dx + _dz; // manhatten distance, tends to over-estimate but fairly good fit
		
	}

	public int NavToLowRes(int _index)
	{
		int x = _index & m_navMask;
		int z = _index >> m_navShift;
		x >>= m_navQuantiseShift;
		z >>= m_navQuantiseShift;
		return x + (z << (m_navShift - m_navQuantiseShift));
	}	
	public int V2I(ref Vector3 _v) { return TerrainXr(_v.x) + (TerrainZr(_v.z) << m_navShift); }
	public Vector3 I2V(int _i) { int x = _i & m_navMask, z = _i >> m_navShift; return new Vector3((float)x / c_terrainXZScale * TerrainBlock.GlobalScale / c_navScale + c_terrainOriginX, 0, (float)z / c_terrainXZScale * TerrainBlock.GlobalScale / c_navScale + c_terrainOriginZ); }
	public float DistanceHeuristic(int _from, int _to) {
		int x1 = _from & m_navMask, z1 = _from >> m_navShift, x2 = _to & m_navMask, z2 = _to >> m_navShift;
		int dx = x1 - x2, dz = z1 - z2;
		return CalculateDistanceHeuristic(dx, dz);
	}

	public enum NavCostTypes { NoNav, Building, Road, Pavement, Rail, OffRoad, Gate, PushThrough, Slope, BuildingInner, LowNoNav, EnterBuilding, NoNavCanBreak, MixedLowResCell, BridgeEdge };
	// RW-02-MAY-25: I've made Building higher priority than BuildingInner because otherwise, if multiple buildings are close together, the BuildingInner will flow
	// uninterrupted between them and characters will start trying to nav paths between the doors of separate buildings.
	static byte[] s_roadTypePriorities = new byte[] { 0, 8, 12, 12, 12, 16, 2, 14, 18, 9, 4, 2, 0, 0, 13 }; // priority value 0 is highest priority - EnterBuilding has high priority because it's a special case
	static Color[] s_gizmoNavColours = new Color[]
		{Color.black, Color.blue, Color.green, Color.yellow, Color.magenta, Color.red, Color.grey, Color.white, Color.cyan, new Color(1, .5f, .5f), new Color(.3f, .3f, .6f), new Color(.9f, .4f, 0), new Color(.4f, 0, 0), new Color(.6f,.8f,1), new Color(.3f, .5f, 1f)};
	public static float[] s_quantCosts = { 1000, 999, 1.2f, 1, 5, 3, 5, 40, 995, 3, 995, 1001, 1000, 1000, 995 }; // EnterBuilding special-cased again 
	public static float[] s_pedestrianCosts = { 1000, 999, 1.2f, 1, 5, 3, 5, 40, 995, 3, 995, 1.25f, 1000, 1000, 995 };
	public static float[] s_vehicleCosts = { 1000, 999, 1, 3, 5, 4, 5, 40, 995, 4, 995, 10, 1000, 1000, 995 }; //<- TS: Vehicle offroad=4 to stop it going over countryside if bridge is very far (might need even higher value if bridge ends up too far)
	public static float[] s_trainCosts = { 1000, 999, 10, 10, 1, 10, 5, 40, 995, 10, 995, 40, 1000, 995 };
	public static float[] s_threatCosts = { 1000, 999, 1.2f, 1, 5, 5, 1000, 15, 995, 10, 995, 10, 1000, 1000, 995 }; // Threat: like vehicle except can't go on Gate type and ignore roads
	public static float[] s_threatWithSmashCosts = {1000, 999, 1.2f, 1, 5, 5, 1000, 15, 995, 10, 995, 10, 20, 1000, 995 }; // Like Threat but will go straight through NoNavCanBreak
	public static float[] s_animalCosts = { 1000, 999, 1f, 1, 1f, 1f, 3f, 40, 995, 10f, 995, 10f, 1000, 1000, 995 }; //Animals: roads & offroad are the same
	
	public const float C_MaxCostCutCorner = 30f;
	public class PathHandle {
		System.Threading.Thread m_thread;
		public PathHandle(ParameterizedThreadStart _start, object _o) {
			m_thread = new System.Threading.Thread(_start);
			m_thread.Start(_o);
		}
	}

	public static class ThreadSafe
	{
		private static bool s_isInitialised = false;
		private static List<string> s_messages = null;
		public static void LogError(string _s) => Log(_s);
		public static void LogWarning(string _s) => Log(_s);

		private static void CheckInit()
		{
			if (!s_isInitialised)
			{
				s_messages = new List<string>();
				GlobalData.Me.StartCoroutine(Co_ConsumeMessages());
				s_isInitialised = true;
			}
		}

		private static IEnumerator Co_ConsumeMessages()
		{
			while (true)
			{
				yield return null;
				if (s_messages.Count > 0)
				{
					lock (s_messages)
					{
						for (int i = 0; i < s_messages.Count; ++i)
						{
							Debug.Log(s_messages[i]);
						}
						s_messages.Clear();
					}
				}
			}
		}
		public static void Log(string _s)
		{
			CheckInit();
			lock (s_messages)
			{
				s_messages.Add($"TS.Log {_s}");
			}
		}
	}
	private class FindPathParams
	{
		public int m_callerID;
		public Vector3 m_from, m_to;
		public float[] m_costs;
		public bool m_findOnFail, m_isDebug;
		public float m_corridorWidth;
		public SubNavData m_subNav;
		public float m_destinationRadius;
		public Action<NativeList<float3>, NavJob.EResult> m_cb;
	}
	List<FindPathParams> m_waitingFindPathQueue = new();
	const int c_findPathMaxInProgress = 8;
	List<int> m_findPathInProgressIDs = new();

	public enum ENavJobState { None, Queued, InProgress };

	public ENavJobState GetNavState(int _id)
	{
		for (int i = 1; i <= c_findPathMaxInProgress; ++i)
			if (m_navGenerationFrameArray[i] == _id)
				return ENavJobState.InProgress;
		for (int i = 0; i < m_waitingFindPathQueue.Count; ++i)
			if (m_waitingFindPathQueue[i].m_callerID == _id)
				return ENavJobState.Queued;
		return ENavJobState.None;
	}

	public void KillExistingNav(int _id)
	{
		ClearNavInProgressID(_id);
		for (int i = m_waitingFindPathQueue.Count - 1; i >= 0; --i)
			if (m_waitingFindPathQueue[i].m_callerID == _id)
				m_waitingFindPathQueue.RemoveAt(i);
	}

	void AddWaitingFindPath(FindPathParams _params, bool _abandonExisting = false)
	{
		#if UNITY_EDITOR
		//Debug.Log($"{GetType().Name} - Globaldata - addwaitingfindPath - working on {PathInProgressNames()} -  m_waitingFindPathQueue.Count {m_waitingFindPathQueue.Count} - {EditorUtility.InstanceIDToObject(_params.m_callerID)?.name}");
		#endif
		// if this caller already has an entry in the queue replace that entry with this new one
		if (_abandonExisting) ClearNavInProgressID(_params.m_callerID);
		for (int i = 0; i < m_waitingFindPathQueue.Count; ++i)
		{
			if (m_waitingFindPathQueue[i].m_callerID == _params.m_callerID)
			{
				m_waitingFindPathQueue[i] = _params;
				return;
			}
		}
		m_waitingFindPathQueue.Add(_params);
	}

	void CheckWaitingFindPathQueue()
	{
		if (CheckLowResGrid()) return;
		
		if(m_findPathInProgressIDs.Count >= c_findPathMaxInProgress)
		{
			//Debug.LogError($"GLobalDAta CheckWaitingFindPathQueue - m_findPathInProgress {m_findPathInProgress}"); 
			return;
		}
		if (m_waitingFindPathQueue.Count > 0)
		{
			// find a job that doesn't have a navJob in flight
			for (int i = 0; i < m_waitingFindPathQueue.Count; ++i)
			{
				var entry = m_waitingFindPathQueue[i];
				if (m_findPathInProgressIDs.Contains(entry.m_callerID)) continue;
				m_waitingFindPathQueue.RemoveAt(i);
				FindPathJob(entry.m_from, entry.m_to, entry.m_costs, entry.m_cb, entry.m_corridorWidth, entry.m_callerID, entry.m_subNav, entry.m_destinationRadius, entry.m_isDebug);
				break;
			}
		}
	}
	// returns true if path is still valid
	bool CompleteWaitingFindPath(int _callerID)
	{
		m_findPathInProgressIDs.Remove(_callerID);
		return ClearNavInProgressID(_callerID) != 0;
	}

	String PathInProgressDetails()
	{
		String s = "";
		for (int i = 0; i < m_findPathInProgressIDs.Count; ++i)
		{
			s += $"{m_findPathInProgressIDs[i]} ";
		}
		return s;
	}

	String PathInProgressNames()
	{
		String s = "";
#if UNITY_EDITOR
		for (int i = 0; i < m_findPathInProgressIDs.Count; ++i)
		{
			var obj = EditorUtility.InstanceIDToObject(m_findPathInProgressIDs[i]);
			if (obj == null) s += $"\n[{m_findPathInProgressIDs[i]}]";
			else s += $"\n{obj.name}";
		}
#endif
		if (String.IsNullOrEmpty(s)) return "nothing";
		return s;
	}

	static DebugConsole.Command s_shownavqueue = new ("navqueue", _s => {
		GameManager.SetConsoleDisplay(() => $"NavQueue:{Me.m_waitingFindPathQueue.Count} <size=75%>{Me.PathInProgressNames()}</size>");
	});
	
#if UNITY_EDITOR
	public class PathTracker
	{
		public int m_id;
		public Vector3 m_from, m_to;
		public List<Vector3> m_path;
		public float m_timeToGenerate, m_generateStartTime;
		const float c_raise = .5f;

		public PathTracker(int _id, Vector3 _from, Vector3 _to, NativeList<float3> _path, float _startTime)
		{
			m_id = _id;
			m_from = _from.GroundPosition(c_raise);
			m_to = _to.GroundPosition(c_raise);
			m_path = new ();
			for (int i = 0; i < _path.Length; ++i) m_path.Add(_path[i]);
			m_generateStartTime = _startTime;
			m_timeToGenerate = Time.time - _startTime;
		}

		public void DrawGizmo()
		{
			Gizmos.color = Color.green;
			Gizmos.DrawSphere(m_from, .1f);
			Gizmos.color = Color.red;
			Gizmos.DrawSphere(m_to, .1f);
			DrawPathGizmo(m_path);
		}
	}
	private List<PathTracker> m_pathTracker = new();
	public void TrackPath(int _id, Vector3 _from, Vector3 _to, NativeList<float3> _path, float _startTime)
	{
		m_pathTracker.Add(new PathTracker(_id, _from, _to, _path, _startTime));
		if (m_pathTracker.Count > 1000) m_pathTracker.RemoveAt(0);
	}
	public int m_pathTrackerIndex = 0;
	public int m_pathTrackerIndexMax = 0;
	public int m_pathTrackerRequestID;
	public float m_pathTrackerGenerateTime;
	public float m_pathTrackerTimeTaken;

	void DrawPathTrackerGizmo()
	{
		var selAgent = Selection.activeGameObject?.GetComponent<NavAgent>();
		int index;
		if (selAgent != null)
		{
			int count = 0;
			int matchID = selAgent.GetInstanceID();
			for (int i = 0; i < m_pathTracker.Count; ++i)
				if (m_pathTracker[i].m_id == matchID)
					++count;
			selAgent.m_pathTrackerIndexMax = count - 1;
			var agentIndex = selAgent.m_pathTrackerIndex;
			if (agentIndex < 0) agentIndex = 0;
			if (agentIndex > count - 1) agentIndex = count - 1;
			selAgent.m_pathTrackerIndex = agentIndex;
			if (agentIndex >= 0)
			{
				for (index = 0; index < m_pathTracker.Count; ++index)
					if (m_pathTracker[index].m_id == matchID)
						if (--agentIndex < 0)
							break;
				var tracker = m_pathTracker[index];
				selAgent.m_pathTrackerTimeTaken = tracker.m_timeToGenerate;
				selAgent.m_pathTrackerGenerateTime = tracker.m_generateStartTime;
			}
			else 
				index = -1;
		}
		else
		{
			m_pathTrackerIndexMax = m_pathTracker.Count - 1;
			if (m_pathTrackerIndex < 0) m_pathTrackerIndex = 0;
			if (m_pathTrackerIndex > m_pathTrackerIndexMax) m_pathTrackerIndex = m_pathTrackerIndexMax;
			index = m_pathTrackerIndex;
		}
		if (index >= 0)
		{
			var tracker = m_pathTracker[index];
			m_pathTrackerRequestID = tracker.m_id;
			m_pathTrackerTimeTaken = tracker.m_timeToGenerate;
			m_pathTrackerGenerateTime = tracker.m_generateStartTime;
			tracker.DrawGizmo();
		}
	}
#endif

	public bool m_debugNavInfo = false;
	public PathHandle FindPathAsync(int _callerID, Vector3 _from, Vector3 _to, float[] _costs, bool _findClosestOnFail, Action<NativeList<float3>, NavJob.EResult> _cb, float _corridorWidth = 0, bool _abandonExisting = false, SubNavData _subNav = null, float _destinationRadius = 0, bool _isDebug = false) {
		float c_minMapPosX = c_terrainOriginX + 1;
		float c_minMapPosZ = c_terrainOriginZ + 1;
		float c_maxMapPosX = c_terrainOriginX + c_heightmapW / c_terrainXZScale - 1;
		float c_maxMapPosZ = c_terrainOriginZ + c_heightmapH / c_terrainXZScale - 1;
		
		if (_subNav != null)
		{
			c_minMapPosX = _subNav.m_origin.x + 1;
			c_minMapPosZ = _subNav.m_origin.z + 1;
			c_maxMapPosX = _subNav.m_origin.x + _subNav.m_width / c_terrainXZScale - 1;
			c_maxMapPosZ = _subNav.m_origin.z + _subNav.m_height / c_terrainXZScale - 1;
		}

		if (_from.x < c_minMapPosX || _from.z < c_minMapPosZ || _from.x > c_maxMapPosX || _from.z > c_maxMapPosZ || _to.x < c_minMapPosX || _to.z < c_minMapPosZ || _to.x > c_maxMapPosX || _to.z > c_maxMapPosZ)
		{
			Debug.LogError($"Error: nav request out of range {_from} .. {_to}");
			_from.x = Mathf.Clamp(_from.x, c_minMapPosX, c_maxMapPosX);
			_from.z = Mathf.Clamp(_from.z, c_minMapPosZ, c_maxMapPosZ);
			_to.x = Mathf.Clamp(_to.x, c_minMapPosX, c_maxMapPosX);
			_to.z = Mathf.Clamp(_to.z, c_minMapPosZ, c_maxMapPosZ);
		}
		_from.y = NavStrut.NavStrutAtPosition(_from);
		_to.y = NavStrut.NavStrutAtPosition(_to);

#if UNITY_EDITOR
		float startTime = Time.time; 
		var originalCb = _cb;
		_cb = (path, result) => { TrackPath(_callerID, _from, _to, path, startTime); originalCb(path, result); };
#endif
		if (m_debugNavInfo) Debug.LogError($"FindPathAsync {_callerID} {_from} -> {_to} frame {Time.frameCount} navRegen: {m_navGenerationFrame} {PathInProgressNames()}");
		var parameters = new FindPathParams {m_callerID = _callerID, m_from = _from, m_to = _to, m_costs = _costs, m_findOnFail = _findClosestOnFail, m_corridorWidth = _corridorWidth, m_cb = _cb, m_subNav = _subNav, m_destinationRadius = _destinationRadius, m_isDebug = _isDebug};
		AddWaitingFindPath(parameters, _abandonExisting);
		CheckWaitingFindPathQueue();
		return null;
	}

	private int m_activePathThreads = 0, m_activePathCallbacks = 0;
	public int ActivePathThreads => m_activePathThreads;
	public int ActivePathCallbacks => m_activePathCallbacks;

	static Dictionary<float[], NativeArray<float>> m_costsToNativeArray = new();
	static NativeArray<float> GetCostsNativeArray(float[] _costs)
	{
		NativeArray<float> nativeCosts;
		if (m_costsToNativeArray.TryGetValue(_costs, out nativeCosts) == false)
		{
			nativeCosts = new NativeArray<float>(_costs.Length, Allocator.Persistent);
			for (int i = 0; i < _costs.Length; ++i) nativeCosts[i] = _costs[i];
			m_costsToNativeArray[_costs] = nativeCosts;
		}
		return nativeCosts;
	}

	const bool c_dumpLowResNavMixedStats = false;
	
	const bool c_lowResNonBlockersTakeLowestScore = true;
	
	private List<ActiveJob> m_navJobs = new List<ActiveJob>();

	[BurstCompile]
	public struct NavGridLowResJob : IJob
	{
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeArray<byte> m_navGrid;
		[ReadOnly] int m_navShift;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeArray<float> m_costs;
		[ReadOnly] int m_navDownscaleShift;
		[NativeDisableContainerSafetyRestriction] NativeArray<byte> m_navGridLowRes;

		public NavGridLowResJob(NativeArray<byte> _navGrid, int _navShift, NativeArray<byte> _navGridLowRes, int _navDownscaleShift)
		{
			m_navGrid = _navGrid;
			m_navGridLowRes = _navGridLowRes;
			m_navShift = _navShift;
			m_navDownscaleShift = _navDownscaleShift;
			m_costs = GetCostsNativeArray(s_quantCosts);
		}

		public void Execute()
		{
			int step = 1 << m_navDownscaleShift;
			int lowShift = m_navShift - m_navDownscaleShift;
			int numMixed = 0;
			for (int z = 0; z < c_heightmapH; z += step)
			{
				for (int x = 0; x < c_heightmapW; x += step)
				{
					int v = -1, vLow = -1;
					float vCost = 0, vCostLow = 1e23f;
					int cellMixed = 0;
					for (int zz = 0; zz < step; ++zz)
					{
						for (int xx = 0; xx < step; ++xx)
						{
							int index = (x + xx) + ((z + zz) << m_navShift);
							int vv = m_navGrid[index];
							float vvCost = m_costs[vv];
							if (vvCost > vCost)
							{
								v = vv;
								vCost = vvCost;
							}
							if (vvCost < vCostLow)
							{
								vLow = vv;
								vCostLow = vvCost;
							}
							const float c_passableCostThreshold = 500;
							if (vvCost > c_passableCostThreshold) cellMixed |= 1;
							else cellMixed |= 2;
							// Note: the less-than-n blockers/non-blockers scheme doesn't work
							// e.g. You can have surrounding HL cells that make up a blocker allowing a small gap between them, the gap being filled by a small number of LL cells in this HL cell 
						}
					}
					int lx = x >> m_navDownscaleShift;
					int lz = z >> m_navDownscaleShift;
					if (cellMixed == 3) v = (int)NavCostTypes.MixedLowResCell;
					else if (c_lowResNonBlockersTakeLowestScore && cellMixed == 2) v = vLow;
					m_navGridLowRes[lx + (lz << lowShift)] = (byte)v;
					if (cellMixed == 3) ++numMixed;
				}
			}
			if (c_dumpLowResNavMixedStats)
			{
				var fraction255 = numMixed * 255 / ((c_heightmapH >> m_navDownscaleShift) * (c_heightmapW >> m_navDownscaleShift));
				m_navGridLowRes[0] = (byte)fraction255;
			}
		}
	}

	JobHandle m_navGridLowResJobHandle;
	bool m_navGridLowResJobInProgress = false;
	private bool CheckLowResGrid()
	{
		if (m_navGridLowResJobInProgress)
		{
			if (m_navGridLowResJobHandle.IsCompleted == false)
				return true;
			m_navGridLowResJobHandle.Complete();
			m_navGridLowResJobInProgress = false;
			if (c_dumpLowResNavMixedStats)
				Debug.LogError($"[{m_navGridLowRes[1]} - {(NavCostTypes)m_navGridLowRes[2]},{(NavCostTypes) m_navGridLowRes[3]}] Percentage of mixed cells in low res nav grid: {m_navGridLowRes[0] * 100.0f / 255.0f}% - equates to {m_navW * m_navW * m_navGridLowRes[0] / 255.0f:n0} cells on top of {(m_navW >> m_navQuantiseShift)*(m_navW >> m_navQuantiseShift):n0} low-res");
		}
		
		bool hold = false;
		if (m_navQuantiseShift > 0)
		{
			int size = m_navW >> m_navQuantiseShift;
			if (m_navGridLowRes.Length != size * size)
			{
				m_navGridLowRes = new(size * size, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
				m_navGridLowResGenFrame = -1;
			}
			if (m_navGridLowResGenFrame != m_navGenerationFrame)
			{
				if (Time.frameCount > m_navGenerationFrame)
				{
					m_navGridLowResGenFrame = m_navGenerationFrame;
					var gridJob = new NavGridLowResJob(m_navGrid, m_navShift, m_navGridLowRes, m_navQuantiseShift);
					m_navGridLowResJobHandle = gridJob.Schedule();
					m_navGridLowResJobInProgress = true;
				}
				else
					hold = true;
			}
		}
		return m_navGridLowResJobInProgress || hold;
	}

	public class SubNavData
	{
		public NativeArray<byte> m_navGrid;
		public NativeArray<float> m_heightGrid;
		public int m_width, m_height, m_shift, m_mask;
		public Vector3 m_origin;

		public bool ContainsPoint(float _x, float _z)
		{
			float dx = _x - m_origin.x, dz = _z - m_origin.z;
			return dx >= 0 && dz >= 0 && dx * c_terrainXZScale < m_width && dz * c_terrainXZScale < m_height;
		}

		public int GetNav(Vector3 _p)
		{
			if (ContainsPoint(_p.x, _p.z) == false) return 0;
			float dx = _p.x - m_origin.x, dz = _p.z - m_origin.z;
			dx *= c_terrainXZScale; dz *= c_terrainXZScale;
			int ix = (int)dx, iz = (int)dz;
			return m_navGrid[ix + (iz << m_shift)];
		}

		public SubNavData(int _w, int _h, Vector3 _origin, NativeArray<byte> _nav, NativeArray<float> _heights)
		{
			m_navGrid = _nav;
			m_heightGrid = _heights;
			m_width = _w;
			m_height = _h;
			m_shift = 0;
			while ((_w >> m_shift) > 1) ++m_shift;
			m_mask = (1 << m_shift) - 1;
			m_origin = _origin;
		}
	}
	
	private static float s_navRadiusOverride = -1;
	private static DebugConsole.Command s_navRadiusCmd = new("navradius", _s => floatinv.TryParse(_s, out s_navRadiusOverride), "Override nav target radius", "<float,-1,100>");
	
	private static DebugConsole.Command s_relaxPathCmd = new ("relaxpath", _s => Utility.SetOrToggle(ref Me.m_relaxPath, _s), "Apply path relaxation", "<bool>");
	public bool m_relaxPath = true;
	public enum NavMode { Async, JobSync, ExecSync };
	public NavMode m_debugNavMode = NavMode.Async;
	private int m_navGridLowResGenFrame = -1;
	private NativeArray<byte> m_navGridLowRes = new (0, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
	public void FindPathJob(Vector3 _from, Vector3 _to, float[] _costs, Action<NativeList<float3>, NavJob.EResult> _cb, float _corridorWidth, int _callerID, SubNavData _subNav = null, float _destinationRadius = 0, bool _isDebug = false)
	{
		m_findPathInProgressIDs.Add(_callerID);
		var idSlot = FillNavInProgressID(_callerID);
		NavJob job;
		if (s_navRadiusOverride >= 0) _destinationRadius = s_navRadiusOverride;
		if (_subNav == null)
			job = new NavJob(_from, _to, GetCostsNativeArray(_costs), m_navGrid, m_navGridLowRes, m_navW, m_navW, m_navShift, m_navMask, PathBlock.PathBlockData, idSlot, _corridorWidth, null, _destinationRadius,_isDebug);
		else
			job = new NavJob(_from, _to, GetCostsNativeArray(_costs), _subNav.m_navGrid, _subNav.m_navGrid, _subNav.m_width, _subNav.m_height, _subNav.m_shift, _subNav.m_mask, PathBlock.PathBlockData, idSlot, _corridorWidth, _subNav, _destinationRadius, _isDebug);
#if UNITY_EDITOR
		var mode = m_debugNavMode;
#else
		var mode = NavMode.Async;
#endif
		NavJob.EResult res;
		switch (m_debugNavMode)
		{
			case NavMode.Async:
				var handle = job.Schedule();
				int frame = m_navGenerationFrame;
				AddFindPathJobCheck(job, handle, _cb, frame, _callerID, _from, _to, _costs); //, coroutineCount);
				break;
			case NavMode.JobSync:
				job.Schedule().Complete();
				res = job.GetResult();
				_cb(job.GetOutput(), res);
				CompleteWaitingFindPath(_callerID);
				break;
			case NavMode.ExecSync:
				job.Execute();
				res = job.GetResult();
				_cb(job.GetOutput(), res);
				CompleteWaitingFindPath(_callerID);
				break;
		}
	}

	private void AddFindPathJobCheck(NavJob _job, JobHandle _handle, Action<NativeList<float3>, NavJob.EResult> _cb, int _gen, int _callerID, Vector3 _from, Vector3 _to, float[] _costs)
	{
		m_navJobs.Add(new ActiveJob() { callerID = _callerID, handle = _handle, frame = _gen, job = _job, cb = _cb, startTime = Time.realtimeSinceStartup, from = _from, to = _to, costs = _costs } );
	}

	public bool CheckLine(Vector3 _start, Vector3 _end, float[] _costs, int _tolerance = 0)
	{
		if (_start.x < c_terrainMin.x || _start.x > c_terrainMax.x || _start.z < c_terrainMin.z || _start.z > c_terrainMax.z
			|| _end.x < c_terrainMin.x || _end.x > c_terrainMax.x || _end.z < c_terrainMin.z || _end.z > c_terrainMax.z)
			return false;

		var job = new CheckLineJob(_start, _end, m_navShift, _tolerance, m_navGrid, GetCostsNativeArray(_costs));
		job.Schedule().Complete();
		return !job.GetOutput();
	}
	
	public static List<Vector3> NativePathToList(NativeList<float3> _nativePath)
	{
		List<Vector3> listRes = null;
		if (_nativePath.Length > 0)
		{
			var array = new Vector3[_nativePath.Length];
			_nativePath.AsArray().Reinterpret<Vector3>().CopyTo(array);
			return new List<Vector3>(array);

			//This needs to be integrated into the job if we ever use struts again
			listRes = new List<Vector3>(array.Length);
			NavStrut previousStrut = null;
			for (int i = 0; i < array.Length; ++i)
			{
				var p = array[i];
				NavStrut thisStrut = null;
				if (p.y < 0)
				{
					var y = p.y;
					thisStrut = NavStrut.s_allStruts[(int)-y - 2];
					p = thisStrut.GetNavPoint(previousStrut);
					p.y = y;
				}
				listRes.Add(p);
				previousStrut = thisStrut;
			}
		}
		return listRes;
	}

	public static NativeList<float3> NativePathFromList(List<Vector3> _list)
	{
		var nativeList = new NativeList<float3>(_list.Count, Allocator.Persistent);
		UnsafeUtilities.Copy(_list.GetBackingArray(), nativeList, _list.Count);
		return nativeList;
	}

	public float4 GetLookAhead(NativeList<float3> _path, float3 _currentPos, float _currentNavPos, float[] _lookAheads, float[] _costSet, int _cornerTolerance = 0, bool _isUnderground = false)
	{
		var job = new LookAheadJob(m_navGrid, _path, _currentPos, _currentNavPos, _lookAheads, _costSet, m_navShift, _cornerTolerance, _isUnderground);
		job.Schedule().Complete();
		return job.GetOutput();
	}

	[BurstCompile]
	private struct LookAheadJob : IJob
	{
		[ReadOnly] private NativeArray<byte> m_navGrid;
		[ReadOnly] private NativeList<float3> m_nativePath;
		[ReadOnly] private float3 m_currentPos;
		[ReadOnly] private float m_currentNavPos;
		[ReadOnly] private NativeArray<float> m_lookAheadDists;
		[ReadOnly] private NativeArray<float> m_costSet;
		[ReadOnly] private int m_navShift;
		[ReadOnly] private int m_tolerance;
		private NativeArray<float4> m_output;

		public LookAheadJob(NativeArray<byte> _navGrid, NativeList<float3> _path, float3 _currentPos, float _currentNavPos, float[] _lookAheadDists, float[] _costSet, int _navShift, int _tolerance, bool _isUnderground)
		{
			m_navGrid = _navGrid;
			m_nativePath = _path;

			Vector3 terrainMin = GlobalData.c_terrainMin;
			Vector3 terrainMax = GlobalData.c_terrainMax;
		
			var subNav = GameManager.Me.m_state.m_subSceneState.Current;
			if (_isUnderground && subNav != null)
			{
				terrainMin = subNav.SubNav.m_subNav.m_origin;
				terrainMax = terrainMin + new Vector3(subNav.SubNav.m_subNav.m_width, 0, subNav.SubNav.m_subNav.m_height);
			}

			m_currentPos = ClampToTerrain(_currentPos, terrainMin, terrainMax);
			m_currentNavPos = _currentNavPos;
			m_lookAheadDists = new NativeArray<float>(_lookAheadDists, Allocator.TempJob);
			m_costSet = GetCostsNativeArray(_costSet);
			m_navShift = _navShift;
			m_tolerance = _tolerance;
			m_output = new NativeArray<float4>(1, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		}
		
		public void Execute()
		{
			int nextIndex = (int)m_currentNavPos + 1;
			if (nextIndex >= m_nativePath.Length)
			{
				m_output[0] = new float4(PathPosition(m_nativePath.Length - 1), m_nativePath.Length - 1);
				return;
			}
			
			var prevIndex = m_currentNavPos;
			var prevPos = m_currentPos;
			var potentialTargets = new NativeArray<float>(m_lookAheadDists.Length,Allocator.Temp, NativeArrayOptions.UninitializedMemory);
			int targetsReached = 0;
			float cumDist = 0f;
			for (; nextIndex < m_nativePath.Length; ++nextIndex)
			{
				var nextPos = m_nativePath[nextIndex];
				if (nextPos.y < 0)
					break; // Don't go past start of next strut

				var dist = math.length((nextPos - prevPos).xz);
				cumDist += dist;
				for (int i = targetsReached; i < m_lookAheadDists.Length; ++i)
				{
					var thisLookAhead = m_lookAheadDists[i];
					var over = cumDist - thisLookAhead;
					if (over < 0)
						break;
					potentialTargets[i] = prevIndex + 1 - (over / dist);
					targetsReached++;
				}
				if (targetsReached == m_lookAheadDists.Length)
					break;
				
				if (nextIndex == m_nativePath.Length - 1)
					potentialTargets[targetsReached++] = nextIndex;
				
				prevIndex = nextIndex;
				prevPos = nextPos;
			}
			
			for (int i = targetsReached - 1; i >= 0; --i)
			{
				var destNavPos = potentialTargets[i];
				var destPos = PathPosition(destNavPos);
				if (CheckLineJob.CheckLine(m_currentPos, destPos, m_navShift, m_tolerance, m_costSet, m_navGrid))
					continue;
				m_output[0] = new float4(destPos, destNavPos);
				potentialTargets.Dispose();
				return;
			}
			var minLookAhead = m_currentNavPos + 1;
			m_output[0] = new float4(PathPosition(minLookAhead), minLookAhead);
			potentialTargets.Dispose();
		}

		private float3 PathPosition(float _pos)
		{
			if (_pos < 0)
				return m_nativePath[0];
			if (_pos >= m_nativePath.Length - 1)
				return m_nativePath[^1];
			
			int index = Mathf.FloorToInt(_pos);
			if (m_nativePath[index].y < 0)
				return m_nativePath[index];
			if (m_nativePath[index + 1].y < 0)
				return m_nativePath[index + 1];
			float frac = _pos - index;
			return math.lerp(m_nativePath[index], m_nativePath[index + 1], frac);
		}

		public float4 GetOutput()
		{
			var output = m_output[0];
			m_output.Dispose();
			m_lookAheadDists.Dispose();
			return output;
		}
	}
	
	public static void JitterPath(NativeList<float3> _path, float _cjitter, float _ljitter)
	{
		var job = new JitterPathJob(_path, _cjitter, _ljitter);
		job.Schedule().Complete();
	}

	[BurstCompile]
	public struct JitterPathJob : IJob
	{
		[ReadOnly] private float2 m_cjitter;
		[ReadOnly] private float m_ljitter;
		[ReadOnly] private Random m_random;
		private NativeList<float3> m_path;

		public JitterPathJob(NativeList<float3> _path, float _cjitter, float _ljitter)
		{
			m_path = _path;
			m_cjitter = UnityEngine.Random.insideUnitCircle * _cjitter;
			m_ljitter = _ljitter;
			m_random = Random.CreateFromIndex((uint)UnityEngine.Random.Range(0, uint.MaxValue));
		}

		public void Execute()
		{
			for (int i = 1; i < m_path.Length - 1; ++i)
				m_path[i] += new float3(m_cjitter + m_random.NextFloat2() * m_ljitter, 0f).xzy;
		}
	}
	
	public static List<float3> CopyTenthOfPath(NativeList<float3> _path)
	{
		var job = new ApproxPathJob(_path, 10);
		job.Schedule().Complete();
		return job.GetOutput();
	}
	
	[BurstCompile]
	public struct ApproxPathJob : IJob
	{
		[ReadOnly] private NativeList<float3> m_fullPath;
		[ReadOnly] private int m_newPathLength;
		[ReadOnly] private float m_reductionFactor;
		private NativeArray<float3> m_approxPath;
		public ApproxPathJob(NativeList<float3> _path, float _reductionFactor)
		{
			m_fullPath = _path;
			m_reductionFactor = _reductionFactor;
			
			m_newPathLength = Mathf.CeilToInt(m_fullPath.Length / m_reductionFactor);
			m_approxPath = new NativeArray<float3>(m_newPathLength, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		}

		public void Execute()
		{
			for (int i = 0; i < m_newPathLength - 1; ++i)
				m_approxPath[i] = m_fullPath[Mathf.RoundToInt(i * m_reductionFactor)];
			m_approxPath[^1] = m_fullPath[^1];
		}

		public List<float3> GetOutput()
		{
			var path = new List<float3>(m_newPathLength);
			path.AddRange(m_approxPath.ToArray());
			m_approxPath.Dispose();
			return path;
		}
	}

	public static float GetPointOnPath(NativeList<float3> _path, Vector3 _pos)
	{
		var job = new PointOnPathJob(_path, _pos);
		job.Schedule().Complete();
		return job.GetOutput();
	}

	[BurstCompile]
	public struct PointOnPathJob : IJob
	{
		[ReadOnly] NativeList<float3> m_path;
		[ReadOnly] float3 m_point;
		[ReadOnly] int m_pathCount;

		NativeArray<float> m_output;

		public PointOnPathJob(NativeList<float3> _path, Vector3 _point)
		{
			m_pathCount = _path.Length;
			m_path = _path;
			m_point = _point.GetXZ();

			m_output = new(1, Allocator.TempJob);
		}

		public void Execute()
		{
			if (m_pathCount == 0)
				return;

			float bestDistSq = 1e23f;
			float bestPoint = 0f;

			var targetPoint = m_point.xz;
			var pointI = m_path[0].xz;
			for (int i = 0; i < m_pathCount - 1; ++i)
			{
				var pointIplus1 = m_path[i + 1].xz;
				var fwd = pointIplus1 - pointI;
				var pointIToTarget = targetPoint - pointI;
				float k = math.clamp(math.dot(pointIToTarget, fwd) / math.lengthsq(fwd), 0f, 1f);
				float dSqrd = math.lengthsq(pointIToTarget - fwd * k);
				if (dSqrd < bestDistSq)
				{
					bestPoint = i + k;
					bestDistSq = dSqrd;
				}
				pointI = pointIplus1;
			}

			m_output[0] = bestPoint;
		}

		public float GetOutput()
		{
			float output = m_output[0];

			m_output.Dispose();

			return output;
		}
	}

	public static float3 PredictPosAlongPath(NativeList<float3> _path, int _startIndex, float3 _currentPos, float _distAlongPath)
	{
		var job = new PredictPosJob(_path, _startIndex, _currentPos, _distAlongPath);
		job.Schedule().Complete();
		return job.GetOutput();
	}

	[BurstCompile]
	public struct PredictPosJob : IJob
	{
		[ReadOnly] NativeList<float3> m_path;
		[ReadOnly] int m_pathLength;

		[ReadOnly] int m_startInd;
		
		float3 m_currentPos;
		float m_distLeft;
		NativeArray<float3> m_output;

		public PredictPosJob(NativeList<float3> _path, int _startInd, float3 _currentPos, float _distAhead)
		{
			m_path = _path;
			m_pathLength = _path.Length;

			m_startInd = _startInd;
			
			m_currentPos = _currentPos;
			m_distLeft = _distAhead;
			m_output = new(1, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		}

		public void Execute()
		{
			for (int index = m_startInd; index < m_pathLength; ++index)
			{
				var nextPoint = m_path[index];
				var nextDist = math.length((m_currentPos - nextPoint).xz);
				m_distLeft -= nextDist;
				if (m_distLeft <= 0)
				{
					m_output[0] = math.lerp(m_currentPos, nextPoint, 1 + m_distLeft);
					return;
				}
				m_currentPos = nextPoint;
			}
			m_output[0]  = m_path[^1];
		}

		public float3 GetOutput()
		{
			var output = m_output[0];
			m_output.Dispose();
			return output;
		}
	}


	private static PSysOutline s_previousOutline;
	private static DebugConsole.Command s_toggle = new DebugConsole.Command("outline", _s =>
	{
		if (s_previousOutline != null) s_previousOutline.Stop();
		s_previousOutline = null;
		
		var bits = _s.ToLower().Split(',');
		foreach (var cmd in NGManager.Me.m_NGCommanderList)
		{
			if (cmd.GetType().ToString().ToLower() == bits[0])
			{
				Color c = new Color(.8f, 1f, .8f);
				float size = 1;
				if (bits.Length >= 4)
				{
					c.r = float.Parse(bits[1]);
					c.g = float.Parse(bits[2]);
					c.b = float.Parse(bits[3]);
					if (bits.Length >= 5)
						size = float.Parse(bits[4]);
				}
				s_previousOutline = PSysOutline.Create(cmd.Visuals.gameObject, c, size);
				break;
			}
		}
	});
	
	
	private List<(Transform, Vector3, Quaternion, System.Action)> m_transformChangeTracker = new();

	public void RegisterTransformChangeTracker(Transform _transform, System.Action _callback)
	{
		m_transformChangeTracker.Add((_transform, _transform.position, _transform.rotation, _callback));
	}

	public void CheckTransformChanges()
	{
		for (int i = m_transformChangeTracker.Count - 1; i >= 0; --i)
		{
			var t = m_transformChangeTracker[i];
			if (t.Item1 == null)
			{
				m_transformChangeTracker.RemoveAt(i);
				continue;
			}
			if (t.Item1.position.Approximately(t.Item2) == false || t.Item1.rotation.AlmostEquals(t.Item3) == false)
			{
				t.Item2 = t.Item1.position;
				t.Item3 = t.Item1.rotation;
				m_transformChangeTracker[i] = t;
				t.Item4();
			}
		}
	}

	public float m_directNavBias = 0;
	
	bool m_navDisableMixedCells = false;
	int m_navGenerationPolishStepSize = 40;
	bool m_navSkipPolish = false;
	int m_navQuantiseShift = 3;
	public bool m_navShowQuantise = false;
	static DebugConsole.Command s_navQuant = new("navquant", _s => Me.m_navQuantiseShift = int.Parse(_s));
	static DebugConsole.Command s_showNavQuant = new("shownavquant", _s => Me.m_navShowQuantise = !Me.m_navShowQuantise);
	static DebugConsole.Command s_skipnavpolish = new("skippolish", _s => Me.m_navSkipPolish = !Me.m_navSkipPolish);
	static DebugConsole.Command s_navpolishstepsize = new("polishstep", _s => Me.m_navGenerationPolishStepSize = int.Parse(_s));
	static DebugConsole.Command s_noMixedCellsCmd = new("nomixednav", _s => Utility.SetOrToggle(ref Me.m_navDisableMixedCells, _s), "Ignore mixed cells during navigation", "<bool>");
	

	static DebugConsole.Command s_chrlastnavcmd = new("chrlastnav", _s =>
	{
		var id = int.Parse(_s);
		var chr = NGManager.Me.m_MACharacterList.Find(c => c.m_ID == id);
		var agent = chr.GetComponent<NavAgent>();
		var first = agent.FirstPathPos;
		var last = agent.LastPathPos;
		if (first != null && last != null)
			Debug.LogError($"ID {id} from {first.Value.x:n1},{first.Value.z:n1} to {last.Value.x:n1},{last.Value.z:n1}");
		else
			Debug.LogError($"ID {id} has no current nav path");
	});
	static DebugConsole.Command s_chrposcmd = new("chrpos", _s => {
		var id = int.Parse(_s);
		var chr = NGManager.Me.m_MACharacterList.Find(c => c.m_ID == id);
		Debug.LogError($"ID {id} pos {chr.transform.position.x:n1},{chr.transform.position.z:n1}");
	});
	static DebugConsole.Command s_chrnavcmd = new ("chrnav", _s => {
		var bits = _s.Split(',');
		if (bits.Length == 2)
		{
			var debugs = Resources.Load<TextAsset>("NavDebug").text;
			var lines = debugs.Split('\n');
			var key = bits[1].ToLower() + ":";
			foreach (var line in lines)
			{
				if (line.ToLower().StartsWith(key))
				{
					bits = $"{bits[0]},{line.Substring(key.Length)}".Split(',');
					break;
				}
			}
		}
		if (bits.Length < 5)
		{
			Debug.LogError($"Usage: chrnav=id,fromX,fromZ,toX,toZ or chrnav=id,testName where testName is a line from Assets/Resources/NavDebug.txt");
			return;
		}
		var id = int.Parse(bits[0]);
		var from = new Vector3(floatinv.Parse(bits[1]), 0, floatinv.Parse(bits[2]));
		var to = new Vector3(floatinv.Parse(bits[3]), 0, floatinv.Parse(bits[4]));
		var chr = NGManager.Me.m_MACharacterList.Find(c => c.m_ID == id);
		chr.transform.position = from.GroundPosition(.2f);
		chr.NavigateToPosition(to);
	});
#if UNITY_EDITOR
	void LateUpdate()
	{
		CheckTransformChanges();
		if (m_terrainHeightsUpdated)
		{
			m_terrainHeightsUpdated = false;
			NGDecorationInfoManager.Me.Reseat();
		}
		RunTest(false);
	}

	private static DebugConsole.Command s_testpathcmd = new ("testpath", _s => Me.TestPath(_s));
	private void TestPath(string _s)
	{
		if (m_testFrom == null) m_testFrom = GameObject.CreatePrimitive(PrimitiveType.Sphere);
		if (m_testTo == null) m_testTo = GameObject.CreatePrimitive(PrimitiveType.Sphere);
		var bits = _s.Split(',');
		m_testFrom.transform.position = (new Vector3(float.Parse(bits[0]), 0, float.Parse(bits[1]))).GroundPosition(1);
		m_testTo.transform.position = (new Vector3(float.Parse(bits[2]), 0, float.Parse(bits[3]))).GroundPosition(1);
		if (bits.Length > 4)
		{
			if (bits[4] != "v") m_testUsingThreatCosts = true;
			else m_testUsingVehicleCosts = true;
			if (bits.Length > 5)
			{
				floatinv.TryParse(bits[5], out m_testDestRadius);
			}
		}
	}

	public GameObject m_testFrom, m_testTo;
	public bool m_testUsingThreatCosts = false, m_testUsingVehicleCosts = false;
	public float m_testCorridorSize = 0;
	public Vector3 m_testFromPos, m_testToPos;
	public float m_testDestRadius = 0;
	private float m_testSideBias;
	public List<Vector3> m_testResults;
	public bool m_testRunning = false;
	public int m_testNumber = 0;
	public int m_randomTest = 0;
	private bool m_randomTestInProgress = false;
	public int m_houseTest = 0;
	public bool m_houseTestWithClosedGates = true;
	private bool m_houseTestHasClosedGates = false;
	private bool m_houseTestInProgress = false;
	private int m_houseTestFailures = 0;
	private long m_worstMS = 0;
	private int m_worstFrom, m_worstTo;
	void RunTest(bool _draw = true)
	{
		if (Utility.IsShuttingDown) return;
		if (m_houseTest > 0 && !m_houseTestInProgress)
		{
			if (m_houseTestWithClosedGates && !m_houseTestHasClosedGates)
			{
				m_houseTestHasClosedGates = true;
				foreach (var gate in GameObject.FindObjectsOfType<GateOpener>())
					gate.ForceOpenState(false);
			}
			var commanders = NGManager.Me.m_NGCommanderList;
			int count = commanders.Count;
			int indexA = (m_houseTest - 1) % count;
			int indexB = (m_houseTest - 1) / count;
			if (indexB >= count - 1)
			{
				Debug.LogError($"HousePathCheck complete - errors:{m_houseTestFailures} longest nav {m_worstMS}ms - {m_worstFrom}:{commanders[m_worstFrom].m_linkUID} to {m_worstTo}:{commanders[m_worstTo].m_linkUID}");
				m_houseTest = 0;
				return;
			}
			if (indexB >= indexA) ++indexB;
			m_houseTestInProgress = true;
			var prof = System.Diagnostics.Stopwatch.StartNew();
			FindPathAsync(-1, commanders[indexA].DoorPosInner, commanders[indexB].DoorPosInner, s_pedestrianCosts, false, (_list, _res) =>
			{
				prof.Stop();
				var ms = prof.ElapsedMilliseconds;
				if (ms > m_worstMS)
				{
					m_worstMS = ms;
					m_worstFrom = indexA;
					m_worstTo = indexB;
				}
				m_houseTestInProgress = false;
				if (_list.Length < 2 || math.lengthsq(_list[^1]) < .01f * .01f)
				{
					++m_houseTestFailures;
					Debug.LogError($"HousePathCheck: Error in {ms}ms [{m_houseTest}] generating path from {indexA}:{commanders[indexA].m_linkUID} to {indexB}:{commanders[indexB].m_linkUID} - worst so far {m_worstFrom}:{commanders[m_worstFrom].m_linkUID} to {m_worstTo}:{commanders[m_worstTo].m_linkUID} {m_worstMS}ms");
				}
				else
					Debug.Log($"HousePathCheck: success in {ms}ms [{m_randomTest}] from {indexA}:{commanders[indexA].m_linkUID} to {indexB}:{commanders[indexB].m_linkUID} - worst so far {m_worstFrom}:{commanders[m_worstFrom].m_linkUID} to {m_worstTo}:{commanders[m_worstTo].m_linkUID} {m_worstMS}ms");
			});
			++m_houseTest;
		}
		if (m_randomTest > 0 && !m_randomTestInProgress)
		{
			++m_randomTest;
			var from = new Vector3(
				c_terrainOrigin.x + UnityEngine.Random.Range(.1f, .9f) * c_terrainExtent.x,
				0,
				c_terrainOrigin.z + UnityEngine.Random.Range(.1f, .9f) * c_terrainExtent.z);
			var to = new Vector3(
				c_terrainOrigin.x + UnityEngine.Random.Range(.1f, .9f) * c_terrainExtent.x,
				0,
				c_terrainOrigin.z + UnityEngine.Random.Range(.1f, .9f) * c_terrainExtent.z);
			m_randomTestInProgress = true;
			FindPathAsync(-1, from, to, s_pedestrianCosts, false, (_list, _res) => {
				m_randomTestInProgress = false;
				if (_list.Length < 2 || math.lengthsq(_list[^1]) < .01f * .01f)
					Debug.LogError($"RndPathCheck: Error [{m_randomTest}] generating path from {from} to {to}");
				else
					;//Debug.Log($"RndPathCheck: success [{m_randomTest}] from {from} to {to}");
			});
		}
		if (m_testFrom == null || m_testTo == null) return;
		if ((m_testFromPos - m_testFrom.transform.position).sqrMagnitude > .001f || (m_testToPos - m_testTo.transform.position).sqrMagnitude > .001f || m_sideBias.Nearly(m_testSideBias, .1f) == false)
		{
			if (m_testRunning == false)
			{
				m_testFromPos = m_testFrom.transform.position;
				m_testToPos = m_testTo.transform.position;
				m_testSideBias = m_sideBias;

				/*++m_testNumber;
				if (m_testNumber == 2)
				{
					var t1 = System.DateTime.UtcNow.Ticks;
					for (int i = 0; i < 10; ++i)
					{
						var job = new NavJob(m_testFromPos, m_testToPos, GetCostsNativeArray(s_pedestrianCosts), m_navGrid, m_navW, m_navShift, m_navMask, PathBlock.PathBlockData);
						job.Schedule().Complete();
						job.GetOutput();
					}
					var t2 = System.DateTime.UtcNow.Ticks;
					for (int i = 0; i < 10; ++i)
					{
						FindPath(m_testFromPos, m_testToPos, s_pedestrianCosts, false);
					}
					var t3 = System.DateTime.UtcNow.Ticks;
					Debug.LogError($"job {(t2-t1)/10000} old {(t3-t2)/10000}");
				}
				m_testRunning = true;*/
				
				var prof = System.Diagnostics.Stopwatch.StartNew();
				Vector3 from = m_testFromPos, to = m_testToPos;
				FindPathAsync(-1, m_testFromPos, m_testToPos, m_testUsingThreatCosts ? s_threatCosts : (m_testUsingVehicleCosts ? s_vehicleCosts : s_pedestrianCosts), false, (_s, _res) =>
				{
					prof.Stop();
					Debug.LogError($"FindPath {from} {to} result {_res} took {prof.ElapsedMilliseconds}ms - {_s.Length} steps - {(_s.Length > 0 ? _s[0] : Vector3.zero)} to {(_s.Length > 0 ? _s[^1] : Vector3.zero)} complexity {m_lastNavComplexityQuant},{m_lastNavComplexity}");
					m_testResults = NativePathToList(_s);
					m_testRunning = false;
				}, m_testCorridorSize, _isDebug:true, _destinationRadius: m_testDestRadius);
			}
		}
		if (m_testResults != null && _draw)
		{
			Handles.color = new Color(1, .8f, .8f);
			DrawPathGizmo(m_testResults);
			
			if (m_testResults.Count > 0)
			{
				Gizmos.color = Color.red;
				Gizmos.DrawSphere(m_testResults[0].GroundPosition(.5f), .5f);
				Gizmos.color = Color.blue;
				Gizmos.DrawSphere(m_testToPos.GroundPosition(.5f), .5f);
				Gizmos.color = Color.green;
				Gizmos.DrawSphere(m_testResults[^1].GroundPosition(.5f), .5f);

				if ((m_testResults[^1] - m_testToPos).xzSqrMagnitude() > 1 * 1)
				{
					var hitPoint = RoadManager.Me.m_pathSet.GetClosestPathToPoint(null, true, m_testResults[^1], 10, out var path, out _);
					Gizmos.color = Color.white;
					Gizmos.DrawSphere(hitPoint.GroundPosition(.5f), .5f);
				}
			}
		}
	}

	public static void DrawPathGizmo(List<Vector3> _path, Color? customColour = null)
	{
		DrawPathGizmoAux(_path, Color.black, Color.black, 3.0f, false);
		DrawPathGizmoAux(_path, customColour ?? Color.red, customColour ?? Color.green, 1.5f, true);
	}
	private static void DrawPathGizmoAux(List<Vector3> _path, Color _color1, Color _color2, float _width, bool _drawGizmo)
	{
		if (_path.Count == 0) return;
		var lastPos = _path[0].GroundPosition(.5f);
		Gizmos.matrix = Matrix4x4.identity;
		Handles.matrix = Matrix4x4.identity;
		for (int i = 1; i < _path.Count; ++i)
		{
			Gizmos.color = Handles.color = Color.Lerp(_color1, _color2, (float)i / _path.Count);
			NavStrut lastStrut = null, thisStrut = null;
			if (_path[i].y < 0) thisStrut = NavStrut.GetStrutFromY(_path[i].y);
			if (_path[i - 1].y < 0) lastStrut = NavStrut.GetStrutFromY(_path[i - 1].y);
			Vector3 thisPos;
			if (thisStrut != null)
				thisPos = thisStrut.GetNavPoint(lastStrut);
			else
				thisPos = _path[i].GroundPosition(.5f);
			Handles.DrawLine(lastPos, thisPos, _width);
			if (_drawGizmo) Gizmos.DrawLine(lastPos, thisPos);
			if ((i & 15) == 15)
			{
				var fwd = thisPos - lastPos;
				var side = new Vector3(-fwd.z, fwd.y, fwd.x);
				Handles.DrawLine(lastPos + side, thisPos, _width);
				Handles.DrawLine(lastPos - side, thisPos, _width);
				if (_drawGizmo) Gizmos.DrawLine(lastPos + side, thisPos);
				if (_drawGizmo) Gizmos.DrawLine(lastPos - side, thisPos);
			}

			if (i == _path.Count - 1 && thisStrut != null)
			{
				lastPos = thisPos;
				Vector3 nextPos;
				(thisPos, nextPos) = thisStrut.NavInStrut(lastStrut, _path[i]);
				Handles.DrawLine(lastPos, thisPos, _width);
				if (_drawGizmo) Gizmos.DrawLine(lastPos, thisPos);
				lastPos = thisPos;
				thisPos = nextPos;
				Handles.DrawLine(lastPos, thisPos, _width);
				if (_drawGizmo) Gizmos.DrawLine(lastPos, thisPos);
			}
			lastPos = thisPos;
		}
	}
	
	private static DebugConsole.Command s_dumpNavGridImageCmd = new ("navgridimage", _s => Me.DumpNavGridImage(), "Exports a PNG image of the whole nav grid");

	private void DumpNavGridImage()
	{
		var tex = new Texture2D(m_navW, m_navW);
		var pixels = new Color[m_navW * m_navW];
		for (int y = 0; y < m_navW; ++y)
		{
			for (int x = 0; x < m_navW; ++x)
			{
				var cell = m_navGrid[x + y * m_navW];
				var clr = s_gizmoNavColours[(int)cell];
				pixels[x + y * m_navW] = clr;
			}
		}
		tex.SetPixels(pixels);
		tex.Apply();
		var png = tex.EncodeToPNG();
		System.IO.File.WriteAllBytes("NavGrid.png", png);
	}

	public bool m_showHLGrassDebug = false;
	private static DebugConsole.Command s_showgrassdebug = new ("showhlgrass", _s => Utility.SetOrToggle(ref Me.m_showHLGrassDebug, _s));
	private static DebugConsole.Command s_shownavgrid = new ("shownav", _s => Utility.SetOrToggle(ref Me.m_showNavigationGizmo, _s));
	private static DebugConsole.Command s_showwatergrid = new("showwater", _s => Utility.SetOrToggle(ref Me.m_showBlockerGizmo, _s));
	private static DebugConsole.Command s_showseagrid = new("showsea", _s => Utility.SetOrToggle(ref Me.m_showSeaGizmo, _s));
	public bool m_showNavigationGizmo = false, m_showBlockerGizmo = false, m_showSeaGizmo = false, m_showBedrockGizmo = false;
	private static DebugConsole.Command s_showbedrock = new ("showbedrock", _s => Utility.SetOrToggle(ref Me.m_showBedrockGizmo, _s));
	void OnDrawGizmos() {
		DrawPathTrackerGizmo();
		RunTest();
		if (Utility.IsShuttingDown || (!m_showNavigationGizmo && !m_showBlockerGizmo && !m_showSeaGizmo && !m_showBedrockGizmo && !m_showHLGrassDebug)) return;
		
		if(SceneView.currentDrawingSceneView != null)
		{
			var cam = UnityEditor.SceneView.currentDrawingSceneView.camera;
			RaycastHit hit;
			Physics.Raycast(cam.transform.position, cam.transform.forward, out hit, 1e10f, -1, QueryTriggerInteraction.Ignore);
			var pos = hit.point;

			if (m_showHLGrassDebug)
			{
				var splatPresenceData = CameraRenderSettings.Me.HighLevelGrassPresence;
				var posQ = pos;
				posQ.x = (Mathf.Floor(posQ.x / CameraRenderSettings.c_grassPatchSize) + .5f) * CameraRenderSettings.c_grassPatchSize;
				posQ.z = (Mathf.Floor(posQ.z / CameraRenderSettings.c_grassPatchSize) + .5f) * CameraRenderSettings.c_grassPatchSize;
				var style = new GUIStyle {fontSize = 10, normal = {textColor = Color.white}};
				for (int z = -6; z <= 6; ++z)
				{
					for (int x = -6; x <= 6; ++x)
					{
						var p = posQ;
						p.x += x * CameraRenderSettings.c_grassPatchSize;
						p.z += z * CameraRenderSettings.c_grassPatchSize;
						p = p.GroundPosition();
						int sx = TerrainX(p.x), sz = TerrainZ(p.z);
						sx /= (int) c_terrainXZScale * CameraRenderSettings.c_grassPatchSize;
						sz /= (int) c_terrainXZScale * CameraRenderSettings.c_grassPatchSize;
						if (sx < 0 || sx >= CameraRenderSettings.c_grassPatchCountW || sz < 0 || sz >= CameraRenderSettings.c_grassPatchCountH) continue;
						var splatIndex = sx + sz * CameraRenderSettings.c_grassPatchCountW;
						var splatByte = (int) splatPresenceData[splatIndex];
						var s = "";
						int onLine = 0;
						for (int i = 0; i < 8; ++i)
						{
							if ((splatByte & 1) != 0)
							{
								s += $"{i + 1} ";
								++onLine;
								if (onLine == 3)
								{
									s += "\n";
									onLine = 0;
								}
							}
							splatByte >>= 1;
						}
						if (string.IsNullOrEmpty(s)) s = "<none>";
						Handles.color = Color.white;
						Handles.Label(p, $"[{sx},{sz}]:\n{s}", style);
					}
				}
			}
			if (m_showBedrockGizmo)
			{
				var gp = pos;
				gp.x = Mathf.Floor(gp.x / 4) * 4 + 2;
				gp.z = Mathf.Floor(gp.z / 4) * 4 + 2;
				for (int z = -40; z <= 40; ++z)
				{
					for (int x = -40; x <= 40; ++x)
					{
						var p = gp;
						p.x += x / c_terrainXZScale;
						p.z += z / c_terrainXZScale;
						p = p.GroundPosition();
						var bedrock = CameraRenderSettings.Me.BedrockAtPoint(p);
						Handles.Label(p, $"{bedrock}");
					}
				}
			}
			
			if (m_showBlockerGizmo || m_showSeaGizmo) {
				var gp = pos; gp.x = Mathf.Floor(gp.x / 4) * 4 + 2; gp.z = Mathf.Floor(gp.z / 4) * 4 + 2;
				for (int z = -40; z <= 40; ++z) {
					for (int x = -40; x <= 40; ++x) {
						var p = gp;
						p.x += x / c_terrainXZScale; p.z += z / c_terrainXZScale;
						p = p.GroundPosition();
						if (p.x >= m_gizmoTB.x && p.x <= m_gizmoTB.z && p.z >= m_gizmoTB.y && p.z <= m_gizmoTB.w) {
							Gizmos.color = Color.black; Gizmos.DrawSphere(p, 1.5f);
						}
						if (m_showSeaGizmo)
						{
							var distance = Mathf.Min(1, CameraRenderSettings.Me.LookupCoastDistance(p) / NGManager.Me.m_audioCoastalDistance);
							var clr = distance <= 1 ? Color.green : new Color(0, (1 - distance) * .5f, 0);
							Gizmos.color = new Color(0, distance, 0);
							Gizmos.DrawSphere(p, .5f);
						}
						else
						{
							Gizmos.color = CheckTerrainBlocker(p.x, p.z, p.x, p.z) ? Color.red : Color.blue;
							Gizmos.DrawSphere(p, .3f / c_terrainXZScale);
						}
					}
				}
			}

			if(m_showNavigationGizmo)
			{
				int px = (int)(pos.x / TerrainBlock.GlobalScale), pz = (int)(pos.z / TerrainBlock.GlobalScale);
				
				const int gizmoAreaSize = 45 * 2;
				int scale = m_navQuantiseShift > 0 && m_navShowQuantise ? 1 << m_navQuantiseShift : 1;
				var gizmoSize = new Vector3(.2f * scale, .5f * 2, .2f * scale) * (TerrainBlock.GlobalScale / Mathf.Sqrt(c_terrainXZScale));
				if (m_navQuantiseShift > 0 && m_navShowQuantise)
				{
					px &= ~3;
					pz &= ~3;
					gizmoSize = Vector3.Scale(gizmoSize, new Vector3(2, 1, 2));
				}
				var offs = Vector3.zero;
				var cellCenterOffs = (float) (1 << m_navQuantiseShift) / (2 * c_navScale * c_terrainXZScale);
				for(int z = -gizmoAreaSize; z <= gizmoAreaSize; z++)
				{
					offs.z = (float)z * scale / (float)(c_navScale * c_terrainXZScale) + (float)pz;
					if (m_navQuantiseShift > 0 && m_navShowQuantise) offs.z += cellCenterOffs * 2;
					for(int x = -gizmoAreaSize; x <= gizmoAreaSize; x++)
					{
						offs.x = (float)x * scale / (float)(c_navScale * c_terrainXZScale) + (float)px;
						if (m_navQuantiseShift > 0 && m_navShowQuantise) offs.x += cellCenterOffs * 2;
						
						int v;
						var subNav = GameManager.Me.m_state.m_subSceneState.Current;
						if (subNav != null && subNav.ContainsPoint(offs))
						{
							v = subNav.SubNav.m_subNav.GetNav(offs);
						}
						else
						{
							int fx = TerrainX(offs.x), fz = TerrainZ(offs.z);
							if (fx < 0 || fz < 0 || fx >= NavW || fz >= NavW) continue;
							if (m_navQuantiseShift > 0 && m_navShowQuantise)
								v = m_navGridLowRes[(fx >> m_navQuantiseShift) + ((fz >> m_navQuantiseShift) << (m_navShift - m_navQuantiseShift))];
							else
								v = (int) NavGrid[fx + fz * NavW];
						}

						float s = GetRealHeight(offs * TerrainBlock.GlobalScale);
						offs.y = s / TerrainBlock.GlobalScale;

						Gizmos.color = s_gizmoNavColours[v];
						if(m_testCorridorSize > 0)
						{
							var seg = m_testToPos - m_testFromPos;
							seg.y = 0;
							var segLengthSqrd = seg.sqrMagnitude;
							seg /= segLengthSqrd;
							if(DistanceSqrdFromSegment(segLengthSqrd, seg, m_testFromPos, m_testToPos,
								   offs * TerrainBlock.GlobalScale) > m_testCorridorSize * m_testCorridorSize)
								continue;
						}
						/*if (m_sideBias * m_sideBias > .001f * .001f)
						{
							var m_segDistancePrep = m_testToPos - m_testFromPos;
							m_segDistancePrep.y = 0;
							var m_segLengthSqrd = math.lengthsq(m_segDistancePrep);
							if (m_segLengthSqrd > .1f)
								m_segDistancePrep /= m_segLengthSqrd;
							var m_segSide = math.normalize(new float3(-m_segDistancePrep.z, 0, m_segDistancePrep.x));
							var dist = SignedDistanceFromLine(m_segLengthSqrd, m_segDistancePrep, m_testFromPos, m_testToPos, m_segSide, offs * TerrainBlock.GlobalScale);
							var distFactor = Mathf.Clamp01(dist / (1 + m_sideBias));
							Gizmos.color = Color.Lerp(Color.black, Color.white, distFactor);
							Gizmos.DrawCube(offs * TerrainBlock.GlobalScale, Vector3.Scale(gizmoSize, new Vector3(2, .5f, 2)));
						}*/

						Gizmos.DrawCube(offs * TerrainBlock.GlobalScale, gizmoSize);
						if ((NavCostTypes) v == NavCostTypes.MixedLowResCell && x * x < 10 * 10 && z * z < 10 * 10)
						{
							int subSize = 1 << m_navQuantiseShift;
							int fx = TerrainX(offs.x), fz = TerrainZ(offs.z);
							for (int zz = 0; zz < subSize; ++zz)
							for (int xx = 0; xx < subSize; ++xx)
							{
								var vv = (int) NavGrid[(fx + xx - subSize / 2) + (fz + zz - subSize / 2) * NavW];
								Gizmos.color = s_gizmoNavColours[vv];
								Gizmos.DrawCube(offs * TerrainBlock.GlobalScale + new Vector3((float)(xx * 2 + 1) / scale - cellCenterOffs, 0, (float)(zz * 2 + 1) / scale - cellCenterOffs) + Vector3.up * (gizmoSize.y * .5f), gizmoSize / scale);
							}
						}
					}
				}

				if (fillNavGridFromSlopeJob)
				{
					fillNavGridFromSlopeJob = false;
					float minX = (float)-gizmoAreaSize * (float)scale / (float)(c_navScale * c_terrainXZScale) + (float)px;
					float minZ = (float)-gizmoAreaSize * (float)scale / (float)(c_navScale * c_terrainXZScale) + (float)pz;
					float maxX = (float)gizmoAreaSize * (float)scale / (float)(c_navScale * c_terrainXZScale) + (float)px;
					float maxZ = (float)gizmoAreaSize * (float)scale / (float)(c_navScale * c_terrainXZScale) + (float)pz;
					int iMinX = TerrainX(minX), iMinZ = TerrainZ(minZ);
					int iMaxX = TerrainX(maxX), iMaxZ = TerrainZ(maxZ);
					var job = new GenNavGridOrFillFromSlopeJob(iMinX, iMinZ, iMaxX-iMinX, iMaxZ-iMinZ);
					job.Schedule().Complete();
					RoadManager.Me.m_pathSet.UpdateNav(c_terrainMin, c_terrainMax);
				}
			}
		}
	}
	public bool fillNavGridFromSlopeJob = false;
#endif
}

public class DeedsAndKeys
{
	public string Name;
	public string Rarity;
	public int StartingKeys;
	public int MaxKeys;
	public float CashMultiplier;
	public float GemsMultiplier;
	public float TicketsMultiplier;
}

public class ActiveJob
{
	public JobHandle handle;
	public GlobalData.NavJob job;
	public int callerID;
	public int frame;
	public float startTime;
	public Vector3 from, to;
	public float[] costs;
	public Action<NativeList<float3>, GlobalData.NavJob.EResult> cb;
}
