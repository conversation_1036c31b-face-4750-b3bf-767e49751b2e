using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class HapticExplorer : MonoBehaviour
{
    static DebugConsole.Command s_haptics = new DebugConsole.Command("haptics", _s => Create());

    public Slider m_sliderIntensity;
    public Slider m_sliderDuration;
    public Slider m_sliderSharpness;
    public Slider m_sliderRepeats;
    public Slider m_sliderGap;
    public Slider m_sliderIntensityChange;
    public TMPro.TMP_InputField m_textIntensity;
    public TMPro.TMP_InputField m_textDuration;
    public TMPro.TMP_InputField m_textSharpness;
    public TMPro.TMP_InputField m_textRepeats;
    public TMPro.TMP_InputField m_textGap;
    public TMPro.TMP_InputField m_textIntensityChange;
    public TMPro.TextMeshProUGUI m_exportString;
    public TMPro.TMP_Dropdown m_presetDropdown;

    void Start()
    {
        SliderChange(m_sliderIntensity);
        SliderChange(m_sliderDuration);
        SliderChange(m_sliderSharpness);
        SliderChange(m_sliderRepeats);
        SliderChange(m_sliderGap);
        SliderChange(m_sliderIntensityChange);
        UpdateRepeatUI();
        FillPresetDropdown();
    }

    void FillPresetDropdown()
    {
        var list = new List<TMPro.TMP_Dropdown.OptionData>();
        int maxEnum = 0;
        foreach (HapticInterface.EHapticPreset e in System.Enum.GetValues(typeof(HapticInterface.EHapticPreset)))
            maxEnum = Mathf.Max(maxEnum, (int)e);
        for (int i = 0; i <= maxEnum; ++i)
            list.Add(new TMP_Dropdown.OptionData(((HapticInterface.EHapticPreset)i).ToString()));
        m_presetDropdown.options = list;
    }

    public void OnSetPreset()
    {
        HapticInterface.ReplacePreset((HapticInterface.EHapticPreset)m_presetDropdown.value, m_exportString.text);
    }

    public void OnPlay()
    {
        HapticInterface.PlayHaptic(m_exportString.text);
    }

    public void OnDefaults()
    {
        m_sliderIntensity.value = 0.5f;
        m_sliderDuration.value = 0.01f;
        m_sliderSharpness.value = 1f;
        m_sliderRepeats.value = 1;
        m_sliderGap.value = 0.02f;
        m_sliderIntensityChange.value = 1;
    }

    public void SliderChange(Slider _which)
    {
        TMPro.TMP_InputField field = null;
        if (_which == m_sliderIntensity) field = m_textIntensity;
        else if (_which == m_sliderDuration) field = m_textDuration;
        else if (_which == m_sliderSharpness) field = m_textSharpness;
        else if (_which == m_sliderRepeats) field = m_textRepeats;
        else if (_which == m_sliderGap) field = m_textGap;
        else if (_which == m_sliderIntensityChange) field = m_textIntensityChange;
        if (field == null) return;
        field.SetTextWithoutNotify(_which.value.ToString("0.##"));
        if (_which == m_sliderRepeats)
            UpdateRepeatUI();
        UpdateExportString();
    }
    private void UpdateExportString()
    {
        m_exportString.text = HapticInterface.HapticToString(m_sliderIntensity.value, m_sliderDuration.value, m_sliderSharpness.value, (int)m_sliderRepeats.value, m_sliderGap.value, m_sliderIntensityChange.value);
    }

    public void InputFieldChange(TMPro.TMP_InputField _which)
    {
        Slider field = null;
        if (_which == m_textIntensity) field = m_sliderIntensity;
        else if (_which == m_textDuration) field = m_sliderDuration;
        else if (_which == m_textSharpness) field = m_sliderSharpness;
        else if (_which == m_textRepeats) field = m_sliderRepeats;
        else if (_which == m_textGap) field = m_sliderGap;
        else if (_which == m_textIntensityChange) field = m_sliderIntensityChange;
        if (field == null) return;
        field.SetValueWithoutNotify(float.Parse(_which.text));
        if (_which == m_textRepeats)
            UpdateRepeatUI();
        UpdateExportString();
    }

    private void UpdateRepeatUI()
    {
        m_sliderGap.transform.parent.gameObject.SetActive(m_sliderRepeats.value > 1);
        m_sliderIntensityChange.transform.parent.gameObject.SetActive(m_sliderRepeats.value > 1);
    }

    public void OnClose()
    {
        Destroy(gameObject);
    }

    public static void Create()
    {
        Instantiate(GlobalData.Me.m_hapticExplorerPrefab, GameManager.Me.CurrentCanvas);
    }
}
