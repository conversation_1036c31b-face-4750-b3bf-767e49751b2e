using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SafeRegionAdjust : MonoBehaviour {
	public float m_adjustTopIndent = 0, m_adjustBottomIndent = 0, m_adjustLeftIndent = 0, m_adjustRightIndent = 0;
	private GUICanvasResizer m_resizer;
	private RectTransform m_rectTransform;
	private Vector2 m_offsetApplied = Vector2.zero;
	void Start() {
		m_resizer = GetComponentInParent<GUICanvasResizer>();
        m_rectTransform = transform as RectTransform;
	}

	void Update() {
		if(m_resizer != null)
			Apply();
	}

	void Apply() {
		var offs = m_resizer.CalculateRelaxation(m_adjustLeftIndent, m_adjustRightIndent, m_adjustTopIndent, m_adjustBottomIndent);
		if ((m_offsetApplied - offs).sqrMagnitude > 0.01f * 0.01f) {
			var delta = offs - m_offsetApplied;
			m_rectTransform.anchoredPosition += delta;
			m_offsetApplied = offs;
		}
	}
}
