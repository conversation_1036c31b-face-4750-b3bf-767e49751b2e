using System.Collections.Generic;
using UnityEngine;

public class NavTestManager : MonoSingleton<NavTestManager>
{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
    private bool m_testStarted = false;
    private bool m_testInProgress = false;

    public enum ECharacterType
    {
        <PERSON>,
        Zombie,
        Hero
    };
    public enum ENavType
    {
        Worker,
        Threat,
    }

    private int m_testEntrances = 0;
    private List<(ECharacterType, int, int, float, ENavType)> m_tests = new ();
    private List<(MACharacterBase, MABuilding, int)> m_testCharacters = new();
    private float m_timeOut = 0, m_testStartTime = 0;

    private static DebugConsole.Command s_startTestCmd = new ("navtest", _s => Me.StartTest(_s), "Start a navigation test", "<string>");
    
    public enum ELogType { Info, Error, Finish, Fail }

    private string m_log = "";
    
    private void Log(ELogType _type, string _s)
    {
        Debug.LogError(_s);
        
        m_log += $"{_s}\n";
        if (_type == ELogType.Finish)
            System.IO.File.WriteAllText($"{Application.persistentDataPath}/navTestSuccess.txt", m_log);
        else if (_type == ELogType.Fail)
            System.IO.File.WriteAllText($"{Application.persistentDataPath}/navTestFail.txt", m_log);
    }

    void Start()
    {
        string[] args = System.Environment.GetCommandLineArgs();
        const string c_navTestCmd = "navtest=";
        foreach (var s in args)
        {
            if (s.StartsWith(c_navTestCmd))
            {
                StartTest(s.Substring(c_navTestCmd.Length));
                break;
            }
        }
    }

    public void StartTest(string _name)
    {
        var data = Resources.Load<TextAsset>($"NavTest/{_name}")?.text;
        if (data == null)
        {
            Log(ELogType.Error, $"NavTest: Unable to open nav test NavTest/{_name}");
            return;
        }
        var saveResource = "seedmoa.bytes";
        int timeout = 120;
            
        var lines = data.Split('\n');
        for (int i = 0; i < lines.Length; ++i)
        {
            var line = lines[i].Trim();
            if (line.StartsWith("#")) continue;
            if (string.IsNullOrEmpty(line)) continue;
            var bits = line.Split('=');
            switch (bits[0].ToLower())
            {
                case "load":
                    saveResource = bits[1];
                    break;
                case "timeout":
                    if (int.TryParse(bits[1], out var t))
                        timeout = t;
                    break;
                case "entrances":
                    m_testEntrances = 1;
                    break;
                case "allentrances":
                    m_testEntrances = 2;
                    break;
                case "test":
                    var bits2 = bits[1].Split('@');
                    if (bits2.Length < 3)
                    {
                        Log(ELogType.Error, $"NavTest: Invalid test command in NavTest/{_name} line {i}: {line}");
                        break;
                    }
                    // Worker@BuildingIDFrom@BuildingID2@Speed@NavType
                    if (System.Enum.TryParse<ECharacterType>(bits2[0], true, out var type) && int.TryParse(bits2[1], out var fromID) && int.TryParse(bits2[2], out var toID))
                    {
                        float speed = 1;
                        ENavType navType = ENavType.Worker;
                        if (bits.Length > 3)
                        {
                            if (float.TryParse(bits2[3], out var s)) speed = s;
                            if (bits.Length > 4)
                            {
                                
                            }
                        }
                        m_tests.Add((type, fromID, toID, speed, navType));
                    }
                    else
                        Log(ELogType.Error, $"NavTest: Error parsing test command in NavTest/{_name} line {i}: {line}");
                    break;
            }
        }
        
        m_testStarted = true;
        m_testInProgress = false;
        m_timeOut = timeout;
        Log(ELogType.Info, $"NavTest: Running nav test {saveResource} with {m_tests.Count} test{(m_tests.Count == 1 ? "" : "s")}");
        GameManager.Me.CopySaveFromResourceAndLoad(saveResource, 3);
    }

    void Update()
    {
        if (m_testStarted == false) return;
        if (m_testInProgress == false && GameManager.Me.LoadComplete)
        {
            m_testInProgress = true;
            SetInitialConditions();
        }
        UpdateTest();
    }
    
    static Vector3 s_centerOfTown = new Vector3(-60, 103, 75);
    List<int> m_fails = new();
    int m_buildingExitTests = 0, m_buildingExitCompletes = 0;
    void SetInitialConditions()
    {
        DebugConsole.Me.ExecuteConsole("navqueue", true);

        m_testStartTime = Time.realtimeSinceStartup; 
        for (int i = 0; i < m_tests.Count; ++i)
        {
            var (type, fromID, toID, speed, navType) = m_tests[i];
            
            var buildingFrom = NGManager.Me.FindBuildingByID(fromID);
            var buildingTo = NGManager.Me.FindBuildingByID(toID);
            
            switch (type)
            {
                case ECharacterType.Worker:
                    var worker = MAWorker.Create(MAWorkerInfo.WorkerTypeEnum.Worker, buildingFrom.DoorPosInner);
                    buildingFrom.AddWorkerToWorkPermanently(worker, false);
                    worker.m_insideMABuilding = buildingFrom;
                    worker.SetMoveToBuilding(buildingTo);
                    m_testCharacters.Add((worker, buildingTo, i));
                    break;
                case ECharacterType.Zombie:
                    break;
                case ECharacterType.Hero:
                    break;
            }
        }
        
        m_fails.Clear();
        m_buildingExitTests = 0;
        m_buildingExitCompletes = 0;

        void TestBuilding(NGCommanderBase _building)
        {
            ++m_buildingExitTests;
            var id = _building.GetInstanceID() ^ 0x18273645;
            var inner = _building.DoorPosInner;
            //var outer = building.DoorPosOuter; outer += (outer - inner) * .5f;
            var outer = s_centerOfTown;
            GlobalData.Me.FindPathAsync(id, inner, outer, GlobalData.s_pedestrianCosts, false, (_l, _res) =>
            {
                if (_l.Length <= 1)
                    m_fails.Add(_building.m_linkUID);
                ++m_buildingExitCompletes;
                if (m_buildingExitCompletes == m_buildingExitTests)
                    Log(ELogType.Info, $"NavTest: tested {m_buildingExitTests} building exits, {m_fails.Count} failed: {string.Join(" ", m_fails)}");
            });
        }
        
        if (m_testEntrances >= 1)
            foreach (var building in NGManager.Me.m_NGCommanderList) TestBuilding(building);
        if (m_testEntrances >= 2)
            foreach (var building in NGManager.Me.m_NGCommanderListOutOfRange) TestBuilding(building);
    }

    string DumpTests()
    {
        string s = "";
        for (int i = 0; i < m_testCharacters.Count; ++i)
        {
            var (character, building, index) = m_testCharacters[i];
            var (type, fromID, toID, speed, navType) = m_tests[index];
            s += $"{type} from {fromID} to {toID} [speed {speed} navType {navType}] failed\n";
        }
        return s;
    }
    
    void UpdateTest()
    {
        if (m_testInProgress == false) return;
        if (Time.realtimeSinceStartup > m_testStartTime + m_timeOut)
        {
            Log(ELogType.Fail, $"NavTest: Test failed, timed out after {m_timeOut} seconds with {m_testCharacters.Count} remaining test{(m_testCharacters.Count == 1 ? "" : "s")}\n{DumpTests()}");
            EndTest(false);
            return;
        }
        for (int i = m_testCharacters.Count - 1; i >= 0; --i)
        {
            var (character, building, index) = m_testCharacters[i];
            if (character.m_insideMABuilding == building && character.m_nav.IsNavigating == false)
            {
                Log(ELogType.Info, $"NavTest: Character {index} [{character.m_ID}] reached destination {building.DoorPosInner} ({character.transform.position}) in building [{building.m_linkUID}] {building.name}");
                m_testCharacters.RemoveAt(i);
            }
        }
        if (m_testCharacters.Count == 0)
        {
            Log(ELogType.Finish, $"NavTest: Test complete, all characters have reached their destination in {Time.realtimeSinceStartup - m_testStartTime} seconds");
            EndTest(true);
        }
    }

    void EndTest(bool _didComplete)
    {
        GameManager.Quit(true);
    }
#endif
}
