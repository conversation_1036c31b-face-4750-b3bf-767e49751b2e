using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(PerformanceManager))]
public class PerformanceManagerEditor : Editor
{
    private bool m_showPhysicsInfo = false;
    
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        var pm = target as PerformanceManager;
        if (pm == null) return;

        GUILayout.BeginHorizontal();
        if (GUILayout.But<PERSON>(pm.IsLayerProfileRunning ? "Stop Profile" : "Profile Layers", GUILayout.Width(120)))
        {
            if (pm.IsLayerProfileRunning) pm.StopLayerProfile();
            else pm.StartLayerProfile();
        }
        if (GUILayout.Button("Midday")) DayNight.Me.SetTimeOfDayFraction(0.5f, true);
        if (GUILayout.Button("Midnight")) DayNight.Me.SetTimeOfDayFraction(0.0f, true);
        if (GUILayout.But<PERSON>("Capture")) GameManager.Me.StartFPSCapture();
        GUILayout.EndHorizontal();
        GUILayout.BeginHorizontal();
        var newHDR = GUILayout.Toggle(HDROutputSettings.main.active, "HDR");
        if (newHDR != HDROutputSettings.main.active) HDROutputSettings.main.RequestHDRModeChange(newHDR);
        QualitySettings.maxQueuedFrames = EditorGUILayout.DelayedIntField("Latency", QualitySettings.maxQueuedFrames);
        GUILayout.EndHorizontal();
        GUILayout.Space(6);

        GUI.enabled = pm.IsLayerProfileRunning == false;
        for (int i = 0; i < pm.m_layers.Length; ++i)
        {
            var layer = pm.m_layers[i];
            if (pm.IsLayerProfileRunning && (i == pm.LayerProfileIndex || pm.LayerProfileIndex == -1)) GUI.color = Color.yellow;
            else GUI.color = Color.white;
            GUILayout.Space(layer.Spacing);
            GUILayout.BeginHorizontal();
            GUILayout.Label("", GUILayout.Width(layer.Indent));
            var enabled = GUILayout.Toggle(layer.m_isEnabled, layer.m_name);
            if (enabled != layer.m_isEnabled) layer.Enable(enabled);
            GUILayout.EndHorizontal();
        }
        GUI.color = Color.white;
        GUILayout.Space(6);
        if (GameManager.Me != null)
            GameManager.Me.ProfileMode = GUILayout.Toggle(GameManager.Me.ProfileMode, "Profile Mode");
        GUI.enabled = true;
        
        m_showPhysicsInfo = EditorGUILayout.Foldout(m_showPhysicsInfo, "Physics Info");
        if (m_showPhysicsInfo)
        {
            GUIStyle style = new GUIStyle(GUI.skin.label);
            style.richText = true;

            if (m_rigidBodies == null)
                m_rigidBodies = Resources.FindObjectsOfTypeAll<Rigidbody>();
            foreach (var rb in m_rigidBodies)
            {
                if (rb.gameObject.scene.name == null) continue;
                if (rb != null && rb.gameObject.active && rb.IsSleeping() == false && rb.isKinematic == false && rb.detectCollisions)
                    if (GUILayout.Button($"{rb.name}", style))
                        Selection.activeGameObject = rb.gameObject;
            }
        }
        else
            m_rigidBodies = null;
    }
    Rigidbody[] m_rigidBodies;
}
#endif

public class PerformanceManager : MonoSingleton<PerformanceManager>
{
    public class PerformanceLayerBase
    {
        public string m_name = "";
        public bool m_isEnabled = true;
        public virtual float Indent => 20;
        public virtual float Spacing => 0;
        public virtual bool IsSeparator => false;
        public virtual bool IsAll => false;

        public void Enable(bool _enable)
        {
            if (m_isEnabled != _enable)
            {
                DoEnable(_enable);
                m_isEnabled = _enable;
            }
        }

        protected virtual void DoEnable(bool _enable) {}
    }

    public class PerformanceLayerSeparator : PerformanceLayerBase
    {
        public override float Indent => 10;
        public override float Spacing => 6;
        public override bool IsSeparator => true;

        protected override void DoEnable(bool _enable)
        {
            var layers = Me.m_layers;
            var thisIndex = System.Array.IndexOf(layers, this);
            for (int i = thisIndex + 1; i < layers.Length; ++i)
            {
                if (layers[i].IsSeparator) break;
                layers[i].Enable(_enable);
            }
        }
    }
    
    public class PerformanceLayerAll : PerformanceLayerBase
    {
        public override float Indent => 0;
        public override bool IsAll => true;

        protected override void DoEnable(bool _enable)
        {
            for (int i = 1; i < Me.m_layers.Length; ++i) Me.m_layers[i].Enable(_enable);
        }
    }
    
    public class PerformanceLayerRenderers : PerformanceLayerBase
    {
        public string m_path;
        private Dictionary<Renderer, bool> m_renderers = new ();

        protected override void DoEnable(bool _enable)
        {
            if (_enable == false)
            {
                m_renderers.Clear();
                var root = GameObject.Find(m_path);
                foreach (var rnd in root.GetComponentsInChildren<Renderer>())
                {
                    m_renderers[rnd] = rnd.enabled;
                    rnd.enabled = false;
                }
            }
            else
            {
                foreach (var kvp in m_renderers)
                    if (kvp.Key != null)
                        kvp.Key.enabled = kvp.Value;
            }
        }
    }

    public class PerformanceLayerRendererShadows : PerformanceLayerBase
    {
        public string m_path;
        public float m_minimumSizeToDisable = 0;
        private Dictionary<Renderer, UnityEngine.Rendering.ShadowCastingMode> m_renderers = new ();

        protected override void DoEnable(bool _enable)
        {
            if (_enable == false)
            {
                m_renderers.Clear();
                var root = GameObject.Find(m_path);
                foreach (var rnd in root.GetComponentsInChildren<Renderer>())
                {
                    if (m_minimumSizeToDisable > 0)
                    {
                        var bounds = rnd.bounds;
                        var max = Mathf.Max(bounds.size.x, bounds.size.y, bounds.size.z);
                        var min = Mathf.Min(bounds.size.x, bounds.size.y, bounds.size.z);
                        var mid = bounds.size.x + bounds.size.y + bounds.size.z - max - min;
                        var biggestArea = max * mid;
                        if (biggestArea > m_minimumSizeToDisable * m_minimumSizeToDisable) continue;
                    }
                    m_renderers[rnd] = rnd.shadowCastingMode;
                    rnd.shadowCastingMode = ShadowCastingMode.Off;
                }
            }
            else
            {
                foreach (var kvp in m_renderers)
                    if (kvp.Key != null)
                        kvp.Key.shadowCastingMode = kvp.Value;
            }
        }
    }
    
    public class PerformanceLayerComponent<T> : PerformanceLayerBase where T : MonoBehaviour
    {
        public string m_path;
        private Dictionary<T, bool> m_renderers = new ();
        protected override void DoEnable(bool _enable)
        {
            if (_enable == false)
            {
                m_renderers.Clear();
                var cmps = m_path != null ? GameObject.Find(m_path).GetComponentsInChildren<T>() : Resources.FindObjectsOfTypeAll<T>(); 
                foreach (var cmp in cmps)
                {
                    if (cmp.gameObject.scene.name == null) continue;
                    m_renderers[cmp] = cmp.enabled;
                    cmp.enabled = false;
                }
            }
            else
            {
                foreach (var kvp in m_renderers) if (kvp.Key != null) kvp.Key.enabled = kvp.Value;
            }
        }
    }

    public class PerformanceLayerDebugConsole : PerformanceLayerBase
    {
        public string m_command;
        protected override void DoEnable(bool _enable)
        {
            DebugConsole.Me.ExecuteConsole($"{m_command}={_enable}", true);
        }
    }

    public class PerformanceLayerStatic : PerformanceLayerBase
    {
        public System.Action<bool> m_command;
        protected override void DoEnable(bool _enable)
        {
            m_command(_enable);
        }
    }

    public class PerformanceLayerRigidbody : PerformanceLayerBase
    {
        public string m_path;
        private Dictionary<Rigidbody, (bool, bool)> m_states = new();
        
        protected override void DoEnable(bool _enable)
        {
            if (_enable == false)
            {
                m_states.Clear();
                var cmps = m_path != null ? GameObject.Find(m_path).GetComponentsInChildren<Rigidbody>() : Resources.FindObjectsOfTypeAll<Rigidbody>();
                foreach (var cmp in cmps)
                {
                    if (cmp.gameObject.scene.name == null) continue;
                    m_states[cmp] = (cmp.detectCollisions, cmp.isKinematic);
                    cmp.isKinematic = true;
                    cmp.detectCollisions = false;
                }
            }
            else
            {
                foreach (var kvp in m_states)
                    if (kvp.Key != null)
                        (kvp.Key.detectCollisions, kvp.Key.isKinematic) = kvp.Value;
            }
        }
    }

    const string c_miscUI = "UI";
    const string c_miscGrass = "Grass";
    const string c_miscTrees = "Trees";
    const string c_miscTerrain = "Terrain";
    const string c_miscFog = "Fog";
    const string c_miscDOF = "DoF";
    const string c_miscSSAO = "SSAO";
    const string c_miscLGG = "LGG";
    const string c_miscVisualEnvironment = "VisEnv";
    const string c_miscSky = "Sky";
    const string c_miscColourAdjust = "ClrAdj";
    const string c_miscExposure = "Exposure";
    const string c_miscClouds = "Clouds";
    const string c_miscFogPP = "FogPP";
    const string c_miscShadows = "Shadows";
    const string c_miscBloom = "Bloom";
    const string c_miscSMH = "SMH";
    const string c_physUpdateType = "PhysUpdate";
    const string c_physCallbacksType = "PhysCallbacks";
    const string c_miscPointShadows = "PointShadows";
    public class PerformanceLayerMisc : PerformanceLayerBase
    {
        private Dictionary<Component, bool> m_componentStateBackup = new ();
        public string m_type;
        protected override void DoEnable(bool _enable)
        {
            switch (m_type)
            {
                case c_miscTerrain: Terrain.activeTerrain.drawHeightmap = _enable; break;
                case c_miscGrass: TerrainPopulation.ShowGrass = _enable; break;
                case c_miscFog: TerrainPopulation.Me.m_newFogSystem = _enable; break;
                case c_miscTrees: Terrain.activeTerrain.drawTreesAndFoliage = _enable; break;
                case c_miscUI: UIManager.Me.InhibitUI(!_enable); break;
                
                case c_physUpdateType: Physics.simulationMode = _enable ? SimulationMode.FixedUpdate : SimulationMode.Update; break;
                case c_physCallbacksType: Physics.invokeCollisionCallbacks = _enable; break;
                
                case c_miscDOF: DepthOfFieldController.Me.m_disableDOF = !_enable; break;
                case c_miscSSAO: GameSettings.SRPOptions.HDSSAO.active = _enable; break;
                case c_miscLGG: GameSettings.SRPOptions.HDLiftGammaGain.active = _enable; break;
                case c_miscVisualEnvironment: GameSettings.SRPOptions.HDVisualEnvironment.active = _enable; break;
                case c_miscSky: GameSettings.SRPOptions.HDGradientSky.active = _enable; break;
                case c_miscColourAdjust: GameSettings.SRPOptions.HDColorAdjustments.active = _enable; break;
                case c_miscExposure: GameSettings.SRPOptions.HDExposure.active = _enable; break;
                case c_miscClouds: GameSettings.SRPOptions.HDCloudLayer.active = _enable; break;
                case c_miscFogPP: GameSettings.SRPOptions.HDFog.active = _enable; break;
                case c_miscShadows: GameSettings.SRPOptions.HDShadows.cascadeShadowSplitCount.Override(_enable ? 4 : 1); break;// active = _enable; break;
                case c_miscBloom: GameSettings.SRPOptions.HDBloom.active = _enable; break;
                case c_miscSMH: GameSettings.SRPOptions.HDShadowsMidtonesHighlights.active = _enable; break;
                
                case c_miscPointShadows:
                    if (_enable == false)
                    {
                        m_componentStateBackup.Clear();
                        foreach (var light in Resources.FindObjectsOfTypeAll<Light>())
                        {
                            if (light.gameObject.scene.name == null) continue;
                            if (light.type == LightType.Point && light.shadows != LightShadows.None)
                            {
                                m_componentStateBackup[light] = light.shadows == LightShadows.Soft;
                                light.shadows = LightShadows.None;
                            }
                        }
                    }
                    else
                    {
                        foreach (var kvp in m_componentStateBackup)
                            if (kvp.Key is Light light)
                                light.shadows = kvp.Value ? LightShadows.Soft : LightShadows.Hard;
                        m_componentStateBackup.Clear();
                    }
                    break;
            }
        }
    }

    public PerformanceLayerBase[] m_layers =
    {
        new PerformanceLayerAll {m_name = "All"},
        
        new PerformanceLayerSeparator {m_name = "Renderers"},
        new PerformanceLayerRenderers {m_name = "Buildings", m_path = "/Town/BuildingHolder"},
        new PerformanceLayerRendererShadows {m_name = "Building Shadows", m_path = "/Town/BuildingHolder"},
        //new PerformanceLayerRendererShadows {m_name = "Small Building Shadows", m_path = "/Town/BuildingHolder", m_minimumSizeToDisable = 2},
        new PerformanceLayerRenderers {m_name = "Decorations", m_path = "/Town/DecorationHolder"},
        new PerformanceLayerRenderers {m_name = "Characters", m_path = "/Town/CharacterHolder"},
        new PerformanceLayerRenderers {m_name = "Wild Blocks", m_path = "/WildBlocks"},
        new PerformanceLayerRenderers {m_name = "Quest Items", m_path = "/Town/QuestSpawnHolder"},
        new PerformanceLayerComponent<UnityEngine.Rendering.HighDefinition.DecalProjector> {m_name = "Decals", m_path = null},
        
        new PerformanceLayerSeparator {m_name = "Landscape"},
        new PerformanceLayerMisc {m_name = "Terrain", m_type = c_miscTerrain},
        new PerformanceLayerRenderers {m_name = "Roads", m_path = "/PathHolder"},
        new PerformanceLayerRenderers {m_name = "Sea", m_path = "/Town/MoATerrain/Sea"},
        new PerformanceLayerRenderers {m_name = "MoAVisuals", m_path = "/Town/MoAVisuals"},
        new PerformanceLayerMisc {m_name = "Trees", m_type = c_miscTrees},
        new PerformanceLayerRenderers {m_name = "Cut Trees", m_path = "/Town/Managers/MoATerrainPopulation"},
        new PerformanceLayerMisc {m_name = "Grass", m_type = c_miscGrass},
        new PerformanceLayerMisc {m_name = "Fog", m_type = c_miscFog},
        
        new PerformanceLayerSeparator {m_name = "Post-processes"},
        new PerformanceLayerMisc {m_name = "Depth of Field", m_type = c_miscDOF},
        new PerformanceLayerMisc {m_name = "SSAO", m_type = c_miscSSAO},
        new PerformanceLayerMisc {m_name = "Lift/Gamma/Gain", m_type = c_miscLGG},
        new PerformanceLayerMisc {m_name = "Visual Environment", m_type = c_miscVisualEnvironment},
        new PerformanceLayerMisc {m_name = "Gradient Sky", m_type = c_miscSky},
        new PerformanceLayerMisc {m_name = "Colour Adjustments", m_type = c_miscColourAdjust},
        new PerformanceLayerMisc {m_name = "Exposure", m_type = c_miscExposure},
        new PerformanceLayerMisc {m_name = "Cloud Layer", m_type = c_miscClouds},
        new PerformanceLayerMisc {m_name = "FogPP", m_type = c_miscFogPP},
        new PerformanceLayerMisc {m_name = "Shadow Cascades", m_type = c_miscShadows},
        new PerformanceLayerMisc {m_name = "Bloom", m_type = c_miscBloom},
        new PerformanceLayerMisc {m_name = "Shadows/Midtones/Highlights", m_type = c_miscSMH},

        new PerformanceLayerSeparator {m_name = "Physics"},
        new PerformanceLayerStatic {m_name = "RagdollRoot", m_command = (b) => RagdollController.EnableRagdollRoot(b)},
        new PerformanceLayerMisc {m_name = "FixedUpdate", m_type = c_physUpdateType},
        new PerformanceLayerMisc {m_name = "Physics Callbacks", m_type = c_physCallbacksType},
        new PerformanceLayerRigidbody {m_name = "Decoration RBs", m_path = "/Town/DecorationHolder"},
        new PerformanceLayerRigidbody {m_name = "Character RBs", m_path = "/Town/CharacterHolder"},
        new PerformanceLayerRigidbody {m_name = "Ragdoll RBs", m_path = "/Town/CharacterHolder/RagdollsRoot"},
        new PerformanceLayerRigidbody {m_name = "Wild Block RBs", m_path = "/WildBlocks"},
        new PerformanceLayerRigidbody {m_name = "Quest RBs", m_path = "/Town/QuestSpawnHolder"},

        new PerformanceLayerSeparator {m_name = "Other"},
        new PerformanceLayerMisc {m_name = "UI", m_type = c_miscUI},
        new PerformanceLayerDebugConsole {m_name = "Card Holders", m_command = "cardholders"},
        new PerformanceLayerStatic {m_name = "Quick Tips", m_command = (b) => MAQuickTipGUI.HideAll(!b)},
        new PerformanceLayerRenderers {m_name = "Hand&Related", m_path = "/Town/Managers/NGManagers/PlayerHandManager"},
        new PerformanceLayerMisc {m_name = "Point Shadows", m_type = c_miscPointShadows},
    };

	private static DebugConsole.Command s_setlatencycmd = new ("latency", _s => QualitySettings.maxQueuedFrames = int.Parse(_s));
	private static DebugConsole.Command s_setHDRcmd = new ("sethdr", _s => HDROutputSettings.main.RequestHDRModeChange(Utility.SetOrToggle(HDROutputSettings.main.active, _s)));
    
    private static DebugConsole.Command s_profileLayersCmd = new ("profilelayers", _s => Me.StartLayerProfile());
    private static DebugConsole.Command s_togglePerfLayerCmd = new ("perflayer", _s => Me.ToggleLayer(_s));

    private static DebugConsole.Command s_dumpMeshColliderStats = new("meshcolliderstats", _s => LogPhysicsMeshStats());
    public static void LogPhysicsMeshStats()
    {
        var meshColliders = FindObjectsOfType<MeshCollider>();
        int totalMeshColliders = meshColliders.Length, totalStatic = 0, totalConvex = 0, totalOther = 0, totalConvexRB = 0;
        int totalOtherVertices = 0, totalOtherTriangles = 0, totalStaticVertices = 0, totalStaticTriangles = 0, totalConvexVertices = 0, totalConvexTriangles = 0, totalConvexRBVertices = 0, totalConvexRBTriangles = 0;
        int totalConvexNonRB = 0;
        
        foreach (var meshCollider in meshColliders)
        {
            if (meshCollider.enabled == false) continue;
            var sm = meshCollider.sharedMesh;
            if (sm != null)
            {
                int vertCount = sm.vertexCount;
                int triCount = 0;
                for (int i = 0; i < sm.subMeshCount; ++i)
                    triCount += (int)sm.GetIndexCount(i) / 3;
                var hasRB = meshCollider.gameObject.GetComponentInParent<Rigidbody>() != null;
                if (meshCollider.gameObject.isStatic)
                {
                    if (hasRB) Debug.LogError($"Static mesh collider with rigidbody: {meshCollider.gameObject.name}", meshCollider.gameObject);
                    totalStaticVertices += vertCount;
                    totalStaticTriangles += triCount;
                    ++totalStatic;
                }
                else if (meshCollider.convex)
                {
                    if (hasRB == false)
                    {
                        if (totalConvexNonRB++ < 20)
                            Debug.LogError($"Non-static convex mesh collider without rigidbody: {meshCollider.gameObject.name}", meshCollider.gameObject);
                        totalConvexVertices += vertCount;
                        totalConvexTriangles += triCount;
                        ++totalConvex;
                    }
                    else
                    {
                        totalConvexRBVertices += vertCount;
                        totalConvexRBTriangles += triCount;
                        ++totalConvexRB;
                    }
                }
                else
                {
                    Debug.LogError($"Mesh collider with neither Static nor Convex: {meshCollider.gameObject.name}", meshCollider.gameObject);
                    totalOtherVertices += vertCount;
                    totalOtherTriangles += triCount;
                    ++totalOther;
                }
            }
        }
        Debug.LogError($"Total Mesh Colliders: {totalMeshColliders} (Non-static convex mesh colliders:{totalConvexNonRB}) Clls/Verts/Tris/AvgV\n Static: [{totalStatic}/{totalStaticVertices}/{totalStaticTriangles}/{totalStaticVertices/totalStatic}]\n Convex: [{totalConvex}/{totalConvexVertices}/{totalConvexTriangles}/{totalConvexVertices/totalConvex}]\n ConvexRB: [{totalConvexRB}/{totalConvexRBVertices}/{totalConvexRBTriangles}/{totalConvexRBVertices/totalConvexRB}]\n Other: [{totalOther}/{totalOtherVertices}/{totalOtherTriangles}/{totalOtherVertices/totalOther}]");
    }

    private List<string> m_deferredToggles = new ();
    private void ToggleLayer(string _s)
    {
        if (GameManager.Me.LoadComplete == false)
        {
            Debug.LogError($"ToggleLayer({_s}) will apply after loading");
            m_deferredToggles.Add(_s);
            return;
        }
        foreach (var layer in m_layers) if (layer.m_name == _s) layer.Enable(!layer.m_isEnabled);
        Debug.LogError($"ToggleLayer({_s}) applied");
    }

    public void StartLayerProfile()
    {
        m_profileLayerIndex = 1;
        m_layerProfileResults = "";
    }

    public void StopLayerProfile()
    {
        m_profileLayerIndex = 0;
        GameManager.s_fpsExtra = m_layerProfileResults;
    }

    public bool IsLayerProfileRunning => m_profileLayerIndex != 0;
    public int LayerProfileIndex => m_profileLayerIndex / c_profileLayersFramesPerIndex - 1;
    
    private string m_layerProfileResults = "";
    
    public string GetDisabledLayers()
    {
        if (m_layers[0].m_isEnabled == false) return "All ";
        string s = "";
        for (int i = 1; i < m_layers.Length; ++i)
        {
            if (m_layers[i].m_isEnabled == false)
            {
                s += $"{m_layers[i].m_name} ";
                if (m_layers[i].IsSeparator)
                {
                    for (++i; i < m_layers.Length; ++i)
                        if (m_layers[i].IsSeparator)
                            break;
                    --i;
                }
            }
        }
        if (s.Length == 0) return "None ";
        return s;
    }

    private int m_profileLayerIndex = 0;
    private float m_profileLayerStartTime = 0;
    private float m_profileLayerBaseTime;
    const int c_profileLayersFramesPerIndex = 200;
    const int c_profileLayersFramesBeforeProfile = 80;
    void Update()
    {
        if (m_deferredToggles.Count > 0 && GameManager.Me.LoadComplete)
        {
            foreach (var s in m_deferredToggles) ToggleLayer(s);
            m_deferredToggles.Clear();
        }
        if (m_profileLayerIndex == 0) return;
        int index = m_profileLayerIndex / c_profileLayersFramesPerIndex - 1;
        int sub = m_profileLayerIndex - (index + 1) * c_profileLayersFramesPerIndex;
        
        bool isBaseRun = false;
        if (index < 0)
        {
            index = 0;
            isBaseRun = true;
        }

        var layer = m_layers[index];

        GameManager.s_fpsExtra = $"<size=24>\nLayer profiling on layer {layer.m_name}</size>";

        var isSep = layer.IsSeparator;
        if (sub == 1)
        {
            if (isSep)
            {
                int endOfSection;
                for (endOfSection = index + 1; endOfSection < m_layers.Length; ++endOfSection)
                    if (m_layers[endOfSection].IsSeparator)
                        break;
                for (int i = 0; i < m_layers.Length; ++i) m_layers[i].Enable(i < index || i >= endOfSection);
            }
            else if (layer.IsAll)
            {
                layer.Enable(!isBaseRun);
                layer.Enable(isBaseRun);
            }
            else
                for (int i = 0; i < m_layers.Length; ++i)
                    m_layers[i].Enable(i != index);
        }
        else if (sub == c_profileLayersFramesBeforeProfile)
        {
            m_profileLayerStartTime = Time.unscaledTime;
        }
        else if (sub == c_profileLayersFramesPerIndex - 1)
        {
            var duration = Time.unscaledTime - m_profileLayerStartTime;
            var mspf = duration * 1000 / (c_profileLayersFramesPerIndex - c_profileLayersFramesBeforeProfile);
            string s;
            if (isBaseRun)
            {
                m_profileLayerBaseTime = mspf;
                s = $"Render with <color=#ff6060>all layers</color> ran at <color=#ff6060>{mspf:f1}</color>ms/f";
            }
            else
                s = $"Render without {(isSep ? "group" : "layer")} <color=#{(isSep ? "a060ff" : "8090ff")}>{m_layers[index].m_name}</color> ({index}) ran at <color=#ffff60>{mspf:f1}</color>ms/f - saving <color=#60ff60>{m_profileLayerBaseTime-mspf:f1}</color>ms/f";
            m_layerProfileResults += $"<size=24>\n{s}</size>";
            Debug.LogError(s);    
            if (index == m_layers.Length - 1)
            {
                m_layers[0].Enable(false);
                m_layers[0].Enable(true);
                StopLayerProfile();
                Debug.LogError("Layer profiling complete");
                return;
            }
        }
        ++m_profileLayerIndex;
    }
}
