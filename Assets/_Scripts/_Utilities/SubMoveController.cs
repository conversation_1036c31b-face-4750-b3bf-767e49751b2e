using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class SubMoveController : MonoBehaviour
{
    public Bounds m_sub1 = new Bounds();
    public Vector3 m_subMove1;
    public Bounds m_sub2 = new Bounds();
    public Vector3 m_subMove2;
    
    void Update()
    {
        var mrs = GetComponentsInChildren<MeshRenderer>();
        foreach (var mr in mrs)
        {
            var mpb = new MaterialPropertyBlock(); 
            mr.GetPropertyBlock(mpb);
            mpb.SetVector("_SubMoveCenter1", m_sub1.center);
            mpb.SetVector("_SubMoveExtent1", m_sub1.extents);
            mpb.SetVector("_SubMoveMove1", m_subMove1);
            mpb.SetVector("_SubMoveCenter2", m_sub2.center);
            mpb.SetVector("_SubMoveExtent2", m_sub2.extents);
            mpb.SetVector("_SubMoveMove2", m_subMove2);
            mr.SetPropertyBlock(mpb);
        }
    }
    
#if UNITY_EDITOR
    void OnDrawGizmos()
    {
        Gizmos.color = new Color(.7f, 1, .7f);
        Gizmos.DrawWireCube(m_sub1.center, m_sub1.size);
        Gizmos.color = new Color(.7f, .7f, 1);
        Gizmos.DrawWireCube(m_sub2.center, m_sub2.size);
    }
#endif
}
