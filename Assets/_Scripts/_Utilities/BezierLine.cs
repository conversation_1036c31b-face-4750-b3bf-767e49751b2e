using UnityEngine;

public class BezierLine : MonoBehaviour {
	
	LineRenderer m_line;
	
	void Awake() {
		m_line = GetComponent<LineRenderer>();
	}
	Vector3[] m_debug;
	Vector3[] m_points;
	public Vector3[] Points => m_points;
	public void SetControlPoints(Vector3 _a, Vector3 _b, Vector3 _c, Vector3 _d) {
		var v = new Vector3[4];
		v[0] = _a; v[1] = _b; v[2] = _c; v[3] = _d;
		SetControlPoints(v);
	}
	public void SetControlPoints(Vector3[] _points, float _lengthPerPoint = .5f) {
		m_debug = _points;
		m_points = GetPoints(_points, _lengthPerPoint);

		if(m_line)
		{
			m_line.positionCount = m_points.Length;
			m_line.SetPositions(m_points);
		}
	}

	public static Vector3[] GetPoints(Vector3[] _controlPoints, float _lengthPerPoint = .5f)
	{
		int totalPoints = 0;
		for (int i = 0; i < _controlPoints.Length - 1; ++i)
		{
			int i1 = i;
			int i2 = i + 1;
			int pointsInSegment = Mathf.FloorToInt((_controlPoints[i2] - _controlPoints[i1]).magnitude / _lengthPerPoint);
			if (pointsInSegment < 2) pointsInSegment = 2;
			totalPoints += pointsInSegment;
		}
		Vector3[] points = new Vector3[totalPoints];
		int nextPoint = 0;
		for (int i = 0; i < _controlPoints.Length-1; ++i) {
			int i0 = i - 1; if (i0 < 0) i0 = 0;
			int i1 = i;
			int i2 = i + 1;
			int i3 = i + 2; if (i3 > _controlPoints.Length-1) i3 = _controlPoints.Length-1;
			Vector3 a = 2f * _controlPoints[i1];
			Vector3 b = _controlPoints[i2] - _controlPoints[i0];
			Vector3 c = 2f * _controlPoints[i0] - 5f * _controlPoints[i1] + 4f * _controlPoints[i2] - _controlPoints[i3];
			Vector3 d = -_controlPoints[i0] + 3f * _controlPoints[i1] - 3f * _controlPoints[i2] + _controlPoints[i3];
			int pointsInSegment = Mathf.FloorToInt((_controlPoints[i2] - _controlPoints[i1]).magnitude / _lengthPerPoint);
			if (pointsInSegment < 2) pointsInSegment = 2;
			var dt = 1.0f / pointsInSegment;
			for (int j = 0; j < pointsInSegment; ++j) {
				float t = (float)j * dt;
				Vector3 pos = 0.5f * (a + (b * t) + (c * t * t) + (d * t * t * t));
				points[nextPoint++] = pos;
			}
		}
		return points;
	}

	public enum ControlGeneration {
		SmoothBoth,
		SmoothStart,
		SmoothEnd,
	};
	public void SetControlPoints(Vector3 _a, Vector3 _d, ControlGeneration _generationType = ControlGeneration.SmoothBoth) {
		var v1 = _a;
		var v4 = _d;
		var v2 = Vector3.Lerp(v1, v4, .33f);
		var v3 = Vector3.Lerp(v1, v4, .66f);
		var d = v4 - v1;
		Vector3 v2x, v3x;
		float smoothing = .2f;
		const float c_oneSidedLerpA = .6f, c_oneSidedLerpB = .25f;
		switch (_generationType) {
			case ControlGeneration.SmoothBoth: default: v2x = v1; v3x = v4; break;
			case ControlGeneration.SmoothStart: v2x = v1; v3x = v1 + (v4 - v1) * c_oneSidedLerpA; v2x = v1 + (v4 - v1) * c_oneSidedLerpB; smoothing = 1; break;
			case ControlGeneration.SmoothEnd: v2x = v4 + (v1 - v4) * c_oneSidedLerpA; v3x = v4 + (v1 - v4) * c_oneSidedLerpB; smoothing = 1; break;
		}
		v2x = Vector3.Lerp(v2, v2x, smoothing);
		v3x = Vector3.Lerp(v3, v3x, smoothing);
		if (d.x*d.x+d.z*d.z > d.y*d.y) {
			v2.y = v2x.y; v3.y = v3x.y;
		} else {
			v2.x = v2x.x; v2.z = v2x.z; v3.x = v3x.x; v3.z = v3x.z;
		}
		var side = Camera.main.transform.right;
		var camPos = Camera.main.transform.position;
		side *= -Mathf.Sign(Vector3.Dot(_a - camPos, side));
		float sideDistance = (_a - _d).magnitude * .01f;
		side *= sideDistance;
		v2 -= side;
		v3 += side;
		SetControlPoints(v1, v2, v3, v4);
	}

#if UNITY_EDITOR
	void OnDrawGizmos() {
		if (m_debug == null || m_debug.Length < 4) return;
		const float c_gizmoSize = .3f;
		Gizmos.color = Color.green;
		Gizmos.DrawCube(m_debug[0], Vector3.one * c_gizmoSize);
		Gizmos.color = Color.yellow;
		for (int i = 1; i < m_debug.Length-1; ++i) {
			Gizmos.DrawCube(m_debug[i], Vector3.one * c_gizmoSize*.6f);
		}
		Gizmos.color = Color.red;
		Gizmos.DrawCube(m_debug[m_debug.Length-1], Vector3.one * c_gizmoSize);
		
		Gizmos.color = Color.magenta;
		for(int i = 0; i < m_points.Length-1; i++) {
			Gizmos.DrawLine(m_points[i], m_points[i+1]);
		}
	}
#endif
}
