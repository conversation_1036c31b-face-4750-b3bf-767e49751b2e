using UnityEditor;
using UnityEngine;

public class SendPosToParent : MonoBehaviour
{
    public Transform m_parent;
    private Transform Parent
    {
        get
        {
            if (m_parent == null)
            {
                var movingObject = GetComponentInParent<NGMovingObject>();
                m_parent = movingObject.transform;
            }

            return m_parent;
        }
    }

    public Rigidbody mainBody = null;
    private Rigidbody MainBody
    {
        get
        {
            if (mainBody == null)
            {
                mainBody = Parent.GetComponent<Rigidbody>();
            }

            return mainBody;
        }
    }

    private Vector3 m_lastPos = Vector3.zero;
    private Quaternion m_lastRot = Quaternion.identity;
    public bool m_useDiff = true;

    public bool m_aboutToUseMotionExtraction = true;
    public bool m_useMotionExtraction = true;

    private bool forceDisable = false;

    private Vector3 deltaPos = Vector3.zero;
    private Quaternion deltaRot = Quaternion.identity;

    void Awake()
    {
        if (!transform.localPosition.sqrMagnitude.IsZero())
            transform.localPosition = Vector3.zero;
        
        var pos = transform.position - Parent.position;
        var rot = transform.rotation * Quaternion.Inverse(Parent.rotation);
        m_lastPos = pos;
        m_lastRot = rot;
    }
    void Start()
    {
        if (!transform.localPosition.sqrMagnitude.IsZero())
            transform.localPosition = Vector3.zero;
    }

    public bool garyFlag;
    void LateUpdate()
    {
        var pos = transform.position - Parent.position;
        var rot = transform.rotation * Quaternion.Inverse(Parent.rotation);
        var dPos = m_useDiff ? pos - m_lastPos : pos;
        var dRot = m_useDiff ? rot * Quaternion.Inverse(m_lastRot) : rot;

        transform.localPosition = Vector3.zero;
        transform.localRotation = Quaternion.identity;

        m_lastPos = pos;
        m_lastRot = rot;
        deltaPos = dPos;
        deltaRot = dRot;
    }

    public void FixedUpdate()
    {
        if (!forceDisable && (m_useMotionExtraction || garyFlag))
        {
            if (MainBody != null)
            {
                MainBody.Move(MainBody.position + deltaPos, MainBody.rotation * deltaRot);
            }
            else
            {
                Parent.position += deltaPos;
                Parent.rotation *= deltaRot;
            }
        }

        deltaPos = Vector3.zero;
        deltaRot = Quaternion.identity;

        m_useMotionExtraction = m_aboutToUseMotionExtraction;
        garyFlag = false;
    }

    public void StartMotionExtraction(bool _instant)
    {
        m_aboutToUseMotionExtraction = true;
        m_useMotionExtraction = _instant;
    }

    public void StopMotionExtraction()
    {
        m_aboutToUseMotionExtraction = false;
        m_useMotionExtraction = false;
    }

    public void SkipMotionExtraction()
    {
        m_useMotionExtraction = false;
    }

    public void ToggleForceDisable(bool disable)
    {
        forceDisable = disable;
    }
}
