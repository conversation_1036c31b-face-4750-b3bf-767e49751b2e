using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

#if UNITY_EDITOR
using UnityEditor;

[CustomPropertyDrawer(typeof(EnumArrayAttribute))]
public class EnumArrayDrawer : PropertyDrawer
{
	public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
	{
		var enumNames = ((EnumArrayAttribute)attribute).enumNames;

		if( enumNames == null ) {
			base.OnGUI( position, property, label );
			return;
		}

		if( int.TryParse(property.propertyPath.Split('[', ']')[1], out int pos ) && pos < enumNames.Length )
			label.text = enumNames[pos];

		label = EditorGUI.BeginProperty(position, label, property);

		EditorGUI.PropertyField( position, property, label );
	
		EditorGUI.EndProperty();
	}
}
#endif

public class EnumArrayAttribute : PropertyAttribute
{
	public readonly string[] enumNames = null;

	public EnumArrayAttribute( Type _type )
	{
		if( !_type.IsEnum ) {
			Debug.LogError("Enum Types only");
			return;
		}

		this.enumNames = System.Enum.GetNames( _type );
	}
}