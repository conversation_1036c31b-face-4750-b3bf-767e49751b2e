using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

public class StartingLoad : MonoBehaviour
{
    IEnumerator Start()
    {
        const float c_initialTextFadeTime = .5f;
        var cg = GetComponent<CanvasGroup>();
        for (float t = 0; t < c_initialTextFadeTime; t += Time.deltaTime)
        {
            var f = t / c_initialTextFadeTime;
            cg.alpha = f;
            yield return null;
        }
        cg.alpha = 1;
        yield return null;
        UnityEngine.SceneManagement.SceneManager.LoadSceneAsync("Town");
    }
}
