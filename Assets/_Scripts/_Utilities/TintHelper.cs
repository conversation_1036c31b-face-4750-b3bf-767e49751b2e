using System;
using System.Collections.Generic;
using UnityEngine;

public class TintHelper : MonoBehaviour
{
    private Dictionary<Material, (Texture, Texture, Texture)> m_backupTextures = new();
    
    public void SetProperties(Material _mat, Color _tint, float _tintAmount, Texture _base, Texture _bump, Texture _mask, int _channel)
    {
        if (m_backupTextures.ContainsKey(_mat) == false)
        {
            if (_mat.HasProperty(Decoration.c_baseMapProperty) == false)
                return;
            var baseMap = _mat.GetTexture(Decoration.c_baseMapProperty);
            var bumpMap = _mat.GetTexture(Decoration.c_normalMapProperty);
            var maskMap = _mat.GetTexture(Decoration.c_maskMapProperty);
            m_backupTextures.Add(_mat, (baseMap, bumpMap, maskMap));
        }
        if (_base == null)
            (_base, _bump, _mask) = m_backupTextures[_mat];
        if (_base != null) _mat.SetTexture(Decoration.c_baseMapProperty, _base);
        if (_bump != null) _mat.SetTexture(Decoration.c_normalMapProperty, _bump);
        if (_mask != null) _mat.SetTexture(Decoration.c_maskMapProperty, _mask);
        var (colourProprerty, amountProperty) = Decoration.GetTintPropertyNames(_channel);
        _mat.SetColor(colourProprerty, _tint);
        _mat.SetFloat(amountProperty, _tintAmount);
    }

    public static void SetTintProperties(GameObject _obj, Material _mat, Color _tint, float _tintAmount, Texture _base, Texture _bump, Texture _mask, int _channel)
    {
        var dec = _obj.GetOrAddComponent<TintHelper>();
        dec.SetProperties(_mat, _tint, _tintAmount, _base, _bump, _mask, _channel);
    }
}
