using UnityEngine;

#if UNITY_EDITOR
[UnityEditor.CustomPropertyDrawer(typeof(AkRTPCHolder))]
public class RTPCDrawer : AK.Wwise.Editor.BaseTypeDrawer
{
    protected override WwiseObjectType WwiseObjectType
    {
        get { return WwiseObjectType.GameParameter; }
    }
}
#endif

[System.Serializable]
public class AkRTPCHolder : AK.Wwise.RTPC
{
    public string RTPC(string _default) => string.IsNullOrEmpty(Name) ? _default : Name;
    public void Set(float _value, GameObject _o) => AudioClipManager.Me.SetGameParameter(Name, _value, _o);
}
