using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FlashObject : MonoBehaviour
{
    public Color m_highlightColour;
    public Color m_highlightColour2;
    public float m_highlightFlashSpeed = 0;
    
    private Material m_material;
    private Dictionary<Renderer, Material[]> m_backupMaterials;
    private float m_timeBase;
    void Start()
    {
        m_material = new Material(GlobalData.Me.m_negativeSelectionMaterial);
        m_material.color = m_highlightColour;
        m_backupMaterials = gameObject.ReplaceAllMaterials(m_material);
        if (m_highlightFlashSpeed > 0)
            StartCoroutine(Co_Flash());
    }

    void OnDestroy()
    {
        if (gameObject != null)
            gameObject.RestoreAllMaterials(m_backupMaterials);
    }

    IEnumerator Co_Flash()
    {
        m_timeBase = Time.unscaledTime;
        while (true)
        {
            var t = Mathf.Repeat(Time.unscaledTime - m_timeBase, m_highlightFlashSpeed) / m_highlightFlashSpeed;
            t = Mathf.Min(2 * t, 2 - 2 * t);
            t = t * t * (3 - t - t);
            var c = Color.Lerp(m_highlightColour, m_highlightColour2, t);
            m_material.color = c;
            yield return null;
        }
    }

    public static FlashObject Flash(GameObject _o, Color _highlight, Color _highlight2, float _flashSpeed = 0)
    {
        var fo = _o.GetComponent<FlashObject>();
        if (fo == null)
            fo = _o.AddComponent<FlashObject>();
        fo.m_highlightColour = _highlight;
        fo.m_highlightColour2 = _highlight2;
        fo.m_highlightFlashSpeed = _flashSpeed;
        return fo;
    }

    public static void Stop(GameObject _o)
    {
        var fo = _o.GetComponent<FlashObject>();
        if (fo != null) Destroy(fo);
    }

    public static void Stop(FlashObject _fo)
    {
        if (_fo != null) Destroy(_fo);
    }
}
