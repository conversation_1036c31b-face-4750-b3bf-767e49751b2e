using System;
using UnityEngine;
using UnityEngine.Events;

[RequireComponent(typeof(Animator))]
public class MAAttackAnimCallback : MonoBehaviour
{
	public Action m_onAttackStart;
	public Action m_onAttackHit;
	public Action m_onAttackEnd;
	
	private void OnAttackStart()
	{
		m_onAttackStart?.Invoke();
	}
        
	private void OnAttackHit()
	{
		m_onAttackHit?.Invoke();
	}
    
	private void OnAttackEnd()
	{
		m_onAttackEnd?.Invoke();
	}
}
