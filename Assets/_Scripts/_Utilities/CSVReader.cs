using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System.Text;

public class CSVReader
{
	Dictionary<string, List<string>> m_columns = new Dictionary<string, List<string>>();
	List<string> m_columnHeaders = new List<string>(); public List<string> headers{ get { return m_columnHeaders; }}
	List<List<string>>		m_simpleCells;
	public List<List<string>> Cells => m_simpleCells;
	public enum direction { row, column, simple }
	public	direction		m_direction;
	public	bool				m_valid;

	/// <summary>
	/// Initializes a new instance of the <see cref="CSVReader"/> class.
	/// </summary>
	/// <param name="_path">Path of the csv.</param>
	/// <param name="_direction">The order of the data i.e. row or column. or simple for no columns or rows</param>
	public CSVReader(string _path, direction _direction, bool _expectError = false)
	{
		TextAsset CSVData = ResManager.Load<TextAsset>(_path);
		if(ConvertToLists(CSVData, _direction, _expectError, true) == false)
			if (!_expectError)
				Debug.LogError (string.Format("Unable to find csv at location {0}",_path));

	}
		/// <summary>
	/// Initializes a new instance of the <see cref="CSVReader"/> class from document folder.
	/// </summary>
	/// <param name="_path">Path of the csv.</param>
	/// <param name="_direction">The order of the data i.e. row or column. or simple for no columns or rows</param>
	public CSVReader(string _path)
	{
		m_valid = false;
		if(_path.Contains(".csv") == false) _path+= ".csv";
		if(File.Exists(_path) == false) return;
		var lines = new List<List<string>>();
		System.IO.StreamReader file = new System.IO.StreamReader(_path);
		if(file != null)
		{
			string line;
			while((line = file.ReadLine()) != null)
			{
				var split = line.Split(',');
				List<string> columns = new List<string>(split);
				lines.Add(columns);
			}
			m_simpleCells = lines;
			m_valid = true;
			file.Close();
		}
	}

	public	CSVReader(TextAsset _text, direction _direction, bool _expectError = false)
	{
		ConvertToLists(_text, _direction, _expectError, false);
	}

	static StringBuilder cur_item = new StringBuilder("", 32);
	// https://github.com/frozax/fgCSVReader/blob/master/fgCSVReader.cs
	public static void LoadFromString(string file_contents, List<List<string> > _output)
	{
		int file_length = file_contents.Length;

		// read char by char and when a , or \n, perform appropriate action
		int cur_file_index = 0; // index in the file
		List<string> cur_line = new List<string>(32); // current line of data
		cur_item.Clear();
		bool inside_quotes = false; // managing quotes
		while (cur_file_index < file_length)
		{
			char c = file_contents[cur_file_index++];

			switch (c)
			{
				case '"':
					if (!inside_quotes)
					{
						inside_quotes = true;
					}
					else
					{
						if (cur_file_index == file_length)
						{
							// end of file
							inside_quotes = false;
							goto case '\n';
						}
						else if (file_contents[cur_file_index] == '"')
						{
							// double quote, save one
							cur_item.Append("\"");
							cur_file_index++;
						}
						else
						{
							// leaving quotes section
							inside_quotes = false;
						}
					}
					break;
				case '\r':
					// ignore it completely
					break;
				case ',':
					goto case '\n';
				case '\n':
					if (inside_quotes)
					{
						// inside quotes, this characters must be included
						cur_item.Append(c);
					}
					else
					{
						// end of current item
						cur_line.Add(cur_item.ToString());
						cur_item.Length = 0;
						if (c == '\n' || cur_file_index == file_length)
						{
							// also end of line, call line reader
							_output.Add(cur_line);
							cur_line = new List<string>(32);
						}
					}
					break;
				default:
					// other cases, add char
					cur_item.Append(c);
					if (cur_file_index == file_length) goto case '\n'; // in case there is no ending cr
					break;
			}
		}
	}

	private static byte[] byte_buffer = new byte[524288];// 524,288 is 19^2. We only need around 400,000 but power of 2 is always better for memory.
	public static void LoadFromString(byte[] file_contents, List<List<string> > _output)
	{
		int file_length = file_contents.Length;
		int byteIndex = 0;
		int capacity = 0;
		int lines = 0;
		// read char by char and when a , or \n, perform appropriate action
		int cur_file_index = 0; // index in the file

		bool inside_quotes = false; // managing quotes
		while (cur_file_index < file_length)
		{
			byte c = file_contents[cur_file_index++];

			switch ((char)c)
			{
				case '"':
					if (!inside_quotes)
					{
						inside_quotes = true;
					}
					else
					{
						if (cur_file_index == file_length)
						{
							// end of file
							inside_quotes = false;
							goto case '\n';
						}
						else if (file_contents[cur_file_index] == '"')
						{
							// double quote, save one
							byte_buffer[byteIndex++] = (byte)'"';
							cur_file_index++;
						}
						else
						{
							// leaving quotes section
							inside_quotes = false;
						}
					}
					break;
				case '\r':
					// ignore it completely
					break;
				case ',':
					goto case '\n';
				case '\n':
					if (inside_quotes)
					{
						// inside quotes, this characters must be included
						byte_buffer[byteIndex++] = c;
					}
					else
					{
						// Null terminate.
						byte_buffer[byteIndex++] = 0;
						capacity++;

						if (c == '\n' || cur_file_index == file_length)
						{
							_output.Add( new List<string>( capacity ) );
							capacity = 0;
							lines++;
						}
					}
					break;
				default:
					// other cases, add char
					byte_buffer[byteIndex++] = c;
					if (cur_file_index == file_length) goto case '\n'; // in case there is no ending cr
					break;
			}
		}

		int newSearchIndex = -1;
		for( int i = 0; i < lines; ++i )
		{
			var list = _output[i];
			capacity = list.Capacity;
			for( int c = 0; c < capacity; ++c )
			{
				int before = newSearchIndex+1;
				while( byte_buffer[ ++newSearchIndex ] != 0 );

				list.Add( Encoding.UTF8.GetString( byte_buffer, before, (newSearchIndex-before) ) );
			}
		}
	}

	public	bool ConvertToLists(TextAsset _text, direction _direction, bool _expectError = false, bool _unloadData = false)
	{
		if (_text == null)
		{
			m_valid = false;
			m_direction = direction.simple;
			m_simpleCells = new List<List<string>>();
			return false;
		}

		var lines = new List<List<string>>(128);
		// LoadFromString(_text.text, lines);

		// var linesbytes = new List<List<string>>(128);
		LoadFromString(_text.bytes, lines);

		if(_unloadData)
			Resources.UnloadAsset(_text);
		
		m_valid = true;
		m_direction = _direction;
		if(_direction == direction.simple) 
		{
			m_simpleCells = lines;
			return true;
		}

		for (int i = 0; i < lines.Count; i ++)
		{
			var splitLine = lines[i];
			switch (_direction)
			{
			case direction.row:
				m_columnHeaders.Add(splitLine[0]);

				List<string> data = new List<string>();

				for (int jj = 1; jj < splitLine.Count; jj++)
					data.Add(splitLine[jj]);

				m_columns.Add(splitLine[0], data);
				break;
			case direction.column:
                if (i == 0)
				{
					m_columnHeaders.AddRange(splitLine);
					foreach (var entry in m_columnHeaders)
						m_columns.Add(entry, new List<string>());
				}
				else
				{
					for (int jj = 0; jj < splitLine.Count; jj++)
					{
						m_columns[m_columnHeaders[jj]].Add(splitLine[jj]);
					}
				}
				break;
			case	direction.simple:
				var line = new List<string>();
				line.AddRange(splitLine);
				m_simpleCells.Add(line);
				break;
			}
		}
		return	true;
	}

	/// <summary>
	/// Gets a list of data based on a header
	/// </summary>
	/// <returns>A list of strings containing the data relating to the header</returns>
	/// <param name="_key">Header.</param>
	public List<string> GetDataFromKey(string _key)
	{
		if(m_direction == direction.simple)
			Debug.LogError(" GetDataFromKey Invalid for a table that was loaded as "+m_direction);
		if (!m_columns.ContainsKey (_key))
			Debug.LogError(string.Format("There is no data for {0} in this CSV as it has been loaded", _key));

		return m_columns[_key];
	}

	/// <summary>
	/// Gets the data as entrys.
	/// </summary>
	/// <returns>A list of entrys, which are themselves lists</returns>
	public List<List<string>> GetDataAsEntrys()
	{
		if(m_direction == direction.simple)
			return	m_simpleCells;
		List<List<string>> data = new List<List<string>> ();

		for (int ii = 0; ii < m_columns[m_columnHeaders[0]].Count; ii++)
		{
			List<string> entry = new List<string> ();
			foreach (var header in m_columnHeaders)
			{
				var list = m_columns [header];
				entry.Add ((ii < list.Count) ? list[ii] : "");
			}
			data.Add (entry);
		}
		return data;
	}
	/// <summary>
	/// Loads and Initalieses a new instance of the <see cref="CSVReader"/> class.
	/// </summary>
	/// <param name="_path">Path of the csv.</param>
	/// <param name="_direction">The order of the data i.e. row or column. or simple for no columns or rows</param>
	/// <returns>The CSVReader or null.</returns>
	static	public	CSVReader	Load(string _path, direction _direction = direction.simple)
	{
		var reader = new CSVReader(_path, _direction, true);
		if (!reader.m_valid) return null;
		return reader;
	}
	static	public	CSVReader	Load(TextAsset _text, direction _direction)
	{
		var reader = new CSVReader(_text, _direction, true);
		if (!reader.m_valid) return null;
		return reader;
	}
	static public CSVReader LoadDocumentCSV(string _path)
	{
		var reader = new CSVReader(_path);
		if (!reader.m_valid) return null;
		return reader;
	}

}

public class DataUtilities {

	public static void ParseAssignment( string assignment, out string left, out string right, char delimiter = '=' )
	{
		int indexOf = assignment.IndexOf(delimiter);
		Debug.Assert( indexOf != -1, $"Delimiter \'{delimiter}\' in \"{assignment}\" not found." );
		if (indexOf == -1)
		{
			left = right = "";
			return;
		}
		left = assignment.Substring( 0, indexOf++ );
		right = assignment.Substring( indexOf, assignment.Length - indexOf );
	}
	
	public static List<T> NGLoadInfoTable<T>(string _fileName, System.Func<T> _new)
	{
		var csv = CSVReader.Load(_fileName);
		if(csv == null) { Debug.LogError($"Unable to load { _fileName}"); return null; }
		var table = csv.GetDataAsEntrys();
		return NGLoadInfoTable<T>(table, _new);
	}
	public static List<T> NGLoadInfoTable<T>(List<List<string>> _table, System.Func<T> _new)
	{
		List<string> fields = _table[0];
		List<T> infos = new List<T>();
		for (int row = 1; row < _table.Count; row++)
		{
			var line = _table[row];
			var info = _new();
			for (int col = 0; col < line.Count; col++)
			{
				var cell = line[col];
				ReactReflection.NGDecodeAndSetField(info, fields[col], cell, DataUtilities.GetCellRef(row, col));
			}
			infos.Add(info);
		}
		return infos;
	}
	public static NGCommanderBase NGLoadPrefabCommanberBase(string _name)
	{
		string prefab_location = (_name.Contains("/")) ? _name : "React/" + _name;
		var go = ResManager.Load<GameObject>(prefab_location + "_SLOD2");
		if (go == null) go = ResManager.Load<GameObject>(prefab_location + "_SLOD1");
		if (go == null) go = ResManager.Load<GameObject>(prefab_location);
		if (go)
		{
			var commanderBase = go.GetComponent<NGCommanderBase>();
			if (commanderBase)
				return commanderBase;
		}
		//Debug.Log($"{_name} not a valid prefab");
		return null;
	}

	public static	string		GetCellRef(int _line, int _column)
	{
		_line++;
		int 	h = _column/26;
		char	ll = (char)(65+_column%26);
		char	hl = (char)(65+h);
		string	col = (_column/26 > 0) ? hl.ToString() + ll.ToString() : ll.ToString();
		return	col+_line.ToString();
	}
}
