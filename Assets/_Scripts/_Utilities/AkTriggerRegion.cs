using System.Collections.Generic;
using UnityEngine;

public class AkTriggerRegion : MonoBehaviour
{
    public AkEventHolder m_audioEvent;
    //public AkEventHolder.EBus m_bus = AkEventHolder.EBus.SFX;
    public AkEventHolder m_voEvent;
    [Header("Switch must be MusicName")]
    public AkSwitchHolder m_musicState;
    public int m_triggerCount = 1;
    private Collider[] m_colliders;
    private bool m_wasInside;
    private Vector3 m_lastCameraPos = Vector3.zero;
    private bool m_initialisedAfterLoad;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
    public bool m_visualise = false;
    private Dictionary<Collider, GameObject> m_visualisation = new();
    
    private static bool s_showVisualisation = false;
    private static List<AkTriggerRegion> s_all = new();
    void Awake() => s_all.Add(this);
    void OnDestroy() => s_all.Remove(this);
    private static DebugConsole.Command s_visAllCmd = new ("showaktriggers", _s => { s_showVisualisation = !s_showVisualisation; foreach (var atr in s_all) atr.m_visualise = s_showVisualisation; }, "Show all AkTriggerRegion visualisations");
#endif
    
    void CheckVisualisation()
    {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
        if (m_colliders == null || m_colliders.Length == 0)
            return;
        if (m_visualise)
        {
            foreach (var c in m_colliders)
            {
                if (m_visualisation.TryGetValue(c, out var vis) == false)
                {
                    if (c is SphereCollider)
                        vis = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                    else
                        vis = GameObject.CreatePrimitive(PrimitiveType.Cube);
                    Destroy(vis.GetComponent<Collider>());
                    vis.transform.SetParent(transform);
                    m_visualisation[c] = vis;
                }
                if (c is SphereCollider sc)
                {
                    vis.transform.localPosition = sc.center;
                    vis.transform.localScale = Vector3.one * sc.radius;
                }
                else if (c is BoxCollider bc)
                {
                    vis.transform.localPosition = bc.center;
                    vis.transform.localScale = bc.size;
                }
            }
        }
        else if (m_visualise == false && m_visualisation != null)
        {
            foreach (var kvp in m_visualisation)
                Destroy(kvp.Value);
            m_visualisation.Clear();
        }
#endif
    }

    void Start()
    {
        m_colliders = GetComponentsInChildren<Collider>();
        if (m_colliders == null || m_colliders.Length == 0)
        {
            Debug.LogError("AkTriggerRegion: No collider found, disabling");
            gameObject.SetActive(false);
            return;
        }
        foreach (var c in m_colliders)
            c.isTrigger = true;
    }

    void CheckInitialiseAfterLoad()
    {
        if (m_initialisedAfterLoad || GameManager.Me.LoadComplete == false) return;
        m_initialisedAfterLoad = true;
        if (m_triggerCount > 0 && GameManager.Me.m_state.m_akTriggers.GetValue(name, 0) >= m_triggerCount)
        {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (s_showVisualisation)
                GameManager.ShowCriticalError($"AkTriggerRegion: Already triggered {name} count {GameManager.Me.m_state.m_akTriggers.GetValue(name, 0)} of {m_triggerCount}");
#endif
            gameObject.SetActive(false);
        }
    }
    void LateUpdate()
    {
        CheckVisualisation();
        CheckInitialiseAfterLoad();
        bool isInside = false;
        var camPos = Camera.main.transform.position;
        var lastCamPos = m_lastCameraPos;
        m_lastCameraPos = camPos;
        var lastToThis = camPos - lastCamPos;
        var lastToThisMag = lastToThis.magnitude;
        if (lastToThisMag > .00001f && lastToThisMag < 5)
        {
            lastToThis /= lastToThisMag;
            var ray = new Ray(lastCamPos, lastToThis);
            foreach (var c in m_colliders)
                if (c.Raycast(ray, out RaycastHit hit, lastToThisMag + .01f))
                    //if (Vector3.Dot(hit.normal, lastToThis) < 0)
                        isInside = true;
        }
        bool wasInside = m_wasInside;
        m_wasInside = isInside;
        if (isInside == false || wasInside)
            return;
        // just entered
        var triggerCount = GameManager.Me.m_state.m_akTriggers.AddValue(name, 1, (a,b) => a+b);
        if (triggerCount == m_triggerCount || m_triggerCount == 0)
        {
            m_audioEvent.Play(gameObject);
            if (m_voEvent != null)
                m_voEvent.Play(gameObject, AkEventHolder.EBus.Voice);
            if (m_musicState != null)
                GameManager.Me.SetFlowMusicState(m_musicState.ObjectReference?.ObjectName);
            if (m_triggerCount > 0)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (s_showVisualisation)
                    GameManager.ShowCriticalError($"AkTriggerRegion: Now triggered {name} count {GameManager.Me.m_state.m_akTriggers.GetValue(name, 0)} of {m_triggerCount}");
#endif
                gameObject.SetActive(false);
            }
        }
    }
}
