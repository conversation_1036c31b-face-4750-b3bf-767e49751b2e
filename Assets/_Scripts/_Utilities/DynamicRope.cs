using System.Collections;
using System.Collections.Generic;
using Unity.Mathematics;
using UnityEngine;

public class DynamicRope : MonoBehaviour
{
    public float m_length = 1;
    public bool m_debugVisualiseBones = false;
    public GameObject m_optionalStart;
    public GameObject m_segment;
    public GameObject m_optionalEnd;
    public enum EVisualAxis { PositiveX, PositiveY, PositiveZ, NegativeX, NegativeY, NegativeZ };
    public EVisualAxis m_visualAxis = EVisualAxis.PositiveZ;
    static Vector3 c_segmentAxis => Vector3.up;
    public float m_segmentScale = 1;
    public float m_bonesPerSegment = 1;
    public GameObject lastSegment;
    private GameObject m_connectedTo;
    private Transform[] m_transforms;
    private SkinnedMeshRenderer m_smr;

    private Rigidbody[] m_allBodies = null;
    public Rigidbody[] RopeBodies
    {
        get { return m_allBodies; }
    }

    private int numSegments = 0;
    public int SegmentsCount
    {
        get { return numSegments; }
    }

    private float segmentLength = 0.0f;
    public float SegmentLength
    {
        get { return segmentLength; }
    }

    public float segmentRadius = 0.2f;

    public bool addColliders = false;

    public Rigidbody FirstBody => m_allBodies[^1];
    public Rigidbody LastBody => m_allBodies[0];

    void Start()
    {
        CreateRopes();
    }
    void CreateRopes()
    {
        if (numSegments > 0) return;

        var fm = new MeshUtils.FlexibleMesh();
        var scale = Vector3.one * m_segmentScale;
        Quaternion rot;
        switch (m_visualAxis)
        {
            case EVisualAxis.PositiveZ: rot = Quaternion.Euler(90, 0, 0); break;
            case EVisualAxis.NegativeZ: rot = Quaternion.Euler(-90, 0, 0); break;
            case EVisualAxis.PositiveX: rot = Quaternion.Euler(0, 0, 90); break;
            case EVisualAxis.NegativeX: rot = Quaternion.Euler(0, 0, -90); break;
            default: case EVisualAxis.PositiveY: rot = Quaternion.Euler(180, 0, 0); break;
            case EVisualAxis.NegativeY: rot = Quaternion.Euler(0, 0, 0); break;
        }
        fm.AddMesh(m_segment.GetComponent<MeshFilter>().sharedMesh, Matrix4x4.TRS(Vector3.zero, rot, scale), false);
        segmentLength = Vector3.Dot(c_segmentAxis, fm.Extents);
        fm.SetLength(c_segmentAxis, m_length);
        // note: end and start are flipped since we build the rope backwards
        if (m_optionalEnd != null) fm.AddMesh(m_optionalEnd.GetComponent<MeshFilter>().sharedMesh, Matrix4x4.TRS(c_segmentAxis * -segmentLength, rot, scale), false);
        if (m_optionalStart != null) fm.AddMesh(m_optionalStart.GetComponent<MeshFilter>().sharedMesh, Matrix4x4.TRS(c_segmentAxis * m_length, rot, scale), false);
        
        var totalMin = fm.Min;
        var totalMax = fm.Max;
        Vector3 subMin = totalMin; 
        fm.ApplyToPoints((v) => v - subMin);
        totalMax -= totalMin; totalMin = Vector3.zero;
        
        numSegments = Mathf.CeilToInt(m_bonesPerSegment * m_length / segmentLength);
        var mesh = fm.Mesh(false, false);
        var meshObj = new GameObject("Mesh");
        meshObj.transform.SetParent(transform);
        var smr = meshObj.AddComponent<SkinnedMeshRenderer>();
        smr.sharedMesh = mesh;
        smr.material = m_segment.GetComponent<MeshRenderer>().sharedMaterial;

        var finalLength = Vector3.Dot(totalMax - totalMin, c_segmentAxis);
        segmentLength = finalLength / numSegments;
        
        ConfigurableJoint prevJoint = null;
        Rigidbody prevBody = null;
        m_allBodies = new Rigidbody[numSegments];
        var transforms = new Transform[numSegments];
        for (int i = 0; i < numSegments; ++i)
        {
            GameObject segment = new GameObject($"Segment{i + 1}");
            segment.transform.parent = transform;

            if (m_debugVisualiseBones)
            {
                var vis = GameObject.CreatePrimitive(PrimitiveType.Cube);
                Destroy(vis.GetComponent<Collider>());
                vis.transform.parent = segment.transform;
                vis.transform.localPosition = Vector3.zero;
                vis.transform.localScale = Vector3.one * .2f;
                vis.transform.localRotation = Quaternion.identity;
            }

            transforms[i] = segment.transform;
            segment.transform.localPosition = c_segmentAxis * (i * -segmentLength);
            segment.transform.rotation = Quaternion.identity;
            segment.transform.localEulerAngles = Vector3.zero;
            var body = segment.GetComponent<Rigidbody>();
            if (body == null)
                body = segment.AddComponent<Rigidbody>();
            body.linearDamping = 0.1f;
            body.angularDamping = 0.01f;

            if (addColliders)
            {
                var collider = segment.GetComponent<CapsuleCollider>();
                if (collider == null)
                    collider = segment.AddComponent<CapsuleCollider>();
                collider.direction = 1;
                collider.center = new Vector3(0.24f, segmentLength * 0.5f, 0.24f);
                collider.height = segmentLength * 0.8f;
                collider.radius = segmentRadius * 0.8f;

                body.automaticCenterOfMass = false;
                body.centerOfMass = Vector3.zero;
                body.automaticInertiaTensor = false;
                body.inertiaTensor = Vector3.one;
                body.inertiaTensorRotation = Quaternion.identity;
            }

            if (prevJoint != null)
            {
                body.transform.rotation = Quaternion.identity;
                body.transform.localEulerAngles = Vector3.zero;
                prevJoint.transform.rotation = Quaternion.identity;
                prevJoint.transform.localEulerAngles = Vector3.zero;
                
                prevJoint.autoConfigureConnectedAnchor = false;
                prevJoint.anchor = new Vector3(0, segmentLength, 0);
                prevJoint.connectedAnchor = Vector3.zero;
                prevJoint.axis = Vector3.right;
                prevJoint.secondaryAxis = Vector3.up;
                prevJoint.xMotion = ConfigurableJointMotion.Locked;
                prevJoint.yMotion = ConfigurableJointMotion.Locked;
                prevJoint.zMotion = ConfigurableJointMotion.Locked;
                prevJoint.angularXMotion = ConfigurableJointMotion.Limited;
                prevJoint.angularYMotion = ConfigurableJointMotion.Locked;
                prevJoint.angularZMotion = ConfigurableJointMotion.Limited;
                const float angleLimit = 30.0f;
                prevJoint.lowAngularXLimit = new SoftJointLimit() {limit = -angleLimit, bounciness = 0.0f, contactDistance = 0.0f};
                prevJoint.highAngularXLimit = new SoftJointLimit() {limit = angleLimit, bounciness = 0.0f, contactDistance = 0.0f};
                prevJoint.angularZLimit = new SoftJointLimit() {limit = angleLimit, bounciness = 0.0f, contactDistance = 0.0f};
                prevJoint.connectedBody = body;
            }
            prevJoint = segment.AddComponent<ConfigurableJoint>();
            prevBody = body;
            if (i == 0) body.isKinematic = true;
            lastSegment = segment;
            m_allBodies[i] = body;
        }
        if (prevJoint != null)
        {
            prevJoint.transform.rotation = Quaternion.identity;
            prevJoint.transform.localEulerAngles = Vector3.zero;

            prevJoint.autoConfigureConnectedAnchor = false;
            prevJoint.anchor = new Vector3(0, segmentLength * 0.5f, 0);
            prevJoint.connectedAnchor = Vector3.zero;
            prevJoint.axis = Vector3.right;
            prevJoint.secondaryAxis = Vector3.up;
            prevJoint.xMotion = ConfigurableJointMotion.Free;
            prevJoint.yMotion = ConfigurableJointMotion.Free;
            prevJoint.zMotion = ConfigurableJointMotion.Free;
            prevJoint.angularXMotion = ConfigurableJointMotion.Free;
            prevJoint.angularYMotion = ConfigurableJointMotion.Free;
            prevJoint.angularZMotion = ConfigurableJointMotion.Free;
            prevJoint.connectedBody = null;
        }
        if(m_connectedTo)
        {
            m_connectedTo.transform.SetParent(lastSegment.transform);
            m_connectedTo.transform.localPosition = Vector3.zero;
            m_connectedTo.transform.rotation = Quaternion.identity;
            m_connectedTo.transform.localEulerAngles = Vector3.zero;
        }
        var weights = new BoneWeight[mesh.vertexCount];
        var bindPoses = new Matrix4x4[numSegments];
        for (int i = 0; i < numSegments; ++i)
            bindPoses[i] = Matrix4x4.Translate(c_segmentAxis * (i * -segmentLength));
        var verts = mesh.vertices;
        Vector3 min =  totalMin;
        for (int i = 0; i < mesh.vertexCount; ++i)
        {
            var pos = verts[i];
            var along = Vector3.Dot(pos - min, c_segmentAxis) / segmentLength;
            int thisIndex = (int)along;
            var fract = along - thisIndex;
            float wPrev = 0, wThis = 0, wNext = 0;
            thisIndex = Mathf.Clamp(thisIndex, 0, numSegments - 1);
            int prevIndex = Mathf.Max(0, thisIndex - 1);
            int nextIndex = Mathf.Min(numSegments - 1, thisIndex + 1);
            if (fract <= .5f)
            {
                wThis = .5f + fract;
                wPrev = .5f - fract;
            }
            else
            {
                wThis = 1.5f - fract;
                wNext = -.5f + fract;
            }
            //thisIndex = 0; prevIndex = 0; nextIndex = 0; wThis = 1; wPrev = 0; wNext = 0;
            weights[i].boneIndex0 = thisIndex;
            weights[i].weight0 = wThis;
            weights[i].boneIndex1 = prevIndex;
            weights[i].weight1 = wPrev;
            weights[i].boneIndex2 = nextIndex;
            weights[i].weight2 = wNext;
            weights[i].boneIndex3 = 0;
            weights[i].weight3 = 0;
        }
        mesh.boneWeights = weights;
        mesh.bindposes = bindPoses;
        mesh.UploadMeshData(false);
        smr.sharedMesh = mesh;
        smr.bones = transforms;
        smr.rootBone = transforms[0];
        smr.quality = SkinQuality.Bone4;
        m_smr = smr;
        m_transforms = transforms;
    }

    void LateUpdate()
    {
        Vector3 min = Vector3.one * 1e23f, max = Vector3.one * -1e23f;
        foreach (var t in m_transforms)
        {
            min = Vector3.Min(min, t.position);
            max = Vector3.Max(max, t.position);
        }
        m_smr.bounds = new Bounds((min + max) * .5f, max - min);
    }
    
    void Activate(GameObject _connectTo)
    {
        m_connectedTo = _connectTo;
        CreateRopes();
    }
    
    public static DynamicRope Create(DynamicRope _prefab, Transform _holder, GameObject _connectTo, Vector3 _pos, float _overrideLength = 0)
    {
        var go = Instantiate(_prefab, _holder);
        go.transform.position = _pos;
        var dr = go.GetComponent<DynamicRope>();
        if (_overrideLength > 0)
            dr.m_length = _overrideLength;
        dr.Activate(_connectTo);
        return dr;
    }
}
