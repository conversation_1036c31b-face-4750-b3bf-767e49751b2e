using System;
using System.Reflection;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FinalizerScanner : MonoBehaviour
{
    //[RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    static void LogFinalizerTypes()
    {
        List<string> finalizerTypes = new List<string>();

        foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
        {
            Type[] types = null;
            try { types = assembly.GetTypes(); }
            catch (ReflectionTypeLoadException e) { types = e.Types; }

            if (types == null) continue;

            foreach (var type in types)
            {
                if (type == null || !type.IsClass || type.IsAbstract) continue;

                var method = type.GetMethod("Finalize",
                    BindingFlags.Instance | BindingFlags.NonPublic);

                if (method != null && method.DeclaringType == type)
                {
                    finalizerTypes.Add($"{type.FullName} (Assembly: {assembly.GetName().Name})");
                }
            }
        }

        Debug.Log($"[FinalizerScanner] Found {finalizerTypes.Count} types with finalizers:");
        foreach (var t in finalizerTypes)
        {
            Debug.Log($"[FinalizerScanner]   → {t}");
        }
    }
}

