using UnityEngine;

public class AnimatorParamBlender : MonoBehaviour
{
    public string m_paramName;

    System.Action<float> Callback; //To allow further processing after the blend, or for setting layer weights
    Animator m_animator;
    float m_target;
    float m_speed;

    float m_currentVal;

    public void UpdateWith(float _newVal, float _newSpeed)
    {
        m_target = _newVal;
        m_speed = _newSpeed;
    }

    public static AnimatorParamBlender Create(Animator _animator, string _name, float _from, float _to, float _speed, System.Action<float> _Callback = null)
    {
        var gO = _animator.gameObject;
        var blenders = gO.GetComponents<AnimatorParamBlender>();
        foreach (var blender in blenders)
        {
            if (blender.m_paramName == _name)
            {
                blender.UpdateWith(_to, _speed);
                return blender;
            }
        }
        var apb = gO.AddComponent<AnimatorParamBlender>();
        apb.Setup(_animator, _name, _from, _to, _speed, _Callback);
        return apb;
    }

    void Setup(Animator _animator, string _name, float _from, float _to, float _speed, System.Action<float> _Callback)
    {
        m_animator = _animator;
        m_paramName = _name;
		m_currentVal = _from;
        m_target = _to;
        m_speed = _speed;

        Callback = _Callback ?? ((currentVal) => m_animator.SetFloat(m_paramName, currentVal));
    }

    private void Update()
    {
        var changeAmount = m_speed * Time.deltaTime;
        var difference = m_target - m_currentVal;
		if (Mathf.Abs(difference) < changeAmount)
        {
            Callback(m_target);
            enabled = false;
            Destroy(this);
            return;
        }
        m_currentVal += changeAmount * Mathf.Sign(difference);
        Callback(m_currentVal);
    }
}
