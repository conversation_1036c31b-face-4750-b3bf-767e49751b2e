using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PhysicsAudio : MonoBehaviour
{
    public string m_audioEvent;
    public string m_audioRTPC;
    
    int m_lastCollisionSoundFrame = 0;
    
    private static bool s_logPhysicsAudio = false;
    private static DebugConsole.Command s_togglePhysicsAudioLog = new ("logphysaudio", _s => Utility.SetOrToggle(ref s_logPhysicsAudio, _s));

    private void OnCollisionEnter(Collision other)
    {
        if (GameManager.Me.LoadComplete == false) return;
        var body = GetComponent<Rigidbody>();
        if (body == null) return;
        var bodyMass = body.mass;
        var hitVelocity = other.impulse / bodyMass; 
        var hitSpeedSqrd = hitVelocity.sqrMagnitude;
        if (hitSpeedSqrd > NGManager.Me.m_minimumAudioImpactSpeed * NGManager.Me.m_minimumAudioImpactSpeed && Time.frameCount - m_lastCollisionSoundFrame > 1)
        {
            if (other.gameObject.GetComponentInParent<NGMovingObject>() == null)
            {
                var hitSpeed = Mathf.Sqrt(hitSpeedSqrd);
                var held = InputUtilities.GetCurrentDragObject();
                if (held != null && transform.IsChildOf(held.transform))
                    if (other.gameObject.GetComponent<Terrain>() != null)
                        return; // don't collide with terrain when held
                if (s_logPhysicsAudio) Debug.LogError($"PhysicsAudio: {transform.Path()} hit {other.transform.Path()} speed {hitSpeed} - last sfx {Time.frameCount - m_lastCollisionSoundFrame} frames ago");
                m_lastCollisionSoundFrame = Time.frameCount;
                AudioClipManager.Me.SetSurfaceType("Material_Hard", gameObject, true);
                if (m_audioRTPC != null)
                    AudioClipManager.Me.SetGameParameter(m_audioRTPC, hitSpeed, gameObject);
                AudioClipManager.Me.PlaySound(m_audioEvent, gameObject);
            }
        }
    }

    public static void Create(GameObject _obj, string _audioEvent, string _rtpc = null)
    {
        var pa = _obj.AddComponent<PhysicsAudio>();
        pa.m_audioEvent = _audioEvent;
        pa.m_audioRTPC = _rtpc;
    }

    public static void Destroy(GameObject _obj)
    {
        var pa = _obj.GetComponent<PhysicsAudio>();
        if (pa != null)
            Destroy(pa);
    }
}
