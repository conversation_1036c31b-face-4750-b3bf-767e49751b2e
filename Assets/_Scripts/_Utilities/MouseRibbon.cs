using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MouseRibbon : MonoBehaviour  {
	public float m_lineWidth = .05f;
	public bool m_active = true;
	public int m_maxPoints = 30;
	public Color m_colour = Color.green;
	LineRenderer m_line;
	List<Vector3> m_points = new List<Vector3>();
	List<Vector3> m_points2D = new List<Vector3>();
	void Start() {
		m_line = gameObject.AddComponent<LineRenderer>();
		m_line.material = new Material(Shader.Find("_UI/S_UI"));
		var c = m_colour; c.a = 0;
		m_line.startColor = c;
		m_line.endColor = m_colour;
		m_line.startWidth = m_line.endWidth = m_lineWidth;
	}
	void OnDisable() {
		m_active = false;
	}
	void Update() {
		if (m_active) {
			AddPoint(Input.mousePosition);
		} else if (m_points2D.Count > 0) {
			m_points2D.RemoveAt(0);
		} else {
			Destroy(gameObject);
			return;
		}
		Fill();
	}
	public float m_maxScreenFractionBetweenPoints = .01f;
	void AddPoint(Vector3 _p2D) {
		m_points2D.Add(_p2D);
		if (m_points2D.Count > m_maxPoints) m_points2D.RemoveAt(0);
	}
	void RefreshPoints() {
		float distancePerPoint = Screen.height * m_maxScreenFractionBetweenPoints;
		m_points.Clear();
		if (m_points2D.Count <= 1) return;
		if (m_points2D.Count == 2) {
			AddOutputPoint(m_points2D[0]);
			AddOutputPoint(m_points2D[1]);
			return;
		}
		float fTotal = 0;
		int current = 1;
		float currentPairDistance = (m_points2D[current+1] - m_points2D[current]).magnitude;
		bool complete = false;
		while (true) {
			while (fTotal > currentPairDistance || currentPairDistance < .001f) {
				fTotal -= currentPairDistance;
				++current;
				if (current >= m_points2D.Count-1) {
					complete = true;
					break;
				}
				currentPairDistance = (m_points2D[current+1] - m_points2D[current]).magnitude;
			}
			if (complete) break;
			float f = fTotal / currentPairDistance;
			var next = (current < m_points2D.Count-2) ? m_points2D[current+2] : m_points2D[current+1];
			var interp2D = SplineInterpolate(m_points2D[current-1], m_points2D[current], m_points2D[current+1], next, f);
			if (interp2D.x * 0 != 0) Debug.LogError($"NAN found interpolating {m_points2D[current-1]}, {m_points2D[current]}, {m_points2D[current+1]}, {next}, {f}");
			AddOutputPoint(interp2D);
			fTotal += distancePerPoint;
		}
	}
	void AddOutputPoint(Vector3 _p2d) {
		_p2d.z = 5;
		m_points.Add(Camera.main.ScreenToWorldPoint(_p2d));
	}
	
	public static Vector3 SplineInterpolate(Vector3 _a, Vector3 _b, Vector3 _c, Vector3 _d, float _t) {
		Vector3 a = 2f * _b;
		Vector3 b = _c - _a;
		Vector3 c = 2f * _a - 5f * _b + 4f * _c - _d;
		Vector3 d = -_a + 3f * _b - 3f * _c + _d;
		return 0.5f * (a + (b * _t) + (c * _t * _t) + (d * _t * _t * _t));
	}
	void Fill() {
		RefreshPoints();
		m_line.positionCount = m_points.Count;
		m_line.SetPositions(m_points.ToArray());
	}
	
	public static MouseRibbon Create(Transform _parent, Color _colour, int _maxPoints) {
		var ribbonGO = new GameObject("Ribbon");
		ribbonGO.transform.SetParent(_parent, false);
		var ribbon = ribbonGO.AddComponent<MouseRibbon>();
		ribbon.m_colour = _colour;
		ribbon.m_maxPoints = _maxPoints;
		return ribbon;
	}
}
