using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RandomOscillate : MonoBehaviour
{
    public Vector3 m_oscillationAmplitude;
    public Vector3 m_oscillationFrequency;
    public bool m_randomise;

    private Vector3 m_initialLocalPosition;
    void Start()
    {
        m_initialLocalPosition = transform.localPosition;
    }

    void Update()
    {
        var t = Time.time;
        var tVec = m_oscillationFrequency * t;
        var sin = new Vector3(Mathf.Sin(tVec.x), <PERSON>f.<PERSON>(tVec.y), Mathf.Sin(tVec.z));
        transform.localPosition = m_initialLocalPosition + Vector3.Scale(sin, m_oscillationAmplitude);
    }
}
