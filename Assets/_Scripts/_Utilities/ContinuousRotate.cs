using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ContinuousRotate : MonoBehaviour {
    public Vector3 m_eulerInitial = Vector3.zero;
    public Vector3 m_eulerSpeed = Vector3.forward * 180;
    private Vector3 m_eulers;

    void OnEnable() {
        m_eulers = m_eulerInitial;
    }
    void Update() {
        m_eulers += m_eulerSpeed * Time.unscaledDeltaTime;
        m_eulers.x = Utility.ClampAngle(m_eulers.x);
        m_eulers.y = Utility.ClampAngle(m_eulers.y);
        m_eulers.z = Utility.ClampAngle(m_eulers.z);
        transform.eulerAngles = m_eulers;
    }
}
