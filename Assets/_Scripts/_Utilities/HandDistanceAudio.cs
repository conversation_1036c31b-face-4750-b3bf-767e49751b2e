using UnityEngine;

public class HandDistanceAudio : MonoBehaviour
{
    public AkEventHolder m_audio;
    public AkRTPCHolder m_distanceRTPC;
    private int m_id;
    void Start()
    {
        m_id = m_audio?.Play(gameObject, _allowPreLoad:true) ?? 0;
    }
    void OnDisable()
    {
        if (AudioClipManager.Me != null)
        {
            AudioClipManager.Me.StopSound(m_id, gameObject);
        }
    }

    void Update()
    {
        var cam = Camera.main;
        var handPos = cam.WorldToScreenPoint(PlayerHandManager.Me.Fingertip.position);
        var thisPos = cam.WorldToScreenPoint(transform.position);
        var distance = 2f;
        if (thisPos.z > .2f)
        {
            var toHand = thisPos - handPos;
            toHand.z = 0;
            distance = toHand.magnitude / Screen.height;
        }
        m_distanceRTPC?.Set(distance, gameObject);
    }
}
