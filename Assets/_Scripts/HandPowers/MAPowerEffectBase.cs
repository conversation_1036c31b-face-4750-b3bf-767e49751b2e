using System.Collections;
using System.Collections.Generic;
using System.Dynamic;
using UnityEngine;

public interface IImpactDataSupplier
{
    void PlayImpactAudioOnObject(NGMovingObject _obj);
    void ApplyDamage(IDamageReceiver.DamageSource _source, NGMovingObject _obj, bool _isContinuous, float _multiplier = 1.0f);
}

public interface IPrefabScaler
{
    void SetScale(float _scale);
}

public class MAPowerEffectBase : MonoBehaviour, IImpactDataSupplier
{
    public AkEventHolder m_initAudioEvent = new();
    public AkEventHolder m_noManaAudioEvent = new ();
    public AkEventHolder m_fireAudioEvent = new ();
    public AkEventHolder m_impactAudioEvent = new ();
    public AkEventHolder m_perTargetStrikeAudioEvent = new ();
    public AkEventHolder m_onFinishAudioEvent = new ();
    public AkEventHolder m_onDestroyAudioEvent = new ();
    public string m_targetAnimation;
    public string m_targetRecoverAnimation;
    public Light m_light;
    public float m_lightIntensityBase = 40;
    public float m_handRaise = 0;
    private bool m_haveSentRegistration = false;
    protected string m_type;
    protected int m_audioID;
    protected Transform m_source;
    protected List<Transform> m_targets;
    protected float m_time = 0, m_previousTime = 0;
    protected float m_realTime = 0;
    protected int m_baseIndex = 0;
    protected int m_level = 0;
    private bool m_haveChained = false;
    protected bool m_hasDiscontinued = false;
    protected bool m_couldNotAfford = false;
    protected float m_progress = 0;
    protected virtual GameObject audioFocus => m_source == null ? null : m_source.gameObject;
    protected virtual GameObject impactAudioFocus => null;
    protected MAHandPowerInfo m_info;
    private int m_mouseButton = 0; public int MouseButton => m_mouseButton;
    private float m_startTime;
    private int m_startFrame;
    
    protected void PlayInitialAudio()
    {
        var audio = m_initAudioEvent.Name;
        var noManaAudio = m_noManaAudioEvent.Name;
        if (m_couldNotAfford && string.IsNullOrEmpty(noManaAudio) == false) audio = noManaAudio;
        if (string.IsNullOrEmpty(audio)) return;
        AudioClipManager.Me.PlaySound(audio, audioFocus);
    }

    protected void PlayFireAudio()
    {
        var fireAudio = m_fireAudioEvent.Name;
        if (string.IsNullOrEmpty(fireAudio)) return;
        if (m_audioID > 0) return;
        m_audioID = AudioClipManager.Me.PlaySound(fireAudio, audioFocus);
    }
    
    public void PlayImpactAudioOnObject(NGMovingObject _obj)
    {
        if (_obj == null) return;
        string audio = m_perTargetStrikeAudioEvent.Name;
        if (_obj is not MAWorker && _obj is not MACharacterBase)
            return;
        if (string.IsNullOrEmpty(audio)) return;
        AudioClipManager.Me.PlaySound(audio, _obj.gameObject);
    }

    protected void PlayImpactAudio(int _index)
    {
        var audio = m_impactAudioEvent.Name;
        if (string.IsNullOrEmpty(audio)) return;
        AudioClipManager.Me.PlaySound(audio, impactAudioFocus ?? audioFocus);
    }

    protected void PlayFinishAudio()
    {
        var audio = m_onFinishAudioEvent.Name;
        if (string.IsNullOrEmpty(audio) == false)
            AudioClipManager.Me.PlaySound(audio, audioFocus);
    }

    protected void StopAudio()
    {
        var audio = m_onDestroyAudioEvent.Name;
        if (string.IsNullOrEmpty(audio) == false)
            AudioClipManager.Me.PlaySound(audio, audioFocus);
    }

    protected void TickTime(float _delta = 0)
    {
        if (_delta < .0001f) _delta = Time.deltaTime;
        m_previousTime = m_time;
        m_time += _delta;
        m_realTime += _delta;
    }

    protected bool JustPassedTime(float _t)
    {
        return m_time >= _t && m_previousTime < _t;
    }

    protected int Tick()
    {
        if (m_info.m_tickSpeed <= 0) return 0;
        m_progress += Time.deltaTime;
        if (m_progress > m_info.m_tickSpeed)
        {
            int ticks = (int)(m_progress / m_info.m_tickSpeed);
            m_progress -= ticks * m_info.m_tickSpeed;
            return ticks;
        }
        return 0;
    }

    public void ApplyDamage(IDamageReceiver.DamageSource _source, NGMovingObject _obj, bool _isContinuous, float _multiplier = 1.0f)
    {
        if (_obj == null || m_source == null) return; // was destroyed during damage
        var damage = Mathf.Max(m_info.m_minDamage, m_info.m_baseDamage * _multiplier);
        if (_isContinuous) damage *= Time.deltaTime;
        _obj.ApplyDamageEffect(_source, damage, m_source.position, null, m_info);
    }

    protected void PlayTargetAnimation()
    {
        if (string.IsNullOrEmpty(m_targetAnimation)) return;
        var obj = m_targets[m_baseIndex].GetComponentInChildren<NGMovingObject>();
        if (obj == null) return;
        
        var pauseId = gameObject.Id("Power");
        obj.PlayPowerEffectTargetAnim(pauseId, m_targetAnimation, m_targetRecoverAnimation);
    }

    protected virtual bool NeedsTarget => true;
    
    protected bool HasTarget => m_targets != null && m_targets.Count > 0 && m_couldNotAfford == false;
    protected bool HasTargetAny => m_targets.Count > 0 && m_couldNotAfford == false;
    protected NGMovingObject TargetObject => m_targets.Count > 0 && m_targets[m_baseIndex] != null ? m_targets[m_baseIndex].GetComponentInChildren<NGMovingObject>() : null;
    
    Dictionary<NGMovingObject, AnimationOverride> m_animationLoops = new(); 
    protected void StartTargetAnimationLoop(NGMovingObject _obj)
    {
        if (_obj == null || _obj.m_nav == null) return;
        if (m_animationLoops.ContainsKey(_obj)) return;
        _obj.m_nav.PushPause($"FF{GetInstanceID()}", _freeze: true);
        m_animationLoops[_obj] = AnimationOverride.PlayClip(_obj.GetComponentInChildren<AnimationHandler>().gameObject, m_targetAnimation, (b) => { }, true);
        _obj.GetComponentInChildren<HeadTracker>()?.Stop();
    }

    protected void StopTargetAnimationLoop(NGMovingObject _obj)
    {
        if (_obj == null || _obj.m_nav == null) return;
        if (m_animationLoops.ContainsKey(_obj) == false) return;
        _obj.m_nav.PopPause($"FF{GetInstanceID()}");
        m_animationLoops[_obj].Interrupt(true);
        m_animationLoops.Remove(_obj);
        _obj.GetComponentInChildren<HeadTracker>()?.Resume();
    }

    private bool SpendManaBase(float _amount)
    {
        if (NeedsTarget && m_targets.Count == 0) return true;
        if (GameManager.NoManaCost) return true;
        var state = GameManager.Me.m_state;
        if (_amount > state.m_powerMana) return false;
        if (m_baseIndex == 0)
            state.m_powerMana -= _amount;
        return true;
    }
    protected bool SpendMana() => SpendManaBase(m_info?.m_manaCost ?? 0);
    protected bool SpendManaContinuous(float _multiplier = 1) => SpendManaBase(m_info.m_manaCostPerSecond * _multiplier);
    
    protected void StopCasterAnimation()
    {
        PlayerHandManager.Me.StopCastAnimation();
    }

    protected bool IsPowerContinuing {
        get 
        {
            if (m_hasDiscontinued) return false;
            if (PlayerHandManager.Me.LastPowerCancelFrame >= m_startFrame) return false;
            var isButtonDown = Input.GetMouseButton(MouseButton);
            if (isButtonDown == false) m_hasDiscontinued = true;
            return isButtonDown;
        }
    }

    void OnDestroy()
    {
        if (PlayerHandManager.Me == null) return; // shut down
        PlayerHandManager.Me.SetHandRaise(0);
        StopAudio();
    }
    protected virtual void Activate(MAHandPowerInfo _info, string _type, Transform _source, List<Transform> _targets, int _mouseButton, int _level, int _index = 0)
    {
        m_info = _info;
        m_type = _type;
        m_time = 0;
        m_source = _source;
        m_targets = _targets;
        m_baseIndex = _index;
        m_level = _level;
        m_mouseButton = _mouseButton;
        PlayInitialAudio();
        PlayerHandManager.Me.SetHandRaise(m_handRaise);
        if (HasTarget && NeedsTarget) RegisterSuccessfulTrigger();
        m_startTime = Time.unscaledTime;
        m_startFrame = Time.frameCount;
    }

    protected void FireNext(bool _allowChaining)
    {
        if (m_haveChained || m_baseIndex >= m_targets.Count - 1) return;
        m_haveChained = true;
        Duplicate(_allowChaining);
    }

    protected void RegisterSuccessfulTrigger()
    {
        if (m_haveSentRegistration) return;
        m_haveSentRegistration = true;
        PlayerHandManager.Me.RegisterPowerTrigger(m_type);
    }

    protected MAPowerEffectBase Duplicate(bool _allowChaining)
    {
        var instance = Instantiate<MAPowerEffectBase>(this, transform.parent);
        var source = m_source;
        if (_allowChaining)
        {
            if (m_targets[m_baseIndex + 1] == null) return null;
            var targetPos = m_targets[m_baseIndex + 1].position;
            for (int i = 0; i <= m_baseIndex; ++i)
                if (m_targets[i] != null && (m_targets[i].position - targetPos).sqrMagnitude < (source.position - targetPos).sqrMagnitude)
                    source = m_targets[i];
        }
        instance.Activate(m_info, m_type, source, m_targets, m_mouseButton, m_level, m_baseIndex + 1);
        return instance;
    }

    public void SetLightDamping(bool _damp)
    {
        Marcos_Procedural_Sky.Me.SetDarkening(this, _damp ? NGManager.Me.m_handPowerLightingDampen : 0);
    }

    private static bool s_showAreaOfEffectVisual = false;
    private static DebugConsole.Command s_showAreaOfEffectVisualCommand = new DebugConsole.Command("aoevisual", (_s) => Utility.SetOrToggle(ref s_showAreaOfEffectVisual, _s), "Show Area of Effect visual", "<bool>");
    
    private static int s_explosionID = 1;
    public static void CreateExplosionAtPoint(IDamageReceiver.DamageSource _source, Vector3 _position, float _radius, float _power, IImpactDataSupplier _dataSupplier, Vector3 _incomingDirection = default, GameObject _exclude = null, string _debugLabel = null, float _damageMultiplier = 1f)
    {
        if (_debugLabel != null)
        {
            GameManager.Me.ClearGizmos(_debugLabel);
            GameManager.Me.AddGizmoPoint(_debugLabel, _position, _radius, new Color(1, 1, .5f, .2f));
            GameManager.Me.AddGizmoLabel(_debugLabel, _position, $"{_power:n1}", new Color(1, 1, 1, 1));
        }
        
        CreateAreaOfEffectVisual(_position, _radius);
        
        var colliders = Physics.OverlapSphere(_position, _radius);
        HashSet<NGMovingObject> hitObjects = new();
        // RW-23-MAY-25: If a creature is hit by the rock, it shouldn't damage heroes or workers.
        bool friendlyFireOn = true;
        foreach (var collider in colliders)
        {
            var rb = collider.attachedRigidbody;
            if (rb == null) continue;
            var obj = rb.GetComponentInParent<NGMovingObject>();
            var creature = obj as MACreatureBase;
            if(creature != null)
            {
                friendlyFireOn = false;
                break;
            }
        }

        foreach (var collider in colliders)
        {
            var rb = collider.attachedRigidbody;
            if (rb == null) continue;
            if (rb.gameObject.layer == LayerMask.NameToLayer("RagDollMovingObject"))
            {
                if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, rb.transform.position, .5f, new Color(1, 0, 0, 1));
                continue;
            }
            if (_exclude != null && rb.transform.IsChildOf(_exclude.transform))
            {
                if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, rb.transform.position, .5f, new Color(0, 0, 1, 1));
                continue;
            }
            if (rb.transform.parent != null && rb.transform.parent.gameObject.GetComponentInParent<Rigidbody>() != null) continue; // has parent rb, skip this one
            float distance = (_position - rb.transform.position).magnitude;
            float falloff = (1 - distance / _radius);
            float finalPower = _power * falloff;
            var obj = rb.GetComponentInParent<NGMovingObject>();
            var worker = obj as MAWorker;
            if(worker != null)
            {
                if(worker.IsInPrisonerState)
                {
                    worker.TauntPrisoner();
                    continue;
                }
            }
            if (obj != null)
            {
                if (hitObjects.Contains(obj)) continue;
                var character = obj as MACharacterBase;
                if (character != null && character.IsAnyDeadState) continue;
                var hero = character as MAHeroBase;
                if (!friendlyFireOn && (worker != null || hero != null))
                {
                    continue;
                }
                hitObjects.Add(obj);
                if (finalPower > 5)
                {
                    if (_incomingDirection.sqrMagnitude < .001f * .001f) _incomingDirection = Random.onUnitSphere.GetXZ();
                    else _incomingDirection = _incomingDirection.GetXZNorm();
                    _dataSupplier.PlayImpactAudioOnObject(obj);
                    _dataSupplier.ApplyDamage(_source, obj, false, falloff * _damageMultiplier);
                    if (_debugLabel != null)
                    {
                        GameManager.Me.AddGizmoPoint(_debugLabel, rb.transform.position, .5f, new Color(0, 1, 0, 1));
                        GameManager.Me.AddGizmoLabel(_debugLabel, rb.transform.position, $"{falloff:n1}", new Color(1, 1, 1, 1));
                    }
                    var explosionID = $"Explosion{s_explosionID++}";
                    if (obj.m_nav != null && falloff > .5f)
                    {
                        obj.m_nav.PushPause(explosionID, false, true);
                        //Debug.LogError($"{obj.name} explosion ragdoll {explosionID}");
                        Vector3 ragdollVelocity = ((obj.transform.position + _incomingDirection - _position).GetXZNorm() + Vector3.up) * (finalPower * 0.2f);
                        obj.ActivateRagDoll(ragdollVelocity, (System.Action<bool>)((_interrupted) => {
                            //Debug.LogError($"{obj.name} explosion ragdoll end {explosionID}");
                            obj.m_nav.PopPause(explosionID);
                        }));
                        
                        if (character != null && character.IsUnconscious() == MACharacterBase.Consciousness.Conscious &&
                            character.StateLibrary().ContainsKey(CharacterStates.KnockedDown))
                            MACharacterStateFactory.ApplyCharacterState(CharacterStates.KnockedDown, character);                        
                    }
                }
                else if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, rb.transform.position, .5f, new Color(1, 0, 1, 1));
            }
            else
            {
                if (rb.GetComponentInParent<PathBreakExplode>() != null && rb.isKinematic) continue; // controlled by PBE
                if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, rb.transform.position, .5f, new Color(1, 1, 0, 1));
                rb.isKinematic = false;
                rb.AddExplosionForce(_power, _position, _radius, finalPower);
            }
        }
    }

    public static void CreateAreaOfEffectVisual(Vector3 _position, float _radius)
    {
        if (NGManager.Me.m_areaOfImpactPrefab == null) return;
        var obj = Instantiate(NGManager.Me.m_areaOfImpactPrefab, _position, Quaternion.Euler(0, Random.Range(0, 360), 0));
        var scaler = obj.GetComponent<IPrefabScaler>();
        if (scaler != null) scaler.SetScale(_radius);
        //if (s_showAreaOfEffectVisual)
        //    GlobalData.Me.StartCoroutine(Co_FadeIndicator(_position, _radius));
    }

    private static IEnumerator Co_FadeIndicator(Vector3 _position, float _radius)
    {
        var _indicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        Destroy(_indicator.GetComponent<Collider>());
        _indicator.transform.position = _position;
        var mat = new Material(GlobalData.Me.m_areaOfEffectMaterial);
        _indicator.GetComponent<MeshRenderer>().material = mat;
        _indicator.transform.localScale = Vector3.one * _radius;
        var initialColour = mat.GetColor("_Color");
        const float c_duration = .5f;
        for (float t = 0; t < c_duration; t += Time.deltaTime)
        {
            var f = t / c_duration;
            var r = Mathf.Lerp(_radius, 0, f);
            _indicator.transform.localScale = Vector3.one * r;
            mat.SetColor("_Color", initialColour * (1 - f));
            yield return null;
        }
        Destroy(_indicator);
    }

    public static MAPowerEffectBase Create(string _type, MAHandPowerInfo _info, Transform _source, List<Transform> _targets, int _mouseButton, int _level)
    {
        var effect = Resources.Load<MAPowerEffectBase>($"PowerEffects/{_type}");
        var instance = Instantiate<MAPowerEffectBase>(effect);
        instance.Activate(_info, _type, _source, _targets, _mouseButton, _level);
        instance.transform.SetParent(PlayerHandManager.Me.transform, true);
        return instance;
    }
}
