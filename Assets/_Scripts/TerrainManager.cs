using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TerrainManager : MonoBehaviour {
	public static TerrainManager Me;
	void Awake() { Me = this; }
	public MouseRibbon m_ribbon;
#if true
	public bool AllBlocksReady => true;
	public float Progress => 1;

	public void Load() {}
	public void PostLoad() {}
	
	public bool IsVisible(Vector3 _pos) => true;
	public void ClearAllTerrainBlocks() {}

	public void Regenerate(Vector3 _min, Vector3 _max) {}
	public void RegenerateRoads(Vector3 _from, Vector3 _to) {}
	public void RegenerateDegrass(Vector3 _from, Vector3 _to) {}


	public enum MeshBlend
	{
		None,
		Edge,
		Circular
	}
	public Mesh GetLandscapeMesh(Vector3 _center, float _halfWidth, float _halfHeight, float _maxScaleU, float _maxScaleV, int _stepSize = 1, float _raise = 0, MeshBlend _raiseBlend = MeshBlend.None, int _alphaBlendSize = 0, bool _calculateTangents = false, bool _ignoreRivers = false)
	{
		var mesh = new Mesh();
		int w = (int) _halfWidth * 2 + 1;
		int h = (int) _halfHeight * 2 + 1;
		var verts = new Vector3[w * h];
		var uvs = new Vector2[w * h];
		var colors = _alphaBlendSize > 0 ? new Color[w * h] : null;
		int cornerX = Mathf.RoundToInt(_center.x) - w / 2, cornerZ = Mathf.RoundToInt(_center.z) - h / 2;
		var corner = _center - Vector3.right * (float) (w / 2) - Vector3.forward * (float) (h / 2);
		for (int z = 0; z < h; ++z)
		{
			for (int x = 0; x < w; ++x)
			{
				var p = corner;
				p.x += (float) x;
				p.z += (float) z;
				p.y = GlobalData.Me.GetRawHeight(cornerX + x, cornerZ + z) + _raise;
				verts[x + z * w] = p;
				uvs[x + z * w] = new Vector2((float) x / (float) (w - 1), (float) z / (float) (h - 1));
				if (_alphaBlendSize > 0)
				{
					// Color fade
					int minLevel = Mathf.Min(x, z);
					int maxLevel = Mathf.Max(x, z);
					float lowerFade = (float) minLevel / _alphaBlendSize;
					float upperFade = (float) (w - 1 - maxLevel) / _alphaBlendSize;
					float alpha = Mathf.Min(1, Mathf.Min(lowerFade, upperFade));
					colors[x + z * w] = new Color(1, 1, 1, alpha);
				}
			}
		}
		var inds = new int[(w - 1) * (h - 1) * 6];
		int next = 0;
		for (int z = 0; z < h - 1; ++z)
		{
			for (int x = 0; x < w - 1; ++x)
			{
				inds[next++] = (x + 0) + (z + 0) * w;
				inds[next++] = (x + 0) + (z + 1) * w;
				inds[next++] = (x + 1) + (z + 0) * w;
				inds[next++] = (x + 1) + (z + 0) * w;
				inds[next++] = (x + 0) + (z + 1) * w;
				inds[next++] = (x + 1) + (z + 1) * w;
			}
		}
		mesh.vertices = verts;
		mesh.uv = uvs;
		mesh.colors = colors;
		mesh.SetIndices(inds, MeshTopology.Triangles, 0);
		mesh.RecalculateBounds();
		mesh.RecalculateNormals();
		mesh.UploadMeshData(true);
		return mesh;
	}	
#else
	//
	GameObject m_root;
	public void Load() {}
	public void PostLoad() {
		m_root = new GameObject("Terrain");
		m_root.transform.SetParent(GameManager.Me.transform.parent);
	}

	Dictionary<int, TerrainBlock> m_blocks = new Dictionary<int, TerrainBlock>();
	HashSet<int> m_initialisedBlocks = new HashSet<int>();
	int m_currentBlocksX = -1, m_currentBlocksZ = -1, m_currentBlocksRange = -1;
	public void ClearAllTerrainBlocks() {
		foreach (var kvp in m_blocks) {
			kvp.Value.DisposeAll();
			Destroy(kvp.Value.gameObject);
		}
		m_blocks.Clear();
	}
	public bool IsVisible(Vector3 _pos) {
		int x = Mathf.FloorToInt(_pos.x / TerrainBlock.c_chunkSize);
		int z = Mathf.FloorToInt(_pos.z / TerrainBlock.c_chunkSize);
		int id = (x + 512 / TerrainBlock.c_chunkSize) + (z + 512 / TerrainBlock.c_chunkSize) * 65536;
		return m_blocks.ContainsKey(id);
	}
	
	int m_overrideBlockRange = -1;
	static DebugConsole.Command s_overrideRange = new DebugConsole.Command("overriderange", _s => Me.m_overrideBlockRange = int.Parse(_s));
	
	bool m_lastGeneratedSmoothedHeightfield;
	bool m_allBlocksReady = false; public bool AllBlocksReady => m_allBlocksReady;
	float m_blockLoadProgress = 0; public float Progress => m_blockLoadProgress;
	void Update() {
		if (!GameManager.Me.DataReady) return; // don't update terrain blocks until we've finished loading

		var camPos = GameManager.Me.m_camera.transform.position;
		RaycastHit hit;
		int range = 4;
		if (GameManager.Me.RaycastAtPoint(new Vector3(Screen.width*.5f,Screen.height*.5f), out hit, GameManager.c_layerTerrainBit)) {
			range = Mathf.CeilToInt(Mathf.Sqrt((camPos - hit.point).magnitude) * .25f) + 1;
			camPos = hit.point;
		}
		range = 12;
		if (m_overrideBlockRange >= 0) range = m_overrideBlockRange;
		int blockX = Mathf.FloorToInt(camPos.x / TerrainBlock.c_chunkSize);
		int blockZ = Mathf.FloorToInt(camPos.z / TerrainBlock.c_chunkSize);
		if (blockX != m_currentBlocksX || blockZ != m_currentBlocksZ || range != m_currentBlocksRange) {
			m_currentBlocksX = blockX;
			m_currentBlocksZ = blockZ;
			m_currentBlocksRange = range;
			var newBlocks = new Dictionary<int, TerrainBlock>();
			var blockHalfWH = 512 / TerrainBlock.c_chunkSize; 
			var minZ = Mathf.Max(-blockHalfWH, blockZ - range);
			var maxZ = Mathf.Min(blockHalfWH - 1, blockZ + range);
			var minX = Mathf.Max(-blockHalfWH, blockX - range);
			var maxX = Mathf.Min(blockHalfWH - 1, blockX + range);
			for (int z = minZ; z <= maxZ; ++z) {
				for (int x = minX; x <= maxX; ++x) {
					int id = (x + blockHalfWH) + (z + blockHalfWH) * 65536;
					TerrainBlock block;
					if (!m_blocks.TryGetValue(id, out block)) {
						int bx = x * TerrainBlock.c_chunkRes, bz = z * TerrainBlock.c_chunkRes;
						var go = new GameObject($"Block_{x}_{z}");
						go.transform.SetParent(m_root.transform);
						block = go.AddComponent<TerrainBlock>();
						block.Set(bx, bz, m_initialisedBlocks.Contains(id));
						go.transform.position = new Vector3(bx, 0, bz) * TerrainBlock.GlobalScale;
					}
					newBlocks[id] = block;
					m_initialisedBlocks.Add(id);
				}
			}
			foreach (var kvp in m_blocks) {
				if (!newBlocks.ContainsKey(kvp.Key)) {
					Destroy(kvp.Value.gameObject);
				}
			}
			m_blocks = newBlocks;

		}
		m_allBlocksReady = true;
		int blocksTotal = 0, blocksOutstanding = 0;
		foreach (var kvp in m_blocks) {
			++ blocksTotal;
			if (kvp.Value.IsGenerating) {
				m_allBlocksReady = false;
				++ blocksOutstanding;
			}
		}
		if (blocksTotal > 0)
			m_blockLoadProgress = 1f - (float)blocksOutstanding / (float)blocksTotal;
	}

	public void Regenerate(Vector3 _min, Vector3 _max)
	{
		int x1 = ((int)_min.x + 512) / TerrainBlock.c_chunkSize, z1 = ((int)_min.z + 512) / TerrainBlock.c_chunkSize;
		int x2 = ((int)_max.x + 512) / TerrainBlock.c_chunkSize, z2 = ((int)_max.z + 512) / TerrainBlock.c_chunkSize;
		for (int z = z1; z <= z2; ++z)
		{
			for (int x = x1; x <= x2; ++x)
			{
				int id = x + z * 65536;
				if (m_blocks.TryGetValue(id, out var block)) block.SetDirty();
			}
		}
		RegenerateRoads(_min, _max);
	}
	
	public enum MeshBlend {
		None,
		Edge,
		Circular
	}
	public Mesh GetLandscapeMesh(Vector3 _center, float _halfWidth, float _halfHeight, float _maxScaleU, float _maxScaleV, int _stepSize = 1, float _raise = 0, MeshBlend _raiseBlend = MeshBlend.None, int _alphaBlendSize = 0, bool _calculateTangents = false, bool _ignoreRivers = false) {
		var mesh = new Mesh();
		int w =(int)_halfWidth * 2 + 1;
		int h = (int)_halfHeight * 2 + 1;
		var verts = new Vector3[w * h];
		var uvs = new Vector2[w * h];
		var colors = _alphaBlendSize > 0 ? new Color[w * h] : null;
		int cornerX = Mathf.RoundToInt(_center.x) - w / 2, cornerZ = Mathf.RoundToInt(_center.z) - h / 2;
		var corner = _center - Vector3.right * (float)(w / 2) - Vector3.forward * (float)(h / 2); 
		for (int z = 0; z < h; ++z) {
			for (int x = 0; x < w; ++x) {
				var p = corner; p.x += (float)x; p.z += (float)z;
				p.y = GlobalData.Me.GetRawHeight(cornerX+x, cornerZ+z) + _raise;
				verts[x+z*w] = p;
				uvs[x+z*w] = new Vector2((float)x / (float)(w-1), (float)z / (float)(h-1));
				if (_alphaBlendSize > 0) {
					// Color fade
					int minLevel = Mathf.Min(x,z);
					int maxLevel = Mathf.Max(x,z);
					float lowerFade = (float)minLevel / _alphaBlendSize;
					float upperFade = (float)(w - 1 - maxLevel) / _alphaBlendSize;
					float alpha = Mathf.Min(1, Mathf.Min(lowerFade, upperFade));
					colors[x+z*w] = new Color(1,1,1,alpha);
				}
			}
		}
		var inds = new int[(w-1)*(h-1)*6];
		int next = 0;
		for (int z = 0; z < h-1; ++z) {
			for (int x = 0; x < w-1; ++x) {
				inds[next++] = (x+0) + (z+0) * w;
				inds[next++] = (x+0) + (z+1) * w;
				inds[next++] = (x+1) + (z+0) * w;
				inds[next++] = (x+1) + (z+0) * w;
				inds[next++] = (x+0) + (z+1) * w;
				inds[next++] = (x+1) + (z+1) * w;
			}
		}
		mesh.vertices = verts;
		mesh.uv = uvs;
		mesh.colors = colors;
		mesh.SetIndices(inds, MeshTopology.Triangles, 0);
		mesh.RecalculateBounds();
		mesh.RecalculateNormals();
		mesh.UploadMeshData(true);
		return mesh;
	}

	public void RegenerateRoads(Vector3 _from, Vector3 _to) {
		CameraRenderSettings.Me.BeginDetailOps();
		int fx = Mathf.FloorToInt((_from.x + 512) / TerrainBlock.c_chunkSize);
		int fz = Mathf.FloorToInt((_from.z + 512) / TerrainBlock.c_chunkSize);
		int tx = Mathf.FloorToInt((_to.x + 512) / TerrainBlock.c_chunkSize);
		int tz = Mathf.FloorToInt((_to.z + 512) / TerrainBlock.c_chunkSize);
		if (fx > tx) { int t = fx; fx = tx; tx = t; }
		if (fz > tz) { int t = fz; fz = tz; tz = t; }
		++tx; ++tz;
		for (int z = fz; z < tz; ++z) {
			for (int x = fx; x < tx; ++x) {
				int id = x + z * 65536;
				TerrainBlock block;
				if (m_blocks.TryGetValue(id, out block)) {
					block.GenerateRoads(true);
				}
			}
		}
		CameraRenderSettings.Me.EndDetailOps();
		GlobalData.Me.UpdateNavGrid(fx * TerrainBlock.c_chunkRes,fz * TerrainBlock.c_chunkRes, tx * TerrainBlock.c_chunkRes,tz * TerrainBlock.c_chunkRes);
	}

	public void RegenerateDegrass(Vector3 _from, Vector3 _to) {
		CameraRenderSettings.Me.BeginDetailOps();
		int fx = Mathf.FloorToInt((_from.x + 512) / TerrainBlock.c_chunkSize);
		int fz = Mathf.FloorToInt((_from.z + 512) / TerrainBlock.c_chunkSize);
		int tx = Mathf.FloorToInt((_to.x + 512) / TerrainBlock.c_chunkSize);
		int tz = Mathf.FloorToInt((_to.z + 512) / TerrainBlock.c_chunkSize);
		if (fx > tx) { int t = fx; fx = tx; tx = t; }
		if (fz > tz) { int t = fz; fz = tz; tz = t; }
		++tx; ++tz;
		for (int z = fz; z < tz; ++z) {
			for (int x = fx; x < tx; ++x) {
				int id = x + z * 65536;
				TerrainBlock block;
				if (m_blocks.TryGetValue(id, out block)) {
					block.GenerateDegrass();
				}
			}
		}
		CameraRenderSettings.Me.EndDetailOps();
	}
#endif
}
