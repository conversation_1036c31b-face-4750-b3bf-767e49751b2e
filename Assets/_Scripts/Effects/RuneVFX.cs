using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuneVFX : MonoBehaviour
{
    public AkRTPCHolder m_onTriggerPowerRTPC;
    public AkEventHolder m_onTriggerAudio;
    public AkRTPCHolder m_onImpactPowerRTPC;
    public AkEventHolder m_onImpactPushbackAudio;
    public AkEventHolder m_onImpactThrowAudio;

    public GameObject m_movingObject;
    public int m_movingObjectRepeats = 0;
    public float m_movingObjectTotalSpread = 120;
    public float m_initialForwardMovementAtBase = 1;
    public float m_initialForwardMovementAtTop = 1;
    public float m_finalForwardMovementAtBase = 15;
    public float m_finalForwardMovementAtTop = 15;
    public float m_riseTime = 0;
    public float m_riseDistance = 0;
    private float m_initialForwardMovement;
    private float m_finalForwardMovement;
    
    private GameObject[] m_allMovingObjects;
    private Vector3[] m_allMovingObjectDirections;

    [System.Serializable]
    public class ParticleModifier
    {
        public enum EParticleModifierParameter
        {
            VelocityDampen,
            ShapeRadius,
        }
        public ParticleSystem m_system;
        public EParticleModifierParameter m_parameter;
        public float m_atBase;
        public float m_atTop;

        public void Apply(float _at)
        {
            var value = Mathf.Lerp(m_atBase, m_atTop, _at);
            switch (m_parameter)
            {
                case EParticleModifierParameter.VelocityDampen:
                    var limit = m_system.limitVelocityOverLifetime;
                    limit.dampen = value;
                    break;
                case EParticleModifierParameter.ShapeRadius:
                    var shape = m_system.shape;
                    shape.radius = value;
                    break;
            }
        }

        public static void ApplyAll(float _at, ParticleModifier[] _mods)
        {
            if (_mods == null) return;
            foreach (var mod in _mods) mod.Apply(_at);
        }
    }
    
    public ParticleModifier[] m_particleModifiers;

    private Vector3 m_initialPosition;
    private Quaternion m_rotation;
    private float m_progress, m_powerModifier;
    private Transform m_parent;
    private float m_startTime;
    public void Activate(Transform _parent, Vector3 _dir, float _initialProgress, float _powerMultiplier)
    {
        m_startTime = Time.time;
        
        m_parent = _parent;
        transform.position = _parent.position;
        transform.rotation = Quaternion.LookRotation(_dir);
        m_initialPosition = transform.position;

        m_initialForwardMovement = Mathf.Lerp(m_initialForwardMovementAtBase, m_initialForwardMovementAtTop, _initialProgress);
        m_finalForwardMovement = Mathf.Lerp(m_finalForwardMovementAtBase, m_finalForwardMovementAtTop, _initialProgress);

        m_rotation = transform.rotation;
        m_progress = 0;
        m_powerModifier = (1 - _initialProgress * .4f) * _powerMultiplier;

        ParticleModifier.ApplyAll(_initialProgress, m_particleModifiers);
        
        m_allMovingObjects = new GameObject[1 + m_movingObjectRepeats];
        m_allMovingObjects[0] = m_movingObject;
        for (int i = 0; i < m_movingObjectRepeats; ++i)
            m_allMovingObjects[i + 1] = Instantiate(m_movingObject, m_movingObject.transform.parent);
        m_allMovingObjectDirections = new Vector3[1 + m_movingObjectRepeats];
        var spread = m_movingObjectRepeats == 0 ? 0 : m_movingObjectTotalSpread;
        for (int i = 0; i <= m_movingObjectRepeats; ++i)
        {
            var fraction = i / (float)m_movingObjectRepeats;
            var angle = spread * (fraction - .5f);
            m_allMovingObjectDirections[i] = transform.forward.RotateAbout(Vector3.up, angle * Mathf.Deg2Rad);
        }

        m_onTriggerPowerRTPC.Set(m_powerModifier, gameObject);
        m_onTriggerAudio.Play(gameObject);
        
        Set();
        StartCoroutine(Co_Fire());
    }

    private void Set()
    {
        float riseDistance = 0;
        if (m_riseTime > 0 && m_riseDistance > 0)
        {
            var elapsedTime = Time.time - m_startTime;
            var riseProgress = Mathf.Clamp01(elapsedTime / m_riseTime);
            riseDistance = (riseProgress - 1) * m_riseDistance; // -m_riseDistance to 0 over m_riseTime seconds
        }

        transform.rotation = m_rotation;
        for (int i = 0; i < m_allMovingObjects.Length; ++i)
        {
            var obj = m_allMovingObjects[i];
            if (obj == null) continue;
            var direction = m_allMovingObjectDirections[i];
            var distance = Mathf.Lerp(m_initialForwardMovement, m_finalForwardMovement, m_progress);
            var pos = m_initialPosition + direction * distance;
            obj.transform.position = pos.GroundPosition(riseDistance);
        }
    }

    private IEnumerator Co_Fire()
    {
        while (m_progress < 1)
        {
            const float c_completeTime = .7f;
            m_progress += Time.deltaTime * (1.0f / c_completeTime);
            Set();
            // AffectCharacters();
            yield return null;
        }
    }

    HashSet<NGMovingObject> m_damagedThisFrame = new();
    HashSet<NGMovingObject> m_damagedWithThrow = new();
    HashSet<NGMovingObject> m_damagedWithPushback = new();
    
    private void PlayDamageAudio(NGMovingObject _obj, bool _throw, float _power)
    {
        var set = _throw ? m_damagedWithThrow : m_damagedWithPushback;
        if (set.Add(_obj) == false) return;
        m_onImpactPowerRTPC.Set(m_powerModifier, _obj.gameObject);
        (_throw ? m_onImpactThrowAudio : m_onImpactPushbackAudio).Play(_obj.gameObject);
    }

    private void AffectCharacters()
    {
        m_damagedThisFrame.Clear();
        var hits = Physics.OverlapSphere(transform.position, transform.localScale.x);
        var runeID = $"rune{GetInstanceID()}_{Time.frameCount}";
        foreach (var hit in hits)
        {
            var obj = hit.GetComponentInParent<NGMovingObject>();
            if (obj == null) continue;
            if (m_parent == obj.transform) continue;
            if (m_damagedThisFrame.Contains(obj)) continue;
            m_damagedThisFrame.Add(obj);

            var toObj = obj.transform.position - transform.position;
            var distance = toObj.magnitude;
            var direction = toObj / distance;
            if (Vector3.Dot(transform.forward, direction) < 0) continue;
            const float c_maxRagdollSpeed = 50;
            var powerBase = 1 - distance / m_finalForwardMovement;
            var speed = powerBase * m_powerModifier * c_maxRagdollSpeed;
            bool willBeRagdolled = speed >= 20;
            obj.m_nav.PushPause(runeID, false, willBeRagdolled);
            if (!willBeRagdolled)
            {
                PlayDamageAudio(obj, false, powerBase);
                obj.transform.LookAt(transform.position, Vector3.up);
                obj.PlayRuneEffecStaggerAnim(runeID);
            }
            else
            {
                PlayDamageAudio(obj, true, powerBase);
                Vector3 ragdollVelocity = (direction + Vector3.up * .8f) * speed;
                obj.ActivateRagDoll(ragdollVelocity, (System.Action<bool>) ((_b) => {
                    obj.m_nav.PopPause(runeID);
                }));
            }
        }
    }

    public static RuneVFX Create(string _type, Transform _parent, Vector3 _dir, float _initialProgress, float _powerMultiplier)
    {
        var prefab = Resources.Load<RuneVFX>($"RuneShield{_type}");
        var rfx = Instantiate(prefab);
        rfx.Activate(_parent, _dir, _initialProgress, _powerMultiplier);
        return rfx;
    }
}
