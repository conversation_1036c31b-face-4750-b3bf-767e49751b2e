using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BlockCage : DragBase
{
    public Transform m_contents;
    public Quaternion CurrentRotation => transform.rotation;
    
    public override void OnDragStart()
    {
        float bestContentHitDist = 1e23f;
        DragBase bestContentHit = null;
        var hits = Physics.RaycastAll(RayAtInputPosition(), 100, -1);
        foreach (var hit in hits)
        {
            if (hit.distance < bestContentHitDist && hit.collider.transform.IsChildOf(m_contents))
            {
                var hitDrag = hit.collider.gameObject.GetComponentInParent<DragBase>();
                if (hitDrag != null)
                {
                    bestContentHitDist = hit.distance;
                    bestContentHit = hitDrag;
                }
            }
        }
        TransferDrag(bestContentHit);
        if (bestContentHit != null)
        {
            bestContentHit.RemoveRigidbody();
            bestContentHit.transform.SetParent(null, true);
        }
    }

    public void AddContent(GameObject _obj)
    {
        _obj.transform.SetParent(m_contents, true);
        var rb = _obj.GetComponent<Rigidbody>();
        if (rb == null)
            rb = _obj.AddComponent<Rigidbody>();
        rb.isKinematic = false;
        rb.useGravity = false;
        rb.linearVelocity = Vector3.zero;
        rb.angularVelocity = Vector3.zero;
        rb.sleepThreshold = .1f;
    }

    void Update()
    {
        foreach (var rb in m_contents.GetComponentsInChildren<Rigidbody>())
        {
            var gravity = Physics.gravity;
            rb.AddForce(transform.up * gravity.y * .5f);
            if (rb.position.y < transform.position.y - 1)
            {
                rb.transform.SetParent(GlobalData.Me.m_buildingHolder, true);
                rb.useGravity = true;
                DesignTableManager.ConvertToWildBlock(rb.gameObject);
            }
        }
    }
}
