#define BYPASS_PRODUCT_SCREEN
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using SaveContainers;
using TMPro;
using UnityEngine;
using Color = UnityEngine.Color;
using Random = System.Random;
using Vector2 = UnityEngine.Vector2;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(DesignTableManager))]
public class DesignTableManagerEditor : Editor {
	public override void OnInspectorGUI() {
		var dtm = target as DesignTableManager;
		base.OnInspectorGUI();
		if (dtm != null)
			dtm.DesignAsGUI();
		// Misc debug
		LocalDb.EditorDebugCommand();
	}
}
#endif
public partial class DesignTableManager : MonoSingleton<DesignTableManager>, IDTDragTrigger {
	[System.Serializable]
	public enum DESIGN_CATEGORY {
		NONE,
		PRODUCT,
		FACTORY,
		HOUSE,
		FUNITURE,
		CIVIC,
		RESEARCH_LAB,
		PACKAGING,
		PRODUCT_SELECTION,
		PIP_UNLOCK,
		TUTORIAL_PRODUCT,
		AVATAR,
		LAST,
	}

	public Camera m_designCamera;
	public GameObject m_tableRoot;
	public GameObject m_turntable;
	public GameObject m_rarityPlanePrefab;
	public GameObject m_blockLabelPrefab;
	public GameObject m_blockBuffPrefab;
	public Transform m_designTableUI;
	public TMPro.TextMeshProUGUI m_categoryLabel;
	GameObject m_blockHolder, m_obstacleHolder;
	public Transform BlockHolder => m_blockHolder.transform;
	public float m_widthBasedFoV = 60;
	public TMPro.TextMeshProUGUI[] m_drawerLabels;
	public ProductInfoItem[] m_productInfoItemPrefabs;
	public GameObject m_paintbrushCursor;
	public GameObject m_textureCursor;
	public GameObject m_eraserCursor;
	public GameObject m_eraserPrefab;
	
	public DTPriceVisual m_designTablePriceVisualPrefab;

	public Material m_stickerMaterial;
	public GameObject m_stickerControls;

	public Color m_blockLabelBorder_Normal = new Color(1, .3f, .3f);
	public Color m_blockLabelBorder_Value = new Color(.1f, .8f, .1f);
	public Color m_blockLabelBorder_Costs = new Color(.8f, .1f, .1f);
	public Color m_blockLabelBorder_Function = new Color(.1f, .1f, .8f);
	
	public static Action<int> OnProductBlocksChanged;
	private static Action OnPaintAdded;
	private static Action OnPatternAdded;
	private static Action OnStickerAdded;
	private static Action<string> OnNewPartPlaced;
	private static Action OnDesignConfirmed;
	private static Action<int> OnDrawerOpened;
	private static Action OnReturnToProductSelectionScreen;
	private static Action OnUndo;
	private static Action OnRedo;
	
	public static Action OnRefreshInfo;
	
	private int m_ambientSounds;

	public bool IsTableAnimating => false;

	public bool m_onboardingRemovePartsAllowed = true;
    public bool m_lockBlocksOnBuildings = false;
    
    static DebugConsole.Command s_lockBuildingsCmd = new DebugConsole.Command("lockbuildings", _s => Utility.SetOrToggle(ref Me.m_lockBlocksOnBuildings, _s));
    
    private int m_turningSound;
	private int m_bezSound;
	
	GameState_Design m_origionalDesign;
	GameState_Design m_currentDesign;
	
	private bool m_grabbingIsBlockedByTutorial = false;
	private string m_nextDesignDrawerState = "";

	public void SetNextDesignDrawerState(string _s)
	{
		m_nextDesignDrawerState = _s;
	}

	GameObject m_turntableVisual; public GameObject TurntableVisual => m_turntableVisual;
	public GameObject m_turntableOrigin;
	private GameObject m_turntableTop;
	private MonoBehaviour m_coroutineOwner = null;
	protected override void _Awake() {
		gameObject.SetActive(false);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		m_turntableVisual = m_turntable.GetComponentInChildren<DragToRotate>().gameObject;
		m_turntableTop = m_turntableVisual.transform.FindChildRecursiveByName("TurnTableTop").gameObject;
		m_turntableOrigin = new GameObject("Origin");
		m_turntableOrigin.transform.parent = m_turntableVisual.transform;
		m_turntableOrigin.transform.localPosition = Vector3.forward * -1;
#endif

		OnProductBlocksChanged += (activeBlockCount) => { Debug.Log($"DesignTableManager - OnProductBlocksChanged - activeBlockCount {activeBlockCount}"); };
		OnPaintAdded += () => { Debug.Log($"DesignTableManager - OnPaintAdded"); }; 
		OnPatternAdded += () => { Debug.Log($"DesignTableManager - OnPatternAdded"); };
		OnStickerAdded += () => { Debug.Log($"DesignTableManager - OnStickerAdded"); };
		OnNewPartPlaced += (partId) => { Debug.Log($"DesignTableManager - OnNewPartPlaced - partId {partId}"); };
		OnDesignConfirmed += () => { Debug.Log($"DesignTableManager - OnDesignConfirmed"); };
		OnDrawerOpened += (iDrawer) => { Debug.Log($"DesignTableManager - OnDrawerConfirmed"); };
		OnUndo += () => { DesignTableAnalytics.m_lastAction = DesignTableAnalytics.kUndo; Debug.Log($"DesignTableManager - OnUndo"); };
		OnRedo += () => { DesignTableAnalytics.m_lastAction = DesignTableAnalytics.kRedo; Debug.Log($"DesignTableManager - OnRedo"); };
		OnReturnToProductSelectionScreen += () => { Debug.Log($"DesignTableManager - OnReturnToProductSelectionScreen"); };
		OnRefreshInfo += () =>
		{
			Debug.Log($"DesignTableManager - OnRefreshInfo");
			SendAnalyticsEvent();
		};
	}
	
	
	public void InitialseDesignMode()
	{
		m_designCamera = GameManager.Me.m_camera;
		m_coroutineOwner = GlobalData.Me;
	}

    protected override void _OnEnable() {
		m_stickerControls.SetActive(false);
		StartUp();
	}

	void OnDisable() {
		ShutDown();
	}

    public bool IsDecorationDrawerOpen()
	{
		return true;
	}

	public bool IsDrawerOpen(int _i)
	{
		return true;
	}

	public bool ForceDecorations => DesignMode == DESIGN_CATEGORY.AVATAR;
	public bool IsInitialised => gameObject?.activeSelf ?? false;
	public bool IsEnabled => IsInitialised;
	string m_defaultPartSet = "Teddy";
	public const string c_avatarPartSet = "Avatars";
	GameState_Product m_product; // product or null if building etc
	public NGCommanderBase Building => m_designInPlaceBuilding;
	public string m_currentDesignCategory;
	public GameState_Product Product => m_product;
	public bool IsShuttingDown => m_shuttingDown;
	public bool IsDesignInPlaceExiting => m_designInPlaceExiting;

	public void Toggle(string _type, int _designId, NGCommanderBase _factory) {
		if (EndDesignGlobally(m_acceptingChanges)) return;
		if (EndDesignInPlace(!m_acceptingChanges)) return;
	}

	private bool m_refreshDrawerContents = false;
	private bool m_earlyRefreshDrawerContents = false;
	
	void RefreshDrawerContents()
	{
		m_refreshDrawerContents = true;
		m_earlyRefreshDrawerContents = true;
	}

	public void OpenDrawSet(string _topLevelDraw, string _partSet, string _subSet)
	{
		m_currentDrawerSetType = _topLevelDraw;
		GenerateDrawerSetLists();
		m_currentDrawerSet = m_categoryList.IndexOf(_partSet);
		UpdateDesignInPlaceCatLabels();
		FillDrawerSetDropdowns(_subSet);
	}
	
	const string c_lowerTurntable = "Launch";
	const string c_raiseTurntable = "Elevate";
	public Animator m_turntableAnimator; 
	void ChangeTurntableContents(System.Action _cb) {
		m_onTurntableAnimationComplete = _cb;
		m_turntableAnimator.SetTrigger(c_lowerTurntable);
		if (GameManager.Me.IsOKToPlayDesignTableSound())
			AudioClipManager.Me.PlaySoundOld("PlaySound_Table_PlatformLowers", transform);
	}
	System.Action m_onTurntableAnimationComplete = null;
	public void TurntableAnimationComplete(bool _isLowered) {
		if (_isLowered) {
			if (m_onTurntableAnimationComplete != null) {
				m_onTurntableAnimationComplete();
				m_onTurntableAnimationComplete = null;
			}
			m_turntableAnimator.SetTrigger(c_raiseTurntable);
		}
	}

	public void ResetTurntableRot()
	{
		m_turntable.transform.localRotation = Quaternion.identity;
		m_turntableVisual.transform.localRotation = Quaternion.identity;
	}

	List<Transform> m_flaggedForDeletion = new List<Transform>();
	void FlagTurnTable(bool _runUnlocks)
    {
		if (m_blockHolder != null)
        {
	        foreach (Transform child in m_blockHolder.transform)
	        {
		        if (_runUnlocks) ReturnBlock(child.gameObject, false, true);
		        m_flaggedForDeletion.Add(child);
	        }
        }
	}

	void DeleteFlagged()
    {
		foreach (Transform flagged in m_flaggedForDeletion)
		{
			flagged.gameObject.SetActive(false);
			Destroy(flagged.gameObject); 
	    }
	    m_flaggedForDeletion.Clear();
    }

	void ClearTurntable() {
		if (m_blockHolder != null)
			m_blockHolder.transform.DestroyChildren();
	}
	void ClearObstacles() {
		if (m_obstacleHolder != null)
			m_obstacleHolder.transform.DestroyChildren();
	}
	
	static string[] s_numberSuffix = new string[] { "st", "nd", "rd" };
	public static string NumberToPosition(int _n) {
		var digit = _n % 10;
		var tens = (_n / 10) % 10;
		string suffix = "th";
		if (digit >= 1 && digit <= 3 && tens != 1) suffix = s_numberSuffix[digit-1];
		return $"{_n}{suffix}";
	}
	
	private string[] s_fakeNames = { "Lan Langley", "Alfred Band", "Iain McDarth", "Rich Stanville", "Reagan Mac", "Ratna Molly", "Sir Gerald Leechington", "Villaneil", "Hannah Button Winless" };
	
	private void RandomiseFakeNames()
	{
		Random rng = new Random();
		s_fakeNames = s_fakeNames.OrderBy(a => rng.Next()).ToArray();
	}

	IEnumerator Co_SwapScale(GameObject _in, GameObject _out) {
		Vector3 finalScaleIn = _in.transform.localScale;
		Vector3 finalScaleOut = (_out == null) ? Vector3.one : _out.transform.localScale;
		for (float f = 0; f < 1; f += Time.deltaTime / .5f) {
			float fs = f * f * (3 - f - f);
			_in.transform.localScale = finalScaleIn * fs;
			if (_out != null) _out.transform.localScale = finalScaleOut * (1 - fs);
			yield return null;
		}
		_in.transform.localScale = finalScaleIn;
		if (_out != null) Destroy(_out);
	}
	
	DESIGN_CATEGORY m_currentTableMode;
	public DESIGN_CATEGORY DesignMode => m_currentTableMode;
	public bool IsInDesignMode => m_currentTableMode != DESIGN_CATEGORY.PRODUCT_SELECTION;
	public bool IsInDesignModeActively => m_currentTableMode != DESIGN_CATEGORY.PRODUCT_SELECTION && m_designGloballyClosing == false;
	public bool IsInProductMode => m_currentTableMode == DESIGN_CATEGORY.PRODUCT_SELECTION || m_currentTableMode == DESIGN_CATEGORY.PRODUCT || m_currentTableMode == DESIGN_CATEGORY.PACKAGING;
	public bool IsInAvatarMode => m_currentTableMode == DESIGN_CATEGORY.AVATAR;
	public bool IsInBuildingMode => m_currentTableMode == DESIGN_CATEGORY.CIVIC || m_currentTableMode == DESIGN_CATEGORY.FACTORY || m_currentTableMode == DESIGN_CATEGORY.HOUSE;

	private bool m_isShowingLackOfQualityDialogue = false;
	private bool m_isShowingGeneralProblemDialogue = false;

	private void ShowLackOfQualityMessage(MAOrder _order, float _currentQuality)
	{
		AudioClipManager.Me.PlayUISound("PlaySound_DesignTableNotEnoughInputsOpen");
		
		NotificationUIController qualityMessageUI = InfoPlaqueManager.Me.LoadUI<NotificationUIController>();;
		qualityMessageUI.titleText.text = LocalizeKnack.TranslateLocalisedString("Insufficient Quality");
		
		System.Action<UnityEngine.EventSystems.PointerEventData> CancelConfirmDesign = (data) =>
		{
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			AudioClipManager.Me.PlayUISound("PlaySound_DesignTableNotEnoughInputsCancel");
			qualityMessageUI.Close();
			m_isShowingLackOfQualityDialogue = false;
		};

		MAOrderDataManager.QualityEntry currentQual = MADesignGuage.GetQualityEntry(_order.m_productLine, _currentQuality);
		MAOrderDataManager.QualityEntry requiredQual = MADesignGuage.GetQualityEntry(_order.m_productLine, _order.m_orderQuality);
		
		string msg = $"Your design is currently rather <b>{currentQual.GetColordString()}</b>, but the order requires <b>{requiredQual.GetColordString()}</b>.\n\nTry improving the design by using different parts or expanding on what you have.";
		
		qualityMessageUI.AddButton(LocalizeKnack.TranslateLocalisedString("Ok"), CancelConfirmDesign);
		qualityMessageUI.descriptionText.text = msg;
		qualityMessageUI.ToggleCloseButtonX(false);
		qualityMessageUI.Show();
		m_isShowingLackOfQualityDialogue = true;
	}

	private void ShowGeneralProblemMessage(string _title, string _message, System.Action _closeCb = null, float _yPos = 0)
	{
		AudioClipManager.Me.PlayUISound("PlaySound_DesignTableNotEnoughInputsOpen");
		
		NotificationUIController messageUI = InfoPlaqueManager.Me.LoadUI<NotificationUIController>();
		messageUI.titleText.text = LocalizeKnack.TranslateLocalisedString(_title);
		
		(messageUI.transform as RectTransform).anchoredPosition = new Vector2(0, _yPos);

		System.Action<UnityEngine.EventSystems.PointerEventData> CancelConfirmDesign = (data) =>
		{
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			messageUI.Close();
			m_isShowingGeneralProblemDialogue = false;
			AudioClipManager.Me.PlayUISound("PlaySound_DesignTableNotEnoughInputsCancel");
			if (_closeCb != null)
				_closeCb();
		};

		messageUI.AddButton(LocalizeKnack.TranslateLocalisedString("Ok"), CancelConfirmDesign);
		messageUI.descriptionText.text = _message;
		messageUI.ToggleCloseButtonX(false);
		messageUI.Show();
		m_isShowingGeneralProblemDialogue = true;
	}

	public enum BlockLabelMode
    {
	    None,
	    Cost,
	    Material,
	    Function,
	    Value,
    }
    private BlockLabelMode m_blockLabelMode = BlockLabelMode.None;

    private void CheckForAndCloseGUIs()
    {
    }
    
	public void HapticExit()
	{
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Tap);
        Close();
	}
	public void Close() 
    {
		if (m_confirmOrRevertUI != null)
		{
			m_confirmOrRevertUI.Close();
			m_confirmOrRevertUI = null;
		}
		CheckForAndCloseGUIs();        
        Close(false); 
    }

	private bool IsDesignLegal() 
	{
		if (MAGameInterface.CheckIsDesignLegal() == false)
		{
			return false;
		}
		var design = GetDesignOnTable();
		return design == null || design.GetNumBlocks() > 0;
	}
	
	private void NoBlocksUI() {
		NotificationUIController uiController = InfoPlaqueManager.Me.LoadUI<NotificationUIController>();
		uiController.titleText.text = "No Blocks";
		uiController.descriptionText.text = "Your design has no blocks, your changes will not be applied.";

		Action<UnityEngine.EventSystems.PointerEventData> editClicked = (data) => {
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			uiController.Close();
		};
		Action<UnityEngine.EventSystems.PointerEventData> okClicked = (data) => {
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			uiController.Close();
			Close(false);
		};

		uiController.AddButton("Edit Design", editClicked);
		
		if (m_origionalDesign != null && m_origionalDesign.HasDesign)
		{
			uiController.AddButton(Localizer.Get(TERM.GUI_OK), okClicked);
		}
		uiController.ToggleCloseButtonX(false);
		uiController.Show();
	}

	private RevertOrRedesignUIController m_confirmOrRevertUI = null;
	public RevertOrRedesignUIController ConfirmOrRevertUI => m_confirmOrRevertUI;
	public bool IsShowingConfirmDialog => m_confirmOrRevertUI != null || m_isShowingLackOfQualityDialogue;
	private void ShowConfirmChanges(bool _checkDesignWarnings = true) {
        UpdateSavedState();
        if (!IsDesignLegal()) {
			NoBlocksUI();
		} else if (_checkDesignWarnings && ShowDesignWarning()) {
		} else if (IsProductRedesigned()) {
			m_confirmOrRevertUI = InfoPlaqueManager.Me.LoadUI<RevertOrRedesignUIController>();
			m_confirmOrRevertUI.Setup();
			m_confirmOrRevertUI.m_confirm.onButtonClick += (data) => {
				Close(true);
				m_confirmOrRevertUI.Close();
				m_confirmOrRevertUI = null;
			};
			m_confirmOrRevertUI.m_revert.onButtonClick += (data) => {
				RefreshDesign(m_origionalDesign.m_design, true);
				ClearUndo();
				m_confirmOrRevertUI.Close();
				m_confirmOrRevertUI = null;
			};
			m_confirmOrRevertUI.Show();
		} else {
            //NGTutorialManager.Me.EndDesignTableSection();
            //NGTutorialManager.Me.FireConfirmTrigger();
			Close(true);
		}
	}

	private bool ShowDesignWarning()
	{
		// show a warning if necessary, then ShowConfirmChanges again
		if (IsBuilding() && m_designInPlaceBuilding.CanHaveWorkersOrDeliveries())
		{
			// check door
			if (InteractTransform.Find(m_blockHolder, InteractTransform.Type.Door) == null)
			{
				NotificationUIController uiController = InfoPlaqueManager.Me.LoadUI<NotificationUIController>();
				uiController.titleText.text = "Warning";
				uiController.descriptionText.text = "Buildings require a door!";
				uiController.AddButton("Go Back", _e =>
				{
					HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
					uiController.Close();
				});
				uiController.AddButton("Do it anway!", _e =>
				{
					HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
					uiController.Close();
					ShowConfirmChanges(false);
				});
				uiController.ToggleCloseButtonX(false);
				uiController.Show();
				return true;
			}
		}
		return false;
	}
	
	private BaseUIController m_submitCompetitionBuyDialog = null;
	
	public bool ClickerInterface()
	{
		return false;
	}

	bool m_acceptingChanges = false;
	bool m_shuttingDown = false;
	
	public MAOrder Order => m_designInPlaceBuilding?.Order;
	
	private void EndDragsBeforeClose()
	{
		// in case we're forced out of design mode while dragging a block (e.g. by failing the day)
		if (m_grabbedBlock != null)
		{
			var dtd = m_grabbedBlock.GetComponent<DTDragBlock>();
			if (dtd != null) dtd.EndDrag();
		}
	}

	public void Close(bool _acceptChanges)
	{
		EndDragsBeforeClose();
		if (MAGameInterface.CheckIsDesignLegal() == false)
			return;
		if (EndDesignGlobally(_acceptChanges))
			return;
		if (_acceptChanges && m_origionalDesign != null && m_designInPlaceIsFirstDesign == false)
		{
			if(m_origionalDesign.m_design == m_currentDesign.m_design)
			{
				if(IsDesigningProduct() == false || m_designInPlaceBuilding.ProductMade != null)
				{
					_acceptChanges = false;
				}
			}
		}
		m_shuttingDown = true;
		m_acceptingChanges = _acceptChanges;
		
		if(m_product != null && m_acceptingChanges)
		{
			m_product = GameState_Product.CreateUniquePersistentProduct(m_product);
		}
		
		var design = GetDesignOnTable(); // GameState_Design currently on the table
		if (_acceptChanges) 
		{
			GameManager.Me.m_state.m_designTableDetails.m_lastDesignByCategory[Order.ProductLine] = design.m_design;

			if (IsBuilding())
			{
				m_designInPlaceBuilding.m_stateData.m_buildingDesign = m_currentDesign;
			}
			RefreshSave(false);
		} else {
			if (DesignMode == DESIGN_CATEGORY.AVATAR)
			{
				m_currentDesign = m_origionalDesign;
			}
			GameManager.ClearDeferredUnlocks();
		}
		LeaveTable();
	}

	private void LeaveTable() {
		Toggle("", -1, null);
	}

	public void DragTriggered(DTDragCategory _who) {
		Close(true);
	}

	public static bool IsDesignInPlace => Me != null && (Me.IsInDesignInPlace || Me.m_isInDesignGlobally);
	public static bool IsDesignInPlaceRoom => (Me?.IsInDesignInPlaceRoom ?? false) || (Me?.m_isInDesignGlobally ?? false);
	public static bool IsMakingBuilding => Me != null && Me.IsInDesignInPlace == false;
	public const float c_blockBaseScale = 1f;
	private static float s_baseProductScale = 1.0f;
	public static float c_blockBuildScaleProduct => .05f * c_blockBaseScale * s_baseProductScale;
	public static float c_blockBuildScaleBuilding => 2f * c_blockBaseScale;
	public static float c_blockDrawerScaleBuilding => 1f * c_blockBaseScale;
	public static float c_blockDrawerScaleProduct => .05f * c_blockBaseScale;
	public static float OverHarnessScaleBase => .005f;
	public static float OverHarnessScale => Me.m_isOverHarness ? OverHarnessScaleBase : 1f;
	public static float CurrentBlockBuildScale => IsMakingBuilding ? c_blockBuildScaleBuilding : c_blockBuildScaleProduct;

	const float c_overallDrawerScale = .02f;
	const float c_defaultBuildingScale = .5f;
	public static float DesignScale(NGBlockInfo _info, bool _isWildBlock) {
		float baseScale = _info.m_tableScale;
		if (baseScale < .001f) baseScale = c_defaultBuildingScale;
		if (_isWildBlock) return baseScale * c_blockBuildScaleBuilding;
		return baseScale * CurrentBlockBuildScale;
	}
	public static float DrawerScale(NGBlockInfo _info, bool _isInDrawer = true) {
		if (_isInDrawer) return c_overallDrawerScale;
		return _info.Type == NGBlockInfo.BlockType.Building ? c_blockDrawerScaleBuilding : c_blockDrawerScaleProduct;
	}

	private string m_currentDrawerSetType = null;

	public void SetDrawerSetType(string _s)
	{
		m_currentDrawerSetType = _s;
		GenerateDrawerSetLists();
		UpdateDesignInPlaceCatLabels();
		FillDrawerSetDropdowns();
	}
	
	private List<string> m_allDrawerSets = new();
	List<string> FindUnlockedCategories() {
		if (m_currentDrawerSetType == "Tweaks")
			return new List<string> { "Decorations" };
		var res = new List<string>();

		m_allDrawerSets.Clear();
		foreach (var info in MADrawerInfo.s_drawerInfos)
		{
			if (m_currentDrawerSetType != null && info.m_drawerType != null &&
			    info.m_drawerType != m_currentDrawerSetType)
				continue;
			if (DEBUG_IsAllUnlocked == false && info.IsUnlocked() == false)
				continue;
			if (m_allDrawerSets.Contains(info.m_drawerName) == false)
				m_allDrawerSets.Add(info.m_drawerName);
			if (res.Contains(info.m_drawerName) == false)
				res.Add(info.m_drawerName);
		}
		return res;
	}

	
	List<string> m_categoryList, m_totalCategoryList, m_lockedCategoryList;
	void StartUp() {
		GameStateTimer.TimeStampDesignTableOpened = DateTime.Now.Ticks;
		
		m_categoryList = FindUnlockedCategories();
        m_grabbingIsBlockedByTutorial = false;

        ClearUndo();
        
		RestoreSavedState();
		RefreshDesignInfo();

        if (IsDesigningProduct()) SetupInitialProductLabels();
	}
	
	void ShutDown() {
		if (Utility.IsShuttingDown) return;
		
		ReleaseDrawerSet();
		m_currentDrawerSet = -1;
		m_changingDrawerSet = false;
		ClearTurntable();
		ClearObstacles();
		CleanupProductLabels();
	}

	float c_cellSize => CurrentBlockBuildScale * BuildingPlacementManager.c_buildingTileSize * .5f;
	public bool CheckForBlockers(Vector3 _turntablePosition, bool _ignoreExtent) {
		if (_ignoreExtent == false && DesignInPlaceCheckValidPosition(_turntablePosition) == false) return true;
		return false;
	}
	public bool CheckForBlockers(Transform _t, bool _ignoreExtent, int _w = 1, int _h = 1)
	{
		if (CheckDesignGloballyBlockers(_t, _ignoreExtent)) return true;
		var basePos = _t.position + _t.forward * c_cellSize * .5f;
		for (int y = 0; y < _h; ++y)
		{
			for (int x = 0; x < _w; ++x)
			{
				var subPos = basePos;
				if (CheckForBlockers(subPos, _ignoreExtent)) return true;
			}
		}
		return false;
	}

	public bool CheckForBlockersNew(Transform _owner, float _yRotation, Transform _source, Transform _match, bool _ignoreExtents)
	{
		if (_ignoreExtents) return false;
		if (m_isGrabbedBlockExtentIgnorer) return false;
		if (m_isInDesignGlobally == false) return false;

		var ng = _source.GetComponentInParent<NGCommanderBase>();
		if (ng == null) return true; // don't allow connection to a non-building
		var baseBlock = ng.Visuals.GetComponentInChildren<BaseBlock>();
		if (baseBlock == null) return false; // old style building, allow anywhere
		
		var oldPos = _owner.transform.position;
		var oldRot = _owner.transform.rotation;
		BlendTransform(_owner, _yRotation, _source, _match, null, _blend: false, _isFinal: false);
		bool isClear = true;
		foreach (var snap in ng.Visuals.GetComponentsInChildren<SnapHinge>())
		{
			if (baseBlock.IsPointInside(snap.transform.position, 1f) == false)
			{
				isClear = false;
				break;
			}
		}
		_owner.transform.position = oldPos;
		_owner.transform.rotation = oldRot;
		return isClear == false;
	}

	public bool CheckForBlockers(Transform _t, Transform _held, bool _ignoreExtent)
	{
		return CheckForBlockers(_t, _ignoreExtent);
	}
	void GenerateRoadAndBuildingMarkers() {
	}
	
	void ReleaseDrawerSet() {
		for (int i = 0; i < m_nextDrawerItemPos.Length; ++i) m_nextDrawerItemPos[i] = 0;
	}
	
	public void DisableDrawerSetChange() { m_changingDrawerSet = true; }
	bool m_changingDrawerSet = false;
	public string CategoryID => (IsInDesignMode && m_currentDrawerSet != -1) ? m_categoryList[m_currentDrawerSet] : "Product";
	public void ActivateDrawerLabels()
	{
		m_changingDrawerSet = false;
		ChangeDrawerSet();
	}
	void ChangeDrawerSet() {
	}
	IEnumerator Co_RotateDrawerLabels(float _from, float _to) {
		const float c_rotateSpeed = 180f;
		while (_from < _to) {
			_from = Mathf.Min(_from + c_rotateSpeed * Time.unscaledDeltaTime, _to);
			for (int i = 0; i < 4; ++i) {
				var t = m_drawerLabels[i].transform.parent.parent;
				t.localEulerAngles = Vector3.right * _from;
			}
			yield return null;
		}
		//var t2 = m_categoryLabel.transform.parent.parent;
		//t2.localEulerAngles += Vector3.right * 10f;
	}
	int[] m_nextDrawerItemPos = new int[4] { 0, 0, 0, 0 };

	void AddRarityObject(GameObject _to, string _id) {
		var rarity = NGBalance.ToRLRarity(NGBlockInfo.GetInfo(_id).Rarity);
		var rarityPlaneObject = Instantiate<GameObject>(m_rarityPlanePrefab, _to.transform);
		rarityPlaneObject.GetComponent<RarityPlane>().Activate(rarity);
		rarityPlaneObject.transform.localPosition = Vector3.zero;
		rarityPlaneObject.transform.localScale = Vector3.one;
		SetDesignInPlaceMaterialParameters(rarityPlaneObject);
	}

	void AddOwnedCountObject(DTDragDrawer[] _drawers, GameObject _to, int _drawerIndex, object _data)
	{
		var countObj = Instantiate(m_blockLabelPrefab, _to.transform);
		var countLabel = countObj.GetComponent<BlockLabel>();
		countLabel.SetDrawerDetails(_drawers, _drawerIndex, m_nextDrawerItemPos[_drawerIndex]);
		
		// Who needs inheritance
		countLabel.SetData(_data as PaintPotData);
		countLabel.SetData(_data as NGBlockInfo);
		countLabel.SetData(_data as PatternData);
		
		countLabel.SetLabel(m_blockLabelMode, IsMakingBuilding, m_dsi);
		countObj.transform.localPosition = _to.transform.InverseTransformVector((Vector3.up * 2f)*0.025f);
		countObj.transform.localScale = _to.transform.InverseTransformVector(Vector3.one * .012f);
	}

	public void AddBuffIcon(DTDragDrawer[] _drawers, GameObject _to, int _drawerIndex = -1, float _scale = 2)
	{
		var mblock = _to.GetComponentInChildren<ManagedBlock>();
		var bblock = _to.GetComponentInChildren<Block>();
		string id = null;
		if (mblock != null) id = mblock.m_blockID;
		else if (bblock != null) id = bblock.BlockID;
		else return;

		var info = NGBlockInfo.GetInfo(id);
		if (info == null) return;
		
		return;
		/*
		if (string.IsNullOrEmpty(type)) return;
		
		var label = value;
		var sprite = BuffIcon(type);

		float minY = -1e23f;
		if (_drawerIndex != -1) minY = _drawers[_drawerIndex].m_contents.transform.position.y;
		var buffObj = Instantiate(m_blockBuffPrefab, _to.transform);
		var buffVis = buffObj.GetComponent<BlockBuffsVisuals>();
		buffVis.SetCamera(DrawerCamera);
		buffVis.SetIcon(sprite);
		buffVis.SetLabel(label);
		buffVis.SetParent(_to);
		buffVis.ClampY(minY);
		buffVis.SetScale(_scale);*/
	}

	public static Sprite BuffIcon(string _buff)
	{
		return Resources.Load<Sprite>($"_Art/Icons/Buffs/IconBuff{_buff}");
	}
	
	public static bool GetItemVisibility(DTDragDrawer[] _drawers,  int _drawerIndex, int _slot)
	{
		return true;
	}
	
	private ProductInfoItem m_costLabel;
	void SetupInitialProductLabels() {
		m_costLabel = GenerateProductLabelPrice(GlobalData.CurrencySymbol+"0<size=2>.00", false);
	}
	 
	ProductInfoItem GenerateProductLabelLine(string _label, bool _addToDrawer) {
		return GenerateProductLabel(_label, _label, EProductItemType.Product_Line, _addToDrawer ? 2 : -1);
	}
	ProductInfoItem GenerateProductLabelTag(string _label, bool _addToDrawer) {
		return GenerateProductLabel(_label, _label, EProductItemType.Product_Tag, _addToDrawer ? 1 : -1);
	}
	ProductInfoItem GenerateProductLabelPrice(string _mod, bool _addToDrawer) {
		
		return GenerateProductLabel(m_dsi.SellingPrice.ToCurrencyString(2), _mod, EProductItemType.Product_Price, _addToDrawer ? 0 : -1);
	}
	void UpdatePriceLabel(string _mod = "")
	{
		UpdatePriceLabelText(m_dsi.SellingPrice.ToCurrencyString(2));
	}
	void UpdatePriceLabelText(string _text) {
		if (m_costLabel != null) {
			m_costLabel.SetPriceText(_text);
		}
	}

	ProductInfoItem GenerateProductLabel(string _label, string _id, EProductItemType _type, int _drawer, DTDragDrawer[] _set = null)
	{
		var item = GetProductInfoItem(_type, _label, _id);
		item.name = _type.ToString() + "|" + _id;
		if (_drawer != -1) {
			AddToDrawer(item.gameObject, _drawer, false, _set);
			item.transform.localEulerAngles += Vector3.right * 60;
			item.transform.localPosition += Vector3.up * .2f;
		} else {
			item.gameObject.SetLayerRecursively(GameManager.c_layerDesignTable);
			SnapSpecial(item.gameObject, _type, true);
		}
		return item;
	}

	void CleanupProductLabels() {
		m_snapPointProductPrice.DestroyChildren();
		m_costLabel = null;
	}
	
	private void RefreshBlockLabels()
	{
	}
	
	private void RefreshDrawerBlockLabels(DTDragDrawer _drawer)
	{
		if (_drawer == null) return;
		
		var labels = _drawer.GetComponentsInChildren<BlockLabel>();
		foreach (var label in labels)
		{
			label.SetLabel(m_blockLabelMode, IsInBuildingMode, m_dsi);
		}
	}

	ProductInfoItem GetProductInfoItem(EProductItemType _type, string _text, string _id) {
		var prefab = m_productInfoItemPrefabs[(int)_type];
		var item = Instantiate(prefab);
		item.transform.localScale = Vector3.one * c_blockBaseScale;
		item.transform.localEulerAngles = Vector3.right * 90f;
		item.Setup(_type, _text, _id);
		return item;
	}

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	static DebugConsole.Command s_unlockAll = new DebugConsole.Command("unlock", (_s) => Utility.SetOrToggle(ref GameManager.Me.m_state.m_gameInfo.m_unlockAllParts, _s));
	public bool m_unlockAll => GameManager.Me.m_state.m_gameInfo.m_unlockAllParts;
	private bool m_wasUnlockAllOn = false;
	void DEBUG_CheckUnlockAll() {
		if (DEBUG_IsAllUnlocked != m_wasUnlockAllOn) {
			var currentCategory = m_categoryList[m_currentDrawerSet];
			m_categoryList = FindUnlockedCategories();
			m_currentDrawerSet = -1;
		}
	}
	bool DEBUG_RecordUnlockAllState() {
		m_wasUnlockAllOn = DEBUG_IsAllUnlocked;
		return DEBUG_IsAllUnlocked;
	}
	public bool DEBUG_IsAllUnlocked => m_unlockAll || GameManager.Me.IsSeedEditMode;
#else
	void DEBUG_CheckUnlockAll() {}
	bool DEBUG_RecordUnlockAllState() { return false; }
	public bool DEBUG_IsAllUnlocked => false;
#endif
	int m_currentDrawerSet = -1;
	
	public void DeactivateConfirmButton() 
    {
    }
    public void ActivateConfirmButton() 
    {
    }

    public bool IsLeftButtonActive => false;
    public bool IsRightButtonActive => false;
    
    bool m_undoRedoDisabled = false;

    int m_drawerFreezeBitmap = 0;
	int m_drawerLockBitmap = 0;
	public bool IsDrawerLocked(int _n) => (m_drawerLockBitmap & (1 << _n)) != 0;
	void UnlockAllDrawers() {
		m_drawerFreezeBitmap = 0;
		m_drawerLockBitmap = 0;
		CheckAllDrawerLocks();
	}
	void RevertAllTutorialLocks() {
		m_onboardingRemovePartsAllowed = true;
		m_undoRedoDisabled = false;
	}
	void CheckDrawerLock(DTDragDrawer _drawer, GameObject _lockObject, bool _freeze, bool _lock) {
	}
	void CheckDrawerLock(int _index, bool _freeze, bool _lock) {
	}
	public void LockDrawer(int _drawerIndex, bool _freeze, bool _lock = true) {
	}
	void CheckAllDrawerLocks() {
	}

	//===== DECORATION

	public const char c_paintId = 'p';
	public const char c_patternId = 't';
	public const char c_stickerId = 's';
	public const char c_eraserId = 'e';

	public GameObject m_paintPotPrefab;
	public GameObject m_patternPrefab;
	public GameObject m_stickerPrefab;


	private List<string> m_tutorialPaintPack = null;
	private List<string> m_avatarStarterPack = null;
	public bool ContextTestUnlock(string _id, bool _consume = false)
	{
		if (string.IsNullOrEmpty(_id)) return true;
		
		if (DesignMode == DESIGN_CATEGORY.AVATAR)
		{
			if (m_avatarStarterPack == null) m_avatarStarterPack = ProductPacks.GetStarterPackDecorationIDs(c_avatarPartSet);
			if (m_avatarStarterPack.Contains(_id)) return true;
			_consume = false;
		}
		else if (!IsBuilding())
			_consume = false;
		bool hasUnlock = GameManager.IsUnlocked(_id);
		if (hasUnlock && _consume) return GameManager.ConsumeUnlock(_id);
		return hasUnlock;
	}

	Transform GenerateDecorationHolder(string _type, int _index, Transform _parent)
	{
		var holder = new GameObject($"{_type}{_index}");
		holder.transform.SetParent(_parent);
		holder.transform.localPosition = new Vector3(.85f, -.65f, _index * -c_drawerRowDepth - 10);
		holder.transform.localRotation = Quaternion.identity;
		return holder.transform;
	}

	public void PopulateDecorations(DTDragDrawer[] _drawers)
	{
		if(_drawers.Length == 0)
			return;
		int i, next;
		bool hasPaints = false;
		bool hasStickers = false;
		bool hasPatterns = false;
		
		int nextDrawer = 0;

		if (MAUnlocks.CanUsePaints)
		{
			for (i = 0, next = 0; i < PaintPotData.s_entries.Count; ++i)
			{
				var entry = PaintPotData.s_entries[i];
				if (entry == null) continue;
				if (DEBUG_IsAllUnlocked || ContextTestUnlock(entry.m_name))
				{
					var holder = GenerateDecorationHolder("Paint", next, _drawers[nextDrawer].transform);

					var obj = AddDecorationInstanceToDesk(holder, -1, i, m_paintPotPrefab, entry.m_colour, c_paintId);

					if (obj != null)
					{
						AddOwnedCountObject(_drawers, obj, nextDrawer, entry);
						++next;
						hasPaints = true;
					}
				}
			}
			int numPaints = next;
			_drawers[nextDrawer++].m_maxOpenFraction = (Mathf.Max((next + 1) / 2, 3) + 1 + 1) * c_drawerRowDepth * .01f;
		}

		if (MAUnlocks.CanUsePatterns)
		{
			for (i = 0, next = 0; i < PatternData.s_entries.Count; ++i)
			{
				var entry = PatternData.s_entries[i];
				if (entry == null) continue;
				if (DEBUG_IsAllUnlocked || ContextTestUnlock(entry.m_name))
				{
					var holder = GenerateDecorationHolder("Pattern", next, _drawers[nextDrawer].transform);
					var obj = AddDecorationInstanceToDesk(holder, -1, i, m_patternPrefab, PatternEntryToPath(entry), c_patternId);

					if (obj != null)
					{
						AddOwnedCountObject(_drawers, obj, nextDrawer, entry);
						++next;
						hasPatterns = true;
					}
				}
			}
			int numPatterns = next;
			_drawers[nextDrawer++].m_maxOpenFraction = (Mathf.Max((next + 1) / 2, 3) + 1 + 1) * c_drawerRowDepth * .01f;
		}

		if (MAUnlocks.CanUseStickers)
		{
			for (i = 0, next = 0; i < StickerData.s_entries.Count; ++i)
			{
				var entry = StickerData.s_entries[i];
				if (entry == null) continue;
				if (DEBUG_IsAllUnlocked || ContextTestUnlock(entry.m_name))
				{
					var holder = GenerateDecorationHolder("Sticker", next, _drawers[nextDrawer].transform);
					if (AddDecorationInstanceToDesk(holder, -1, i, m_stickerPrefab, StickerEntryToPath(entry), c_stickerId))
					{
						++next;
						hasStickers = true;
					}
				}
			}
			int numStickers = next;
			_drawers[nextDrawer++].m_maxOpenFraction = (Mathf.Max((next + 1) / 2, 3) + 1 + 1) * c_drawerRowDepth * .01f;
		}

		if (MAUnlocks.CanUsePaints || MAUnlocks.CanUsePatterns || MAUnlocks.CanUseStickers)
		{
			var holderEraser = GenerateDecorationHolder("Eraser", 0, _drawers[nextDrawer].transform);
			AddDecorationInstanceToDesk(holderEraser, -1, 0, m_designInPlaceEraser.GetChild(0).gameObject, "e", c_eraserId);
			_drawers[nextDrawer++].m_maxOpenFraction = 0;
		}
	}
	
	public static string PaintIdToColourId(int _id) {
		return PaintPotData.s_entries[_id].m_colour;
	}
	public static string PatternIdToPath(int _id) {
		return "Patterns/" + PatternData.s_entries[_id].m_path;
	}
	public static string PatternEntryToPath(PatternData _entry) {
		return "Patterns/" + _entry.m_path;
	}
	public static string PatternIdToDataPath(int _id) {
		return "PatternBytes/" + PatternData.s_entries[_id].m_path;
	}
	public static string StickerIdToPath(int _id) => StickerEntryToPath(StickerData.s_entries[_id]);
	public static string StickerEntryToPath(StickerData _entry) => $"Runes/{_entry.m_path}_BaseColor";

	public static Material StickerIdToMaterial(int _id)
	{
		string path = StickerIdToPath(_id);
		var mat = new Material(Me.m_stickerMaterial);
		SetMaterialFromStickerPath(mat, path);
		return mat;
	}
	public static void SetMaterialFromStickerPath(Material mat, string path)
	{
		string nrmPath = path.Replace("_BaseColor", "_Normal");
		var tex = ResManager.Load<Texture2D>(path);
		var nrmTex = ResManager.Load<Texture2D>(nrmPath);
		mat.SetTexture("_BaseMap", tex);
		mat.SetTexture("_BumpMap", nrmTex);
	}

	public static Texture2D IdToTexture(string _id) {
		int index = int.Parse(_id.Substring(1));
		switch (_id[0]) {
			case c_paintId:
				var clr = StringToColour(PaintPotData.s_entries[index].m_colour);
				var tex = new Texture2D(1, 1);
				tex.SetPixel(0, 0, clr);
				tex.Apply(true, true);
				return tex;
			case c_patternId:
				return ResManager.Load<Texture2D>(PatternIdToPath(index));
			case c_stickerId:
				return ResManager.Load<Texture2D>(StickerIdToPath(index));
		}
		return null;
	}

	public static Sprite IdToSprite(string _id) {
		var tex = IdToTexture(_id);
		if(tex == null) return null;
		return Sprite.Create(tex, new Rect(0, 0, tex.width, tex.height), new Vector2(tex.width / 2, tex.height / 2));
	}
	void ClearDecorationInstancesFromDesk(Transform _holder)
    {
		for(int i = 0; i < _holder.childCount; i++)
        {
			var spot = _holder.GetChild(i);
			spot.DestroyChildren();
		}
	}
	GameObject AddDecorationInstanceToDesk(Transform _holder, int _index, int _id, GameObject _prefab, string _data, char _type) {
		Transform spot;
		float baseScale = 1, yOffs = .85f;
		if (_index == -1)
		{
			spot = _holder;
			yOffs = 0;
			baseScale = .5f;
		}
		else
		{
			if (_index >= _holder.childCount) return null;
			spot = _holder.GetChild(_index);
		}
		baseScale *= c_overallDrawerScale * s_catScale;
		var pot = Instantiate(_prefab);
		pot.SetLayerRecursively(GameManager.c_layerDesignTable);
		pot.transform.parent = spot;
		var rot = 90;

		var rnd = pot.GetComponentInChildren<MeshRenderer>();
        var mat = rnd.material;
		switch (_type) {
			case c_paintId:
				pot.transform.localPosition = Vector3.zero;
				baseScale *= 2.0f;
				pot.transform.localScale = Vector3.one * baseScale;
				Decoration.SetTintProperty(mat, 0, StringToColour(_data));
				break;
			case c_patternId:
				pot.transform.localPosition = new Vector3(0, yOffs, 0);
				pot.transform.localScale = Vector3.one * (.2f * baseScale);
				mat.SetVector("_BaseMap_ST", new Vector4(.333f, .333f, 0, 0));
				var tex = ResManager.Load<Texture2D>(_data);
				if (tex == null)
				{
					Destroy(pot);
					Debug.LogError($"Unable to load pattern/sticker res {_data}");
					return null;
				}
				mat.SetTexture("_BaseMap", tex);
				break;
			case c_stickerId:
				pot.transform.localPosition = new Vector3(0, .8f * baseScale, 0);
				pot.transform.localScale = Vector3.one * (.2f * baseScale);
				//pot.transform.GetChild(0).eulerAngles = Vector3.up * UnityEngine.Random.Range(0, 360);
				SetMaterialFromStickerPath(mat, _data);
				rot = -90;
				break;
			case c_eraserId:
				pot.transform.localPosition = Vector3.zero;
				pot.transform.localScale = Vector3.one * baseScale;
				rot = 30;
				break;
		}
		pot.transform.localEulerAngles = Vector3.up * rot;
		var palette = pot.GetComponent<DTDragPalette>();
		palette.m_context = $"{_type}{_id}";
		if (_index == -1)
			SetDesignInPlaceMaterialParameters(pot);
		return pot;
	}

	public void UpdateSpriteDrag(string _context, Vector3 _position, bool _apply) {
		if (_apply) {
			if ((_context == null || _context[0] == DesignTableManager.c_eraserId) && m_currentHighlightStickerErase != null) {
				m_currentHighlightStickerErase.Remove();
				FinaliseDecoration();
			} else if (_context[0] == c_stickerId && m_currentHighlightRoot != null) {
				m_stickerControls.SetActive(true);
			} else {
				FinaliseDecoration();
			}
		} else {
			CheckDecoration(_context, _position);
		}
	}
	void FinaliseDecoration() {
		if (m_currentHighlightId != null) {
			switch (m_currentHighlightId[0]) {
				case c_eraserId:
					if (GameManager.Me.IsOKToPlayDesignTableSound())
						AudioClipManager.Me.PlaySoundOld("PlaySound_Table_ApplyRubber", transform);
					break;
				case c_paintId: 
                    if (GameManager.Me.IsOKToPlayDesignTableSound())
	                    AudioClipManager.Me.PlaySoundOld("PlaySound_Table_ApplyPaint", transform);
					if (m_currentHighlightRoot != null)
					{
						OnPaintAdded?.Invoke();
                        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Decorate);
                    }
                    break;
				case c_patternId: 
					if(m_currentHighlightRoot != null)
					{	
						OnPatternAdded?.Invoke();
                        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Decorate);
                    }
                    break;
				case c_stickerId:
					if (GameManager.Me.IsOKToPlayDesignTableSound())
						AudioClipManager.Me.PlaySoundOld("PlaySound_Table_ApplySticker", transform);
					if (m_currentHighlightRoot != null)
					{
						OnStickerAdded?.Invoke();
                        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Decorate);
                    }
                    break;
			}
		}

		if (m_currentHighlightRoot != null)
		{
			if (m_isInDesignGlobally)
			{
				m_grabbedTo = m_currentHighlightRoot.GetComponentInParent<NGCommanderBase>();
				var wildBlock = m_currentHighlightRoot.GetComponentInParent<MAWildBlock>();
				if (wildBlock != null)
				{
					m_grabbedFromWildBlock = wildBlock.m_wildBlockState.Copy();
					UpdateWildBlockState(wildBlock);
					m_grabbedToWildBlock = wildBlock.m_wildBlockState.Copy();
				}

				RefreshSave();
			}
		}

		m_currentHighlightRoot = null;
		m_currentHighlightStickerErase = null;
		m_currentHighlight.MeshId = -1;

		if (m_isInDesignGlobally == false)
			UpdateSavedState(true); //OLDSAVE
	}
	void CancelDecoration() {
		if (m_currentHighlightRoot != null) {
			if (m_currentHighlightId == null || m_currentHighlightId[0] != c_stickerId) { // non-sticker
				if (m_currentHighlight.MeshId != -1) {
					SetPaintPatternData(false);
				}
			} else {
				// remove old sticker
				SetStickerData(false);
			}
			m_currentHighlightRoot = null;
			m_currentHighlightStickerErase = null;
			m_currentHighlight.MeshId = -1;
		}
	}
	void RefreshStickerID(string _id) {
		if (m_currentHighlightId.CompareTo(_id) == 0) return;
		if (m_currentHighlightRoot != null) {
			SetStickerData(false);
		}
		m_currentHighlightId = _id;
		SetStickerData(true);
	}

	public static string CombineStrings(string[] _array, char _sep) {
		string s = _array[0];
		for (int i = 1; i < _array.Length; ++i) {
			s += _sep; s += _array[i];
		}
		return s;
	}
	public static DesignUtilities.PartDescription[] GetCurrentDesignParts()
	{
		if(Me == null) return new DesignUtilities.PartDescription[0];
		var design = Me.GetDesignOnTable();
		if (design == null) return new DesignUtilities.PartDescription[0];
		var parts = DesignUtilities.GetDesignData(design);
		return parts;
	}
	public void StickerControls_Accept() {
		FinaliseDecoration();
		m_stickerControls.SetActive(false);
	}
	public void StickerControls_Cancel() {
		CancelDecoration();
		m_stickerControls.SetActive(false);
	}

#if UNITY_EDITOR
    public string CurrentHighlightId
    {
        get { return m_currentHighlightId; }
        set { m_currentHighlightId = value; }
    }

    public void AdjustStickerDataExternal(int _index, float _speed, float _valueScale, float _clampMin = 1, float _clampMax = 0)
    {
		AdjustStickerData(_index, _speed, _valueScale, _clampMin, _clampMax);
    }
#endif

	void AdjustStickerData(int _index, float _speed, float _valueScale, float _clampMin = 1, float _clampMax = 0) {
        print(m_currentHighlightId);
		print($"Index: {_index} speed: {_speed} valueScale: {_valueScale} clampMin: { _clampMin} clampMax: { _clampMax}");

        var bits = m_currentHighlightId.Split(',');
		float current = float.Parse(bits[_index]) / _valueScale;
		current += _speed * Time.unscaledDeltaTime;
		if (_clampMin < _clampMax)
			current = Mathf.Clamp(current, _clampMin, _clampMax);
		bits[_index] = $"{(int)(current * _valueScale)}";
		var id = CombineStrings(bits, ',');
		RefreshStickerID(id);
	}


    void AdjustStickerScale(float _direction) {
		const float c_minStickerScale = .5f, c_maxStickerScale = 2f;
		AdjustStickerData(7, _direction * 1.0f, 100f, c_minStickerScale, c_maxStickerScale);
	}
	void AdjustStickerRotate(float _direction) {
		AdjustStickerData(8, _direction * 90.0f, 1f);
	}

	public void StickerControls_Shrink() {
		AdjustStickerScale(-1);
	}
	public void StickerControls_Grow() {
		AdjustStickerScale(1);
	}
	public void StickerControls_RotateCCW() {
		AdjustStickerRotate(-1f);
	}
	public void StickerControls_RotateCW() {
		AdjustStickerRotate(1f);
	}
	public static Color ContextToColour(string _id) {
		if (_id[0] == c_paintId) {
			int index = int.Parse(_id.Substring(1));
			return StringToColour(PaintIdToColourId(index));
		}
		return Color.white;
	}
	public static Texture2D ContextToTexture(string _id, int _which = 0) { // _which is 0 (baseMap) 1 (bumpMap) 2 (maskMap)
		if (_which != 0) return null;
		if (_id[0] == c_patternId) {
			int index = int.Parse(_id.Substring(1));
			return ResManager.Load<Texture2D>(PatternIdToPath(index));
		}
		return null;
	}
	public static byte[] ContextToTextureData(string _id) {
		if (_id[0] == c_patternId) {
			int index = int.Parse(_id.Substring(1));
			var asset = ResManager.Load<TextAsset>(PatternIdToDataPath(index));
			var bytes = asset.bytes;
			Resources.UnloadAsset(asset);
			return bytes;
		}
		return null;
	}

	public static Color StringToColour(string _clr) {
		int colour = int.Parse(_clr.Substring(0, 6) , System.Globalization.NumberStyles.HexNumber);
		return new Color(((colour >> 16) & 0xFF) / 255f, ((colour >> 8) & 0xFF) / 255f, ((colour >> 0) & 0xFF) / 255f);
	}

	public const float c_stickerQuantise = 1000f;
	Transform m_currentHighlightRoot = null;
	TintWindowAPI.SubmeshWindow m_currentHighlight = new TintWindowAPI.SubmeshWindow(-1,-1,-1);
	Sticker m_currentHighlightStickerErase = null;


	string m_currentHighlightId;

	private static DebugConsole.Command s_showdecdebcmd = new ("decorationdebug", _s => {
		GameManager.SetConsoleDisplay(() => Me.m_debugDecorationWindow);
	});
	string m_debugDecorationWindow = "";

	void CheckDecoration(string _id, Vector3 _pos) {
		float closestHitDistanceSqrd = 1e23f;
		TintWindowAPI.SubmeshWindow best = new TintWindowAPI.SubmeshWindow(-1,-1,-1);
		Transform bestRoot = null;

#if UNITY_EDITOR
		m_debugDecorationWindow = "";
#endif
		
		Sticker stickerErase = null;
		var ray = m_designCamera.RayAtScreenPosition(_pos);
		var hits = Physics.RaycastAll(ray, 100f, -1);
		for (int i = 0; i < hits.Length; ++i) {
			var c = hits[i].collider;
			if (c is not MeshCollider && c.GetComponentInParent<DecorationHolder>() == null)
				c = null;
			if (c != null) {
				if (c.gameObject.GetComponentInParent<BaseBlock>() != null)
					continue;
				var block = c.gameObject.GetComponentInParent<Block>();
				if (block != null && (NGBlockInfo.GetInfo(block.m_blockInfoID)?.m_isInvisible ?? false))
					continue;
				var db = c.GetComponentInParent<DTDragBlock>();
				if (db != null) {
					Vector3 hit;
					var sticker = c.GetComponent<Sticker>();
					if ((_id == null || _id[0] == DesignTableManager.c_eraserId) && sticker != null) {
						var distSqrd = (hits[i].point - ray.origin).sqrMagnitude;
						if (distSqrd < closestHitDistanceSqrd) {
							closestHitDistanceSqrd = distSqrd;
							bestRoot = db.transform;
							stickerErase = c.GetComponent<Sticker>();
						}
					} else if (sticker == null) {
						hit = hits[i].point;
						var window = TintWindowAPI.GetTintWindowFromCollider(db.transform, c, ray, TintWindowAPI.DecorationType.Paint, ref hit);
						if (window.MeshId != -1) {
							var distSqrd = (hit - ray.origin).sqrMagnitude;
							if (distSqrd < closestHitDistanceSqrd) {
#if UNITY_EDITOR
								m_debugDecorationWindow += $"Mesh {window.MeshId} {db.transform.GetTransformFromTopologicalID(window.MeshId).name} @ {distSqrd}\n";
#endif
								closestHitDistanceSqrd = distSqrd;
								best = window;
								bestRoot = db.transform;
								stickerErase = null;
							}
						}
					}
				}
			}
		}
		
		if (stickerErase != null) { // sticker erase
			UnhighlightDecoration();
			m_currentHighlightStickerErase = stickerErase;
			m_currentHighlightRoot = bestRoot;
		} else if (_id == null || _id[0] != c_stickerId) { // non-sticker
			if (bestRoot != m_currentHighlightRoot || !best.Equals(m_currentHighlight)) {
				UnhighlightDecoration();
				if (best.MeshId != -1 && best.Window != 0) { // window=0 when we hit a material without TintWindow properties
					m_currentHighlightRoot = bestRoot;
					m_currentHighlight = best;
					m_currentHighlightId = _id;
					SetPaintPatternData(true);
				}
			}
		} else { // sticker
			if (bestRoot != null) {
				// convert ray to object local space and store it in context
				var obj = bestRoot.GetTransformFromTopologicalID(best.MeshId);
				ray.origin = obj.InverseTransformPoint(ray.origin);
				ray.direction = obj.InverseTransformDirection(ray.direction);
				_id += $",{(int) (ray.origin.x * c_stickerQuantise)},{(int) (ray.origin.y * c_stickerQuantise)},{(int) (ray.origin.z * c_stickerQuantise)},{(int) (ray.direction.x * c_stickerQuantise)},{(int) (ray.direction.y * c_stickerQuantise)},{(int) (ray.direction.z * c_stickerQuantise)},100,0";
			}
			if (bestRoot != m_currentHighlightRoot || !_id.Equals(m_currentHighlightId)) {
				if (m_currentHighlightRoot != null) {
					// remove old sticker
					SetStickerData(false);
				}
				m_currentHighlightRoot = null;
				m_currentHighlightStickerErase = null;
				if (bestRoot != null) {
					m_currentHighlightRoot = bestRoot;
					m_currentHighlight = best;
					m_currentHighlightId = _id;
					SetStickerData(true);
				}
			}
		}
		if (m_currentHighlightRoot != null)
		{
			Block block = m_currentHighlightRoot.GetComponent<Block>();
			if (block != null)
			{
				DesignTableAnalytics.m_lastActionPerformedOnObject = NGBlockInfo.s_allBlocks[block.BlockID].m_displayName;
			}
		}
	}
	void UnhighlightDecoration() {
		if (m_currentHighlight.MeshId != -1) {
			SetPaintPatternData(false);
		}
		m_currentHighlightRoot = null;
		m_currentHighlight.MeshId = -1;
		m_currentHighlightStickerErase = null;
	}

	string m_currentHighlightRestore = null;
	void SetPaintPatternData(bool _highlight) {
		var obj = m_currentHighlightRoot.GetTransformFromTopologicalID(m_currentHighlight.MeshId);
		if (obj == null) {
			Debug.LogWarning($"Topology error in {m_currentHighlightRoot?.name} id {m_currentHighlight.MeshId}");
			return;
		}
		if (_highlight) {
			m_currentHighlightRestore = Decoration.GetWindowData(m_currentHighlightRoot, obj.gameObject, m_currentHighlight.Window);
			Decoration.SetWindowData(m_currentHighlightRoot, obj.gameObject, m_currentHighlight.Window, m_currentHighlightId);
		} else {
			Decoration.SetWindowData(m_currentHighlightRoot, obj.gameObject, m_currentHighlight.Window, m_currentHighlightRestore);
		}
	}
	void SetStickerData(bool _highlight) {
		var obj = m_currentHighlightRoot.GetTransformFromTopologicalID(m_currentHighlight.MeshId);
		Decoration.SetStickerData(m_currentHighlightRoot, obj.gameObject, m_currentHighlight.Window, m_currentHighlightId, _highlight);
	}

	//===== UNDO/REDO
	List<(string, float, List<string>)> m_undoBuffer = new (); // design, spend delta, scattered
	int m_undo = -1;
	void ClearUndo() {
		m_undoBuffer.Clear();
		m_undo = -1;
		
		m_designGloballyUndoStack.Clear();
		m_designGloballyUndoStep = 0;
	}
	void AddToUndo(string _design, List<string> _overrideScattered = null) {
		var _scattered = _overrideScattered != null ? _overrideScattered : GetScattered();
		if (m_undoBuffer.Count > 0 && m_undoBuffer[m_undo].Item1 == _design && m_undoBuffer[m_undo].Item3 == _scattered)
			return;
		
		float costDelta = 0;
		if(m_undoBuffer.Count > 0)
		{
			var scatteredCostDelta = CalculateScatteredCostDelta(m_undoBuffer[m_undo].Item3, _scattered);
			var designCostDelta = CalculateDesignCostDelta(m_undoBuffer[m_undo].Item1, _design);
			costDelta = scatteredCostDelta + designCostDelta;
			ExecuteCurrencyDelta(costDelta, false);
		}
		
		while (m_undo < m_undoBuffer.Count-1) m_undoBuffer.RemoveAt(m_undoBuffer.Count-1);
		m_undoBuffer.Add((_design, costDelta, _scattered));
		++m_undo;
		RefreshUndo();
	}
    public void UnlockDesignTableChanges()
    {
        m_grabbingIsBlockedByTutorial = false;
    }

    public void LockDesignTableChanges()
    {
        m_grabbingIsBlockedByTutorial = true;
    }
    
    public void UndoAndKillRedo()
	{
		if (m_undoBuffer.Count == 0) return;
		ClickedUndo();
		m_undoBuffer.RemoveAt(m_undoBuffer.Count-1);
		RefreshUndo();
	}

    public void ClickedUndo()
    {
		if(IsRestoring) return;
		
	    if (DesignGloballyClickedUndo()) return;
		if (IsInDesignInPlaceActively == false) return;
	    HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
	    if (m_undo < 1) return;
	    OnUndo?.Invoke();
	    --m_undo;
	    RefreshDesign(m_undoBuffer[m_undo].Item1, true);
	    ExecuteCurrencyDelta(m_undoBuffer[m_undo+1].Item2, true);
	    CreateScatterBlocks(m_undoBuffer[m_undo].Item3);
	    RefreshUndo();
	    AudioClipManager.Me.PlayUISound("PlaySound_DesignModeUndo");
    }
    
    public string GetDesign()
    {
        var design = GetDesignOnTable();
        if (design == null || design.m_design == "0|0|")
            return "";
        return design.m_design;
    }

    public void ClickedRedo()
    {
	    if(IsRestoring) return;
	    
	    if (DesignGloballyClickedRedo()) return;
		if (IsInDesignInPlaceActively == false) return;
	    HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
	    if (m_undo >= (m_undoBuffer.Count - 1)) return;
	    OnRedo?.Invoke();
	    ++m_undo;
	    RefreshDesign(m_undoBuffer[m_undo].Item1, true);
	    ExecuteCurrencyDelta(m_undoBuffer[m_undo].Item2, false);
	    CreateScatterBlocks(m_undoBuffer[m_undo].Item3);
	    RefreshUndo();
	    AudioClipManager.Me.PlayUISound("PlaySound_DesignModeRedo");
    }

	void RefreshUndo()
	{
		bool canUndo = m_undo > 0 && !m_undoRedoDisabled;
		bool canRedo = m_undo < m_undoBuffer.Count - 1 && !m_undoRedoDisabled;
		SetUndoStates(canUndo, canRedo);
	}

	void SetUndoStates(bool canUndo, bool canRedo)
	{
		if (m_designInPlaceUndoButton != null)
			m_designInPlaceUndoButton.interactable = canUndo;
		if (m_designInPlaceRedoButton != null)
			m_designInPlaceRedoButton.interactable = canRedo;
		if (DesignUIManager.Me.m_undo3D != null)
			DesignUIManager.Me.m_undo3D.SetActive(canUndo);
		if (DesignUIManager.Me.m_redo3D != null)
			DesignUIManager.Me.m_redo3D.SetActive(canRedo);
	}

	//
	void RefreshDesign(string _design, bool _runUnlocks = false) {
		FlagTurnTable(_runUnlocks);
		RestoreDesign(IsBuilding() ? RestoreType.Building : RestoreType.Product, _design, null, (g,componentsChanged) => { DeleteFlagged(); }, _runUnlocks);
		m_currentDesign.SetDesign(_design);
		RefreshDesignInfo();
	}
	//=====

	private bool CheckSpecialOrderRequirements(out List<Block> _missingBlocks)
	{
		_missingBlocks = null;
		var info = Order?.OrderInfo;
		if (info == null) return true;
		var design = info.m_design;
		if (design.IsEmptyDesign()) return true;
		
		bool allPresent = true;
		var parts = DesignUtilities.GetDesignData(design);
		var blocks = m_blockHolder.GetComponentsInChildren<Block>();
		foreach (var block in blocks) block.GenericFlag = false;
		foreach (Transform t in m_scatteredBlockHolder) t.GetComponent<Block>().GenericFlag = false;
		foreach (var part in parts)
		{
			bool found = false;
			for (int i = 0; i < blocks.Length; ++i)
			{
				var block = blocks[i];
				if (block.BlockID == part.m_blockID && block.GenericFlag == false)
				{
					block.GenericFlag = true;
					found = true;
				}
			}
			if (found == false)
			{
				allPresent = false;
				foreach (Transform t in m_scatteredBlockHolder)
				{
					if(m_flaggedForDeletion.Contains(t) || !t.gameObject.activeSelf) continue;
					
					var block = t.GetComponentInChildren<Block>();
					if (block.BlockID == part.m_blockID && block.GenericFlag == false)
					{
						block.GenericFlag = true;
						if (_missingBlocks == null) _missingBlocks = new List<Block>();
						_missingBlocks.Add(block);
						found = true;
						break;
					}
				}
				if (found == false)
					Debug.LogError($"Block {part.m_blockID} not found in special design on accept");
			}
		}
		return allPresent;
	}

	private void RefreshSpecialBlockFlags()
	{
		var info = Order?.OrderInfo;
		if (info == null) return;
		var design = info.m_design;
		if (design.IsEmptyDesign()) return;
		var parts = DesignUtilities.GetDesignData(design);
		var blocks = m_blockHolder.GetComponentsInChildren<Block>();
		foreach (var block in blocks) block.DoNotAllowDelete = false;
		foreach (Transform t in m_scatteredBlockHolder)
		{
			var block = t.GetComponentInChildren<Block>(true);
			if (block != null) block.DoNotAllowDelete = false; // can be null while loading
		}
		foreach (var part in parts)
		{
			bool found = false;
			for (int i = 0; i < blocks.Length; ++i)
			{
				var block = blocks[i];
				if (block.BlockID == part.m_blockID && block.DoNotAllowDelete == false)
				{
					block.DoNotAllowDelete = true;
					found = true;
					break;
				}
			}
			if (found == false)
			{
				// check scattered blocks in case we're loading with a special block on the table
				foreach (Transform t in m_scatteredBlockHolder)
				{
					var block = t.GetComponentInChildren<Block>(true);
					if (block != null && block.BlockID == part.m_blockID && block.DoNotAllowDelete == false)
					{
						block.DoNotAllowDelete = true;
						found = true;
						break;
					}
				}
				if (found == false)
					Debug.LogError($"Block {part.m_blockID} not found in special design");
			}
		}
	}

	private class CheckDesignConsistencyEntry
	{
		public string m_originalDesign;
		public System.Action<bool, string> m_callback;
	}
	private List<(string, bool, Action<bool, string>)> m_designConsistencyChecks = new List<(string, bool, Action<bool, string>)>();
	private bool m_designConsistencyCheckInProgress = false;
	public void CheckDesignConsistency(string _design, bool _isBuilding, System.Action<bool, string> _cb)
	{
		return; // GL - switching design consistency checks off
		m_designConsistencyChecks.Add(new(_design, _isBuilding, _cb));
		if (!m_designConsistencyCheckInProgress) CheckNextDesignConsistency();
	}

	private void CheckNextDesignConsistency()
	{
		if (isActiveAndEnabled || IsInDesignInPlace) m_designConsistencyChecks.Clear(); // if the user enters design mode kill checks since they blow things up
		if (m_designConsistencyChecks.Count > 0)
		{
			var top = m_designConsistencyChecks[0];
			m_designConsistencyChecks.RemoveAt(0);
			CheckDesignConsistencyWork(top.Item1, top.Item2, top.Item3);
		}
	}
	private void CheckDesignConsistencyWork(string _design, bool _isBuilding, System.Action<bool, string> _cb)
	{
		m_designConsistencyCheckInProgress = true;
		ClearTurntable();
		if(_isBuilding)
		{
			m_product = null;
		}
		else
		{
			m_product = new GameState_Product(_design);
			m_product.m_placeOfManufacture = m_designInPlaceBuilding.m_linkUID;
		}
		RestoreDesign(_isBuilding ? RestoreType.Building : RestoreType.Product, _design, null, (g, componentsChanged) =>
		{
			var snapshot = GenerateDesignData();
			_cb(AreDesignsDifferent(snapshot, _design), snapshot);
			CheckNextDesignConsistency();
		});
	}

	private void CreateScatterBlocks(List<string> _scattered)
	{
		m_scatteredBlockHolder.DestroyChildren();
		for (int i = 0; i < _scattered.Count; ++i)
			CreateScatteredBlockFromTableState(_scattered[i]);
			
		// Only add previous design if current design is empty
		if(m_currentDesign.HasDesign == false)
		{
			var lastDesign = GameManager.Me.m_state.m_designTableDetails.m_lastDesignByCategory.GetValue(Order.ProductLine);
			var lastProductPos = new Vector3(c_scatteredBlockMaxX, 0, c_scatteredBlockMaxZ + .04f);
			CreateScatteredBlock(lastProductPos, lastDesign, true);
		}
	}

	(Vector3, string) ExtractScatteredTableState(string _tableState)
	{
		var bits = _tableState.Split(new[] {"@@@@"}, StringSplitOptions.None);
		if (bits.Length != 2) return (Vector3.zero, null);
		var posBits = bits[0].Split(':');
		var pos = new Vector3(floatinv.Parse(posBits[0]), floatinv.Parse(posBits[1]), floatinv.Parse(posBits[2]));
		return (pos, bits[1]);
	}

	bool CreateScatteredBlockFromTableState(string _tableState)
	{
		var (pos, design) = ExtractScatteredTableState(_tableState);
		return CreateScatteredBlock(pos, design);
	}

	public class ScatteredBlockNoSave : MonoBehaviour { public Vector3 m_startPos; }

	bool CreateScatteredBlock(Vector3 pos, string design, bool _noSave = false)
	{
		if (design == null) return false;
		var tmp = new GameObject("tmp");
		tmp.transform.SetParent(m_scatteredBlockHolder.transform);
		tmp.transform.position = Vector3.zero;
		RestoreDesign(RestoreType.Product, design, tmp.transform, (o, componentsChanged) =>
		{
			var root = o.transform.GetChild(0);
			var parentVis = root.gameObject.GetComponent<Block>().m_toVisuals;
			o.transform.GetChild(0).SetParent(m_scatteredBlockHolder.transform, true);
			while (o.transform.childCount > 0)
				o.transform.GetChild(0).SetParent(parentVis, true);
			root.position = m_turntableOrigin.transform.position + pos;
			Destroy(tmp);
			if (_noSave)
			{
				root.GetComponent<DTDragBlock>().m_isFromPalette = true;
				root.gameObject.AddComponent<ScatteredBlockNoSave>().m_startPos = root.transform.position;
			}
		});
		return true;
	}

	bool DeleteScatteredBlockFromTableState(string _tableState)
	{
		var (pos, design) = ExtractScatteredTableState(_tableState);
		pos += m_turntableOrigin.transform.position;
		if (design == null) return false;
		foreach (Transform t in m_scatteredBlockHolder)
		{
			if ((t.position - pos).sqrMagnitude < .01f * .01f)
			{
				Destroy(t.gameObject);
				return true;
			}
		}
		return false;
	}
	
	void RestoreSavedState()
	{
		ClearTurntable();
		
		if (m_currentDesign == null)
		{
			var snapshot = GenerateDesignData();
			SetNewDesign(snapshot);
			AddToUndo(snapshot, TableState.GetScatteredCopy());
			CreateScatterBlocks(TableState.m_scatteredBlocks);
		}
		else
		{
			AddToUndo(m_currentDesign.m_design, TableState.GetScatteredCopy());
			RestoreDesign(RestoreType.Product, m_currentDesign.m_design, null, (o, componentsChanged) => CreateScatterBlocks(TableState.m_scatteredBlocks));
		}
	}
	public static bool AreDesignsDifferent(string _a, string _b)
	{
		if ((_a == null || _b == null) && _a != _b) return true;
		_a = _a.Replace(".-180|", ".180|");
		_b = _b.Replace(".-180|", ".180|");
		return _a.CompareTo(_b) != 0;
	}

	public static int CurrencyToSpendLevel(float _amount)
	{
		if (_amount < 0) _amount = -_amount;
		return (int)(_amount / 50.0f);
	}
	
	void ExecuteCurrencyDelta(float _spendDelta, bool _undo) {
		if(_spendDelta == 0) return;
		if (_undo) _spendDelta = -_spendDelta;
		var reason = _undo ? "Undo" : "Redo";
		if (_spendDelta < 0) NGPlayer.Me.m_cash.Spend(-_spendDelta, reason, "DesignTable", "DesignTable");
		else if (_spendDelta > 0)  NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Earned, _spendDelta, reason);
	}

	string EncodeScatteredTableState(Transform _scatteredBlockTransform)
	{
		string sbDesign = GenerateDesignData(false, _scatteredBlockTransform.gameObject);
		var pos = _scatteredBlockTransform.position - m_turntableOrigin.transform.position;
		return$"{pos.x.ToStringInv()}:{pos.y.ToStringInv()}:{pos.z.ToStringInv()}@@@@{sbDesign}";
	}

	void UpdateSavedState(bool _refreshInfo = true) {
		var snapshot = GenerateDesignData();
		if (IsInProductMode)
		{
			TableState.m_scatteredBlocks.Clear();
			foreach (Transform sbt in m_scatteredBlockHolder)
			{
				if(m_flaggedForDeletion.Contains(sbt) || !sbt.gameObject.activeSelf) continue;
				if (sbt.GetComponent<ScatteredBlockNoSave>() != null) continue;
				TableState.m_scatteredBlocks.Add(EncodeScatteredTableState(sbt));
			}
		}
		
		// TODO check that we don't need to do anything else
		if(AreDesignsDifferent(m_currentDesign?.m_design, snapshot))
		{
			m_currentDesign = new GameState_Design(snapshot);
		}

		GameManager.Me.WriteSaveData();
		AddToUndo(snapshot);
		if (_refreshInfo) RefreshDesignInfo();
		if (IsInProductMode)
			OnProductBlocksChanged?.Invoke(ActiveBlocksOnTable());
	}
	
	int ActiveBlocksOnTable() {
		int count = 0;
		foreach (Transform t in m_blockHolder.transform) {
			if (t.gameObject.activeSelf) ++count;
		}
		return count;
	}
	
	public GameState_Design GetDesignOnTable() {
		return m_currentDesign;
	}
	
	public float CalculateDesignCostDelta(string _from, string _to)
	{
		var order = Order;
		var fromCost = NGDesignInterface.Get(_from, order).TotalCost();
		var toCost = NGDesignInterface.Get(_to, order).TotalCost();
		return fromCost - toCost;
	}
	
	public float CalculateScatteredCostDelta(List<string> _from, List<string> _to)
	{
		float fromCost = 0;
		float toCost = 0;
		
		if(_from != null)
			foreach(var s in _from)
				fromCost += CalculateScatteredCost(s);
		if(_to != null)
			foreach(var s in _to)
				toCost += CalculateScatteredCost(s);
		return fromCost - toCost;
	}
	
	public float CalculateScatteredCost(string _scattered)
	{
		var bits = ExtractScatteredTableState(_scattered);
		if(bits.Item2 == null) return 0;
		return NGDesignInterface.Get(bits.Item2,Order).TotalCost();
	}
	
	public List<string> GetScattered() {
		List<string> blocks = new();
		foreach (Transform sbt in m_scatteredBlockHolder)
			if(sbt.gameObject.activeSelf && !m_flaggedForDeletion.Contains(sbt) && sbt.GetComponent<ScatteredBlockNoSave>() == null)
				blocks.Add(EncodeScatteredTableState(sbt));
		return blocks;
	}
	
	void SetNewDesign(string _design, bool _setNewDesign = true) 
	{
		if (_setNewDesign)
			m_currentDesign = new GameState_Design(_design);
			
		RefreshDesignInfo();
		
		m_currentDesign.CacheValues(m_dsi);
		
		if (IsDesigningProduct()) 
		{
			m_product.SetDesign(m_currentDesign);
			m_product.m_pricePerProduct = m_dsi.SellingPrice;
			m_product.m_placeOfManufacture = m_designInPlaceBuilding.m_linkUID;
		} 
		else if (IsBuilding()) 
		{
			m_designInPlaceBuilding.m_stateData.m_buildingDesign = m_currentDesign;
		}

		DesignTableAnalytics.m_lastAction = DesignTableAnalytics.kNewDesignConfirmed;
	}

	BlockWall GetRelevantWall(Block _block, Transform _blockHinge)
	{
		DynamicBuildingHinge dh;
		SnapHinge sh;
		if ((dh = _blockHinge.GetComponent<DynamicBuildingHinge>()) != null)
		{
			switch (dh.m_type)
			{
				case DynamicBuildingHinge.EType.Back: return _block.m_wallFront;
				case DynamicBuildingHinge.EType.Front: return _block.m_wallBack;
				case DynamicBuildingHinge.EType.Right: return _block.m_wallRight;
				case DynamicBuildingHinge.EType.Left: return _block.m_wallLeft;
			}
		}
		else if ((sh = _blockHinge.GetComponent<SnapHinge>()) != null)
		{
			switch (sh.GetHingeDirectionType())
			{
				case SnapHinge.EType.Back: return _block.m_wallFront;
				case SnapHinge.EType.Front: return _block.m_wallBack;
				case SnapHinge.EType.Right: return _block.m_wallRight;
				case SnapHinge.EType.Left: return _block.m_wallLeft;
			}
		}
		return null;
	}
	
	const float c_minAboveHeightDifference = 3.0f;
	const float c_maxAboveHeightDifference = 5.0f;
	const float c_doubleHeightThreshold = 7.0f;

	void RemoveWallAbove(Block[] _allBlocks, Block _block, Transform _wallHinge, bool _onlyPrimary)
	{
		for (int i = 0; i < _allBlocks.Length; ++i)
		{
			var other = _allBlocks[i];
			if (other == _block || other == null) continue;
			var otherHinges = other.m_toHinges;
			for (int j = 0; j < otherHinges.childCount; ++j)
			{
				var otherHinge = otherHinges.GetChild(j);
				if (Vector3.Dot(otherHinge.forward, _wallHinge.forward) < .9f) continue;
				var d = otherHinge.position - _wallHinge.position;
				if (d.xzSqrMagnitude() > .1f * .1f) continue;
				if (d.y > c_maxAboveHeightDifference || d.y < c_minAboveHeightDifference) continue;
				var wall = GetRelevantWall(other, otherHinge);
				if (wall != null)
					wall.Enable(false, _onlyPrimary);
			}
		}
	}
	
	void CheckInnerWalls(GameObject _blockHolder, GameObject[] _blockObjects = null, Block[] _finalBlocks = null) {
		if(_blockHolder == null && _blockObjects == null)
			return;
		
		Block[] blocks;
		if (_finalBlocks != null)
		{
			blocks = _finalBlocks;
		}
		else if (_blockObjects == null) {
			blocks = new Block[_blockHolder.transform.childCount];
			for (int i = 0; i < blocks.Length; ++i) {
				blocks[i] = _blockHolder.transform.GetChild(i).GetComponent<Block>();
			}
		} else {
			blocks = new Block[_blockObjects.Length];
			for (int i = 0; i < blocks.Length; ++i) {
				blocks[i] = _blockObjects[i]?.GetComponent<Block>();
			}
		}
		for (int i = 0; i < blocks.Length; ++i) {
			if (blocks[i] != null) {
				blocks[i].m_wallFront?.Enable(true, false);
				blocks[i].m_wallBack?.Enable(true, false);
				blocks[i].m_wallLeft?.Enable(true, false);
				blocks[i].m_wallRight?.Enable(true, false);
			}
		}
		for (int i = 0; i < blocks.Length; ++i) {
			var blockI = blocks[i];
			if (blockI == null) continue;
			for (int j = i + 1; j < blocks.Length; ++j) {
				var blockJ = blocks[j];
				if (blockJ == null) continue;
				if ((blockI.m_wallCategory & blockJ.m_wallCategory) == 0) continue;
				for (int ihinge = 0; ihinge < blockI.m_toHinges.childCount; ++ihinge) {
					var ihingeObj = blockI.m_toHinges.GetChild(ihinge);
					for (int jhinge = 0; jhinge < blockJ.m_toHinges.childCount; ++jhinge) {
						var jhingeObj = blockJ.m_toHinges.GetChild(jhinge);
						if ((ihingeObj.transform.position - jhingeObj.transform.position).sqrMagnitude < .01f * .01f) {
							var bwFrom = GetRelevantWall(blockI, ihingeObj);
							var bwTo = GetRelevantWall(blockJ, jhingeObj);
							if (bwFrom != null && bwTo != null) {
								if (blockJ.m_dynamicRemoveType != Block.EDynamicRemoveType.None)
								{
									bwFrom.Enable(false, blockJ.m_dynamicRemoveType == Block.EDynamicRemoveType.OnlyPrimary);
									if (blockJ.VisualBounds.max.y >= c_doubleHeightThreshold)
										RemoveWallAbove(blocks, blockI, ihingeObj, blockJ.m_dynamicRemoveType == Block.EDynamicRemoveType.OnlyPrimary);
								}
								if (blockI.m_dynamicRemoveType != Block.EDynamicRemoveType.None)
								{
									bwTo.Enable(false, blockI.m_dynamicRemoveType == Block.EDynamicRemoveType.OnlyPrimary);
									if (blockI.VisualBounds.max.y >= c_doubleHeightThreshold)
										RemoveWallAbove(blocks, blockJ, jhingeObj, blockI.m_dynamicRemoveType == Block.EDynamicRemoveType.OnlyPrimary);
								}
							}
						}
					}
				}
			}
		}
		for (int i = 0; i < blocks.Length; ++i)
		{
			if (blocks[i] != null)
			{
				bool f = blocks[i].m_wallFront.IsEnabled; 
				bool b = blocks[i].m_wallBack.IsEnabled; 
				bool l = blocks[i].m_wallLeft.IsEnabled; 
				bool r = blocks[i].m_wallRight.IsEnabled;
				blocks[i].m_cornerFrontRight.Enable(f || r);
				blocks[i].m_cornerFrontLeft.Enable(f || l);
				blocks[i].m_cornerBackRight.Enable(b || r);
				blocks[i].m_cornerBackLeft.Enable(b || l);
			}
		}
	}

	void LinkParts(string[] bits, GameObject[] parts, bool _reparent) {
		int numParts = parts.Length;
		int numLinks = int.Parse(bits[1+numParts]);
		for (int i = 0; i < numLinks; ++i) {
			var link = bits[1+numParts+1+i].Split('.');
			int partFrom = int.Parse(link[0]);
			int hingeFrom = int.Parse(link[1]);
			int partTo = int.Parse(link[2]);
			int hingeTo = int.Parse(link[3]);
			int rotAroundHinge = 0;
			if (link.Length >= 5) rotAroundHinge = int.Parse(link[4]);
			SnapLink(parts[partFrom], (float)rotAroundHinge, hingeFrom, parts[partTo], hingeTo);
			if (_reparent) parts[partFrom].transform.SetParent(parts[partTo].transform, true);
		}
	}

	public static bool IsHeld(GameObject _o)
	{
		if (Me.IsInDesignGloballyActively == false) return false;
		if (Me.m_lastGrabList == null) return false;
		return Me.m_lastGrabList.Contains(_o);
	}

	public void FreezeTableContents(bool _freeze) {
		SetVisualSwitcherStateRecursively(m_blockHolder, _freeze);
	}

	public static void SetVisualSwitcherStateSingle(GameObject _o, bool _freeze)
	{
		int switchTo = (Me.IsEnabled || Me.m_isInDesignGlobally || Me.IsInDesignInPlace) && _freeze ? 0 : 1;
		if (_o == null) return;
		var vs = _o?.GetComponentInChildren<Block>()?.m_visualSwitcher;
		if (vs != null)
		{
			vs.SwitchTo(switchTo);
			vs.SetEnabled(!_freeze);
			_o.GetComponentInChildren<Decoration>()?.UpdateMaterials();
		}
	}

	public static void SetVisualSwitcherState(GameObject _o, bool _freeze) {
		int switchTo = (Me.IsEnabled || Me.m_isInDesignGlobally || Me.IsInDesignInPlace) && _freeze ? 0 : 1;
		if (_o == null) return;
		var vs = _o?.GetComponent<Block>()?.m_visualSwitcher;
		if (vs != null) {
			vs.SwitchTo(switchTo);
			vs.SetEnabled(!_freeze);
			_o.GetComponentInChildren<Decoration>()?.UpdateMaterials();
		}
	}
	public static void SetVisualSwitcherStateRecursively(GameObject _o, bool _freeze) {
		if (_o == null) return;
		SetVisualSwitcherState(_o, _freeze);
		foreach (Transform t in _o.transform) SetVisualSwitcherStateRecursively(t.gameObject, _freeze);
	}

	public static Transform GetHinge(Block _block, SnapHinge.EType _type)
	{
		foreach (var snap in _block.m_toHinges.GetComponentsInChildren<SnapHinge>(true))
		{
			if (snap.HingeDirection == _type)
			{
				return snap.transform;
			}
		}
		return null;
	}

	static void ExecuteAllFurnitureControls(Transform _root, bool _needsFoundations)
	{
		var blocks = _root.GetComponentsInChildren<Block>();
		ExecuteAllFurnitureControls(blocks, _needsFoundations);
	}

	static void ExecuteAllFurnitureControls(Block[] _blocks, bool _needsFoundations)
	{
		foreach (var block in _blocks)
		{
			ExecuteFurnitureControl(block, _blocks, _needsFoundations); 
		}
	}

	static void ExecuteFurnitureControl(Block _block, Block[] _allBlocks, bool _needsFoundations)
	{
		if (_block == null) return;
		bool isAtBottom = _block.transform.localPosition.y < 3, isAtTop = true;
		_block.ShowFoundations(_needsFoundations && isAtBottom);
		_block.CheckTopmost(_allBlocks);
		var furnitureControl = _block.GetComponent<BlockFurnitureControl>();
		if (furnitureControl != null)
		{
			var topHinge = GetHinge(_block, SnapHinge.EType.Top);
			if (topHinge != null)
			{
				foreach (var otherBlock in _allBlocks)
				{
					if (otherBlock == null || otherBlock == _block) continue;
					var bottomHinge = GetHinge(otherBlock, SnapHinge.EType.Bottom);
					if (bottomHinge != null)
					{
						var d = topHinge.position - bottomHinge.position;
						if (d.sqrMagnitude < .5f * .5f)
						{
							isAtTop = false;
							break;
						}
					}
				}
			}
			furnitureControl.Execute(isAtTop, isAtBottom);
		}
	}
	
	public static bool IsRestoring => s_outstandingRestoreOpperations.Count > 0 || s_pendingOperations.Count > 0;
	private static HashSet<int> s_outstandingRestoreOpperations = new ();
	private static int s_nextRestoreIndex = 0;
	
	public const char c_decorationSeparator = '@';
	public enum RestoreType
	{
		Building,
		Product,
		ImageCapture,
	}
	public void RestoreDesign(RestoreType _restoreType, string _design, Transform _parent, Action<GameObject,bool> _onComplete = null, bool _runUnlocks = false, bool _buildInHierarchy = false, SDictionary<int,ArrayWrapper<long>> _cmpIds = null) { // _parent null == on design table, otherwise general GO
		bool isOnDesignTable = _parent == null || _parent.IsChildOf(m_scatteredBlockHolder); 
		bool isWildBlock = m_wildBlockHolder == _parent;
		int parentID = _parent == null ? 0 : _parent.gameObject.GetInstanceID();
		string parentDesc = _parent == null ? "" : $"{_parent.name} {_parent.GetComponentInParent<NGMovingObject>()?.GetType()?.ToString() ?? ""} {_parent.GetComponentInParent<NGMovingObject>()?.gameObject?.GetInstanceID() ?? -1}";
		
		var bits = (_design ?? "").Split(c_designSeparator);

		int numParts;
		if (string.IsNullOrEmpty(_design) || _design == "0|0|" || !int.TryParse(bits[0], out numParts)) {
			if (_onComplete != null) _onComplete(isWildBlock ? null : _parent?.gameObject, false);
			return;
		}
		
		int restoreIndex = s_nextRestoreIndex++;
		s_outstandingRestoreOpperations.Add(restoreIndex);
		
		if (numParts == 1 && bits[1].StartsWith("#"))
			bits[1] = $"MABase{bits[1].Replace("#", "").Replace(":", "x")}";
		if (isOnDesignTable)
			ResetTurntableRot();
		var parts = new GameObject[numParts];
		int partsToComplete = numParts;
		bool componentsChanged = false;
		bool needsFoundations = _parent != null && isWildBlock == false;
		for (int ii = 0; ii < numParts; ++ii) {
			int i = ii; // for action capture
			var bits2 = bits[1+i].Split(c_decorationSeparator);
			var entry = bits2[0];
			// TODO use batchConsumes (similar to batchUnlocks)
			if (_runUnlocks) GameManager.ConsumeUnlock(entry);
			Block.Load(entry, (_g) => {
				parts[i] = _g;
				if (parts[i] != null)
				{
					foreach (var bc in parts[i].GetComponentsInChildren<BCBase>(true))
						bc.enabled = false;
					
					Block block = _g.GetComponent<Block>();
					block.m_indexInDesign = i;
					
					componentsChanged |= block.LoadComponents(GameManager.Me.m_state.m_maComponentData, _cmpIds);
					VerifyActiveColliderInObject(block.m_toVisuals);
					SetVisualSwitcherState(_g, false);
					if (bits2.Length > 1) Decoration.Import(parts[i].gameObject, bits2[1]);
					if (isOnDesignTable) {
						if(m_turntableOrigin != null)
							DTDragBlock.Attach(0, _g, entry, m_turntableOrigin.transform.position + Vector3.up * .1f, false, _parent, DesignTableManager.Me.IsInDesignInPlace ? 0 : 31, 1f);
						if(m_turntable != null)
							parts[i].transform.localRotation = Quaternion.identity;
							//parts[i].transform.eulerAngles = m_turntable.transform.parent.eulerAngles;
					} else {
						if (parts[i] != null) {
							float baseScale = 1;
							var info = NGBlockInfo.GetInfo(entry);
							if(info != null) baseScale = info.m_tableScale;
							float visScale = 1;
							if (baseScale > .001f) visScale = baseScale / c_defaultBuildingScale;
							parts[i].transform.parent = _parent;
							parts[i].transform.localPosition = Vector3.zero;
							parts[i].transform.localScale = Vector3.one * visScale;
						}
					}
				} else {
					Debug.LogError($"Error loading block {entry}");
				}
				
				if (--partsToComplete == 0) {
					s_outstandingRestoreOpperations.Remove(restoreIndex);
					for (int i = 0; i < parts.Length; ++i) if (parts[i] != null) parts[i].transform.SetSiblingIndex(i);
					LinkParts(bits, parts, _buildInHierarchy);
					var blocks = new Block[parts.Length];
					for (int i = 0; i < parts.Length; ++i)
					{
						var part = parts[i];
						if (part == null) continue;
						var block = part.GetComponent<Block>();
						blocks[i] = block;
					}
					ExecuteAllFurnitureControls(blocks, needsFoundations);
					if (isOnDesignTable) {
						ReseatBlocks();
						RefreshSpecialBlockFlags();
					} else {
						CheckInnerWalls(m_blockHolder, parts, blocks);
					}
					if (_onComplete != null)
					{
						if (isOnDesignTable == false && _parent == null && isWildBlock == false) // parent was destroyed during load
						{
							//Debug.LogError($"Parent destroyed during load {parentID} {parentDesc}");
							for (int i = 0; i < parts.Length; ++i)
								Destroy(parts[i]);
						}
						else
						{
							if(_restoreType == RestoreType.Building)
							{
								MABuilding building = _parent?.GetComponentInParent<MABuilding>(true);
								if(building != null)
								{
									for (int i = 0; i < parts.Length; ++i)
									{
										if (parts[i] == null) continue;
										foreach (var bc in parts[i].GetComponentsInChildren<BCBase>(true))
										{
											building.AddComponent(bc);
										}
									}
									building.StartupComponents();
								}
							}
							
							if(isWildBlock)
								_onComplete(_g, componentsChanged);
							else
								_onComplete(_parent == null ? null : _parent.gameObject, componentsChanged);
						}
					}
				}
			});
		}
	}
	
	private void VerifyActiveColliderInObject(Transform block)
	{ //this ensures a block always has only one non-trigger collider active (box vs mesh)
		MeshCollider[] meshColliders = block.GetComponentsInChildren<MeshCollider>();
		bool foundMeshColliders = System.Array.FindIndex(meshColliders, x => x.enabled && !x.isTrigger) > -1;
		var boxCollider = block.GetComponent<BoxCollider>();
		if (boxCollider != null)
			TryToggleBoxCollider(true, boxCollider, !foundMeshColliders);
	}

	public static void TryToggleBoxCollider(bool _enable, BoxCollider boxCollider, bool isNeeded)
	{
		if (_enable)
		{
			boxCollider.enabled = isNeeded || (boxCollider.enabled && boxCollider.isTrigger);
		}
		else
		{
			boxCollider.enabled = _enable;
		}
	}
	public Block GetBlockOnTable(string _id)
	{
        var blocks = m_blockHolder.GetComponentsInChildren<Block>();
		foreach(var block in blocks)
		{
			if (block.BlockID == _id)
				return block;
		}
		return null;
    }
    private Vector3 m_blockMin, m_blockMax, m_originalBlockMin, m_blockMove;
	private void CalculateBlockExtents(GameObject _override = null)
	{
		Transform blockHolder = _override != null ? _override.transform : m_blockHolder.transform;
		var blocks = blockHolder.GetComponentsInChildren<Block>();
		var rootXform = blockHolder.transform;
		Vector3 min = Vector3.one * 1e23f, max = Vector3.one * -1e23f;
		foreach (var b in blocks)
		{
			var d = rootXform.InverseTransformPoint(b.transform.position);
			min = Vector3.Min(min, d);
			max = Vector3.Max(max, d);
		}
		const float tileSize = BuildingPlacementManager.c_buildingTileSize;
		m_blockMin = min / c_cellSize;
		m_blockMax = max / c_cellSize;
		if (m_originalBlockMin.x > 1e22f) m_originalBlockMin = m_blockMin;
		m_blockMove = m_blockMin - m_originalBlockMin;
		m_blockMove.x = Mathf.RoundToInt(m_blockMove.x);
		m_blockMove.y = Mathf.RoundToInt(m_blockMove.y);
		m_blockMove.z = Mathf.RoundToInt(m_blockMove.z);
	}

	public bool IsBlockEditable(string _id) {
		var info = NGBlockInfo.GetInfo(_id);
		bool canUserEdit = true;//!info.m_userLockedInProducts.Contains(m_currentDesignType);
		return canUserEdit || GameManager.Me.IsSeedEditMode;
	}
	
	bool IsBuilding() { return !IsDesigningProduct() && m_designInPlaceBuilding != null; }
	bool IsDesigningProduct() { return m_product != null; }
	bool IsProductRedesigned() {
		if (!IsDesigningProduct()) return false;
		//if (!NGTutorialManager.Me.IsDesignTableFullyUnlocked()) return false;
		if (!m_designInPlaceBuilding.ProductMade.HasDesign) return false;
		if (m_designInPlaceBuilding.ProductMade.m_design == m_currentDesign) return false;
		if (m_designInPlaceBuilding.ProductMade.Design.m_design == GetDesignOnTable().m_design) return false;
		return true;
	}
	
	bool ApplyMirror(SnapHinge _source, bool _mirror) {
		bool wasMirrored = false;
		if (_source.gameObject.GetComponentInParent<Block>()?.UseFlippedVisuals(_mirror, out wasMirrored) ?? false) return wasMirrored;
		var t = _source.transform.parent.parent;
		wasMirrored = t.localScale.x < 0 || t.localScale.y < 0 || t.localScale.z < 0;
		Vector3 sourceScale = t.localScale;
		sourceScale.x = Mathf.Abs(sourceScale.x);
		sourceScale.y = Mathf.Abs(sourceScale.y);
		sourceScale.z = Mathf.Abs(sourceScale.z);
		if (_mirror) {
			switch (_source.GetHingeDirectionType()) {
				case (SnapHinge.EType.Left):
				case (SnapHinge.EType.Right):
					sourceScale.x = -sourceScale.x;
					break;
				case (SnapHinge.EType.Top):
				case (SnapHinge.EType.Bottom):
					sourceScale.y = -sourceScale.y;
					break;
				case (SnapHinge.EType.Front):
				case (SnapHinge.EType.Back):
					sourceScale.z = -sourceScale.z;
					break;
			}
		}
		t.localScale = sourceScale;
		return wasMirrored;
	}
	
	public bool UpdateMirror(SnapHinge _source, SnapHinge _target) {
		if (_source != null && _target != null && _source.Mirrorable) {
			bool shouldBeMirrored = _source.GetHingeDirectionType() != SnapHinge.EType.None && _target.HingeDirection == _source.HingeDirection;
			var wasMirrored = ApplyMirror(_source, shouldBeMirrored);
			if (wasMirrored != shouldBeMirrored) OffsetToCompensateForMirror(_source, wasMirrored, shouldBeMirrored);
			return true;
		}
		return false;
	}

	public bool m_dragInProgress = false;
	
	private LineRenderer m_snapLine;
	Transform m_lastSnapHighlightFrom = null, m_lastSnapHighlightTo = null;
	void SetSnapHighlight(Transform _who, bool _set) {
		if (_who == null) return;
		var snapVis = _who.gameObject.GetComponentInChildren<SnapVisuals>(true);
		if (snapVis != null)
			snapVis.Select(_set);
		//if (_who.gameObject.GetComponent<SnapHinge>() == null) return;
		//var r = _who.GetComponentInChildren<MeshRenderer>(true);
		//if (r != null) r.material = _set ? GlobalData.Me.m_snapPointMaterialSelected : GlobalData.Me.m_snapPointMaterialUnselected;
	}
	void ClearSnapHighlights() {
		SetSnapHighlight(m_lastSnapHighlightFrom, false);
		SetSnapHighlight(m_lastSnapHighlightTo, false);
		m_lastSnapHighlightFrom = null;
		m_lastSnapHighlightTo = null;
	}
	void SetSnapHighlights(Transform _from, Transform _to) {
		m_lastSnapHighlightFrom = _from;
		m_lastSnapHighlightTo = _to;
		SetSnapHighlight(m_lastSnapHighlightFrom, true);
		SetSnapHighlight(m_lastSnapHighlightTo, true);
	}

	private void CheckSnapLine()
	{
		if (m_snapLine == null)
		{
			var go = Instantiate(GlobalData.Me.m_pickupLinePrefab, GlobalData.Me.transform);
			m_snapLine = go.GetComponent<LineRenderer>();
			go.layer = GameManager.c_layerTerrain;
		}
	}

	public void UpdateSnapLine(Transform _from, Transform _to, bool _isGroundSnap = false) {
		if (_from != m_lastSnapHighlightFrom || _to != m_lastSnapHighlightTo) {
			ClearSnapHighlights();
			SetSnapHighlights(_from, _to);
			if (GameManager.Me.IsOKToPlayDesignTableSound() && m_bezSound == 0)
				m_bezSound = AudioClipManager.Me.PlaySoundOld("PlaySound_BezierLine_Loop", GameManager.Me.transform);
			AudioClipManager.Me.SetBezierDistance((_from.position-_to.position).magnitude);
		}
		CheckSnapLine();
		m_snapLine.gameObject.SetActive(true);
		m_snapLine.textureScale = new Vector2(.25f, 1);
		var bezier = m_snapLine.gameObject.GetComponent<BezierLine>();
		var fromPos = _from.position;
		var toPos = _to.position;
		bool fromSnap = _from.GetComponent<SnapHinge>() != null;
        bool toSnap = _to.GetComponent<SnapHinge>() != null;
		var fromFwd = fromSnap ? _from.forward : _from.up;
		var toFwd = toSnap ? _to.forward : _to.up;
		float widthScale = CurrentBlockBuildScale;
		float lengthPerPoint = .5f;
		if (IsInProductMode)
		{
            widthScale *= .5f + CurrentZoom * .5f;
            lengthPerPoint = .005f;
		}
		if (m_isOverHarness)
		{
			var plane = new Plane(m_blockCage.transform.up, m_blockCage.transform.position);
			var ray = new Ray(fromPos, -m_blockCage.transform.up);
			plane.Raycast(ray, out var hit);
			toPos = ray.GetPoint(hit);
			fromFwd = toFwd = Vector3.zero;
			widthScale = .02f;
		}
		const float c_pushFwdSize = .06f;
		float pushFwdSteps = IsMakingBuilding ? 8.0f : 200.0f;
		//float pushFwd = IsMakingBuilding ? (fromPos - toPos).magnitude * c_pushFwdSize : .01f;
		float pushFwd = IsMakingBuilding ? .1f : .004f;
		fromFwd *= pushFwd; toFwd *= pushFwd;
		if (fromSnap)
			fromPos += fromFwd * (pushFwd * pushFwdSteps);
		if (toSnap)
			toPos += toFwd * (pushFwd * pushFwdSteps);
		var a = fromPos + fromFwd;
		var b = a + fromFwd * 15f;
		var d = toPos + toFwd;
		var c = d + toFwd * 15f;
		var b2 = Vector3.Lerp(b, c, .25f) + fromFwd * 2;
		var c2 = Vector3.Lerp(b, c, .75f) + toFwd * 2;
		var p = new Vector3[6];
		p[0] = a; p[1] = b; p[2] = b2; p[3] = c2; p[4] = c; p[5] = d;
		bezier.SetControlPoints(p, lengthPerPoint);
		m_snapLine.widthMultiplier = widthScale; 
		var clr = _isGroundSnap ? GlobalData.Me.m_miscNegativeBezierColour : GlobalData.Me.m_miscPositiveBezierColour;
		DragBase.UpdateBezier(m_snapLine, clr, GlobalData.Me.m_designTableXrayColour);
	}
	public void RemoveSnapLine()
	{
		if (m_snapLine == null) return;
        if (GameManager.Me.IsOKToPlayDesignTableSound() && m_bezSound != 0)
        {
            AudioClipManager.Me.StopSound(m_bezSound, GameManager.Me.gameObject);
            m_bezSound = 0;
        }
		m_snapLine.gameObject.SetActive(false);
		ClearSnapHighlights();
	}
	bool m_isCurrentlyPositive = true;
	Dictionary<Renderer, Material[]> m_currentDragBackupMaterials;
	public void SetPositiveMaterial(List<GameObject> _held, bool _positive) {
		if (_positive != m_isCurrentlyPositive) {
			m_isCurrentlyPositive = _positive;
			if (_positive) _held[0].RestoreAllMaterials(m_currentDragBackupMaterials);
			else m_currentDragBackupMaterials = _held[0].ReplaceAllMaterials(GlobalData.Me.m_negativeSelectionMaterial);
		}
	}

	public GameObject[] m_currentLabels = new GameObject[3];
//	public Transform m_snapPointProductTag;
//	public Transform m_snapPointProductLine;
	public Transform m_snapPointProductPrice;
	public Transform m_factorySpawnPos;
	public void SnapSpecial(GameObject _obj, EProductItemType _snap, bool _apply) {
		Transform snapTo;
		switch (_snap) {
			default:
//			case EProductItemType.Product_Tag: snapTo = m_snapPointProductTag; break;
//			case EProductItemType.Product_Line: snapTo = m_snapPointProductLine; break;
			case EProductItemType.Product_Price: snapTo = m_snapPointProductPrice; break;
		}
		if (_apply) {
			if (m_currentLabels[(int)_snap] != null) 
                Destroy(m_currentLabels[(int)_snap]);
			m_currentLabels[(int)_snap] = _obj;
			_obj.transform.parent = snapTo;
			_obj.transform.localPosition = Vector3.zero;
			_obj.transform.localEulerAngles = Vector3.zero;
			var bits = _obj.name.Split('|');
			var details = bits[bits.Length-1].Replace("(Clone)", "");
			if (m_product != null) {
				switch (_snap) {
					default:
//					case EProductItemType.Product_Tag: m_product.m_tag = details; break;
//					case EProductItemType.Product_Line: m_product.m_productLine = details; break;
					case EProductItemType.Product_Price: break;
				}
			}
		} else {
			UpdateSnapLine(_obj.transform, snapTo);
		}
	}

#if UNITY_EDITOR
	static DebugConsole.Command s_debugsnaps = new DebugConsole.Command("debugsnaps", _s => DebugHingeSnaps = Utility.SetOrToggle(DebugHingeSnaps, _s));
	public static bool DebugHingeSnaps = false;
	static DebugConsole.Command s_debugsnapuses = new DebugConsole.Command("debugsnapuses", _s => DebugHingeSnapUses = Utility.SetOrToggle(DebugHingeSnapUses, _s));
	static bool DebugHingeSnapUses = false;
	static DebugConsole.Command s_debugnophys = new DebugConsole.Command("dtnophys", _s => DebugNoSnapPhysics = Utility.SetOrToggle(DebugNoSnapPhysics, _s));
	static bool DebugNoSnapPhysics = false;
	static bool IsDebugSnap(SnapHinge _hinge)
	{
		if (!(Selection.activeObject is GameObject go)) return true;
		return _hinge.transform.IsChildOf(go.transform);
	}
#else
	static bool DebugHingeSnaps => false;
	static bool DebugHingeSnapUses => false;
	static bool DebugNoSnapPhysics => false;
	static bool IsDebugSnap(SnapHinge _hinge) => false;
#endif

	public bool IsSnapActive(Transform _t)
	{
		if (_t.IsChildOf(m_scatteredBlockHolder)) return false;
		if (m_isInDesignGlobally == false) return true;
		if (m_designGloballyFocusBuilding == null) return true;
		return _t.IsChildOf(m_designGloballyFocusBuilding.transform);
	}

	public bool IsArmourType => m_currentDesignCategory == "Armour" || m_currentDesignCategory == "Clothing";
	
	const float c_scatteredBlockMinX = -.5f;
	const float c_scatteredBlockMaxX = .5f;
	const float c_scatteredBlockMinZ = -.3f;
	const float c_scatteredBlockMaxZ = .24f;

	const float c_scatterBlockExcludeRadius = .3f; 
	const float c_turntablePosToTop = .04f;
	public float LowestDragHeight => m_turntableVisual.transform.position.y + (IsInDesignInPlace ? 0 : c_turntablePosToTop);
	public (SnapHinge, SnapHinge, bool) SnapBlock(GameObject _obj, List<GameObject> _inHand, bool _apply, ref Transform _previousSnapObject, out bool _nullAction) {
		_nullAction = false;
		var blockType = _obj.GetComponent<DesignPartType>()?.DesignFitnessType ?? EDesignFitnessType.None;
		var cam = m_designCamera;
		var objT = _obj.transform;
		var hinges = _obj.GetComponentsInChildren<SnapHinge>(true);
		float bestD2 = 1e23f;
		SnapHinge bestMatch = null;
		SnapHinge bestSource = null;
		var camFwd = cam.transform.forward;
		var camFwdFlat = camFwd; camFwdFlat.y = 0; camFwdFlat.Normalize();
		// if magnets are very close in 2D give a big boost
		float forceSnap2DDistSqrd = Screen.height * .05f * Screen.height * .05f;
		
		bool ignoreExtent = NGBlockInfo.GetInfo(_obj.GetComponent<Block>().m_blockInfoID)?.m_extentType == BuildingPlacementManager.c_extentTypeNone;

		const float c_maxDGSnapDistance = 12;
		const float c_maxProductSnapDistance = .2f;
		float maxSnapDist = m_isInDesignGlobally ? c_maxDGSnapDistance : (IsMakingBuilding ? 100000 : c_maxProductSnapDistance);
		var bh = _obj.GetComponent<BuildHelper>(); 
		if (bh != null && bh.Building.HasBuildingComponent<BCBeacon>() == false) maxSnapDist *= 2;
		if (IsDesigningProduct() && IsArmourType)
			maxSnapDist = 1e6f;
		float maxSnapDistSqrd = maxSnapDist * maxSnapDist;
		
		var maxSnapDistanceBiasDirection = camFwdFlat;
		var maxSnapDistanceBiasValue = DesignGloballyNonFocusMode ? .25f : .75f;

		bool IsSnapInDistance(Vector3 _from, Vector3 _to)
		{
			var fromToTo = _to - _from;
			var dot = Vector3.Dot(fromToTo, maxSnapDistanceBiasDirection);
			fromToTo -= maxSnapDistanceBiasDirection * (dot * maxSnapDistanceBiasValue);
			return fromToTo.xzSqrMagnitude() <= maxSnapDistSqrd;
		}

		if (m_debugSnaps != null) m_debugSnaps.Clear();

		for (int ti = 0; ti < m_openBlocks.Count; ++ti) {
			var t = m_openBlocks[ti];
			if (t == null || t.gameObject == null) continue; // got destroyed
			if (IsSnapActive(t) == false)
				continue;
			
			var checkHinges = t.gameObject.GetComponentsInChildren<SnapHinge>(true);
			for (int i = 0; i < hinges.Length; ++i) {
				var heldHinge = hinges[i];

				if ((m_heldUsedHinges & (1ul << i)) != 0)
					continue; // hinge is used by another held block
				
				var screenDirectionModifierI = 0f;//Vector3.Dot(heldHinge.transform.forward, camFwdFlat); // Ignore held part orientation
				screenDirectionModifierI = 1 + screenDirectionModifierI * screenDirectionModifierI;
				for (int j = 0; j < checkHinges.Length; ++j) {
					var staticHinge = checkHinges[j];
					if (staticHinge.gameObject.activeInHierarchy == false) continue;

					//if ((heldHinge.transform.position - staticHinge.transform.position).xzSqrMagnitude() > maxSnapDistSqrd)
					if (IsSnapInDistance(_obj.transform.position, t.position) == false)
					{
						if (DebugHingeSnaps && IsDebugSnap(staticHinge)) 
						{
							//Debug.LogError($"<color=#ffe060>S:{staticHinge.transform.parent.parent.name}.{staticHinge.transform.name} H:{heldHinge.transform.parent.parent.name}.{heldHinge.transform.name} TooFar</color> {staticHinge.gameObject.GetInstanceID()}", staticHinge.gameObject);
						}
						continue;
					}

					// check if hinge j is in use other than with object
					if ((m_usedHinges[ti] & 1ul << j) != 0) {
						if (DebugHingeSnaps && IsDebugSnap(staticHinge)) {
							//Debug.LogError($"<color=#ffc040>S:{staticHinge.transform.parent.parent.name}.{staticHinge.transform.name} H:{heldHinge.transform.parent.parent.name}.{heldHinge.transform.name} InUse</color> {staticHinge.gameObject.GetInstanceID()}", staticHinge.gameObject);
						}
						continue;
					}
					var turntableToStatic = staticHinge.transform.position - m_turntableOrigin.transform.position;
					float rootModifier = (turntableToStatic.sqrMagnitude < .1f*.1f) ? 10f : 1f;

					// if the piece is on the table, don't snap under it
					if (IsInDesignInPlace && turntableToStatic.y * turntableToStatic.y < .1f * .1f && staticHinge.transform.forward.y < -.5f) continue;
					
					var d2 = GetSnapScore(bestD2, heldHinge, staticHinge, blockType, cam, camFwd, camFwdFlat, rootModifier, forceSnap2DDistSqrd);
					
					if (d2 < bestD2)
					{
						bool isValidSnap;
						const bool c_newBoundsChecks = false;
						if (c_newBoundsChecks)
						{
							float yRotation = 0;
							if (staticHinge.HingeDirection == SnapHinge.EType.Top || IsInProductMode)
							{
								// snapping to a Top hinge (building mode), record the relative y-orientation
								yRotation = Vector3.SignedAngle(staticHinge.transform.up, heldHinge.transform.up, staticHinge.transform.forward);
							}

							isValidSnap = !CheckForBlockersNew(_obj.transform, yRotation, staticHinge.transform, heldHinge.transform, ignoreExtent);
						}
						else
							isValidSnap = !CheckForBlockers(staticHinge.transform, heldHinge.transform, ignoreExtent);
						if (isValidSnap)
						{
							bestD2 = d2;
							bestMatch = staticHinge;
							bestSource = heldHinge;
						}
					}
				}
			}
		}

		bool isValid = false;
		bool isRootFree = IsRootFree();
		SnapHinge bottomHinge = null;
		if (bestSource == null)
		{
			foreach (var h in hinges)
			{
				if (h.HingeDirection == SnapHinge.EType.Bottom)
				{
					if (h.gameObject.GetComponentInParent<Block>().gameObject == _obj)
					{
						bottomHinge = h;
						break;
					}
				}
			}
		}
		
		if (isRootFree && bestSource == null && !m_isInDesignGlobally && bottomHinge != null)
			if (IsSnapInDistance(bottomHinge.transform.position, m_turntableOrigin.transform.position))
				bestSource = bottomHinge;

		var heldBlockID = _inHand[0].GetComponent<Block>().BlockID;
		bool wasPlacedOnDesign = false;

		if (Input.GetKey(KeyCode.N))
		{
			// Temp hack: hold N to delete the block
		}
		else if (bestSource == null || m_wasOverHarness)
		{
			// no hit or too far, snap to ground
			_previousSnapObject = null;
			var groundPos = _obj.transform.position;
			if (m_isInDesignGlobally) 
				groundPos = groundPos.GroundPosition(2);
			else
			{
				var ttPos = m_turntableOrigin.transform.position;
				groundPos -= ttPos;
				groundPos.x = Mathf.Clamp(groundPos.x, c_scatteredBlockMinX, c_scatteredBlockMaxX);
				groundPos.y = -.015f;
				groundPos.z = Mathf.Clamp(groundPos.z, c_scatteredBlockMinZ, c_scatteredBlockMaxZ);
				if (groundPos.xzSqrMagnitude() < c_scatterBlockExcludeRadius * c_scatterBlockExcludeRadius)
					groundPos = groundPos.GetXZNorm() * c_scatterBlockExcludeRadius + Vector3.up * groundPos.y;
				groundPos += ttPos;
				
				var sbns = _obj.GetComponent<ScatteredBlockNoSave>();
				if(sbns != null)
				{
					groundPos = sbns.m_startPos;
					_obj.GetComponent<DTDragBlock>().m_isFromPalette = true;
					_nullAction = true;
				}
			}
			if (_apply)
			{
				if (m_wasOverHarness == false)
				{
					if (m_isInDesignGlobally)
					{
						m_grabbedToWildBlock = ConvertToWildBlock(_obj);
						m_lastTargetObject = null;
						EndGrab(m_wildBlockHolder, _inHand);
                    }
					else
					{
						// create a scattered block from held blocks
						_obj.transform.position = groundPos;
						_obj.transform.LookAt(_obj.transform.position + _obj.transform.forward, Vector3.up);
						_obj.transform.SetParent(m_scatteredBlockHolder, true);
						m_lastTargetObject = null;
						EndGrab(m_scatteredBlockHolder, _inHand);
						// bounds after end grab to make sure SwitchObjects has happened
						var bounds = ManagedBlock.GetTotalVisualBounds(_obj);
						_obj.transform.position += Vector3.up * (_obj.transform.position.y - bounds.min.y);
					}
				}
				else
				{
					m_blockCage.AddContent(_obj);
					m_lastTargetObject = null;
					EndGrab(m_blockCage.transform, _inHand);
				}
			}
			else
			{
				m_wildSnapHolder.position = groundPos;
				UpdateSnapLine(bottomHinge?.transform ?? _obj.transform, m_wildSnapHolder, true);
			}
			
			isValid = true;
		}
		else
		{
			wasPlacedOnDesign = true;
			var old = _obj.transform.position;
			if (bestSource != null && bestMatch != null)
			{
				if (_inHand.Count <= 1)
				{
					UpdateMirror(bestSource, bestMatch);
				}
				if (_apply)
				{
					//m_grabbedFromWildBlock = ConvertFromWildBlock(_obj);
					float yRotation = 0;
					if (bestMatch.HingeDirection == SnapHinge.EType.Top || IsInProductMode)
					{
						// snapping to a Top hinge (building mode), record the relative y-orientation
						yRotation = Vector3.SignedAngle(bestMatch.transform.up, bestSource.transform.up, bestMatch.transform.forward);
						//if (m_isInDesignGlobally) yRotation = Mathf.Round((yRotation + 360) / 90) * 90;
					}
					m_lastTargetObject = bestMatch.gameObject;
					SnapLink(_obj, yRotation, bestSource, bestMatch, _inHand);
				}
				else
				{
					var newSnapObject = bestMatch.GetComponentInParent<NGCommanderBase>()?.transform;
					if (_previousSnapObject != newSnapObject)
					{
						_previousSnapObject = newSnapObject;
						if (newSnapObject != null)
							_obj.transform.rotation = newSnapObject.rotation;
					}
					UpdateSnapLine(bestSource.transform, bestMatch.transform);
				}
				isValid = true;
			}
			else if (isRootFree)
			{
				// make sure special items snap properly - search all held hinges, if any of them are special then don't allow this
				bool isSpecialDisallowed = false;
				foreach (var h in hinges)
					if (h.IsClothesSource)
						isSpecialDisallowed = true; // any item with clothes source hinges can't be placed on the turntable

				if (isSpecialDisallowed == false)
				{
					if (_apply)
					{
						m_lastTargetObject = m_turntableOrigin;
						//m_grabbedFromWildBlock = ConvertFromWildBlock(_obj);
						BlendTransform(_obj.transform, 0, bestSource.transform, m_turntableOrigin.transform, _inHand, false);
					}
					else
					{
						UpdateSnapLine(bestSource.transform, m_turntableOrigin.transform);
					}
					isValid = true;
				}
			}
		}

		m_lastSnapWasPlacedOnDesign = wasPlacedOnDesign;
		if (_apply && wasPlacedOnDesign) {
			OnNewPartPlaced?.Invoke(heldBlockID);
			var sbns = _obj.GetComponent<ScatteredBlockNoSave>();
			if(sbns != null) Destroy(sbns);
		}
		return (bestMatch, bestSource, isValid);
	}

	GameObject m_lastTargetObject;
	void SetAudioSwitchForBlock(GameObject _o, GameObject _target)
	{
		string surface;
		if (_o == m_turntableOrigin)
		{
			surface = "Material_Turntable";
		}
		else if (_o == null) // null - dropped on table
		{
			surface = "Material_Hard";
		}
		else
		{
			var block = _o.GetComponentInParent<Block>();
			if (block.m_typeAudioSwitch.IsValid())
			{
				block.m_typeAudioSwitch.SetValue(_target);
				return;
			}
			var info = NGBlockInfo.GetInfo(block.m_blockInfoID);
			var line = info.m_mADrawerInfos.Split(':');
			// todo - these should come from block info or block data
			switch (line[0])
			{
				case "Food":
					if (line[1] == "Cutlery")
						surface = "Material_Metal";
					else
						surface = "Material_Soft";
					break;
				case "Weapons":
					surface = "Material_Metal";
					break;
				default:
					surface = "Material_Hard";
					break;
			}
		}
		AudioClipManager.Me.SetSurfaceType(surface, _target, true);
	}

	string GetAudioHookForBlock(GameObject _o, GameObject _target)
	{
		var block = _o.GetComponent<Block>();
		string eventID;
		if (block.m_connectAudioEvent.IsValid())
			eventID = block.m_connectAudioEvent.Name;
		else
		{
			var info = NGBlockInfo.GetInfo(block.m_blockInfoID);
			var line = info.m_mADrawerInfos.Split(':');
			// todo - these should come from block info or block data
			switch (line[0])
			{
				case "Food":
					if (line[1] == "Cutlery")
						eventID = "PlaySound_DesignTable_Cutlery_PlaceItem";
					else
						eventID = "PlaySound_DesignTable_Food_PlaceItem";
					break;
				case "Weapons":
					eventID = "PlaySound_DesignTable_Weapon_PlaceItem";
					break;
				default:
					eventID = "PlaySound_DesignTable_Building_PlaceItem";
					break;
			}
		}
		SetAudioSwitchForBlock(_target, _o);
		return eventID;
	}

	bool m_lastSnapWasPlacedOnDesign = false;
	public void PlaySnapAudio(GameObject _o)
	{
		string soundID;
		if (m_lastSnapWasPlacedOnDesign)
			soundID = m_isInDesignGlobally ? "PlaySound_TownDesignDropBlock" : GetAudioHookForBlock(_o, m_lastTargetObject);
		else
			soundID = m_isInDesignGlobally ? "PlaySound_TownDesignAttachBlock" : GetAudioHookForBlock(_o, m_lastTargetObject);
		AudioClipManager.Me.PlaySound(soundID, _o);
	}

	public void PlayPickupAudio(GameObject _o)
	{
		var soundID = m_isInDesignGlobally ? "PlaySound_TownDesignPickupBlock" : "PlaySound_TownDesignPickupBlock";
		AudioClipManager.Me.PlaySound(soundID, _o);
	}

	public void PlayDeleteAudio(GameObject _o)
	{
		var soundID = m_isInDesignGlobally ? "PlaySound_TownDesignDeleteBlock" : "PlaySound_DesignTable_DeleteItem";
		AudioClipManager.Me.PlaySound(soundID, GameManager.Me.gameObject); // about to delete _o so don't play the sound on that!
	}

	GameState_WildBlock m_grabbedFromWildBlock, m_grabbedToWildBlock;

	private bool IsRootFree()
	{
		// are we holding all of the blocks?
		if (BlockHolder.childCount == 0) return true;
		if (m_lastGrabList == null) return false;
		foreach (Transform t in BlockHolder)
			if (!m_lastGrabList.Contains(t.gameObject))
				return false;
		return true;
	}

	// Gets the change in value (price for products, score for buildings) effected when adding the specified block to the design on the table
	public float ValueModWithAddedBlock(string _s)
	{
		if (DesignMode == DESIGN_CATEGORY.AVATAR) return 0;
		var designOnTable = GetDesignOnTable();
		var design = designOnTable == null ? "0|0|" : designOnTable.m_design;
		var augmented = DesignUtilities.DesignWithAddBlock(design, _s);
		var newDI = NGDesignInterface.Get(augmented, Order);
		if (IsBuilding()) return newDI.TotalScore - m_dsi.TotalScore;
		return newDI.SellingPrice - m_dsi.SellingPrice;
	}
	
	bool m_currentDesignInvalid = false;
	string m_currentDesignInvalidReason = "";
    NGCarriableResource m_currentDesignInvalidStuff;
	
    public NGDesignInterface.DesignScoreInterface DesignInterface => m_dsi;
	private NGDesignInterface.DesignScoreInterface m_dsi = NGDesignInterface.Get();
	
	public void RefreshDesignInfo() 
	{
		m_dsi = NGDesignInterface.Get();
	
		if (m_refreshDrawerContents)
		{
			m_refreshDrawerContents = false;
			PopulateDrawerSet();
		}

		RefreshInfoDisplay();
		
		var design = GetDesignOnTable(); 
		if (design == null || !design.HasDesign)
		{
			CurrentDesignIndividualPartScores = new List<Tuple<NGBlockInfo, float>>();
			
			UpdatePriceLabel();
			RefreshBlockLabels();

            DesignUIManager.Me.m_problemDisplayText.text = "";
            return;
		}
		
		RefreshBlockLabels();
		
		float maxMaterials = 10000;
        float totalMaterials = 0;
		List<string> unsupportedMaterials = null;
		if(DesignMode != DESIGN_CATEGORY.AVATAR)
		{
			foreach (var kvp in m_dsi.MaterialsRequired) {
				totalMaterials += kvp.Value;
				if (!GameManager.Me.DoesTownSupportResource(kvp.Key)) {
	                if (unsupportedMaterials == null)
	                {
	                    unsupportedMaterials = new List<string>();
	                    m_currentDesignInvalidStuff = kvp.Key;
	                }
					unsupportedMaterials.Add(kvp.Key.m_title);
				}
			}
		}
		m_currentDesignInvalid = false;
		m_currentDesignInvalidReason = "";
		if (unsupportedMaterials != null) {
			//m_currentDesignInvalid = true;
			//m_currentDesignInvalidReason = $"Your town doesn't currently support material {unsupportedMaterials[0]}. ";
		} else if (totalMaterials > maxMaterials) {
			m_currentDesignInvalid = true;
			m_currentDesignInvalidReason = "You have used more materials in your design than your factory can store";
		}

		UpdatePriceLabel();

		OnRefreshInfo?.Invoke();
		m_lastGrabbedObjectDataOnDragEnd = null;

		DesignUIManager.Me.m_problemDisplayText.text = m_currentDesignInvalidReason;
	}
	
	public List<Tuple<NGBlockInfo, float>> CurrentDesignIndividualPartScores { get; set; }

	private void RefreshInfoDisplay()
	{
		if (m_designSingly) return;
		Utility.DoNextFrame(() => 
		{
			DTDragDrawer draw = null;
			if(m_blockPalette != null && m_blockPalette.m_drawers.Length > 0)
			 draw = m_blockPalette.m_drawers[m_currentDrawerIndex];
			RefreshDrawerBlockLabels(draw); 
		});
	}

	public void RefreshSave(bool _refreshInfo = true)
	{
		if (m_isInDesignGlobally)
		{
			UpdateDesignDataAndSave(true);
			RefreshDesignInfo();
			return;
		}
		ReseatBlocks();
		UpdateSavedState(_refreshInfo); //OLDSAVE
		m_grabbedFrom = m_grabbedTo = null;
		ClearGrabbedBlock();
		m_grabbedToTransform = null;
		RefreshSpecialBlockFlags();
		RefreshInfoDisplay();
	}

	public void ClearGrabbedBlock()
	{
		m_grabbedBlock = null;
	}
	
	public bool UpdateBuildingDesign(NGCommanderBase _ng, string _design, SDictionary<int,ArrayWrapper<long>> _cmpIds)
	{
		if (_ng == null) return false;
		if(_ng.Design != null && _ng.Design.m_design == _design)
			return false; // Not changed

		_ng.m_stateData.m_buildingDesign = new GameState_Design(_design, _cmpIds);
		return true;
	}

	public void RefreshDesign() {
		ReseatBlocks();
		var snapshot = GenerateDesignData();
		AddToUndo(snapshot);
		RefreshDesignInfo();
		OnProductBlocksChanged?.Invoke(ActiveBlocksOnTable());
	}

	public static float GetMinimumY(GameObject _root, float _default) {
		var bounds = ManagedBlock.GetTotalVisualBounds(_root);
		if (bounds.size.sqrMagnitude > 0) return bounds.center.y - bounds.extents.y;
		return _default;
	}
	public static void ClampAboveTable(GameObject _o) {
		float minY = GetMinimumY(_o, _o.transform.position.y);
		if (minY < Me.LowestDragHeight) _o.transform.position += Vector3.up * (Me.LowestDragHeight - minY);
	}
	void ReseatBlocks(Transform _override = null) {
		var blockHolder = _override ?? m_blockHolder?.transform;
		float minY = 1e23f;
		GameObject[] blocks = null;
		if (IsInDesignInPlace || m_isInDesignGlobally)
		{
			blocks = new GameObject[blockHolder.childCount];
			int next = 0;
			foreach (Transform t in blockHolder)
			{
				if (!t.gameObject.activeSelf) continue;
				if (m_flaggedForDeletion.Contains(t)) continue;
				t.GetComponent<Block>()?.ShowFoundations(t.transform.localPosition.y < 3);
				minY = Mathf.Min(minY, t.position.y);
				blocks[next++] = t.gameObject;
			}
		}
		else if (m_blockHolder != null)
		{
			foreach (Transform t in m_blockHolder.transform)
			{
				if (!t.gameObject.activeSelf) continue;
				var block = t.GetComponent<Block>();
				if (IsInDesignInPlace) block.ShowFoundations(false);
				var bounds = ManagedBlock.GetTotalVisualBounds(t.gameObject);
				if (IsInDesignInPlace) block.ShowFoundations(t.transform.localPosition.y < 3);
				if (bounds.size.sqrMagnitude > 0)
				{
					var y = bounds.center.y - bounds.extents.y;
					minY = Mathf.Min(minY, y);
				}
			}
		}
		
		if(m_blockHolder != null)
		{
			float turntableTop = LowestDragHeight;
        	var moveInY = Vector3.up * (turntableTop - minY);
			foreach(Transform t in m_blockHolder.transform)
			{
				t.position += moveInY;
			}
		}
		
		if(blockHolder != null)
		{
			CheckInnerWalls(blockHolder.gameObject, blocks);
			CalculateBlockExtents(blockHolder.gameObject);
			ExecuteAllFurnitureControls(blockHolder, true);
		}
	}
	
	Vector3 SnapPosAdjustForTableSurface(Transform _sourceObject, Transform _source, Transform _to, bool _negateForward) {
		var posAdjust = Vector3.zero;
		if (!_negateForward) { // snapping to table, make sure we don't snap under
			var oldPos = _sourceObject.position;
			var newPos = _sourceObject.position + _to.position - _source.position;
			_sourceObject.position = newPos;
			ClampAboveTable(_sourceObject.gameObject);
			posAdjust.y = _sourceObject.position.y - newPos.y;
			_sourceObject.position = oldPos;
		}
		return posAdjust;
	}
	void BlendTransform(Transform _sourceObject, float _yRotation, Transform _source, Transform _to, List<GameObject> _inHand, bool _negateForward = true, bool _blend = true, bool _isFinal = true) {
		if (_blend) {
			if (_inHand != null && _inHand.Count > 0 && _inHand[0].transform.parent == m_scatteredBlockHolder)
				_inHand[0].transform.SetParent(m_blockHolder.transform, true);
            if (GameManager.Me.IsOKToPlayDesignTableSound())
                AudioClipManager.Me.PlaySoundOld("PlaySound_Table_ItemDrop", transform);
            m_coroutineOwner.StartCoroutine(Co_BlendTransform(_sourceObject, _yRotation, _source, _to, _inHand, _negateForward));
		} else {
			var toUp = _to.up;
			var toForward = _to.forward;
			if (_to.GetComponent<SnapHinge>() == null)
				(toUp, toForward) = (-toForward, -toUp);
			
			var toUpAdjusted = toUp.RotateAbout(toForward, Mathf.Deg2Rad * _yRotation);
			var fromRot = _sourceObject.rotation;
			var srcRot = _source.rotation;
			float forwardMul = _negateForward ? -1 : 1;
			var tgtRotFinal = Quaternion.LookRotation(toForward * forwardMul, toUpAdjusted);
			var posAdjust = SnapPosAdjustForTableSurface(_sourceObject, _source, _to, _negateForward);
			_sourceObject.rotation = tgtRotFinal * Quaternion.Inverse(srcRot) * fromRot;
			_sourceObject.position += _to.position - _source.position + posAdjust;
			if (_isFinal)
				InheritVisuals(_sourceObject.gameObject, _source.GetComponent<SnapHinge>(), _to.GetComponent<SnapHinge>());
		}
	}
	private static bool s_isBlending = false; public static bool IsBlending => s_isBlending;
	IEnumerator Co_BlendTransform(Transform _sourceObject, float _yRotation, Transform _source, Transform _to, List<GameObject> _inHand, bool _negateForward) {
		s_isBlending = true;
		const float c_blendTime = .35f;
		var fromPos = _sourceObject.position;
		var fromRot = _sourceObject.rotation;
		var srcRot = _source.rotation;
		var srcDelta = _sourceObject.position - _source.position;
		float forwardMul = _negateForward ? -1 : 1;
		
		var toUp = _to.up;
		var toForward = _to.forward;
		if (_to.GetComponent<SnapHinge>() == null)
			(toUp, toForward) = (-toForward, -toUp);

		var posAdjust = SnapPosAdjustForTableSurface(_sourceObject, _source, _to, _negateForward);

		var toUpAdjusted = toUp.RotateAbout(toForward, Mathf.Deg2Rad * _yRotation);
		for (float t = 0; t < c_blendTime; t += Time.deltaTime) {
			if (_sourceObject == null || _to == null) break; // got destroyed
			var tSmooth = t / c_blendTime;
			tSmooth = tSmooth * tSmooth * (3f - tSmooth - tSmooth);
			var tgtRot = Quaternion.LookRotation(toForward * forwardMul, toUpAdjusted);
			var toRot = tgtRot * Quaternion.Inverse(srcRot) * fromRot;
			var toPos = srcDelta + _to.position + posAdjust;
			var nextRot = Quaternion.Lerp(fromRot, toRot, tSmooth);
			var nextPos = Vector3.Lerp(fromPos, toPos, tSmooth);
			_sourceObject.rotation = nextRot;
			_sourceObject.position = nextPos;
			yield return null;
		}

		if (_sourceObject != null && _to != null)
		{
			var tgtRotFinal = Quaternion.LookRotation(toForward * forwardMul, toUpAdjusted);
			_sourceObject.rotation = tgtRotFinal * Quaternion.Inverse(srcRot) * fromRot;
			_sourceObject.position += _to.position - _source.position + posAdjust;
		}
		InheritVisuals(_sourceObject.gameObject, _source.GetComponent<SnapHinge>(), _to.GetComponent<SnapHinge>());
		s_isBlending = false;
		yield return null;
		CheckBuildHelpers(_inHand);
		EndGrab(_to, _inHand);
	}
	void EndGrab(Transform _to, List<GameObject> _inHand)
	{
		if (_to != m_wildBlockHolder && _inHand != null && _inHand.Count > 0)
		{
			PlaySnapAudio(_inHand[0]);
			// remove all BuildHelpers since we have used them in placing here
			foreach (var item in _inHand)
				foreach (var helper in item.GetComponentsInChildren<BuildHelper>())
					if (helper.Height >= 0)
						Destroy(helper);
		}

		m_grabbedToTransform = _to;
		m_grabbedTo = _to.GetComponentInParent<NGCommanderBase>();
		if (m_isInDesignGlobally && m_grabbedTo != null && m_grabbedTo != m_grabbedFrom && m_grabbedBlock != null)
			m_grabbedBlock.transform.SetParent(m_grabbedTo.Visuals, true);
		Ungrab(_inHand);
        RefreshSave();
        EndDesignSingly();
	}

	void CheckBuildHelpers(List<GameObject> _inHand)
	{
		if (_inHand == null || _inHand.Count == 0) return;
		var obj = _inHand[0];
		var helper = obj.GetComponent<BuildHelper>();
		if (helper == null) return;
		foreach (Transform wb in m_wildBlockHolder)
		{
			if (wb == obj.transform) continue;
			var wildHelper = wb.GetComponent<BuildHelper>();
			if (wildHelper == null) continue;
			if (wildHelper.BuildingID == helper.BuildingID)
				return;
		}
		helper.Building.OnLastBuildHelper(obj);
	}

	void RemoveInheritedVisuals(GameObject _sourceObject)
    {
	    var block = _sourceObject.GetComponent<Block>();
	    foreach (var h in block.m_toHinges.GetComponentsInChildren<SnapHinge>())
        {
	        if (h.HingeCategory == SnapHinge.ECategory.InheritVisuals)
	        {
	            var child = h.transform.GetChild(0);
	            child.GetComponent<MeshRenderer>().enabled = false;
	            block.m_toVisuals.gameObject.SetActive(true);
	        }
        }
    }

	void InheritVisuals(GameObject _sourceObject, SnapHinge _source, SnapHinge _match)
	{
		if (_source.HingeCategory == SnapHinge.ECategory.InheritVisuals)
		{
			var sourceChild = _source.transform.GetChild(0);
			var matchChild = _match.transform.GetChild(0);
			// copy visual mesh and transform
			var sourceFilter = sourceChild.GetComponent<MeshFilter>();
			var matchFilter = matchChild.GetComponent<MeshFilter>();
			sourceFilter.mesh = matchFilter.mesh;
			sourceFilter.transform.rotation = matchFilter.transform.rotation;
			sourceFilter.transform.position = matchFilter.transform.position;
			var sourceRenderer = sourceChild.GetComponent<MeshRenderer>();
			sourceRenderer.enabled = true;
			if (string.IsNullOrEmpty(_match.m_inheritTag) == false)
			{
				// try to find a matching material
				var materialReplace = _source.GetMaterial(_match.m_inheritTag);
				if (materialReplace != null) sourceRenderer.material = materialReplace;
			}
			// copy mesh collider
			var sourceCollider = sourceChild.GetComponent<MeshCollider>();
			if (sourceCollider == null)
			{
				sourceCollider = sourceChild.gameObject.AddComponent<MeshCollider>();
				sourceCollider.convex = true;
			}
			var matchCollider = matchChild.GetComponent<MeshCollider>();
			var matchColliderMesh = matchCollider != null ? matchCollider.sharedMesh : matchFilter.mesh;
			sourceCollider.sharedMesh = matchColliderMesh;
			// hide main visual
			_sourceObject.GetComponent<Block>().m_toVisuals.gameObject.SetActive(false);
		}
	}

	void SnapLink(GameObject _sourceObject, float _yRotation, SnapHinge _source, SnapHinge _match, List<GameObject> _inHand, bool _blend = true) {
		if (_sourceObject == null || _source == null || _match == null) {
			Debug.LogError($"SnapFailed for {_sourceObject?.name} - {_source?.name} - {_match?.name}");
			return;
		}
		UpdateMirror(_source, _match);
		//InheritVisuals(_sourceObject, _yRotation, _source, _match, _inHand);
		BlendTransform(_sourceObject.transform, _yRotation, _source.transform, _match.transform, _inHand, true, _blend);
	}
	void SnapLink(GameObject _sourceObject, float _yRotation, int _sourceHinge, GameObject _matchObject, int _matchHinge) {
		if (_sourceObject == null || _matchObject == null) return;
		var sourceBlock = _sourceObject.GetComponentInChildren<Block>();
		var matchBlock = _matchObject.GetComponentInChildren<Block>();
		if (_sourceHinge < 0 || _sourceHinge >= sourceBlock.m_toHinges.childCount) _sourceHinge = 0;
		var sh = sourceBlock.m_toHinges.GetChild(_sourceHinge);
		var shSnap = sh.GetComponent<SnapHinge>();
		if (shSnap == null) shSnap = sh.gameObject.AddComponent<SnapHinge>();
		if (_matchHinge < 0 || _matchHinge >= matchBlock.m_toHinges.childCount) _matchHinge = 0;
		var mh = matchBlock.m_toHinges.GetChild(_matchHinge);
		var mhSnap = mh.GetComponent<SnapHinge>();
		if (mhSnap == null) mhSnap = mh.gameObject.AddComponent<SnapHinge>();
		SnapLink(_sourceObject, _yRotation, shSnap, mhSnap, null, false);
	}

	static float HingeSnapPriority(Transform _a, Transform _b, float _threshold = .001f) {
		if ((_a.position - _b.position).sqrMagnitude > _threshold*_threshold) return 0;
		//var dirMatch = Vector3.Dot(_a.up, _b.up) - Vector3.Dot(_a.forward, _b.forward); // -2 (opposite directions to expected) 0 (rotated) 2 (as expected)
		//if (dirMatch < 1.5f) return 0;
		var dirMatch = -Vector3.Dot(_a.forward, _b.forward); // ignoring up vectors since we can be rotated; -1 (wrong way) 0 (perpendicular) 1 (right way)
		if (dirMatch < .9f) return 0;
		var snapA = _a.GetComponent<SnapHinge>();
		var snapB = _b.GetComponent<SnapHinge>();
		if (snapA == null || snapB == null) return 0;
		if (snapA.HingeCategory == SnapHinge.ECategory.InheritVisuals)
		{
			if (snapB.HingeCategory == SnapHinge.ECategory.InheritVisualsSource) return 1;
			return 0;
		}
		if (snapB.HingeCategory == SnapHinge.ECategory.InheritVisuals)
		{
			if (snapA.HingeCategory == SnapHinge.ECategory.InheritVisualsSource) return 1;
			return 0;
		}
		if (snapA.CanSnapTo(snapB) == false)
			return 0;
		if (snapA.IsGeneric && snapB.IsGeneric && snapA.HingeCategory != snapB.HingeCategory && snapA.HingeCategory != SnapHinge.ECategory.LastChoice && snapB.HingeCategory != SnapHinge.ECategory.LastChoice)
			return .1f;
		if (SnapHinge.OppositeHinge.TryGetValue(snapA.HingeDirection, out var matchingDirection)) {
			if (snapB.HingeDirection == matchingDirection) {
				return 1f;
			} else if (snapA.Mirrorable || snapB.Mirrorable) {
				if (SnapHinge.OppositeHinge.TryGetValue(matchingDirection, out matchingDirection)) {
					if (snapB.HingeDirection == matchingDirection) {
						return 1f;
					}
				}
			}
		}
		return 0.2f;
	}
	public (List<Block>, int) GetBlockHierarchy(List<GameObject> _exclude = null, GameObject _override = null, bool _forceGrabAll = false) {
		if (_override != null && (_override.transform.parent == m_scatteredBlockHolder || _forceGrabAll))
		{
			// just grab all contained blocks
			var containedBlocks = _override.GetComponentsInChildren<Block>();
			var containedBlocksList = new List<Block>(containedBlocks); 
			return (containedBlocksList, containedBlocks.Length); 
		}
		var blocks = new List<Block>();
		var root = _override ?? m_blockHolder;//_override != null ? _override.Visuals.gameObject : m_blockHolder;
		if (root != null)
		{
			foreach (Transform t in root.transform)
			{
				if (!t.gameObject.activeSelf) continue;
				if (_exclude != null && _exclude.Contains(t.gameObject)) continue;
				var block = t.GetComponentInChildren<Block>();
				if (block != null)
				{
					blocks.Add(block);
				}
			}
		}
		int lastAssigned = 1;
		if (blocks.Count > 0) {
			// sort blocks based on hierarchy, give id zero to the item closest to turntable center
			// now giving priority to first block placed - might be better to go back to the above but with a proper check
			// (it used to use block center, should have checked all hinges)
			int bestI = -1;
			int bestSI = int.MaxValue;
			for (int i = 0; i < blocks.Count; ++i)
			{
				var block = blocks[i];
				int sI = block.transform.GetSiblingIndex();
				if (sI < bestSI)
				{
					bestI = i;
					bestSI = sI;
				}
			}
			Vector3 consistencyDot = new Vector3(1.23f, 4.56f, 7.89f);
			Block tmp = blocks[0]; blocks[0] = blocks[bestI]; blocks[bestI] = tmp;
			int nextAssign = 1;
			for (int i = 0; i < blocks.Count; ++i) {
				// find all blocks attached to this block, give them the next slots
				var blockI = blocks[i];
				int startAssign = nextAssign;
				for (int hi = 0; hi < blockI.m_toHinges.childCount; ++hi) {
					var hingeI = blockI.m_toHinges.GetChild(hi);
					for (int j = nextAssign; j < blocks.Count; ++j) {
						var blockJ = blocks[j];
						for (int hj = 0; hj < blockJ.m_toHinges.childCount; ++hj) {
							var hingeJ = blockJ.m_toHinges.GetChild(hj);
							if (HingeSnapPriority(hingeI, hingeJ) > 0) {
								// j snaps to i, give it the next slot
								tmp = blocks[nextAssign]; blocks[nextAssign] = blocks[j]; blocks[j] = tmp;
								++nextAssign;
								break;
							}
						}
					}
				}
				if (startAssign != nextAssign && i < lastAssigned) lastAssigned = nextAssign;
				int assignedToI = nextAssign - startAssign;
				if (assignedToI > 1)
				{
					// sort the newly linked blocks based on some consistent factor such as position
					blocks.Sort(startAssign, assignedToI, s_blockLinkComparer);
				}
			}
		}
		return (blocks, lastAssigned);
	}

	static private BlockLinkComparer s_blockLinkComparer = new BlockLinkComparer(); 
	public class BlockLinkComparer : IComparer<Block>
	{
		public int Compare(Block a, Block b)
		{
			var d = a.transform.position - b.transform.position;
			return (d.x * 1.23f + d.y * 4.56f + d.z * 7.89f).Sign();
		}
	}

	bool AreSnapped(Block _a, Block _b, bool _topOnly) {
		for (int hi = 0; hi < _a.m_toHinges.childCount; ++hi) {
			var hingeI = _a.m_toHinges.GetChild(hi);
			if (_topOnly && hingeI.name != "top" && hingeI.name != "Top0") continue;
			for (int hj = 0; hj < _b.m_toHinges.childCount; ++hj) {
				var hingeJ = _b.m_toHinges.GetChild(hj);
				if (HingeSnapPriority(hingeI, hingeJ) > 0) {
					return true;
				}
			}
		}
		return false;
	}

	private List<GameObject> m_lastGrabList = null; public List<GameObject> GrabList => m_lastGrabList;
	private List<Transform> m_openBlocks = null;
	private ulong[] m_usedHinges = null;
	private ulong m_heldUsedHinges = 0;
	
	public class Bitmap {
		private uint[] m_bits;
		private int m_maxX, m_maxY;
		public Bitmap(int _count) {
			m_maxX = _count; m_maxY = 0;
			m_bits = new uint[(_count+31) / 32];
		}
		public Bitmap(int _countX, int _countY) {
			int total = _countX *  _countY;
			m_maxX = _countX; m_maxY = _countY;
			m_bits = new uint[(total+31) / 32];
		}
		public bool this[int _x] {
			get => (m_bits[_x / 32] & (1u << (_x & 31))) != 0;
			set { if (value) m_bits[_x / 32] |= (1u << (_x & 31)); else m_bits[_x / 32] &= ~(1u << (_x & 31)); }
		}
		public bool this[int _x, int _y] {
			get => this[_x + _y * m_maxX];
			set => this[_x + _y * m_maxX] = value;
		}
	}


	public bool IsHingeUsed(Transform _parent, Transform _hinge) {
		if (m_openBlocks == null || m_usedHinges == null) return false;
		int parentIndex = m_openBlocks.IndexOf(_parent);
		if (parentIndex == -1) return false;
		int childIndex = _hinge.GetSiblingIndex();
		return (m_usedHinges[parentIndex] & (1UL << childIndex)) != 0;
	}
	
	private bool m_drawerSetsDirty = false;

	private void CheckDrawerSetsDirty()
	{
		if (m_drawerSetsDirty == false) return;
		m_drawerSetsDirty = false;
		SetDrawerSetType(m_currentDrawerSetType);
	}

	public static void ReturnBlock(GameObject _go, bool _destroyObject = true, bool _isReconstruction = false)
	{
		var block = _go.GetComponent<Block>();
		if (block != null)
		{
			Me.m_drawerSetsDirty |= GameManager.IsUnlocked(block.BlockID) == false;
			GameManager.AddUnlock(block.BlockID, 1);
		}

		if (_destroyObject)
		{
			_go.SetActive(false);
			Destroy(_go);
		}
	}

	private void RestoreOrphanMaterials()
	{
		if (m_orphanBlockMaterials != null)
		{
			foreach (var o in m_orphanBlocks)
			{
				if (o != null)
					o.RestoreAllMaterials(m_orphanBlockMaterials);
			}
			m_orphanBlocks = null;
			m_orphanBlockMaterials = null;
		}
	}

	public class BlockYCompare : IComparer<Block> { public int Compare(Block a, Block b) => a.transform.position.y.CompareTo(b.transform.position.y); }
	public static BlockYCompare s_blockYCompare = new();

	void DropOrphanBlocks()
	{
		(var dgBlocks, var dgBlocksLinked) = GetBlockHierarchy(null, m_grabbedFrom?.Visuals?.gameObject);
		dgBlocks.Sort(dgBlocksLinked, dgBlocks.Count-dgBlocksLinked, s_blockYCompare);
		for (int i = dgBlocksLinked; i < dgBlocks.Count; ++i)
		{
			// snap [i].bottom to closest .top
			var hinges = dgBlocks[i].m_toHinges.GetComponentsInChildren<SnapHinge>(true);
			SnapHinge bottom = null;
			foreach (var h in hinges)
				if (h.HingeDirection == SnapHinge.EType.Bottom)
					bottom = h;
			if (bottom != null)
			{
				var bestDistSqrd = 1e23f;
				SnapHinge bestSnap = null;
				for (int j = 0; j < i; ++j)
				{
					var toHinges = dgBlocks[j].m_toHinges.GetComponentsInChildren<SnapHinge>(true);
					SnapHinge top = null;
					foreach (var th in toHinges)
					{
						if (th.HingeDirection == SnapHinge.EType.Top)
						{
							top = th;
							var d = top.transform.position - bottom.transform.position;
							d.y *= .2f;
							var distSqrd = d.sqrMagnitude;
							if (distSqrd < bestDistSqrd)
							{
								bestDistSqrd = distSqrd;
								bestSnap = top;
							}
						}
					}
				}
				if (bestSnap != null)
					SnapLink(dgBlocks[i].gameObject, 0, bottom, bestSnap, null, false);
				else
					Debug.LogError($"Error: couldn't find snap to seat block {i} of {dgBlocks.Count}");
			}
		}
	}
	
	public void CullOrphanBlocks()
	{
		RestoreOrphanMaterials();
		if (m_isInDesignGlobally && m_grabbedFrom != null)
		{
			DropOrphanBlocks();
			return;
		}
		// now check for any orphaned blocks and kill them
		(var blocks, var blocksLinked) = GetBlockHierarchy();
		for (int i = blocksLinked; i < blocks.Count; ++i)
		{
			if (m_lastGrabList == null || !m_lastGrabList.Contains(blocks[i].gameObject))
				ReturnBlock(blocks[i].gameObject);
		}
	}
	private void CloseInfoUI()
	{
		foreach (Transform child in m_designTableUI)
		{
			BlockInfoPanel ip = child.GetComponent<BlockInfoPanel>();
			if (ip != null)
				ip.Close();
		}
	}


	public static void SetWorldTriPlanar(GameObject _o, bool _isWorld)
	{
		float worldValue = _isWorld ? 1 : 0;
		foreach (var rnd in _o.GetComponentsInChildren<Renderer>(true))
		{
			foreach (var mat in rnd.materials)
			{
				mat.SetFloat("_WorldTriPlanar", worldValue);
			}
		}
	}

	private NGCommanderBase m_grabbedFrom, m_grabbedTo;
	private Transform m_grabbedToTransform;
	private Block m_grabbedBlock;
	private List<GameObject> m_orphanBlocks;
	private Dictionary<Renderer, Material[]> m_orphanBlockMaterials = null; 
	private bool m_isGrabbedBlockExtentIgnorer = false; public bool IsGrabbedBlockExtentIgnorer => m_isGrabbedBlockExtentIgnorer;
	public bool GrabbingIsBlockedByTutorial { get { return m_grabbingIsBlockedByTutorial;} }
    /// <returns>
    /// List of gameobjects/transforms with root at 0.
    /// + boolean flag to indicate of block came from design table or not (drawers)
    /// </returns>
    public (List<GameObject>, bool) Grab(GameObject _root)
	{
		if (_root.transform.IsChildOf(m_scatteredBlockHolder))
			if (_root.transform.parent != m_scatteredBlockHolder)
				_root.transform.SetParent(m_scatteredBlockHolder); // un-nest scattered sub-block
		m_isGrabbedBlockExtentIgnorer = NGBlockInfo.GetInfo(_root.GetComponent<Block>().m_blockInfoID)?.m_extentType == BuildingPlacementManager.c_extentTypeNone;
		RemoveInheritedVisuals(_root);
		m_grabbedFrom = _root.GetComponentInParent<NGCommanderBase>();
		m_grabbedBlock = _root.GetComponent<Block>();
		CloseInfoUI();
		bool isFromTable = _root.transform.parent != null; 
		if (GameManager.Me.IsOKToPlayDesignTableSound())
		{
			if (isFromTable == false)
            {
                AudioClipManager.Me.PlaySoundOld("PlaySound_Table_ItemPickup", transform);
            }
            else
            {
                AudioClipManager.Me.PlaySoundOld("PlaySound_Table_ItemDetach", transform);
            }
        }

        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.DTDragPickup);

        ResetAllDragBlockCaches();
        
        m_isCurrentlyPositive = true;
		var res = new List<GameObject>();
		(var blocks, _) = GetBlockHierarchy();
		// find _root in blocks then find all lower-level connections
		var firstBlock = blocks.Count == 0 ? null : blocks[0].gameObject;
		if (blocks.Count > 1 && firstBlock.name.StartsWith("MABase")) firstBlock = blocks[1].gameObject;
		bool topsOnly = !IsInProductMode && blocks.Count > 0 && _root != firstBlock;
		res.Add(_root);
		for (int i = 0; i < blocks.Count; ++i) {
			if (blocks[i].gameObject == _root) {
				for (int j = i+1; j < blocks.Count; ++j) {
					// see if blocks[j] connects to anything in res
					for (int k = 0; k < res.Count; ++k) {
						var blockK = res[k].GetComponent<Block>();
						if (AreSnapped(blockK, blocks[j], topsOnly)) {
							res.Add(blocks[j].gameObject);
							break;
						}
					}
				}
				break;
			}
		}
		for (int i = 1; i < res.Count; ++i) {
			res[i].transform.SetParent(_root.GetComponent<Block>().m_toVisuals, true);
		}
		SetVisualSwitcherStateSingle(res[0], true);
		m_lastGrabList = res;
		
		for (int i = 0; i < res.Count; ++i)
			res[i].GetComponent<Block>().ShowFoundations(false);

		foreach (var wb in _root.GetComponentsInChildren<MAWildBlock>())
			m_grabbedFromWildBlock = ConvertFromWildBlock(wb.gameObject);
		
		DTDragBlock dragBlock = res[0].GetComponent<DTDragBlock>();
		
		UpdateGrabData(dragBlock,isFromTable);

		// find orphans - for each block not in the grabbed list, check that you can link to the hierarchy root without the grabbed blocks
		(var blocksPost, var blocksLinked) = GetBlockHierarchy(res);
		RestoreOrphanMaterials(); // restore any previous material changes in case we're spamming blocks
		m_orphanBlocks = new List<GameObject>();
		m_orphanBlockMaterials = new Dictionary<Renderer, Material[]>();
		for (int i = blocksLinked; i < blocksPost.Count; ++i)
		{
			m_orphanBlocks.Add(blocksPost[i].gameObject);
			blocksPost[i].gameObject.ReplaceAllMaterials(GlobalData.Me.m_negativeSelectionMaterial, m_orphanBlockMaterials);
		}
		
		// set up open blocks and used hinges
		m_openBlocks = new List<Transform>();
		if (m_isInDesignGlobally)
		{
			void AddBuildingSnaps(NGCommanderBase _ng)
			{
				if (_ng.Visuals == null) return;
				if (_ng.IsLocked) return;
				foreach (var block in _ng.Visuals.GetComponentsInChildren<Block>())
					if (res.Contains(block.gameObject) == false)
						m_openBlocks.Add(block.transform);
			}
			if (m_designGloballyFocusBuilding != null)
				AddBuildingSnaps(m_designGloballyFocusBuilding);
			else
				foreach (var ng in NGManager.Me.m_NGCommanderList)
					AddBuildingSnaps(ng);
		}
		else
		{
			foreach (Transform t in m_blockHolder.transform)
				if (res.Contains(t.gameObject) == false) // don't snap to things we're holding
					m_openBlocks.Add(t);
		}
		m_usedHinges = new ulong[m_openBlocks.Count];
		FillUsedHingesFromBuildHelper(_root);
		// mark the hinge that's attached to the turntable
		(var blocksHier, _) = GetBlockHierarchy(res);
		if (blocksHier.Count > 0)
		{
			var rootBlock = blocksHier[0]; 
			int root = m_openBlocks.IndexOf(rootBlock.transform);
			if (root != -1)
			{
				// mark the most appropriate hinge on the root block as used by turntable
				var rHinges = rootBlock.gameObject.GetComponentsInChildren<SnapHinge>(true);
				for (int rh = 0; rh < rHinges.Length; ++rh)
				{
					if (rHinges[rh].transform.forward.y < -.9f)
					{
						m_usedHinges[root] |= 1ul << rh;
						break;
					}
				}
			}
		} 
		// and mark all used hinges
		var closenessValue = IsMakingBuilding ? .1f : .001f;
		for (int i = 0; i < m_openBlocks.Count; ++i) {
			var iPos = m_openBlocks[i].transform.position;
			var iHinges = m_openBlocks[i].gameObject.GetComponentsInChildren<SnapHinge>(true);
			if (m_openBlocks[i].GetComponent<BaseBlock>() != null || (m_isInDesignGlobally && m_openBlocks[i].transform.localPosition.y < 2f))
				for (int ih = 0; ih < iHinges.Length; ++ih) // find down hinge and mark as used
					if (iHinges[ih].HingeDirection == SnapHinge.EType.Bottom)
						m_usedHinges[i] |= 1ul << ih;
			for (int j = i+1; j < m_openBlocks.Count; ++j) {
				var jPos = m_openBlocks[j].transform.position;
				if ((iPos - jPos).sqrMagnitude > (4 * 4) * (4 * 4)) continue; // 5x5 is the biggest base block at the moment, so 4 is a good margin
				var jHinges = m_openBlocks[j].gameObject.GetComponentsInChildren<SnapHinge>(true);
				for (int ih = 0; ih < iHinges.Length; ++ih) {
					for (int jh = 0; jh < jHinges.Length; ++jh) {
						if (HingeSnapPriority(iHinges[ih].transform, jHinges[jh].transform, closenessValue) > .1f) {
							m_usedHinges[i] |= 1ul << ih;
							m_usedHinges[j] |= 1ul << jh;
						}
					}
				}
			}
			if (!DebugNoSnapPhysics) {
				// mark all hinges occluded by another block
				Vector3 extent = new Vector3(4.0f, 3.5f, 4.0f) * .5f;
				var toCenterDistance = extent.x;
				extent *= .65f;
				var hits = new Collider[256];
				for (int ih = 0; ih < iHinges.Length; ++ih) {
					if ((m_usedHinges[i] & (1ul << ih)) == 0) {
						var iht = iHinges[ih].transform;
						int hitCount;
						if (m_isInDesignGlobally)
							hitCount = Physics.OverlapBoxNonAlloc(iht.position + iht.forward * toCenterDistance, extent, hits, iHinges[ih].transform.rotation);
						else
							hitCount = Physics.OverlapSphereNonAlloc(iht.position + iht.forward * .4f, .1f, hits);
						bool staticHit = false;
						for (int hit = 0; hit < hitCount; ++hit) {
							var h = hits[hit];
							if (h.isTrigger) continue;
							if (h.GetComponentInParent<Block>() == null) continue;
							if (m_isInDesignGlobally == false && h.gameObject.IsChildOf(m_blockHolder) == false) continue;
							if (iHinges[ih].GetComponentInParent<Block>() == h.GetComponentInParent<Block>()) continue;
							if (h.transform.IsChildOf(res[0].transform)) continue;
							staticHit = true;
#if UNITY_EDITOR
							if (DebugHingeSnapUses) {
								Debug.LogError($"<color=#ff40ff>Snap {iHinges[ih].transform.Path()}</color>", iht.gameObject);
								Debug.LogError($"<color=#40ffff>   hits {h.transform.Path()}</color>", h.gameObject);
								if (iht.gameObject == Selection.activeGameObject)
								{
									GameManager.Me.ClearGizmos("SnapUse");
									GameManager.Me.AddGizmoOCube("SnapUse", iht.position + iht.forward * toCenterDistance, iht.right * extent.x, iht.up * extent.y, iht.forward * extent.z, Color.red, true);
								}
							}
#endif
							break;
						}
						if (staticHit) {
							m_usedHinges[i] |= 1ul << ih;
						}
					}
				}
			}
			// mark used snaps for held object
			m_heldUsedHinges = 0;
			var heldHinges = res[0].gameObject.GetComponentsInChildren<SnapHinge>(true);
			for (int hi = 0; hi < heldHinges.Length; ++hi)
			{
				for (int j = 1; j < res.Count; ++j)
				{
					var heldHingesOther = res[j].gameObject.GetComponentsInChildren<SnapHinge>(true);
					for (int hj = 0; hj < heldHingesOther.Length; ++hj)
					{
						if (HingeSnapPriority(heldHinges[hi].transform, heldHingesOther[hj].transform, .1f) > .9f)
						{
							m_heldUsedHinges |= 1ul << hi;
						}
					}
				}
			}
			//Debug.LogError($"Pick up used hinges: {m_heldUsedHinges}");
		}
		return (res, isFromTable);
	}

	public bool TrySnapBuildHelper(BuildHelper buildHelper)
	{
		var block = buildHelper.GetComponent<Block>();
		var blockBottomSnaps = block.m_toHinges.GetComponentsInChildren<SnapHinge>();
		SnapHinge bottom = null;
		foreach (var snap in blockBottomSnaps)
		{
			if (snap.HingeDirection == SnapHinge.EType.Bottom)
			{
				bottom = snap;
				break;
			}
		}
		if (block == null) return false;
		var snapTo = GetSnapFromBuildHelper(buildHelper);
		if (snapTo == null) return false;
		var snapToSnap = snapTo.GetComponent<SnapHinge>();
		if (snapToSnap == null) return false;

		if (buildHelper.transform.IsChildOf(m_wildBlockHolder))
		{
			ConvertFromWildBlock(buildHelper.gameObject);
			if (m_isInDesignGlobally == false)
				Destroy(buildHelper.gameObject.GetComponent<DTDragBlock>());
			var snapToBlock = snapTo.GetComponentInParent<Block>();
			buildHelper.transform.SetParent(snapToBlock.transform.parent, true);
		}
		
		SnapLink(block.gameObject, 0, bottom, snapToSnap, null, false);
		block.BlockInfo.AddBlockComponents(block.gameObject, false);
		m_grabbedFromWildBlock = buildHelper.GetComponent<MAWildBlock>().m_wildBlockState;
		m_grabbedBlock = block;
		m_grabbedFrom = null;
		m_grabbedTo = buildHelper.Building;
		UpdateDesignDataAndSave(true);
		return true;
	}
	private Transform GetSnapFromBuildHelper(BuildHelper buildHelper)
	{
		var cmd = buildHelper.Building;
		if (cmd == null) return null;
		var baseBlock = cmd.GetComponentInChildren<BaseBlock>();
		if (baseBlock == null) return null;
		var hinges = baseBlock.GetHinges();
		if (buildHelper.PadID < 0 && buildHelper.PadID >= hinges.Count) return null;
		var solo = hinges[buildHelper.PadID];
		var height = buildHelper.Height;
		var anyHeightValid = false;
		if (height < 0)
		{
			height = 100;
			anyHeightValid = true;
		}
		if (height > 0)
		{
			// check attachment links upwards to find the top hinge height-1 above solo
			var soloBlocks = cmd.GetComponentsInChildren<Block>();
			for (int y = 1; y <= height; ++y)
			{
				// find block in soloBlocks that has a bottom hinge at the same position as solo
				bool foundNewSolo = false;
				float bestTopDistSqrd = 1e23f;
				const float c_maxTopBottomDist = 1f;
				for (int sb = 0; foundNewSolo == false && sb < soloBlocks.Length; ++sb)
				{
					if (soloBlocks[sb].gameObject == baseBlock.gameObject) continue;
					SnapHinge blockTop = null;
					var matchDirection = (y < height || buildHelper.Side == 0) ? SnapHinge.EType.Top : (SnapHinge.EType)buildHelper.Side; 
					foreach (Transform soloT in soloBlocks[sb].m_toHinges)
					{
						var soloHinge = soloT.GetComponent<SnapHinge>();
						if (soloHinge == null) continue;
						if (soloHinge.HingeDirection == matchDirection)
						{
							blockTop = soloHinge;
							if (foundNewSolo) break;
						}
						if (soloHinge.HingeDirection != SnapHinge.EType.Bottom) continue;
						var d2 = (soloHinge.transform.position - solo.position).sqrMagnitude;
						if (d2 < bestTopDistSqrd) bestTopDistSqrd = d2;
						if (d2 > c_maxTopBottomDist * c_maxTopBottomDist) continue;
						foundNewSolo = true;
						if (blockTop != null) break;
					}
					if (foundNewSolo)
					{
						if (blockTop != null)
							solo = blockTop.transform;
						else
							foundNewSolo = false;
					}
				}
				if (foundNewSolo == false)
				{
					if (anyHeightValid == false)
						solo = null;
					break;
				}
			}
		}
		return solo ?? transform; // default to any transform that won't match a block 
	}

	void FillUsedHingesFromBuildHelper(GameObject _root)
	{
		var buildHelper = _root.GetComponent<BuildHelper>();
		if (buildHelper == null) return;
		var solo = GetSnapFromBuildHelper(buildHelper);
		if (solo == null) return;
		for (int i = 0; i < m_openBlocks.Count; ++i)
        {
        	var root = m_openBlocks[i];
        	var rHinges = root.GetComponentsInChildren<SnapHinge>(true);
        	for (int rh = 0; rh < rHinges.Length; ++rh)
        		if (rHinges[rh].transform != solo)
        			m_usedHinges[i] |= 1ul << rh;
        }
	}

	public void Ungrab(List<GameObject> _grabbed) {
		if (_grabbed == null) return;
		
		m_lastGrabbedObjectDataOnDragEnd = new DTDragBlock.DragObjectState();
		if (m_lastGrabList != null && m_lastGrabList.Count > 0)
		{
			if (m_lastGrabList[0] != null)
				UpdateGrabData(m_lastGrabList[0].GetComponent<DTDragBlock>(), m_lastGrabbedObjectDataOnDragStart.m_isFromTable);
		}

		bool isWildish = _grabbed[0].transform.parent == m_wildBlockHolder || _grabbed[0].transform.parent == m_scatteredBlockHolder; 
		SetVisualSwitcherStateRecursively(_grabbed[0], isWildish);
		if (_grabbed[0].transform.parent != m_scatteredBlockHolder)
		{
			foreach (var block in _grabbed[0].GetComponent<Block>().m_toVisuals.GetComponentsInChildren<Block>())
				block.transform.SetParent(m_blockHolder.transform, true);
			for (int i = _grabbed.Count - 1; i >= 1; --i) {
				_grabbed[i].transform.SetParent(m_blockHolder.transform, true);
				m_lastGrabList.Remove(_grabbed[i]);
			}
		}
		CullOrphanBlocks();
        if (GameManager.Me.IsOKToPlayDesignTableSound())
        {
            if (m_isCurrentlyPositive)
            {
                AudioClipManager.Me.PlaySoundOld("PlaySound_Table_ItemAttach", transform);
				HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Attach);
            }
            else
            {
                AudioClipManager.Me.PlaySoundOld("PlaySound_TableDiscardPart", transform);
                HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Attach);
            }
        }
        UpdateBlockComponents();
        m_lastGrabList = null;
	}
	public bool IsGrabbed(GameObject _o) {
		return m_lastGrabList != null && m_lastGrabList[0] != null && _o.transform.IsChildOf(m_lastGrabList[0].transform);
	}

	void UpdateBlockComponents()
	{
		if (m_blockHolder == null) return;
		foreach (var block in m_blockHolder.GetComponentsInChildren<Block>(true))
			block.BlockInfo.AddBlockComponents(block.gameObject, false);
	}

	private static DebugConsole.Command s_checkConnections = new DebugConsole.Command("checkconnect", _s =>
	{
		Me.GenerateDesignData(true);
	});
	public const char c_designSeparator = '|';
	public string GenerateDesignData(bool _debug = false, GameObject _override = null, bool _forceGrabAll = false, SDictionary<int,ArrayWrapper<long>> _cmpIds = null) {
		string s = "";
		(var blocks, _) = GetBlockHierarchy(null, _override, _forceGrabAll);
		s += $"{blocks.Count}{c_designSeparator}";
		
		for (int i = 0; i < blocks.Count; ++i) {
			s += $"{blocks[i].BlockID}{c_decorationSeparator}{Decoration.Export(blocks[i].gameObject)}{c_designSeparator}";
		}
		
		string links = "";
		int numLinks = 0;
		bool[] linked = new bool[blocks.Count];
		if (blocks.Count > 0)
			blocks[0].m_indexInDesign = 0;
		for (int i = 1; i < blocks.Count; ++i) {
			for (float minScore = .8f; minScore > 0; minScore -= .75f) {
				if (linked[i]) continue;
				var blockI = blocks[i];
				for (int j = 0; j < i; ++j) {
					var blockJ = blocks[j];
					float bestScore = minScore, bestRot = 0;
					int bestHI = -1, bestHJ = -1;
					for (int hi = 0; hi < blockI.m_toHinges.childCount; ++hi) {
						var hingeI = blockI.m_toHinges.GetChild(hi);
						for (int hj = 0; hj < blockJ.m_toHinges.childCount; ++hj) {
							var hingeJ = blockJ.m_toHinges.GetChild(hj);
							var snapScore = HingeSnapPriority(hingeI, hingeJ) - i * .0001f - hi * .00001f - j * .0001f - hj * .00001f;
							if (snapScore > bestScore) {
								// calculate inline orientation
								bestRot = Vector3.SignedAngle(hingeI.up, hingeJ.up, hingeI.forward);
								bestScore = snapScore;
								bestHI = hi; bestHJ = hj;
							}
						}
					}
					if (bestHI != -1) {
						linked[i] = true;
						links += $"{i}.{bestHI}.{j}.{bestHJ}.{ Mathf.RoundToInt(bestRot)}{c_designSeparator}";
						if (_debug) Debug.LogError($"Link {blockI.name}.{i}.{bestHI} to {blockJ.name}.{j}.{bestHJ} with score {bestScore}");
						++numLinks;
						j = i;
						blockI.m_indexInDesign = numLinks;
					}
				}
			}
		}
		s += $"{numLinks}{c_designSeparator}{links}";
		Block.GenerateComponentInfo(blocks, _cmpIds);
		return s;
	}
#if UNITY_EDITOR
	string GetHingeNameFromTableItem(string _id, int _link) {
		foreach (Transform t in m_blockHolder.transform) {
			var block = t.GetComponent<Block>(); 
			if (block != null && block.BlockID == _id) {
				return block.m_toHinges.GetChild(_link).name;
			}
		}
		return "<unknown>";
	}
	bool m_invertLinkSense = false;
	bool[] m_designPartFoldoutOpen;
	public void DesignAsGUI() {
		var design = GetDesignOnTable()?.m_design;
		if (string.IsNullOrEmpty(design)) return;
		
		EditorGUILayout.TextArea(design, new GUIStyle(EditorStyles.textArea) { wordWrap = true }, GUILayout.MaxHeight(50));
		GUILayout.BeginHorizontal();
		if (GUILayout.Button("Copy")) EditorGUIUtility.systemCopyBuffer = design;
		if (GUILayout.Button("Paste")) RefreshDesign(EditorGUIUtility.systemCopyBuffer);
		GUILayout.EndHorizontal();
		
		m_invertLinkSense = GUILayout.Toggle(m_invertLinkSense, "Invert links");
		var data = DesignUtilities.GetDesignData(design, m_invertLinkSense);
		if (m_designPartFoldoutOpen == null || m_designPartFoldoutOpen.Length != data.Length) m_designPartFoldoutOpen = new bool[data.Length];
		for (int i = 0; i < data.Length; ++i) {
			string desc = $"{i+1}:{data[i].m_blockID} [c:{data[i].m_paint.Count} p:{data[i].m_pattern.Count} s:{data[i].m_stickers.Count}]";
			if (data[i].m_links.Count > 0) {
				m_designPartFoldoutOpen[i] = EditorGUILayout.Foldout(m_designPartFoldoutOpen[i], desc);
				if (m_designPartFoldoutOpen[i]) {
					for (int j = 0; j < data[i].m_links.Count; j += 4) {
						GUILayout.Label($"    {GetHingeNameFromTableItem(data[i].m_blockID, data[i].m_links[j+0])} to {data[i].m_links[j+1]+1}:{data[data[i].m_links[j+1]].m_blockID} {GetHingeNameFromTableItem(data[data[i].m_links[j+1]].m_blockID, data[i].m_links[j+2])} rot {data[i].m_links[j+3]}");
					}
				}
			} else {
				GUILayout.Label(desc);
			}
		}
	}
#endif
	
	public static void LoadProductLaunch (NGCommanderBase _factory = null) {
		Me.StartDesignInPlace(_factory, DESIGN_CATEGORY.PRODUCT);
	}
	public static void LoadProductDesign(NGCommanderBase _factory) {
		Me.StartDesignInPlace(_factory, DESIGN_CATEGORY.PRODUCT);
	}
	public static void LoadProductDesign(NGCommanderBase _factory, string _productLine) {
		Me.StartDesignInPlace(_factory, DESIGN_CATEGORY.PRODUCT);
	}
	
	public static void LoadBuildingDesign(NGCommanderBase _commander) {
		Me.StartDesignGlobally(_commander);
	}

	
	[NonSerialized] public Vector3 m_designInPlaceFocus;
	[NonSerialized] public Vector3 m_designInPlacePerceivedFocus;
	GameObject m_designInPlacePlot;
	GameObject m_designInPlaceInterface;
	
	public NGCommanderBase m_designGloballyFocusBuilding;
	public NGCommanderBase m_designInPlaceBuilding;

	Vector3 m_designInPlaceBuildingPlotCenter, m_designInPlaceBuildingPlotExtent1, m_designInPlaceBuildingPlotExtent2;
	public GameObject CreateDesignInPlacePlots(float _x, float _z, int _w, int _h, float _direction)
	{
		var go = new GameObject("DesignInPlacePlot");
		go.transform.position = new Vector3(_x, 0, _z).SetYToHeight();
		go.transform.eulerAngles = Vector3.up * _direction;
		return go;
	}

	private void SetDesignInPlaceMaterialParameters(GameObject _o, bool _force = false)
	{
		if (IsInDesignInPlace == false && m_isInDesignGlobally == false && _force == false) return;
		if (c_useLocalCamera)
			_o.SetLayerRecursively(0);
		foreach (var rnd in _o.GetComponentsInChildren<Renderer>())
		{
			//rnd.shadowCastingMode = ShadowCastingMode.Off;
			foreach (var mat in rnd.materials)
			{
				mat.SetFloat("_IgnoreDistrictFilter", 1);
				if (c_useLocalCamera)
					mat.SetFloat("_DepthOffset", .02f);
			}
			if (c_useLocalCamera)
				rnd.renderingLayerMask = 1 << 7; 
		}
	}
	private Camera m_inPlaceCamera;
	public Camera DrawerCamera => Camera.main;//IsInDesignInPlace == false ? Camera.main : m_inPlaceCamera;
	
	GameObject m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual;
	
	Vector3 m_previousCameraPosition;
	Quaternion m_previousCameraRotation;
	
	List<GameObject> m_designInPlaceCatLabels = new();
	DESIGN_CATEGORY m_designInPlaceType = DESIGN_CATEGORY.NONE;

	private float c_unscaledMinZoom = 10;
	private float c_unscaledMaxZoom = 60;
	static Vector3 s_infoSheetOnscreen = new Vector3(6.5f, -6f, -5f);
	static Vector3 s_infoSheetOffscreen = s_infoSheetOnscreen + new Vector3(10, 0, 0);

	private void SnapDesignGloballyCamera()
	{
		if (DesignGloballyFocusMode == false) return; // nothing to snap to
		var camPos = m_lastCamAbsolutePos;
		var toFocus = m_designInPlaceFocus - camPos;
		var focusY = toFocus.y;
		var focusXZDistance = toFocus.xzMagnitude();
		var doorInner = m_designGloballyFocusBuilding.DoorPosInner;
		var doorOuter = m_designGloballyFocusBuilding.DoorPosOuter;
		var forwardBase = doorInner - doorOuter;
		var forwardNrm = forwardBase.GetXZNorm();
		const float c_snapFocusXZDistance = 40;
		if (focusXZDistance.Nearly(c_snapFocusXZDistance))
		{
			var angleToNrm = Vector3.SignedAngle(toFocus.GetXZ(), forwardNrm, Vector3.up);
			var angleStep = Mathf.RoundToInt(angleToNrm / 90.0f);
			var angleQuant = angleStep * 90f;
			var dAngle = angleQuant - angleToNrm;
			if (dAngle * dAngle < 5 * 5)
			{
				// close enough to snap to next angle
				angleStep = (angleStep + 1) & 3;
				forwardNrm = forwardNrm.RotateAbout(Vector3.up, -angleStep * 90f * Mathf.Deg2Rad);
			}
		}
		var focusXZDistanceNew = c_snapFocusXZDistance;
		var toFocusNew = forwardNrm * focusXZDistanceNew + Vector3.up * (focusY * focusXZDistanceNew / focusXZDistance);
		m_lastCamAbsolutePos = m_designCamera.transform.position = m_designInPlaceFocus - toFocusNew;
		m_designCamera.transform.LookAt(m_designInPlaceFocus, Vector3.up);
		GameManager.Me.ClearCameraSmoothing();
		if (NGManager.Me.m_cameraSnapAudio != null) NGManager.Me.m_cameraSnapAudio.Play(GameManager.Me.gameObject);
	}

	private void SnapDesignInPlaceCamera()
	{
	}

	public void SnapCamera()
	{
		if (m_isInDesignGlobally)
			SnapDesignGloballyCamera();
		else
			SnapDesignInPlaceCamera();
	}

	bool c_designInFactory => m_designInPlaceBuilding != null;
	void SetupDesignInPlaceLocation()
	{
		if (c_designInFactory)
			m_designInPlacePerceivedFocus = m_designInPlaceBuilding.DoorPosInner.GroundPosition(3 + m_designInPlaceBuilding.m_buildingRaise);
		//else
		//	m_designInPlacePerceivedFocus = NGManager.Me.GetPlayerHousePosition().GroundPosition(3);
		m_designInPlaceFocus = new Vector3(-1000, 0, 0);
		m_designInPlacePlot = new GameObject("DesignInPlace");
		m_designInPlacePlot.transform.position = m_designInPlaceFocus;
		m_designInPlacePlot.layer = 0;
		var tt = Instantiate(m_turntableVisual);
		tt.transform.SetParent(m_designInPlacePlot.transform, false);
		tt.transform.localScale = Vector3.one * c_blockBuildScaleProduct;
		tt.transform.localPosition = Vector3.zero;
		tt.layer = 0;
		var table = Instantiate(Resources.Load<GameObject>("_Art/DesignScene/DesignInPlace"));
		table.transform.SetParent(m_designInPlacePlot.transform, false);
		
		table.GetComponent<EnableByName>()?.Enable(m_locationType.ToString());

		m_designInPlacePlot.SetActive(false);

		if (IsArmourType)
		{
			s_baseProductScale = 2;
			m_designInPlaceFocus += Vector3.up * .2f;
			tt.transform.position = tt.transform.position.NewY(-.6f);
			var tableObjs = ReferenceHolder.Get(table, "Table");
			foreach (var obj in tableObjs) obj?.SetActive(false);
			m_cameraElevation = 25;
			c_unscaledMaxZoom = 15;
			c_unscaledMaxZoom = 35;
			m_designCameraVerticalOffset = -.2f;
			var cardHolder = table.transform.FindChildRecursiveByName("CardHolder")?.parent;
			if (cardHolder != null) cardHolder.transform.localPosition = new Vector3(-.1f, -.63f, .7f);
		}
	}
	
	static Vector3 s_designInPlaceEraserOffPosition = new Vector3(-4, -3, -44);
	static Vector3 s_designInPlaceEraserOnPosition = new Vector3(-4, -3, -9);
	Transform m_designInPlaceEraser;
	public UnityEngine.UI.Button m_designInPlaceUndoButton;
	public UnityEngine.UI.Button m_designInPlaceRedoButton;
	public UnityEngine.UI.Button m_designInPlaceConfirmButton;
	
	Dictionary<string, int> m_designInPlaceBlocksBefore;
	
	Func<bool, bool> m_designInPlaceCompleteCb = null;
	bool m_designInPlaceIsFirstDesign = false;
	
	bool m_designInPlaceIsTesting = false;
	bool m_designInPlaceIsWaitingForTest = false;
	bool m_designInPlaceLeaveAfterTest = true;

	public void SetLeaveAfterTest(bool _leave)
	{
		m_designInPlaceLeaveAfterTest = _leave;
	}

	void UpdateDesignInPlaceTesting()
	{
		if (m_designInPlaceIsWaitingForTest)
		{
			if (IsInDesignInPlaceActively == false)
				m_designInPlaceIsWaitingForTest = false;
			return;
		}
		if (m_designInPlaceIsTesting && IsInDesignInPlaceActively)
		{
			m_designInPlaceIsTesting = false;
			if (m_designInPlaceLeaveAfterTest)
				Close(true);
		}
	}
	void StartDesignInPlaceTesting()
	{
		DesignInPlaceRunTest();
		m_designInPlaceIsWaitingForTest = true;
		m_designInPlaceIsTesting = true;
		m_designInPlaceLeaveAfterTest = true;
	}

	void FindDesignInPlaceButtons()
	{
		if(m_designInPlaceUndoButton == null)
			m_designInPlaceUndoButton = UIManager.Me.transform.Find("DesignInPlaceUI/ConfirmAndUndo/Buttons/Undo").GetComponent<UnityEngine.UI.Button>();
		if(m_designInPlaceRedoButton == null)
			m_designInPlaceRedoButton = UIManager.Me.transform.Find("DesignInPlaceUI/ConfirmAndUndo/Buttons/Redo").GetComponent<UnityEngine.UI.Button>();
		if (m_designInPlaceConfirmButton == null)
		{
			m_designInPlaceConfirmButton = UIManager.Me.transform.Find("DesignInPlaceUI/ConfirmAndUndo/AcceptButton")?.GetComponent<UnityEngine.UI.Button>();
			if (m_designInPlaceConfirmButton == null)
				m_designInPlaceConfirmButton = UIManager.Me.transform.Find("DesignInPlaceUI/ConfirmAndUndo/Infoplaque_Button_NEW_green")?.GetComponent<UnityEngine.UI.Button>();
			if (m_designInPlaceConfirmButton == null)
				m_designInPlaceConfirmButton = UIManager.Me.transform.Find("DesignInPlaceUI/ConfirmAndUndo/DesignConfirm")?.GetComponent<UnityEngine.UI.Button>();
			if (m_designInPlaceConfirmButton == null)
			{
				Debug.LogError($"Confirm button not found, confirm will not work");
				return;
			}
		}
		m_designInPlaceConfirmButton.onClick.RemoveAllListeners();
		m_designInPlaceConfirmButton.onClick.AddListener(() =>
		{
			m_designInPlaceConfirmButton.GetComponent<ContextMenuButton>()?.OnMouseDown();
			ConfirmDesignInPlace();
		});
		m_designInPlaceConfirmButton.gameObject.SetActive(!m_isInDesignGlobally);
	}

	public enum EDesignLocationType
	{
		Food,
		Wood,
		Metal,
	}
	public EDesignLocationType m_locationType = EDesignLocationType.Food;
	
	public GameObject m_drawerSetPrefab;
	
	private int m_itemUniqueIdentifier = 1; // used to track new entries on the table (e.g. for inhibiting quality change audio)
	public int ItemUniqueIdentifier => m_itemUniqueIdentifier;
	
	private float m_cameraElevation = 45;

	private void SetInitialDesignInPlaceValues()
	{
		s_baseProductScale = 1;
		m_cameraElevation = 45;
		m_designCameraVerticalOffset = 0;
		c_unscaledMinZoom = 10;
		c_unscaledMaxZoom = 60;
	}

	public static GameState_DesignTable TableState => GameManager.Me.m_state.m_designTableDetails;
	
	public bool m_designSingly = false;
	public void StartDesignSingly(GameObject _grabbed)
	{
		if (DesignTableManager.IsDesignInPlace) return;

		m_designSingly = true;
		m_isInDesignGlobally = true;
		m_designGloballyFocusBuilding = null;
		foreach (var cmd in NGManager.Me.m_NGCommanderList)
        {
        	if (cmd.IsLocked) continue;
        	CreateAllDrags(cmd);
        }
		(m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual) = (m_blockHolder, m_turntableOrigin, m_turntableVisual);

		m_currentTableMode = DESIGN_CATEGORY.CIVIC;

		m_coroutineOwner = GlobalData.Me;
		m_designCamera = Camera.main;
		m_designInPlaceBuilding = null;
		ClearUndo();
		(m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual) = (m_blockHolder, m_turntableOrigin, m_turntableVisual);
		m_product = null;

		m_blockHolder = new GameObject("BlockHolder");
		m_turntableOrigin = new GameObject("TTO");
		m_turntableOrigin.transform.position = _grabbed.transform.position;
		PrepareWildSnapHolder();
	}

	public void EndDesignSingly()
	{
		if (m_designSingly == false) return;

		Destroy(m_blockHolder);
		Destroy(m_turntableOrigin);
		(m_blockHolder, m_turntableOrigin, m_turntableVisual) = (m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual);
		foreach (var cmd in NGManager.Me.m_NGCommanderList)
		{
			DestroyAllDrags(cmd);
		}
		Destroy(m_wildSnapHolder.gameObject);

		m_designSingly = false;
		m_isInDesignGlobally = false;
	}

	public void StartDesignInPlace(NGCommanderBase _building, DESIGN_CATEGORY _type, Func<bool, bool> _onDesignAccepted = null, bool _isFirstDesign = false)
	{
		s_outstandingRestoreOpperations.Clear();
		if (IsInDesignInPlace) EndDesignInPlace();
		if (m_isInDesignGlobally) EndDesignGlobally(false, true);
		UpdateEndDesignInPlace(true); // finish any current outward transition

		DesignHarness.Me.SetupLights(true);
		
		m_designInPlaceIsTesting = false;
		m_designInPlaceIsWaitingForTest = false;
		m_designInPlaceEnterExitTime = 0;
		m_designInPlaceEnterExitPhase = 0;
		
		m_lastCamAbsolutePos = Vector3.zero;
		
		m_designInPlaceCompleteCb = _onDesignAccepted;
		m_designInPlaceIsFirstDesign = _isFirstDesign;

		++m_itemUniqueIdentifier;
		
		GameManager.BeginDeferredUnlockOperations();
		
		RevertAllTutorialLocks();

		FindDesignInPlaceButtons();
		
		m_designInPlaceBuilding = _building;
		m_designInPlaceType = _type;

		m_coroutineOwner = GlobalData.Me;
		m_designCamera = Camera.main;

		m_previousCameraPosition = m_designCamera.transform.position;
		m_previousCameraRotation = m_designCamera.transform.rotation;

		ClearUndo();

		KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.Design);

		float initialZoom = NGManager.Me.m_designTableDefaultProductZoom;
		
		(m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual) = (m_blockHolder, m_turntableOrigin, m_turntableVisual);

		m_product = null;

		SetInitialDesignInPlaceValues();
		
		m_currentDesign = null;
		m_origionalDesign = null;
		
		m_currentTableMode = _type;
		var drawerSet = "";
		if (_type == DESIGN_CATEGORY.AVATAR)
		{
			//m_designInPlaceInitialDesign = GameManager.Me.GetCurrentBuildingDesign(GlobalData.c_avatarId);
			SetupDesignInPlaceLocation();
			m_currentDesignCategory = c_avatarPartSet;
			m_defaultPartSet = c_avatarPartSet;
		}
		else if (_type == DESIGN_CATEGORY.PRODUCT)
		{
			TableState.m_currentBuildingDesignID = _building.m_linkUID;
			
			m_product = _building.ProductMade;//get rid of option to enter design without order
			MAOrder order = _building.Order;
			if (order.IsNullOrEmpty() == false && order.IsValid && order.IsComplete == false)
				TableState.m_isDesigningProductOrderId = order.OrderId;
			if(m_product == null || m_product.m_productLine.IsNullOrWhiteSpace())
			{
				if(order.IsNullOrEmpty() == false)
				{
					if(order.IsValid && order.IsComplete == false)
					{
						if(order.Design != null)
						{
							m_product = new GameState_Product(order, order.Design);
						}
						else
						{
							m_product = new GameState_Product(order);
						}
					}
					else
					{
						Debug.LogError($"{GetType().Name} - Found no active, incomplete Order for building: {_building.Name} - id: {_building.m_linkUID}. order data: {order.DebugOutputString}");
					}
				}
				else
				{
					Debug.LogError($"{GetType().Name} - No Order found in building: {_building.Name} - id: {_building.m_linkUID}. order data:");
				}
			}

			GameState_Design designInPlaceInitialDesign = null;
			string defaultPartSet = "Food";
			if(m_product != null)
			{
				designInPlaceInitialDesign = m_product.m_design;
				if (m_product.ProductInfo != null)
					defaultPartSet = m_product.ProductInfo.m_prefabName;
			}
			
			m_currentDesignCategory = defaultPartSet;

			m_origionalDesign = m_currentDesign = designInPlaceInitialDesign;
			
			SetupDesignInPlaceLocation();

			SetDrawerState(defaultPartSet, ref drawerSet);
		}
		else if (_type == DESIGN_CATEGORY.CIVIC)
		{
			initialZoom = .3f;

			var blocksBefore = _building.Visuals.GetComponentsInChildren<Block>();
			m_designInPlaceBlocksBefore = new();
			foreach (var b in blocksBefore) m_designInPlaceBlocksBefore[b.BlockIDPlusPos] = b.m_indexInDesign;
			
			GameState_Building data = _building.m_stateData;
			int w = data.m_cachedWidth, h = data.m_cachedHeight;
			Vector3 right = Quaternion.Euler(0, data.m_direction, 0) * Vector3.right, forward = Quaternion.Euler(0, data.m_direction, 0) * Vector3.forward; 
			var pos = new Vector3(data.m_x - BuildingPlacementManager.c_buildingTileSize * .5f, 0, data.m_z - BuildingPlacementManager.c_buildingTileSize * .5f);
			pos += right * ((w - 1) * BuildingPlacementManager.c_buildingTileSize * .5f);
			pos += forward * ((h - 1) * BuildingPlacementManager.c_buildingTileSize * .5f);
			m_designInPlaceFocus = m_designInPlacePerceivedFocus = pos.GroundPosition(2);
			
			m_designInPlacePlot = CreateDesignInPlacePlots(data.m_x, data.m_z, w, h, data.m_direction);
			m_designInPlacePlot.transform.SetParent(_building.Visuals);
			m_designInPlacePlot.transform.localPosition = new Vector3(2-2, 0, 2.5f+.25f - h);

			m_designInPlaceBuildingPlotExtent1 = Quaternion.Euler(0, data.m_direction, 0) * Vector3.forward;
			m_designInPlaceBuildingPlotExtent2 = Quaternion.Euler(0, data.m_direction, 0) * Vector3.right;
			m_designInPlaceBuildingPlotCenter = m_designInPlacePlot.transform.position + m_designInPlaceBuildingPlotExtent1 * (h * 3 - 5) + m_designInPlaceBuildingPlotExtent2 * (w * 2 - 2);
			m_designInPlaceBuildingPlotExtent1 *= h * 2 * .9f;
			m_designInPlaceBuildingPlotExtent2 *= w * 2 * .9f;
			GameManager.Me.ClearGizmos("DIPPlot");
			GameManager.Me.AddGizmoPoint("DIPPlot", m_designInPlaceBuildingPlotCenter, .2f, Color.red);
			GameManager.Me.AddGizmoOCube("DIPPlot", m_designInPlaceBuildingPlotCenter, m_designInPlaceBuildingPlotExtent1, m_designInPlaceBuildingPlotExtent2, Vector3.zero, Color.green);

			SetDrawerState("", ref drawerSet);
			m_currentDesignCategory = drawerSet;
			
			AddToUndo(data.m_buildingDesign?.m_design);
			
			if (m_designInPlaceBuilding.m_balloonHolder != null)
				m_designInPlaceBuilding.m_balloonHolder.gameObject.SetActive(false);
				
			m_origionalDesign = m_currentDesign = data.m_buildingDesign;
		}
		
		m_blockHolder = new GameObject("BlockHolder");
		m_blockHolder.transform.SetParent(m_designInPlacePlot.transform);
		m_blockHolder.transform.localPosition = Vector3.zero;
		m_blockHolder.transform.localRotation = Quaternion.identity;
		m_turntableOrigin = m_designInPlacePlot.transform.GetChild(0).gameObject;
		m_turntableVisual = m_turntableOrigin;

		if (_type == DESIGN_CATEGORY.PRODUCT || _type == DESIGN_CATEGORY.AVATAR)
		{
			RestoreSavedState();
		}
		else if (_type == DESIGN_CATEGORY.CIVIC)
		{
			for (int i = 0; i < _building.Visuals.childCount; ++i)
			{
				var child = _building.Visuals.GetChild(i);
				if (child == m_designInPlacePlot.transform) continue;
				child.SetParent(m_blockHolder.transform, true);
				if (child.gameObject.GetComponent<Block>() != null && child.gameObject.GetComponent<BaseBlock>() == null)
					child.gameObject.AddComponent<DTDragBlock>().InitialiseScale(1);
				--i;
			}
			m_originalBlockMin.x = 1e23f;
			CalculateBlockExtents();
		}

		SetInitialDrawerSetType();
		GenerateDrawerSetLists();
		
		float zoomScale = CurrentBlockBuildScale;
		float zoom = Mathf.Lerp(c_unscaledMinZoom, c_unscaledMaxZoom, initialZoom) * zoomScale; 
		m_designInPlaceInitialZoomDirection = Quaternion.Euler(m_cameraElevation, 0, 0);
		m_designInPlaceInitialZoomTo = m_designInPlacePerceivedFocus - m_designInPlaceInitialZoomDirection * Vector3.forward * zoom;
		m_designInPlaceInitialZoomToInternal = m_designInPlaceFocus - m_designInPlaceInitialZoomDirection * Vector3.forward * zoom;

		m_dragPlaneOrigin = m_designInPlaceFocus;

		m_currentDrawerSet = m_categoryList.IndexOf(m_defaultPartSet);
		UpdateDesignInPlaceCatLabels();
		PrepareDesignInPlaceInterface(drawerSet);
		
		RefreshDesignInfo();
		
		SetupInfoSheet();

		if (m_designInPlaceType == DESIGN_CATEGORY.CIVIC)
			m_designInPlaceBuilding.HighlightBuilding();
		MAHelperSign.CheckForSigns(DesignTableManager.Me.m_turntableOrigin.transform.parent);
	}

	void SetDrawerState(string _fallback, ref string _drawerSet)
	{
		var partSet = _fallback;
		if (string.IsNullOrEmpty(m_nextDesignDrawerState) == false)
		{
			var bits = m_nextDesignDrawerState.Split('|');
			partSet = bits[0];
			if (bits.Length > 1) _drawerSet = bits[1];
			m_nextDesignDrawerState = "";
		}
		m_defaultPartSet = partSet;
	}

	void BeginCloseInfoSheet()
	{
		var priceDrawer = DesignUIManager.Me.m_priceDrawerContent.GetComponentInParent<DTDragDrawer>();
		priceDrawer?.Close();
		var infoDrawer = DesignUIManager.Me.m_infoDrawerContent.GetComponentInParent<DTDragDrawer>();
		infoDrawer?.Close();
	}

	void ShutdownInfoSheet()
	{
		ClearInfoSheets();
	}
	void ClearInfoSheets()
	{
		DesignUIManager.Me.m_priceDrawerContent.DestroyChildren();
		DesignUIManager.Me.m_infoDrawerContent.DestroyChildren();
		DesignUIManager.Me.m_marketPanelContent.DestroyChildren();
		
		DesignUIManager.Me.m_marketPanel.ForceClose();
	}
	

	void SetupInfoSheet()
    {
	    ClearInfoSheets();
	    var scoreParent = DesignUIManager.Me.m_priceDrawerContent;
	    var infoParent = DesignUIManager.Me.m_infoDrawerContent;
	    MADesignPriceSheet.Create(scoreParent);
	    MAMarketInfoSheet.Create(DesignUIManager.Me.m_marketPanelContent);
	    MADesignInfoSheet.Create(infoParent);
	}
	
	bool m_showDesignInPlaceInfoSheet = false;
	bool m_showDesignInPlaceScoreSheet = false;
	public void ToggleDesignInPlaceInfoSheet() => m_showDesignInPlaceInfoSheet = !m_showDesignInPlaceInfoSheet;
	public void ToggleDesignInPlaceScoreSheet() => m_showDesignInPlaceScoreSheet = !m_showDesignInPlaceScoreSheet;

	public ProductTestingManager.EProductTestMode m_productTestModeOverride = ProductTestingManager.EProductTestMode.Auto;
	public float m_productTestScoreOverride = -1;
	public bool m_productTestReplaceDesign = false;
	private void DesignInPlaceRunTest()
	{
		var design = m_productTestReplaceDesign ? ProductTestingManager.Me.m_defaultDefenderArmourDesign : GetDesignOnTable().m_design;
        
		ProductTestingManager.Me.Activate(m_productTestModeOverride, Order, m_dsi.TesterReaction, design);
		AudioClipManager.Me.PlaySound("PlaySound_DesignTable_Accept", GameManager.Me.gameObject);
	}
	private bool ShouldShowProductTesting(MAOrder _order) => ProductTestingManager.GetModeForOrder(_order) != ProductTestingManager.EProductTestMode.Auto;

	void UpdateDesignInPlaceInfoPanels()
	{
	}


	public void ConfirmDesignInPlace()
	{
		if(m_designInPlaceType == DESIGN_CATEGORY.PRODUCT)
		{
			GameState_Design designOnTable = GetDesignOnTable();
			if (designOnTable != null)
			{
				// Rounding to 3 decimal places to match the displayed value
				var order = Order;
				if(designOnTable.HasValidDesign == false || (order.IsNullOrEmpty() == false && order.IsDesignValid(designOnTable) == false))
				{
					ShowGeneralProblemMessage("No Parts", "You must add parts to your design!");
				}
				else if(order.IsNullOrEmpty() == false && m_dsi.TotalScore.Round(3) < order.m_orderQuality.Round(3))
				{
					//Debug.Log($"{GetType().Name} - Confirm - lack Of Quality m_currentDesignScore: {m_dsi.TotalScore}");
					//Debug.Log($"{GetType().Name} - Confirm - lack Of Quality DesignScoreInterface.TotalScore: {m_dsi.TotalScore}");
                        
					ShowLackOfQualityMessage(order, m_dsi.TotalScore);
				}
				else if (CheckSpecialOrderRequirements(out var missing) == false)
				{
					var missingHolder = ShowMissingBlocks(missing);
					ShowGeneralProblemMessage("Missing requirements", "You must include the original items in your design", () => UnshowMissingBlocks(missingHolder), 200);
				}
				else if (ShouldShowProductTesting(m_designInPlaceBuilding.Order))
				{
					StartDesignInPlaceTesting();
				}
				else
				{
					Close(true);
				}
			}
			else
			{
				Close(false);
			}
		}
		else
		{
			if(IsDesignLegal())
				Close(true);
		}
		//EndDesignInPlace(false);
	}

	private GameObject ShowMissingBlocks(List<Block> _missing)
	{
		var missing = new GameObject("Missing");
		var p = new Vector3[6];
		foreach (var m in _missing)
		{
			var go = Instantiate(GlobalData.Me.m_pickupLinePrefab, missing.transform);
			go.layer = GameManager.c_layerTerrain;
			var line = go.GetComponent<LineRenderer>();
			var bezier = go.GetComponent<BezierLine>();
			var widthScale = CurrentBlockBuildScale * (.5f + CurrentZoom * .5f);
			p[0] = m_turntableOrigin.transform.position;
			p[5] = m.transform.position;
			p[1] = Vector3.Lerp(p[0], p[5], .05f) + Vector3.up * (widthScale * 1.6f);
			p[2] = Vector3.Lerp(p[0], p[5], .3f) + Vector3.up * (widthScale * 2f);
			p[3] = Vector3.Lerp(p[0], p[5], .7f) + Vector3.up * (widthScale * 2f);
			p[4] = Vector3.Lerp(p[0], p[5], .95f) + Vector3.up * (widthScale * 1.6f);
			bezier.SetControlPoints(p, .005f);
			line.widthMultiplier = widthScale;
		}
		return missing;
	}

	private void UnshowMissingBlocks(GameObject _holder)
	{
		Destroy(_holder);
	}

	private bool m_designInPlaceExiting = false;
	private bool m_designInPlaceCancelling = false;
	private float m_designInPlaceEnterExitTime = 0;
	private int m_designInPlaceEnterExitPhase = -1;
	
	public bool EndDesignInPlace(bool _cancel = true)
	{
		if (IsInDesignInPlace == false) return false;

		m_designInPlaceEnterExitTime = 0;
		m_designInPlaceEnterExitPhase = 0;
		m_designInPlaceExiting = true;
		m_designInPlaceCancelling = _cancel;
		OnBlockLabelModeSelected(BlockLabelMode.None);
	
		AudioClipManager.Me.PlaySound(_cancel ? "PlaySound_DesignTable_Exit" : "PlaySound_DesignTable_Accept", GameManager.Me.gameObject);

		KeyboardShortcutManager.Me.PopShortcuts();

		TableState.m_currentBuildingDesignID = 0;

		BeginCloseInfoSheet();

		if(m_designInPlaceType == DESIGN_CATEGORY.CIVIC)
		{
			if (m_designInPlaceBuilding.Design == null || (m_designInPlaceCancelling && m_designInPlaceIsFirstDesign))
			{
				m_designInPlaceBuilding.DestroyMe();
				GameManager.Me.m_state.m_buildings.Remove(m_designInPlaceBuilding.m_stateData);
			}
		}
		
		if(IsDesigningProduct())
		{
			if (m_acceptingChanges && _cancel == false)
			{
				SetNewDesign(GenerateDesignData(), false);
				
				
				m_product.m_pricePerProduct = Mathf.Round(m_dsi.SellingPrice);
				m_designInPlaceBuilding.SetProduct(m_product);
			}
		}
		else
		{
			m_designInPlaceBuilding.m_stateData.MoveLocally((int) m_blockMove.x, (int) m_blockMove.z);
			DestroyAllDrags(m_designInPlaceBuilding);
			if (m_designInPlaceIsFirstDesign)
			{
				var onlyBase = new Dictionary<string, int>();
				foreach (var block in m_designInPlaceBlocksBefore)
					if (block.Key.StartsWith("MABase"))
						onlyBase[block.Key] = block.Value;
				m_designInPlaceBlocksBefore = onlyBase;
			}
			RemoveBlocksFromBlockHolder();
			m_designInPlaceBuilding.UnhighlightBuilding();
			if (m_designInPlaceBuilding.m_balloonHolder != null)
				m_designInPlaceBuilding.m_balloonHolder.gameObject.SetActive(true);
		}
		
		foreach (var kvp in m_designInPlaceDrawers)
			kvp.Value.StartDestroy();

		if (m_currentDesign != null && m_acceptingChanges)
		{
			CaptureObjectImage.Use use = IsDesigningProduct() ? CaptureObjectImage.Use.Product : CaptureObjectImage.Use.Building;
			GameManager.Me.SetDesignSprite(m_currentDesign, CaptureObjectImage.Me.Capture(m_blockHolder, use));
		}

		CheckForAndCloseGUIs();
		
		return true;
	}

	void RemoveBlocksFromBlockHolder()
	{
		var visuals = m_designInPlacePlot.transform.parent;
		for (int i = m_blockHolder.transform.childCount - 1; i >= 0; --i)
		{
			var block = m_blockHolder.transform.GetChild(i);
			block.SetParent(visuals, true);
			block.SetSiblingIndex(0);
		}
		Destroy(m_designInPlacePlot);
	}
	
	
	private float m_fadeOverride = 0;
	const float c_switchOffPlayerHouseVisualsAtDistance = 2;
	const float c_fadeOuterStartDistance = 16;
	const float c_fadeOuterEndDistance = 4;
	const float c_fadeInnerStartDistance = .4f;
	const float c_fadeInnerEndDistance = 1;
	private void SetFader(Vector3 _toDest)
	{
		var sqrDist = _toDest.sqrMagnitude;
		var sqrOuter = c_fadeOuterStartDistance * c_fadeOuterStartDistance;
		var sqrOuterEnd = c_fadeOuterEndDistance * c_fadeOuterEndDistance;
		var sqrInner = c_fadeInnerStartDistance * c_fadeInnerStartDistance;
		var sqrInnerEnd = c_fadeInnerEndDistance * c_fadeInnerEndDistance;
		var sqrMid = c_switchOffPlayerHouseVisualsAtDistance * c_switchOffPlayerHouseVisualsAtDistance;
		float fade;
		if (sqrDist > sqrMid)
			fade = (sqrOuter - sqrDist) / (sqrOuter - sqrOuterEnd);
		else
			fade = (sqrInner - sqrDist) / (sqrInner - sqrInnerEnd);
		fade = m_fadeOverride;
		Crossfade.Me.SetFade(fade);
	}

	private void UnsetFader()
	{
		Crossfade.Me.Unfade();
	}

	const float c_designInPlaceEnterExitFadeTime = .4f;
	void SetEnterExitFader()
	{
		if (m_designInPlaceEnterExitPhase == -1) return;
		if (m_designInPlaceEnterExitTime < c_designInPlaceEnterExitFadeTime || m_designInPlaceEnterExitPhase == 1)
		{
			m_designInPlaceEnterExitTime += Time.deltaTime;
			if (m_designInPlaceEnterExitTime > c_designInPlaceEnterExitFadeTime && m_designInPlaceEnterExitPhase == 0)
				m_designInPlaceEnterExitTime = c_designInPlaceEnterExitFadeTime;
			else if (m_designInPlaceEnterExitTime >= c_designInPlaceEnterExitFadeTime * 2)
				m_designInPlaceEnterExitPhase = -1;
		}
		if (m_isInDesignGlobally == false)
		{
			float fade;
			if (m_designInPlaceEnterExitTime < c_designInPlaceEnterExitFadeTime)
				fade = m_designInPlaceEnterExitTime / c_designInPlaceEnterExitFadeTime;
			else
			{
				fade = 1 - (m_designInPlaceEnterExitTime - c_designInPlaceEnterExitFadeTime) / c_designInPlaceEnterExitFadeTime;
				Crossfade.Me.EndFadeOverride();
			}
			Crossfade.Me.SetFade(fade);
		}
	}
	
	private bool UpdateEndDesignInPlace(bool _forceFinish)
	{
		if (m_designInPlaceExiting == false) return false;

		PlayRotateSound(false);

		UpdateDesignInPlaceCatPosition(false);
		m_designInPlaceEraser.localPosition = Vector3.Lerp(m_designInPlaceEraser.localPosition, s_designInPlaceEraserOffPosition, .1f.TCLerp());

		bool abandon = _forceFinish;// || m_designInPlaceInitialZoomTo.sqrMagnitude > 0;

		SetEnterExitFader();
		if (!abandon && m_designInPlaceEnterExitPhase == 0)
		{
			if (m_designInPlaceEnterExitTime >= c_designInPlaceEnterExitFadeTime)
			{
				m_designInPlaceEnterExitPhase = 1;
				m_designInPlaceEnterExitTime = c_designInPlaceEnterExitFadeTime;
				m_designCamera.transform.rotation = m_previousCameraRotation;
				m_designCamera.transform.position = m_designInPlacePerceivedFocus - m_designCamera.transform.forward * c_switchOffPlayerHouseVisualsAtDistance;
			}
			return true;
		}
		else
		{
			var fromDest = m_designCamera.transform.position - m_designInPlacePerceivedFocus;
			//SetFader(fromDest);
			var ph = c_designInFactory ? m_designInPlaceBuilding : null;
			if (abandon || (ph != null && GlobalData.Me.IsHidingTerrain() && fromDest.sqrMagnitude > c_switchOffPlayerHouseVisualsAtDistance * c_switchOffPlayerHouseVisualsAtDistance))
			{
				ToggleExternalLights(true);
				CameraRenderSettings.Me.SetUseDistrictFilter(true);
				GlobalData.Me.HideTerrain(false);
				if (GameSettings.SRPOptions.IsURP == false)
					GameSettings.SRPOptions.HDLiftGammaGain.active = false;
				if (m_designInPlaceType == DESIGN_CATEGORY.PRODUCT || m_designInPlaceType == DESIGN_CATEGORY.AVATAR)
					Destroy(m_designInPlacePlot);
			}

			if (abandon == false && m_designInPlaceType != DESIGN_CATEGORY.CIVIC)
			{
				m_designCamera.transform.position = Vector3.Lerp(m_designCamera.transform.position, m_previousCameraPosition, .1f.TCLerp());
				m_designCamera.transform.rotation = Quaternion.Slerp(m_designCamera.transform.rotation, m_previousCameraRotation, .1f.TCLerp());

				if ((m_designCamera.transform.position - m_previousCameraPosition).sqrMagnitude > .1f * .1f)
					return true;
				m_designCamera.transform.position = m_previousCameraPosition;
				m_designCamera.transform.rotation = m_previousCameraRotation;
			}
		}

		if (abandon == false && (m_designInPlaceEnterExitPhase != -1 || (m_designInPlaceCatHolder.transform.localPosition - s_catOffPos).sqrMagnitude > .1f * .1f))
			return true;
		
		m_designInPlaceExiting = false;
		if (m_designInPlaceType == DESIGN_CATEGORY.PRODUCT || m_designInPlaceType == DESIGN_CATEGORY.AVATAR)
		{
			m_designCamera.transform.position = m_previousCameraPosition;
			m_designCamera.transform.rotation = m_previousCameraRotation;
		}

		BlockInfoPanel.DestroyPreviousInstance();
		GameManager.EndDeferredUnlockOperations();
		//GameManager.Me.Tidy();

		(m_blockHolder, m_turntableOrigin, m_turntableVisual) = (m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual);
		m_designCamera = GetComponentInChildren<Camera>();
		
		ShutDesignInPlaceInterface();
		
		m_dragPlaneOrigin = Vector3.zero;

		m_currentDrawerSet = -1;
		m_designInPlaceType = DESIGN_CATEGORY.NONE;
		
		GameManager.UnlocksChanged -= RefreshDrawerContents;

		if (m_ambientSounds != 0)
		{
			AudioClipManager.Me.StopSound(m_ambientSounds);
			m_ambientSounds = 0;
		}
		
		//OLDSAVE <--
			
		if (m_designInPlaceCompleteCb != null)
			m_designInPlaceCompleteCb(!m_designInPlaceCancelling);
		
		return true;
	}

	public void ResetAllDragBlockCaches()
	{
		if (m_isInDesignGlobally)
		{
			foreach (var cmd in NGManager.Me.m_NGCommanderList)
				foreach (var c in cmd.GetComponentsInChildren<DTDragBlock>())
					c.ResetCache();
		}
		else
		{
			foreach (var c in m_blockHolder.GetComponentsInChildren<DTDragBlock>())
				c.ResetCache();
		}
	}

	public static void CreateAllDrags(NGCommanderBase _f)
	{
		if (_f?.Visuals == null || _f.IsLocked) return; 
		CreateAllDrags(_f.Visuals.gameObject);
	}

	public static void CreateAllDrags(GameObject _o)
	{
		foreach (var obj in _o.GetComponentsInChildren<Block>())
			if (obj.GetComponentInParent<NGReactPickupAny>() == null && obj.GetComponent<DTDragBlock>() == null)
				obj.gameObject.AddComponent<DTDragBlock>().InitialiseScale(1).m_rightClickOnly = obj.GetComponent<BaseBlock>() != null;
	}

	public static void DestroyAllDrags(NGCommanderBase _f)
	{
		if (_f?.Visuals == null) return;
		var isBeacon = (_f as MABuilding).HasBuildingComponent<BCBeacon>();
		DestroyAllDrags(_f.Visuals.gameObject, isBeacon);
	}

	public static void DestroyAllDrags(GameObject _o, bool _isBeacon = false)
	{
		var drags = _o.GetComponentsInChildren<DTDragBlock>();
		foreach (var drag in drags)
			if (_isBeacon == false || drag.GetComponent<BuildHelper>() == null)
				Destroy(drag);
	}


	bool CurrentDesignHasDecorations()
	{
		foreach (var p in GetCurrentDesignParts())
		{
			if (p.m_paint.Count > 0) return true;
			if (p.m_pattern.Count > 0) return true;
			if (p.m_stickers.Count > 0) return true;
		}
		return false;
	}

	bool DesignInPlaceCheckValidPosition(Vector3 _pos)
	{
		if (IsInDesignInPlace == false) return true;
		if (m_designInPlaceType != DESIGN_CATEGORY.CIVIC) return true;
		// building, check that this position is over the "template"
		var toCenter = m_designInPlaceBuildingPlotCenter - _pos;
		var dSqrd1 = Mathf.Abs(Vector3.Dot(toCenter, m_designInPlaceBuildingPlotExtent1));
		var dSqrd2 = Mathf.Abs(Vector3.Dot(toCenter, m_designInPlaceBuildingPlotExtent2));
		return dSqrd1 < m_designInPlaceBuildingPlotExtent1.sqrMagnitude && dSqrd2 < m_designInPlaceBuildingPlotExtent2.sqrMagnitude;
	}

	public bool IsInDesignInPlace => m_designInPlaceType != DESIGN_CATEGORY.NONE;
	public bool IsInDesignInPlaceRoom => IsInDesignInPlace && ((m_designInPlaceEnterExitPhase == 0) == m_designInPlaceExiting); // enter/exit is 1 ot -1 if exiting false, 0 if exiting true
	public bool IsInDesignInPlaceOnLocation => IsInDesignInPlaceActively && m_designInPlaceType != DESIGN_CATEGORY.CIVIC;
	public bool IsInDesignInPlaceActively => IsInDesignInPlace && m_designInPlaceExiting == false && GameManager.Me.IsProductTestingScene == false && m_designInPlaceEnterExitPhase != 0;
	public bool IsInDesignGloballyActively => m_isInDesignGlobally && !m_designGloballyClosing;
	public bool IsInRealDesignGlobally => m_isInDesignGlobally && m_designSingly == false;

	Vector3 m_designInPlaceInitialZoomTo = Vector3.zero;
	Vector3 m_designInPlaceInitialZoomToInternal = Vector3.zero;
	Quaternion m_designInPlaceInitialZoomDirection = Quaternion.identity;

	public float SpherecastRadius => GameManager.Me.IsDesignTable ? .5f * CurrentBlockBuildScale : 0;

	public bool DesignGloballyFocusMode => m_designGloballyFocusBuilding != null;
	public bool DesignGloballyNonFocusMode => m_isInDesignGlobally && !DesignGloballyFocusMode;

	void PlayRotateSound(bool rotateSound)
	{
		if (rotateSound && m_rotateSoundID <= 0)
		{
			m_rotateSoundID = AudioClipManager.Me.PlaySound("PlaySound_DesignTableRotate", m_turntableVisual);
		}
		else if (rotateSound == false && m_rotateSoundID > 0)
		{
			AudioClipManager.Me.StopSound(m_rotateSoundID);
			m_rotateSoundID = 0;
		}
	}

	public bool UpdateDesignInPlace()
	{
		UpdateUnlockShelf();
		
		if (m_designSingly)
		{
			UpdateDesignGloballyOrigin();
			return false;
		}
		if (m_isInDesignGlobally)
		{
			UpdateDesignGlobally();
			return DesignGloballyFocusMode;
		}
		if (IsInDesignInPlace == false) return false;
		
		UpdateDesignInPlaceTesting();
		
		UpdateTurnDesignInPlaceCats();
		UpdateDesignInPlaceDrawerSets();
		
		if (UpdateEndDesignInPlace(false))
			return true;

		CheckDrawerSetsDirty();
		
		UpdateDesignInPlaceInfoPanels();
		UpdateDesignInPlaceCatPosition(true);
		var eraserPos = CurrentDesignHasDecorations() ? s_designInPlaceEraserOnPosition : s_designInPlaceEraserOffPosition;
		m_designInPlaceEraser.localPosition = Vector3.Lerp(m_designInPlaceEraser.localPosition, eraserPos, .1f.TCLerp());

		UpdateDesignUndoRedoKeys();

		UpdateDesignInPlaceCamera();
		return true;
	}
	bool m_designInPlaceIsRotating = false;
	float m_designInPlaceRotateStart;
	Vector3 m_lastCamAbsolutePos = Vector3.zero;
	float DistanceToFocus => (m_lastCamAbsolutePos - m_designInPlaceFocus).magnitude;
	float CurrentZoom
	{
		get
		{
			float zoomScale = CurrentBlockBuildScale;
			float minZoom = c_unscaledMinZoom * zoomScale;
			float maxZoom = c_unscaledMaxZoom * zoomScale;
			return (DistanceToFocus - minZoom) / (maxZoom - minZoom);
		}
	}

	void ToggleExternalLights(bool _enable)
	{
		Marcos_Procedural_Sky.Me.transform.parent.gameObject.SetActive(_enable);
	}

	void UpdateDesignUndoRedoKeys()
	{
		if (Utility.ModifiedKey(KeyCode.Z, false, true, false, "Undo")) ClickedUndo();
		if (Utility.ModifiedKey(KeyCode.Y, false, true, false, "Redo")) ClickedRedo();
		if (Utility.ModifiedKey(KeyCode.Z, true, true, false, "Redo")) ClickedRedo();
	}

	float m_designInPlaceForcedZoom = 0;
	int m_rotateSoundID = 0;
	void UpdateDesignInPlaceCamera()
	{
		bool userRotate = false;
		float rotate = GameManager.Me.GetTotalCameraRotate(true, ref userRotate);
		float zoom = GameManager.Me.GetZoom();
		if (m_designInPlaceForcedZoom > 0)
		{
			m_designInPlaceForcedZoom *= .85f;
			if (m_designInPlaceForcedZoom < .001f) m_designInPlaceForcedZoom = 0;
			zoom += m_designInPlaceForcedZoom;
		}
		var cam = m_designCamera;//GameManager.Me.m_camera;
		var camXform = cam.transform;
		var camPos = m_lastCamAbsolutePos;
        if (camPos.sqrMagnitude < .01f*.01f) camPos = camXform.position;
        SetEnterExitFader();
		if (m_designInPlaceInitialZoomTo.sqrMagnitude > 0 && m_designInPlaceInitialZoomToInternal.sqrMagnitude > 0)
		{
			camPos = Vector3.Lerp(camPos, m_designInPlaceInitialZoomTo, .1f.TCLerp());
			camXform.rotation = Quaternion.Slerp(camXform.rotation, m_designInPlaceInitialZoomDirection, .1f.TCLerp());

			var toDest = m_designInPlaceInitialZoomTo - camPos;
			if (m_designInPlaceType == DESIGN_CATEGORY.PRODUCT || m_designInPlaceType == DESIGN_CATEGORY.AVATAR)
			{
				var ph = c_designInFactory ? m_designInPlaceBuilding : null;
				if (ph != null && ph.Visuals.gameObject.activeSelf && toDest.sqrMagnitude < c_switchOffPlayerHouseVisualsAtDistance * c_switchOffPlayerHouseVisualsAtDistance)
				{
					ToggleExternalLights(false);
					m_designInPlaceForcedZoom = 1;
					m_designInPlaceEnterExitPhase = 1;
					camPos = m_designInPlaceInitialZoomToInternal;
					m_designInPlaceInitialZoomToInternal = Vector3.zero;
					GlobalData.Me.HideTerrain(true);
					CameraRenderSettings.Me.SetUseDistrictFilter(false);
					if (GameSettings.SRPOptions.IsURP == false)
						GameSettings.SRPOptions.HDLiftGammaGain.active = true;
					m_designInPlacePlot.SetActive(true);
				}
			}
			if (toDest.sqrMagnitude < .01f * .01f)
			{
				camPos = m_designInPlaceInitialZoomTo;
				camXform.rotation = m_designInPlaceInitialZoomDirection;
				m_designInPlaceInitialZoomTo = Vector3.zero;
			}
		}
		else 
		{
			float ScreenToAngle(Vector3 _spos) => Mathf.Atan2(_spos.y - Screen.height * .5f, _spos.x - Screen.width * .5f) * Mathf.Rad2Deg;

			const float c_rotationMultiplier = -.25f;
			if (GameManager.GetMouseButtonDown(0) && Utility.IsMouseOverUI() == false)
			{
				m_designInPlaceIsRotating = false;
				if (GameManager.Me.RaycastAtPoint(GameManager.InputPosition(0), out var hit, -1))
				{
					var drag = hit.collider.gameObject.GetComponentInParent<DragBase>();
					if (drag is DragToRotate)
					{
						drag.enabled = false;
						drag = null;
					}
					if (drag == null)
					{
						m_designInPlaceIsRotating = true;
						m_designInPlaceRotateStart = ScreenToAngle(GameManager.InputPosition(0));
					}
				}
			}
			else if (m_designInPlaceIsRotating && GameManager.GetMouseButton(0))
			{
				var newAngle = ScreenToAngle(GameManager.InputPosition(0));
				var delta = Mathf.DeltaAngle(newAngle, m_designInPlaceRotateStart);
				m_designInPlaceRotateStart = newAngle;
				rotate += delta / (c_rotationMultiplier * Time.deltaTime) * Mathf.Deg2Rad;
			}
			else if (GameManager.GetMouseButton(0) == false)
                m_designInPlaceIsRotating = false;

			if (m_isInDesignGlobally == false)
			{
				bool rotateSound = rotate * rotate > .01f * .01f || m_designInPlaceIsRotating;
				PlayRotateSound(rotateSound);
				if (rotateSound) AudioClipManager.Me.SetGameParameter("DesignTableRotationVelocity", Mathf.Clamp01(Mathf.Abs(rotate) * .02f), m_turntableVisual);
			}
			if (rotate * rotate > .01f * .01f || zoom * zoom > .01f * .01f)
			{
				bool rotateCamera = !IsInProductMode && !IsInAvatarMode;
				float zoomScale = CurrentBlockBuildScale;
				float minZoom = c_unscaledMinZoom * zoomScale, maxZoom = c_unscaledMaxZoom * zoomScale;
				var toFocus = m_designInPlaceFocus - camPos;
				var rotateBy = rotate * c_rotationMultiplier * Time.deltaTime;
				if (rotateCamera)
				{
					toFocus = toFocus.RotateAbout(Vector3.up, rotateBy);
				}
				else
				{
					m_blockHolder.transform.Rotate(Vector3.up, rotateBy * Mathf.Rad2Deg);
					m_turntableVisual.transform.rotation = m_blockHolder.transform.rotation;
				}
				var focusLength = Mathf.Clamp(toFocus.magnitude - zoom * zoomScale, minZoom, maxZoom);
				toFocus = toFocus.normalized * focusLength;
				camPos = m_designInPlaceFocus - toFocus;
				camXform.forward = toFocus.normalized;
			}
		}
		m_lastCamAbsolutePos = camPos;
		var adjustSize = 0f;
		if (m_designInPlaceInitialZoomToInternal.sqrMagnitude < .001f * .001f)
			adjustSize = (camPos - m_designInPlaceFocus).magnitude * .5f;
		camPos += (camXform.right * m_designCameraHorizontalOffset + (camXform.forward + camXform.up) * m_designCameraVerticalOffset) * adjustSize;
		camXform.position = camPos;
	}

	public float m_designCameraHorizontalOffset = 0, m_designCameraVerticalOffset = 0;

	private Transform m_wildSnapHolder;
	private BlockCage m_blockCage;
	public BlockCage BlockCage => m_blockCage;
	
	public bool IsInCage(Transform _t) => BlockCage != null && _t.IsChildOf(BlockCage.m_contents);
	
	static Vector3 c_blockCagePosition_On = new Vector3(6, -10, -4);
	static Vector3 c_blockCagePosition_Off = new Vector3(6, -10, -10);

	static bool s_showBlockCage;
	static DebugConsole.Command s_showBlockCageCommand = new("blockcage", _s => Utility.SetOrToggle(ref s_showBlockCage, _s));
	
	const bool c_useLocalCamera = true;

	void CheckDesignInPlaceInterfaceExists()
	{
		if (m_designInPlaceInterface == null)
		{
			m_designInPlaceInterface = new GameObject("DesignInPlaceInterface");
			m_designInPlaceInterface.transform.position = Vector3.zero;

			if (c_useLocalCamera)
			{
				m_designInPlaceInterface.transform.SetParent(DesignHarness.Me.transform);
				m_designInPlaceInterface.transform.localPosition = Vector3.zero;
				m_designInPlaceInterface.transform.localRotation = Quaternion.identity;
				m_designInPlaceInterface.transform.localScale = Vector3.one;
				SetDesignInPlaceMaterialParameters(m_designInPlaceInterface);
			}
		}
		var harness = DesignHarness.Me.transform;
		if (harness.localScale.x.Nearly(c_overallDrawerScale) == false)
		{
			float rescale = c_overallDrawerScale / harness.localScale.x;
			harness.localScale = Vector3.one * c_overallDrawerScale;
			harness.localPosition *= rescale;
		}
	}
	
	private void PrepareDesignInPlaceInterface(string _initialDrawerSet = "")
	{
		if (m_inPlaceCamera == null)
		{
			m_inPlaceCamera = GameManager.Me.m_camera.transform.parent.FindRecursiveByName<Camera>("DesignInPlaceCamera");
			m_inPlaceCamera.transform.SetParent(null, true);
		}
		m_inPlaceCamera.gameObject.SetActive(!c_useLocalCamera);

		CheckDesignInPlaceInterfaceExists();
		
		if (m_designInPlaceEraser == null)
		{
			m_designInPlaceEraser = transform.Find("ErasingManager");
			m_designInPlaceEraser.SetParent(m_designInPlaceInterface.transform);
			m_designInPlaceEraser.localPosition = s_designInPlaceEraserOffPosition;
			m_designInPlaceEraser.localEulerAngles = new Vector3(0, -20, 90);
			SetDesignInPlaceMaterialParameters(m_designInPlaceEraser.gameObject);
			m_designInPlaceEraser.gameObject.SetActive(false);
		}
		m_designInPlaceInterface.SetActive(true);

		if (m_blockCage == null) m_blockCage = DesignHarness.Me.GetComponentInChildren<BlockCage>(true);
		m_blockCage.transform.localPosition = s_showBlockCage ? c_blockCagePosition_On : c_blockCagePosition_Off;
		m_blockCage.gameObject.SetActive(s_showBlockCage);
		
		CreateDesignInPlaceDrawerFramework(_initialDrawerSet);

		PrepareWildSnapHolder();
	}
	void PrepareWildSnapHolder()
	{
		m_wildSnapHolder = new GameObject("WildSnap").transform;
	}

	void SetInitialDrawerSetType()
	{
		if (IsInBuildingMode) m_currentDrawerSetType = "Building";
		else m_currentDrawerSetType = "Product";
	}

	void GenerateDrawerSetLists()
	{
		m_categoryList = FindUnlockedCategories();
		//m_categoryList.Insert(0, c_decorationCategory);
		m_lockedCategoryList = new();
		m_lockedCategoryList.AddRange(m_allDrawerSets);
		foreach (var cat in m_categoryList) m_lockedCategoryList.Remove(cat);
		m_totalCategoryList = new();
		m_totalCategoryList.AddRange(m_categoryList);
		m_totalCategoryList.AddRange(m_lockedCategoryList);
	}

	private void ShutDesignInPlaceInterface()
	{
		m_scatteredBlockHolder.DestroyChildren();
		
		DestroyDesignInPlaceDrawerFramework();
		
		m_blockCage.transform.localPosition = c_blockCagePosition_Off;

		m_designInPlaceInterface.SetActive(false);
		m_inPlaceCamera.gameObject.SetActive(false);

		ShutdownInfoSheet();
		
		Destroy(m_wildSnapHolder.gameObject);
	}




	static DebugConsole.Command s_designglobally = new("dg", _s =>
	{
		bool newDG = Utility.SetOrToggle(Me.m_isInDesignGlobally, _s);
		if (newDG != Me.m_isInDesignGlobally)
		{
			if (newDG) Me.StartDesignGlobally(null);
			else Me.EndDesignGlobally(true);
		}
	});
	public bool m_isInDesignGlobally = false;
	public bool IsDesignGloballyConsuming => m_grabbedBlock != null;
	public Transform GrabbedBlock => m_grabbedBlock?.transform;

	public void StartDesignGloballyUnfocused()
	{
		StartDesignGlobally(null);
	}
	

	Vector3 m_designGloballySavedCameraPosition;
	Quaternion m_designGloballySavedCameraRotation;
	
	private Dictionary<MAComponentInfo, int> m_globalComponentCount;
	
	public void StartDesignGlobally(NGCommanderBase _focus, Func<bool, bool> _onDesignAccepted = null, bool _isFirstDesign = false)
	{
		m_globalComponentCount = MABuilding.CountAllComponents();
		ContextMenuManager.Me.RemoveCurrentMenu();

		DesignHarness.Me.SetupLights(false);

		s_outstandingRestoreOpperations.Clear();
		AudioClipManager.Me.PlaySound("PlaySound_GlobalDesignOpen_Button", GameManager.Me.gameObject);
		
		m_designInPlaceCompleteCb = _onDesignAccepted;
		m_designInPlaceIsFirstDesign = _isFirstDesign;
		
		m_designGloballySavedCameraPosition = Camera.main.transform.position;
		m_designGloballySavedCameraRotation = Camera.main.transform.rotation;
		m_designGloballyClosing = false;
		m_designInPlaceEnterExitTime = 0;
		m_designInPlaceEnterExitPhase = 0;

		SetInitialDesignInPlaceValues();
		
		++m_itemUniqueIdentifier;
		
		m_isInDesignGlobally = true;
		foreach (var cmd in NGManager.Me.m_NGCommanderList)
		{
			if (cmd.IsLocked) continue;
			CreateAllDrags(cmd);
		}
		//for (int i = 0; i < m_wildBlockHolder.childCount; ++i)
		//	CreateAllDrags(m_wildBlockHolder.GetChild(i).gameObject);
		if (_focus != null && NGManager.Me.m_NGCommanderList.Contains(_focus) == false)
		{
			if (_focus.IsLocked == false)
			{
				CreateAllDrags(_focus);
			}
		}

		(m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual) = (m_blockHolder, m_turntableOrigin, m_turntableVisual);

		m_currentTableMode = DESIGN_CATEGORY.CIVIC;
		
		FindDesignInPlaceButtons();
		
		m_coroutineOwner = GlobalData.Me;
		m_designCamera = Camera.main;
		m_designInPlaceBuilding = null;
		ClearUndo();
		(m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual) = (m_blockHolder, m_turntableOrigin, m_turntableVisual);
		m_product = null;

		m_blockHolder = new GameObject("BlockHolder");

		SetInitialDrawerSetType();
		GenerateDrawerSetLists();
		
		var focusBuilding = _focus as MABuilding;
		string drawerSet = "", partSet = "";
		if (focusBuilding != null)
		{
			foreach (var action in focusBuilding.ActionComponents)
			{
				var defaultSet = action?.m_info?.m_defaultDrawerSet;
				if (defaultSet == null) continue;
				var bits = defaultSet.Split(':');
				if (bits.Length >= 2)
				{

					partSet = bits[0];
					drawerSet = bits[1];
					break;
				}
			}
		}
		SetDrawerState(partSet, ref drawerSet);
		m_currentDesignCategory = m_defaultPartSet;

		m_currentDrawerSet = m_categoryList.IndexOf(m_defaultPartSet);
		UpdateDesignInPlaceCatLabels();

		PrepareDesignInPlaceInterface(drawerSet);

		SetupInfoSheet();

		m_turntableOrigin = new GameObject("TTO");

		KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.DesignGlobally);
		m_designGloballyFocusBuilding = null;
		SetDesignGloballyFocus(_focus);
	}

	private const bool c_highlightFocusBuilding = false;
	
	public void SetDesignGloballyFocus(NGCommanderBase _focus)
	{
		KeyboardShortcutManager.Me.PopShortcuts();
		KeyboardShortcutManager.Me.PushShortcuts(_focus == null ? KeyboardShortcutManager.EShortcutType.DesignGlobally : KeyboardShortcutManager.EShortcutType.DesignFocused);
		bool isInitial = m_designGloballyFocusBuilding == null; 
		m_designGloballyFocusBuilding = _focus;
		
		TableState.m_currentBuildingDesignID = _focus?.m_linkUID ?? -1;
		TableState.m_isDesigningProductOrderId = -1;

		++m_itemUniqueIdentifier;

		m_turntableVisual = m_turntableOrigin;

		if (_focus == null)
		{
			var cam = Camera.main.transform;
			cam.position = m_designGloballySavedCameraPosition; // TODO - lerp this
			cam.rotation = m_designGloballySavedCameraRotation;
			if (c_highlightFocusBuilding) CameraRenderSettings.Me.ClearHighlight();
			m_currentDesign = null;
			RefreshDesignInfo();
			return;
		}
		
		m_lastCamAbsolutePos = Vector3.zero;

		m_currentDesign = m_designGloballyFocusBuilding.m_stateData.m_buildingDesign;

		float initialZoom = .3f;
		var rootVisuals = _focus.Visuals.GetChild(0).GetComponentInChildren<Block>().m_toVisuals;
		var averagePos = Vector3.zero;
		var count = 0.0f;
		foreach (Transform t in rootVisuals)
		{
			averagePos += t.position;
			count += 1;
		}
		averagePos /= count;
		var newFocus = averagePos.GroundPosition(1);

		if (isInitial)
		{
			float zoomScale = CurrentBlockBuildScale;
			float zoom = Mathf.Lerp(c_unscaledMinZoom, c_unscaledMaxZoom, initialZoom) * zoomScale;
			m_designInPlaceInitialZoomDirection = Quaternion.Euler(30, m_designCamera.transform.eulerAngles.y, 0); // Start in existing orientation
			m_designInPlaceInitialZoomTo = newFocus - m_designInPlaceInitialZoomDirection * Vector3.forward * zoom;
		}
		else
		{
			var camPos = m_designCamera.transform.position;
			var focusPos = m_designInPlaceFocus;
			var zoom = (camPos - focusPos).magnitude;
			m_designInPlaceInitialZoomDirection = Quaternion.LookRotation(focusPos - camPos);
			m_designInPlaceInitialZoomTo = newFocus - m_designInPlaceInitialZoomDirection * Vector3.forward * zoom;
		}
		m_designInPlaceInitialZoomToInternal = m_designInPlaceInitialZoomTo;
		m_designInPlaceFocus = newFocus;

		if (c_highlightFocusBuilding) CameraRenderSettings.Me.SetHighlight(_focus, .6f);

		m_turntableOrigin.transform.position = m_designInPlaceFocus;
		m_turntableOrigin.transform.forward = _focus.transform.forward;

		m_currentDesign = m_designGloballyFocusBuilding.m_stateData.m_buildingDesign;
		RefreshDesignInfo();
        
		if (_focus is MABuilding ma)
		{
			var type = ma.GetDefaultDrawerSet();
			if (string.IsNullOrEmpty(type) == false)
			{
				int drawerSet = m_categoryList.IndexOf(type);
				if (drawerSet != -1)
				{
					m_defaultPartSet = type;
					m_currentDrawerSet = drawerSet;
					UpdateDesignInPlaceCatLabels();
				}
			}
		}
	}

#if false // render-texture alpha overlay
	Camera m_interiorOverlayCamera;
	Block m_currentHoverBlock = null;
	Block m_incomingHoverBlock = null;
	float m_hoverBlockTimer = 0;
	UnityEngine.UI.RawImage m_hoverDisplay;
	Texture2D m_hoverTexture;
	private void HoverBlock(Block _block)
	{
		if (_block != m_incomingHoverBlock)
			m_hoverBlockTimer = 0;
		if (_block != null || m_currentHoverBlock != null)
			m_hoverBlockTimer += Time.deltaTime;
		else
			m_hoverBlockTimer = 0;
		m_incomingHoverBlock = _block;
		const float c_fadeOutTime = .25f;
		const float c_fadeInStartTime = .6f;
		const float c_fadeInTime = .25f;
		float alpha = 1;
		if (m_hoverBlockTimer < c_fadeOutTime)
			alpha = (c_fadeOutTime - m_hoverBlockTimer) / c_fadeOutTime;
		else if (m_hoverBlockTimer < c_fadeInStartTime)
			alpha = 0;
		else if (m_hoverBlockTimer < c_fadeInStartTime + c_fadeInTime)
			alpha = (m_hoverBlockTimer - c_fadeInStartTime) / c_fadeInTime;
		if (m_hoverBlockTimer < c_fadeOutTime)
		{
			_block = m_currentHoverBlock;
		}
		else
		{
			if (m_currentHoverBlock != _block)
			{
				Destroy(m_interiorObjectInstance);
				m_interiorObjectInstance = null;
			}
			m_currentHoverBlock = _block;
		}
		if (_block != null)
		{
			if (m_interiorObjectInstance == null)
				GenerateHoverInterior(_block, 30);
			var cam = m_interiorOverlayCamera;
			if (cam == null)
				cam = m_interiorOverlayCamera = Instantiate(CaptureObjectImage.Me.m_captureCamera);
			cam.transform.position = m_designCamera.transform.position;
			cam.transform.rotation = m_designCamera.transform.rotation;
			cam.fieldOfView = m_designCamera.fieldOfView;
			var rt = RenderTexture.GetTemporary(Screen.width, Screen.height, 32);
			cam.targetTexture = rt;
			cam.Render();
			cam.targetTexture = null;

			if (m_hoverTexture == null || m_hoverTexture.width != Screen.width || m_hoverTexture.height != Screen.height)
			{
				m_hoverTexture = new Texture2D(Screen.width, Screen.height);
			}
			RenderTexture saveActive = RenderTexture.active;
			RenderTexture.active = rt;
			var texture = m_hoverTexture;
			texture.ReadPixels(new Rect(0, 0, texture.width, texture.height), 0, 0);
			texture.Apply();
			RenderTexture.active = saveActive;
			RenderTexture.ReleaseTemporary(rt);

			if (m_hoverDisplay == null)
			{
				var rawImageObj = new GameObject("Overlay");
				var rawImage = rawImageObj.AddComponent<UnityEngine.UI.RawImage>();
				rawImageObj.transform.SetParent(GameManager.Me.CurrentCanvas);
				m_hoverDisplay = rawImage;
				rawImage.raycastTarget = false;
				var rx = rawImage.transform as RectTransform;
				rx.anchorMin = Vector2.zero;
				rx.anchorMax = Vector2.one;
				rx.pivot = Vector2.one * .5f;
				rx.offsetMin = rx.offsetMax = Vector2.zero;
			}
			m_hoverDisplay.texture = texture;
			const float c_maxAlpha = .8f;
			m_hoverDisplay.color = new Color(1,1,1,alpha * c_maxAlpha);
		}
		else
		{
			if (m_interiorObjectInstance != null)
				GenerateHoverInterior(_block, 30);
		}
	}
#elif false // hide block, draw interior normally in place
	Block m_currentHoverBlock = null;
	GameObject m_currentHoverInterior = null;
	float m_hoverBlockDelay = 0;
	private void HoverBlock(Block _block)
	{
		if (m_currentHoverBlock == _block)
		{
			var delayWas = m_hoverBlockDelay;
			m_hoverBlockDelay += Time.deltaTime;
			const float c_hoverBlockDelayTime = .75f;
			if (m_hoverBlockDelay >= c_hoverBlockDelayTime && delayWas < c_hoverBlockDelayTime)
			{
				HoverEnableRenderer(false);
				GenerateHoverInterior(m_currentHoverBlock);
			}
			return;
		}
		m_hoverBlockDelay = 0;
		HoverEnableRenderer(true);
		m_currentHoverBlock = _block;
	}

	private void HoverEnableRenderer(bool _enable)
	{
		if (m_currentHoverBlock == null) return;
		foreach (var rnd in m_currentHoverBlock.m_toVisuals.GetComponentsInChildren<Renderer>(true))
			rnd.enabled = _enable;
	}

#elif false // draw interior over all other
	Block m_currentHoverBlock = null;
	GameObject m_currentHoverInterior = null;
	float m_hoverFade = -1;

	private void HoverBlock(Block _block)
	{
		if (_block != m_currentHoverBlock)
		{
			//if (m_hoverFade < 0)
			m_hoverFade = 0;
			m_currentHoverBlock = _block;
		}
		if (m_hoverFade >= 0)
		{
			var fadeWas = m_hoverFade;
			m_hoverFade += Time.deltaTime;
			const float c_fadeOutLength = .1f;
			const float c_fadeInStart = .75f;
			const float c_fadeInLength = .1f;
			const float c_depthBiasValue = 0.05f;
			if (m_hoverFade < c_fadeOutLength)
			{
				Shader.SetGlobalFloat("_PixelFade", (1 - m_hoverFade / c_fadeOutLength));
			}
			else if (m_hoverFade < c_fadeInStart)
			{
				if (fadeWas < c_fadeOutLength)
				{
					if (m_interiorObjectInstance != null)
					{
						Destroy(m_interiorObjectInstance);
						m_interiorObjectInstance = null;
					}
				}
			}
			else if (m_hoverFade < c_fadeInStart + c_fadeInLength)
			{
				if (fadeWas < c_fadeInStart)
				{
					GenerateHoverInterior(m_currentHoverBlock, 0, 0, .06f);
					if (m_currentHoverBlock != null)
					{
						m_interiorObjectInstance.transform.position = m_currentHoverBlock.transform.position;
						m_interiorObjectInstance.transform.rotation = m_currentHoverBlock.transform.rotation;
					}
				}
				Shader.SetGlobalFloat("_PixelFade", ((m_hoverFade - c_fadeInStart) / c_fadeInLength));
			}
			else
			{
				Shader.SetGlobalFloat("_PixelFade", 1);
				m_hoverFade = -1;
			}
		}
	}
	
#elif true // subtract cube/ray from visuals - [x]
	Block m_currentHoverBlock = null;
	GameObject m_currentHoverInterior = null;
	bool m_inXRayMode = false;
	public void HoverBlock(Block _block)
	{
		if (_block != null && String.IsNullOrEmpty(_block.m_interiorVisuals))
			_block = null;

		if (_block == m_currentHoverBlock)
		{
			UpdateHoverBlock();
			return;
		}
		SwitchHoverBlock(false);
		m_currentHoverBlock = _block;
		SwitchHoverBlock(true);
	}

	private void SwitchHoverBlock(bool _hoverOn)
	{
		if (m_currentHoverBlock == null) return;
		var clip = m_currentHoverBlock.gameObject.GetComponentInChildren<BoxClip>();
		if (_hoverOn)
		{
			if (clip == null)
			{
				var clipObj = new GameObject("Clip");
				clipObj.transform.SetParent(m_currentHoverBlock.transform);
				clipObj.transform.localRotation = Quaternion.identity;
				clipObj.transform.localScale = Vector3.one;
				clipObj.transform.localPosition = Vector3.zero;
				var clipperObj = new GameObject("Clipper");
				clipperObj.transform.SetParent(clipObj.transform);
				clipperObj.transform.localRotation = Quaternion.identity;
				clip = clipperObj.AddComponent<BoxClip>();
				//clip.m_root = m_currentHoverBlock.m_toVisuals;
				clip.m_root = m_currentHoverBlock.transform.parent;
				clip.SetOnDestroy(clipObj);

				GenerateHoverInterior(m_currentHoverBlock, clip, () => { 
				if (m_interiorObjectInstance != null)
				{
					m_interiorObjectInstance.transform.SetParent(clipObj.transform, true);
					clip.SetOnStart(m_interiorObjectInstance);
				}
				});
			}
			clip.SetTarget(true);
			UpdateHoverBlock();
		}
		else
		{
			if (clip != null)
				clip.SetTarget(false);
		}
	}

	void UpdateHoverInterior()
	{
	}

	private void UpdateHoverBlock()
	{
		if (m_currentHoverBlock == null)
		{
			if (m_interiorObject.activeSelf)
				m_interiorObject.SetActive(false);
			return;
		}

		if (m_interiorObjectInstance != null)
		{
			bool characterOn = true;//(((int)(Time.time / 5)) & 1) == 0;
			var character = m_interiorObjectInstance.GetComponentInChildren<CharacterVisuals>(true);
			if (character != null)
				if (character.gameObject.activeSelf != characterOn)
					character.gameObject.SetActive(characterOn);
		}
		
		UpdateHoverInterior();
	}
#else
	Block m_currentHoverBlock;
	void HoverBlock(Block _which)
	{
	}
#endif
	public GameObject m_interiorObject;
	public GameObject m_interiorObjectRoof;
	GameObject m_interiorObjectInstance;
	private void GenerateHoverInterior(Block _parent, BoxClip _clipper, System.Action _cb, int _layer = 0, float _initialPixelFade = 1, float _initialDepthBias = 0)
	{
		m_interiorObjectInstance = null;
		bool enable = _parent != null;
		if (enable)
		{
			// generate interior based on parent block
			Shader.SetGlobalFloat("_DepthBias", _initialDepthBias);
			Shader.SetGlobalFloat("_PixelFade", _initialPixelFade);
			ResManager.LoadAsync<GameObject>("_Prefabs/Interior/" + _parent.m_interiorVisuals, (_g) =>
			{
				if (_g != null && m_currentHoverBlock != null)
				{
					m_interiorObjectInstance = Instantiate(_g);
					m_interiorObjectInstance.SetActive(true);
					m_interiorObjectInstance.SetLayerRecursively(_layer);
					m_interiorObjectInstance.transform.position = m_currentHoverBlock.transform.position;
					var bounds = ManagedBlock.GetTotalVisualBounds(m_interiorObjectInstance);
					_clipper.transform.localScale = bounds.size * .5f;
					_clipper.transform.position = bounds.center;
					m_interiorObjectInstance.transform.rotation = m_currentHoverBlock.transform.rotation;
				}
				else
				{
					Debug.LogError($"Failed to load interior {_parent.m_interiorVisuals}");
				}
				_cb();
			});
		}
	}
	
	HashSet<MABuilding> m_hiddenBuildings = new();
	HashSet<MABuilding> m_hiddenBuildingsToUnhide = new();
	public void UpdateDesignGloballyBuildingHide()
	{
		m_hiddenBuildingsToUnhide.Clear();
		m_hiddenBuildingsToUnhide.UnionWith(m_hiddenBuildings);
		if (m_isInDesignGlobally && m_designGloballyFocusBuilding != null && m_designGloballyClosing == false)
		{
			var camPos = m_designCamera.transform.position;
			var camFwd = m_designCamera.transform.forward;
			var distanceToFocus = Vector3.Dot(m_designGloballyFocusBuilding.transform.position - camPos, camFwd);
			var hits = Physics.SphereCastAll(camPos, 4, camFwd, 1000, -1);
			foreach (var hit in hits)
			{
				var building = hit.collider.gameObject.GetComponentInParent<MABuilding>();
				if (building == m_designGloballyFocusBuilding) continue;
				if (building != null)
				{
					var distanceToBuilding = Vector3.Dot(building.transform.position - camPos, camFwd);
					if (distanceToBuilding > distanceToFocus) continue;
					if (m_hiddenBuildings.Add(building))
						building.SetTemporarilyHidden(true);
					else
						m_hiddenBuildingsToUnhide.Remove(building);
				}
			}
		}
		foreach (var building in m_hiddenBuildingsToUnhide)
			if (m_hiddenBuildings.Remove(building))
				building.SetTemporarilyHidden(false);
	}
	
	private Vector3 m_previousDragPlaneOrigin = Vector3.zero;
	public bool m_isOverHarness = false, m_wasOverHarness = false;
	private bool m_designGloballyClosing = false; public bool IsDesignGloballyClosing => m_isInDesignGlobally && m_designGloballyClosing;
	public void UpdateDesignGlobally()
	{
		if (m_isInDesignGlobally == false) return;

		if (m_designGloballyClosing)
		{
			var finished = UpdateDesignInPlaceCatPosition(false);
			if (DesignGloballyFocusMode)
			{
				var cam = Camera.main.transform;
				cam.position = Vector3.Lerp(cam.position, m_designGloballySavedCameraPosition, .2f);
				cam.rotation = Quaternion.Slerp(cam.rotation, m_designGloballySavedCameraRotation, .2f);
				if ((cam.position - m_designGloballySavedCameraPosition).sqrMagnitude > .5f * .5f &&
				    !cam.rotation.AlmostEquals(m_designGloballySavedCameraRotation))
					finished = false;
			}
			if (finished)
			{
				CompleteEndDesignGlobally();
				m_isInDesignGlobally = false;
				m_designGloballyClosing = false;
			}
			return;
		}

		UpdateTurnDesignInPlaceCats();
		UpdateDesignInPlaceCatPosition(true);
		UpdateDesignInPlaceDrawerSets();

		UpdateDesignGloballyOrigin();
		
		DesignGloballyRefreshUndoButtons();
		UpdateDesignUndoRedoKeys();
		if (DesignGloballyFocusMode)
			UpdateDesignInPlaceCamera();
	}

	void UpdateDesignGloballyOrigin()
	{
		CheckDrawerSetsDirty();
		
		var ray = m_designCamera.RayAtMouse();

		float bestDist = 1e23f;
		var rayHits = Physics.RaycastAll(ray, 50);
		m_wasOverHarness = m_isOverHarness;
		m_isOverHarness = false;
		if (m_blockCage != null)
		{
			for (int i = 0; i < rayHits.Length; ++i)
			{
				var rayHit = rayHits[i];
				if (rayHit.collider.isTrigger)
				{
					if (rayHit.collider.gameObject == m_blockCage.gameObject)
					{
						if (m_previousDragPlaneOrigin.sqrMagnitude < .001f * .001f)
							m_previousDragPlaneOrigin = m_dragPlaneOrigin;
						m_dragPlaneOrigin = m_blockCage.transform.position;
						bestDist = 0;
						m_isOverHarness = true;
						m_wasOverHarness = true;
						break;
					}
				}
			}
		}
		if (!m_isOverHarness)
		{
			if (m_previousDragPlaneOrigin.sqrMagnitude > 0)
			{
				m_dragPlaneOrigin = m_previousDragPlaneOrigin;
				m_previousDragPlaneOrigin = Vector3.zero;
			}
			var hits = Physics.SphereCastAll(ray, 2);
			for (int i = 0; i < hits.Length; ++i)
			{
				var hit = hits[i];
				if (hit.collider.isTrigger)
				{
					continue;
				}
				var block = hit.collider.GetComponentInParent<Block>();
				if (block != null)
				{
					if (hit.distance < bestDist)
					{
						var dragBlock = block.GetComponent<DTDragBlock>();
						if (dragBlock != null && dragBlock.IsDragging == false)
						{
							bestDist = hit.distance;
							m_dragPlaneOrigin = block.transform.position;
							//GameManager.Me.ClearGizmos("dg");
							//GameManager.Me.AddGizmoCube("dg", block.transform.position + Vector3.up * 2, Vector3.one * 4, new Color(.5f,.5f,.5f,.5f), true);
						}
					}
				}
			}
		}
		var dragPlaneCameraDistance = Vector3.Dot(m_dragPlaneOrigin - m_designCamera.transform.position, m_designCamera.transform.forward);
		if (dragPlaneCameraDistance < 40)
			m_dragPlaneOrigin = Vector3.zero;
		if (m_dragPlaneOrigin.sqrMagnitude < .01f * .01f)
		{
			GameManager.Me.RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * .25f, 0), out var hit, GameManager.c_layerTerrainBit);
			m_dragPlaneOrigin = hit.point;
		}
		/*float bestD2 = 1e23f;
		foreach (var cmd in NGManager.Me.m_NGCommanderList)
		{
			if (cmd.Visuals == null) continue;
			foreach (Transform t in cmd.Visuals)
			{
				var d2 = Utility.DistSqrdToRay(t.position, ray);
				if (d2 < bestD2)
				{
					bestD2 = d2;
					m_dragPlaneOrigin = t.position;
				}
			}
		}*/
		//if (GameManager.Me.RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * .25f, 0), out var hit, GameManager.c_layerTerrainBit)) m_dragPlaneOrigin = hit.point;
		m_turntableOrigin.transform.position = m_dragPlaneOrigin;
	}

	public bool EndDesignGlobally(bool _accept, bool _immediate = false)
	{
		if (m_isInDesignGlobally == false) return false;
		if (m_designGloballyClosing) return false;
		if (true || DesignGloballyFocusMode)
			m_designGloballyClosing = true;
		else
			m_isInDesignGlobally = false;

		// Persist camera pos/rot
		m_designGloballySavedCameraPosition = m_designCamera.transform.position;
		m_designGloballySavedCameraRotation = m_designCamera.transform.rotation;
		
		BeginCloseInfoSheet();
		
		OnBlockLabelModeSelected(BlockLabelMode.None);
		TableState.m_currentBuildingDesignID = 0;

		AudioClipManager.Me.PlaySound(_accept ? "PlaySound_GlobalDesign_Accept" : "PlaySound_GlobalDesign_Exit", GameManager.Me.gameObject);

		foreach (var cmd in NGManager.Me.m_NGCommanderList)
		{
			DestroyAllDrags(cmd);
		}
		//for (int i = 0; i < m_wildBlockHolder.childCount; ++i)
		//	DestroyAllDrags(m_wildBlockHolder.GetChild(i).gameObject);

		Destroy(m_blockHolder);
		Destroy(m_turntableOrigin);
		(m_blockHolder, m_turntableOrigin, m_turntableVisual) = (m_designInPlaceBackupBlockHolder, m_designInPlaceBackupTurntableOrigin, m_designInPlaceBackupTurntableVisual);
		if (c_highlightFocusBuilding) CameraRenderSettings.Me.ClearHighlight();

		KeyboardShortcutManager.Me.PopShortcuts();

		if (_immediate)
		{
			CompleteEndDesignGlobally();
			m_isInDesignGlobally = false;
			m_designGloballyClosing = false;
		}
		
		return true;
	}
	
	void CompleteEndDesignGlobally()
    {
		ShutDesignInPlaceInterface();

		if (m_designInPlaceCompleteCb != null)
			m_designInPlaceCompleteCb(true);
	}

	public static void ResetWildBlockIds()
	{
		if(Me == null || Me.m_wildBlockHolder == null) return;
		
		for (int i = 0; i < Me.m_wildBlockHolder.childCount; ++i)
		{
			var wb = Me.m_wildBlockHolder.GetChild(i).GetComponent<MAWildBlock>();
			wb?.UpdateComponentIds(true);
		}
	}
	
	public static void GetAllWildBlockComponents(Dictionary<long,BCBase> _all)
	{
		if(Me == null || Me.m_wildBlockHolder == null) return;
		
		for (int i = 0; i < Me.m_wildBlockHolder.childCount; ++i)
		{
			var cmps = Me.m_wildBlockHolder.GetChild(i).GetComponentsInChildren<BCBase>(true);
			foreach(var c in cmps)
			{
				_all[c.m_uid] = c;
			}
		}
	}
	
	public static void SaveWildBlocks(SDictionary<long,SaveMABuildingComponent> _s)
	{
		if(Me == null || Me.m_wildBlockHolder == null) return;

		for (int i = 0; i < Me.m_wildBlockHolder.childCount; ++i)
		{
			var cmps = Me.m_wildBlockHolder.GetChild(i).GetComponentsInChildren<BCBase>(true);
			foreach(var c in cmps)
			{
				if(c.m_uid <= 0) continue;
				_s[c.m_uid] = c.Save();
			}
		}
	}
	
	public Transform m_wildBlockHolder;
	public Transform m_scatteredBlockHolder;
	public static void UpdateWildBlockState(MAWildBlock _o)
	{
		var rb = _o.GetComponent<Rigidbody>();
		SDictionary<int,ArrayWrapper<long>> cmpIds = new();

		_o.m_wildBlockState.m_blockDesign = Me.GenerateDesignData(false, _o.gameObject, true, cmpIds);
		_o.m_wildBlockState.m_id = _o.m_wildBlockState.m_id;
		_o.m_wildBlockState.m_position = _o.transform.position;
		_o.m_wildBlockState.m_rotation = _o.transform.rotation.eulerAngles;
		_o.m_wildBlockState.m_velocity = rb != null ? rb.linearVelocity : Vector3.zero;
		_o.m_wildBlockState.m_angularVelocity = rb != null ? rb.angularVelocity : Vector3.zero;
		_o.m_wildBlockState.m_componentIds = cmpIds;
	}
	
	public static GameState_WildBlock ConvertToWildBlock(GameObject _o, bool _flagNonDelete = false)
	{
		if (_o.transform.IsChildOf(Me.m_wildBlockHolder))
			if (_o.GetComponent<MAWildBlock>() != null)
				return null;

		Block block = _o.GetComponent<Block>();
		var state = GameManager.Me.m_state;
		int wildID = block.m_lastWildBlockID == 0 ? state.m_nextWildBlockId++ : block.m_lastWildBlockID;

		SDictionary<int, ArrayWrapper<long>> cmpIds = new ();
		string design = Me.GenerateDesignData(false, _o, true, cmpIds);
		var rb = _o.GetComponent<Rigidbody>();
		var wild = new GameState_WildBlock() { 
			m_blockDesign = design,
			m_id = wildID,
			m_position = _o.transform.position,
			m_rotation = _o.transform.rotation.eulerAngles,
			m_velocity = rb != null ? rb.linearVelocity : Vector3.zero,
			m_angularVelocity = rb != null ? rb.angularVelocity : Vector3.zero,
			m_doNotAllowDelete = _flagNonDelete,
			m_hasEverBeenUnlocked = true,
			Obj = _o,
			m_componentIds = cmpIds
		};
		
		block.DoNotAllowDelete = wild.m_doNotAllowDelete;
		MABuilding building = block.transform.parent.GetComponentInParent<MABuilding>();
		if(building)
		{
			foreach(BCBase component in block.GetComponentsInChildren<BCBase>(true))
			{
				building.RemoveComponent(component, BlockDragAction.ToWild);
			}
		}
		_o.transform.SetParent(Me.m_wildBlockHolder, true);
		_o.AddComponent<NukeAnyHandler>();
		block.m_visualSwitcher?.SwitchTo(0);
		state.m_wildBlocks.Add(wild);

		Me.m_grabbedFrom = building;
		Me.m_grabbedTo = null;
		
		if(!Me.m_isInDesignGlobally)
			Me.UpdateDesignDataAndSave(false);
		
		MAWildBlock.Load(wild, _o);
		return wild.Copy();
	}
	
	public static GameState_WildBlock ConvertFromWildBlock(GameObject _o)
	{
		if (_o.transform.IsChildOf(Me.m_wildBlockHolder) == false) return null;
		var wb = _o.GetComponent<MAWildBlock>();
		if(wb != null) wb.enabled = false;
		Destroy(_o.GetComponent<NukeAnyHandler>());
		Destroy(_o.GetComponent<MAWildBlock>());
		Destroy(_o.GetComponent<Rigidbody>());
		Destroy(_o.GetComponent<PlantController>());
		//Destroy(_o.GetComponent<DistrictOverride>());

		var state = GameManager.Me.m_state;
		for (int i = 0; i < state.m_wildBlocks.Count; ++i)
		{
			if (state.m_wildBlocks[i].Obj == _o)
			{
				var copy = state.m_wildBlocks[i].Copy();
				state.m_wildBlocks.RemoveAt(i);
				return copy;
			}
		}
		return null;
	}

	public static void PreLoadWildBlocks()
	{
		Me.m_wildBlockHolder = new GameObject("WildBlocks").transform;
		Me.m_scatteredBlockHolder = new GameObject("ScatteredBlocks").transform;
	}
	
	public static void LoadWildBlocks()
	{
		var state = GameManager.Me.m_state;
		for (int i = 0; i < state.m_wildBlocks.Count; ++i)
		{
			var w = state.m_wildBlocks[i];
			if (string.IsNullOrEmpty(w.m_blockDesign))
			{
				state.m_wildBlocks.RemoveAt(i);
				--i;
				continue;
			}
			LoadWildBlock(w);
		}
	}
	
	public static void PostLoadWildBlocks()
	{
		for (int i = 0; i < Me.m_wildBlockHolder.childCount; ++i)
		{
			var wb = Me.m_wildBlockHolder.GetChild(i).GetComponent<MAWildBlock>();
			if(wb == null) continue;
			
			bool requiresRebuild = wb.m_componentsChanged;
			if(requiresRebuild == false)
			{
				foreach(var cmp in wb.GetComponents<BCBase>())
				{
					if(cmp.m_uid == 0 && cmp.AllowSave)
					{
						requiresRebuild = true;
						break;
					}
				}
			}
			
			if(requiresRebuild)
			{
				Debug.LogError($"Rebuilding WildBlock component ids due to uninitialised id(s). WildBlock: {wb.name}");
				wb.UpdateComponentIds(true);
				wb.m_componentsChanged = false;
			}
		}
	}

	public static void LoadWildBlock(GameState_WildBlock _wildBlock, bool _disableKinematic = false, Action<bool> _onLoaded = null)
	{
		if(string.IsNullOrEmpty(_wildBlock.m_blockDesign)) return;
		
		Me.RestoreDesign(RestoreType.Building, _wildBlock.m_blockDesign, Me.m_wildBlockHolder, (_o, componentsChanged) => 
		{ 
			if (_o != null)
			{
				var wb = MAWildBlock.Load(_wildBlock, _o);
				wb.m_componentsChanged = componentsChanged;
				_wildBlock.Obj = _o;
				_o.AddComponent<NukeAnyHandler>();
				var block = _o.GetComponent<Block>(); 
				block.m_visualSwitcher?.SwitchTo(0);
				block.DoNotAllowDelete = _wildBlock.m_doNotAllowDelete;
				
				GameState_BuildHelper.Load(_wildBlock);
					
				if (Me.m_isInDesignGlobally)
					_o.AddComponent<DTDragBlock>().InitialiseScale(1);
					
				if(_disableKinematic)
				{
					var rb = _o.GetComponent<Rigidbody>();
					if(rb) rb.isKinematic = false;
				}
				if(_onLoaded != null) _onLoaded(true);
			}
			else
			{
				if(_onLoaded != null) _onLoaded(false);
			}
		}, false, false, _wildBlock.m_componentIds);
	}

	private bool UpdateDesignDataAndSave(bool _reseatBlocks)
	{
		bool changed = m_grabbedFromWildBlock != null || m_grabbedToWildBlock != null;
		var oldFromId = m_grabbedFrom?.m_stateData?.m_buildingDesign ?? null;
		var oldToId = m_grabbedTo?.m_stateData?.m_buildingDesign ?? null;
		
		// Update components
		if (m_grabbedBlock != null && m_grabbedFrom != m_grabbedTo)
		{
			var cmps = m_grabbedBlock.GetComponentsInChildren<BCBase>(true);		
			
			if (m_grabbedFrom is MABuilding from)
			{
				bool isDestroying = m_grabbedToWildBlock == null && m_grabbedTo == null;
				foreach (var cmp in cmps)
				{
					from.RemoveComponent(cmp, isDestroying ? BlockDragAction.Destroy : BlockDragAction.None);
					
					if(isDestroying)
					{
						// Force a save for undo/redo purposes
						cmp.Save(GameManager.Me.m_state.m_maComponentData);
					}
				}
			}
				
			if(m_grabbedTo != null && m_grabbedTo is MABuilding to)
			{
				foreach(var cmp in cmps)
				{
					to.AddComponent(cmp);
				}
				to.StartupComponents();
			}
		}
		
		if (m_grabbedFrom != null)
		{
			SDictionary<int,ArrayWrapper<long>> cmpIds = new();
			var snapshotFrom = GenerateDesignData(false, m_grabbedFrom.Visuals.gameObject, false, cmpIds);
			changed |= UpdateBuildingDesign(m_grabbedFrom, snapshotFrom, cmpIds);
		}
		if (m_grabbedTo != null)
		{
			SDictionary<int,ArrayWrapper<long>> cmpIds = new();
			var snapshotTo = GenerateDesignData(false, m_grabbedTo.Visuals.gameObject, false, cmpIds);
			changed |= UpdateBuildingDesign(m_grabbedTo, snapshotTo, cmpIds);
		}

		if (_reseatBlocks)
		{
			ReseatBuildingBlocks(m_grabbedFrom);
			ReseatBuildingBlocks(m_grabbedTo);
		}

		if (changed)
		{
			if (m_isInDesignGlobally && m_designSingly == false)
				AddDesignGloballyStoreUndo(m_grabbedFrom, oldFromId, m_grabbedFrom?.m_stateData?.m_buildingDesign,
					m_grabbedTo, oldToId, m_grabbedTo?.m_stateData?.m_buildingDesign, m_grabbedFromWildBlock,
					m_grabbedToWildBlock);
			
			m_grabbedFrom?.OnBuildingDesignChanged();
			m_grabbedTo?.OnBuildingDesignChanged();
			
			GameManager.Me.WriteSaveData();
			if(m_designGloballyFocusBuilding != null)
			{
				m_currentDesign = m_designGloballyFocusBuilding.Design;
			}

			if (m_grabbedFrom != null)
			{
				m_grabbedFrom.RefreshPaths(true);
				m_grabbedFrom.GetComponent<PlantController>()?.RefreshInstances();
				m_grabbedFrom.RefreshWildBlockStore();
				m_grabbedFrom.OnDesignChange();
				(m_grabbedFrom as MABuilding)?.SetComponentsChanged();
			}
			if (m_grabbedTo != null)
			{
				m_grabbedTo.RefreshPaths(true);
				m_grabbedTo.GetComponent<PlantController>()?.RefreshInstances();
				m_grabbedTo.RefreshWildBlockStore();
				m_grabbedTo.OnDesignChange();
				(m_grabbedTo as MABuilding)?.SetComponentsChanged();
			}
		}
		m_grabbedFrom = m_grabbedTo = null;
		m_grabbedFromWildBlock = null;
		m_grabbedToWildBlock = null;
		m_grabbedBlock = null;
		m_grabbedToTransform = null;
		return changed;
	}

	private void ReseatBuildingBlocks(NGCommanderBase _building)
	{
		if (_building == null) return;
		BuildingPlacementManager.RefreshBuildingHeight(_building);
		ReseatBlocks(_building.Visuals);
	}

	
	public class DesignGloballyUndoStep
	{
		public NGCommanderBase m_from;
		public GameState_Design m_fromDesignIdBefore, m_fromDesignIdAfter;
		public NGCommanderBase m_to;
		public GameState_Design m_toDesignIdBefore, m_toDesignIdAfter;
		public GameState_WildBlock m_wildBlockBefore;
		public GameState_WildBlock m_wildBlockAfter;
		public float m_spendDelta;
		public Dictionary<MAComponentInfo, int> m_componentCount;
		
		public DesignGloballyUndoStep(Dictionary<MAComponentInfo,int> _cmpCount, NGCommanderBase _from, GameState_Design _fromIdBefore, GameState_Design _fromIdAfter, NGCommanderBase _to, GameState_Design _toIdBefore, GameState_Design _toIdAfter, GameState_WildBlock _wildBefore, GameState_WildBlock _wildAfter)
		{
			m_componentCount = _cmpCount;
			m_from = _from;
			m_fromDesignIdBefore = _fromIdBefore;
			m_fromDesignIdAfter = _fromIdAfter;
			m_to = _to;
			m_toDesignIdBefore = _toIdBefore;
			m_toDesignIdAfter = _toIdAfter;
			m_wildBlockBefore = _wildBefore?.Copy();
			m_wildBlockAfter = _wildAfter?.Copy();
			m_spendDelta = GetTotalCostDelta();
		}
		
		private float GetTotalCostDelta()
		{
			// Create a copy of the components dict
			var componentCounts = m_componentCount.ToDictionary(entry => entry.Key, entry => entry.Value);
			// Get design interfaces
			var fromDesignBefore = m_fromDesignIdBefore == null ? null : NGDesignInterface.Get(m_fromDesignIdBefore);
			var toDesignBefore = m_toDesignIdBefore == null ? null : NGDesignInterface.Get(m_toDesignIdBefore);
			var wildBefore = (m_wildBlockBefore == null || string.IsNullOrEmpty(m_wildBlockBefore.m_blockDesign)) ? null : NGDesignInterface.Get(m_wildBlockBefore.m_blockDesign);
			
			var fromDesignAfter = m_fromDesignIdAfter == null ? null : NGDesignInterface.Get(m_fromDesignIdAfter);
			var toDesignAfter = m_toDesignIdAfter == null ? null : NGDesignInterface.Get(m_toDesignIdAfter);
			var wildAfter = (m_wildBlockAfter == null || string.IsNullOrEmpty(m_wildBlockAfter.m_blockDesign)) ? null : NGDesignInterface.Get(m_wildBlockAfter.m_blockDesign);
			
			// Remove the before counts
			fromDesignBefore?.RemoveComponentsFromCount(componentCounts);
			toDesignBefore?.RemoveComponentsFromCount(componentCounts);
			wildBefore?.RemoveComponentsFromCount(componentCounts);
			
			// Calculate before prices
			var fromBeforeCost = fromDesignBefore?.TotalCost(componentCounts) ?? 0;
			var toBeforeCost = toDesignBefore?.TotalCost(componentCounts) ?? 0;
			var wildBeforeCost = wildBefore?.TotalCost(componentCounts) ?? 0;
			
			// Remove the before counts again
			fromDesignBefore?.RemoveComponentsFromCount(componentCounts);
			toDesignBefore?.RemoveComponentsFromCount(componentCounts);
			wildBefore?.RemoveComponentsFromCount(componentCounts);

			// Calculate after prices
			var fromAfterCost = fromDesignAfter?.TotalCost(componentCounts) ?? 0;
			var toAfterCost = toDesignAfter?.TotalCost(componentCounts) ?? 0;
			var wildAfterCost = wildAfter?.TotalCost(componentCounts) ?? 0;
			
			return (fromBeforeCost-fromAfterCost) + (toBeforeCost-toAfterCost) + (wildBeforeCost-wildAfterCost);
		}

		private static void Apply(string _operationName, NGCommanderBase _cmd, GameState_Design _design)
		{
			if (_cmd == null) return;
			_cmd.m_stateData.m_buildingDesign = _design;
			AddPendingOperation(_operationName);
			BuildingPlacementManager.RecreateBuilding(_cmd.m_stateData, _cmd, false, null, b => RemovePendingOperation(_operationName));
		}

		private static void Apply(string _operationName, GameState_WildBlock _create, GameState_WildBlock _destroy)
		{
			var state = GameManager.Me.m_state;
			if (_destroy != null)
			{
				// destroy from _wildAfter
				for (int i = 0; i < state.m_wildBlocks.Count; ++i)
				{
					var wb = state.m_wildBlocks[i];
					if (wb.m_id == _destroy.m_id)
					{
						ConvertFromWildBlock(wb.Obj);
						Destroy(wb.Obj);
						break;
					}
				}
			}
			
			if (_create != null)
			{
				// create from _wildBefore
				var copy = _create.Copy();
				state.m_wildBlocks.Add(copy);
				AddPendingOperation(_operationName);
				LoadWildBlock(copy, true, b => RemovePendingOperation(_operationName));
			}
		}
		
		public void ApplyUndo()
		{
			Apply("UndoFrom", m_from, m_fromDesignIdBefore);
			Apply("UndoTo", m_to, m_toDesignIdBefore);
			Apply("UndoWild", m_wildBlockBefore, m_wildBlockAfter);
			
			Me.m_globalComponentCount = m_componentCount;
			Me.ExecuteCurrencyDelta(m_spendDelta, true);
		}

		public void ApplyRedo()
		{
			Apply("RedoFrom", m_from, m_fromDesignIdAfter);
			Apply("RedoTo", m_to, m_toDesignIdAfter);
			Apply("RedoWild", m_wildBlockAfter, m_wildBlockBefore);
			Me.ExecuteCurrencyDelta(m_spendDelta, false);
		}
	}
	
	private static List<string> s_pendingOperations = new();
	private static void AddPendingOperation(string _id) { s_pendingOperations.Add(_id); }
	private static void RemovePendingOperation(string _id)
	{
		s_pendingOperations.Remove(_id);
		
		// Buildings are rebuilt on redo / undo destroying all BCBase components
		// meaning all references to them are invalid
		if(s_pendingOperations.Count == 0)
		{
			MABuilding.RebuildComponentMap();
			
			foreach(var character in NGManager.Me.m_MACharacterList)
			{
				character.UpdateComponentReferences();
			}
			
			foreach(var cart in NGManager.Me.m_maVehicles)
			{
				cart.UpdateComponentReferences();
			}
			
			foreach(var order in GameManager.Me.m_state.m_orders.m_values)
			{
				if(order.IsValid == false || order.IsComplete) continue;
				
				order.UpdateComponentReferences();
			}
		}
	}
			
	private List<DesignGloballyUndoStep> m_designGloballyUndoStack = new ();
	private int m_designGloballyUndoStep = 0;

	bool DesignGloballyClickedUndo()
	{
		if (IsInDesignGloballyActively == false) return false;
		GameManager.Me.m_state.PreSaveBuildings();
		AudioClipManager.Me.PlayUISound("PlaySound_DesignModeUndo");
		--m_designGloballyUndoStep;
		m_designGloballyUndoStack[m_designGloballyUndoStep].ApplyUndo();
		return true;
	}

	bool DesignGloballyClickedRedo()
	{
		if (IsInDesignGloballyActively == false) return false;
		GameManager.Me.m_state.PreSaveBuildings();
		AudioClipManager.Me.PlayUISound("PlaySound_DesignModeRedo");
		m_designGloballyUndoStack[m_designGloballyUndoStep].ApplyRedo();
		++m_designGloballyUndoStep;
		return true;
	}
	
	void AddDesignGloballyStoreUndo(NGCommanderBase _from, GameState_Design _fromIdBefore, GameState_Design _fromIdAfter, NGCommanderBase _to, GameState_Design _toIdBefore, GameState_Design _toIdAfter, GameState_WildBlock _wildFrom, GameState_WildBlock _wildTo)
	{
		if (_to == _from) _to = null;
		while (m_designGloballyUndoStep < m_designGloballyUndoStack.Count) m_designGloballyUndoStack.RemoveAt(m_designGloballyUndoStack.Count - 1);
		var undoStep = new DesignGloballyUndoStep(m_globalComponentCount, _from, _fromIdBefore, _fromIdAfter, _to, _toIdBefore, _toIdAfter, _wildFrom, _wildTo);
		ExecuteCurrencyDelta(undoStep.m_spendDelta, false);
		m_designGloballyUndoStack.Add(undoStep);
		++m_designGloballyUndoStep;
		
		m_globalComponentCount = MABuilding.CountAllComponents();
	}

	void DesignGloballyRefreshUndoButtons()
	{
		bool canUndo = m_designGloballyUndoStep > 0;
		bool canRedo = m_designGloballyUndoStep < m_designGloballyUndoStack.Count;
		SetUndoStates(canUndo, canRedo);
	}


	bool CheckDesignGloballyBlockers(Transform _to, bool _ignoreExtents)
	{
		if (_ignoreExtents) return false;
		if (m_isGrabbedBlockExtentIgnorer) return false;
		if (m_isInDesignGlobally == false) return false;
		
		var ng = _to.GetComponentInParent<NGCommanderBase>();
		if (ng == null) return true; // don't allow connection to a non-building
		var baseBlock = ng.Visuals.GetComponentInChildren<BaseBlock>();
		if (baseBlock == null) return false; // old style building, allow anywhere
		return baseBlock.IsPointInside(_to.position + _to.forward * .5f) == false;
	}
}

public class DesignTableAnimationHook : MonoBehaviour {
	protected void TransitionEnd () { }
}
