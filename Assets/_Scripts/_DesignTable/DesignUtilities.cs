using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class DesignUtilities {
    public static int GetDesignPartCountFromDesign(string _design) {
		if (string.IsNullOrEmpty(_design)) return 0;
		var bits = _design.Split(DesignTableManager.c_designSeparator);
		return int.Parse(bits[0]);
	}

    public static int GetStickerCount(PartDescription[] designParts) {
		int count = 0;
		Array.ForEach(designParts, x => { count += x.m_stickers.Count; });
		return count;
	}

    public static int GetPaintCount(PartDescription[] designParts) {
		int count = 0;
		Array.ForEach(designParts, x => { count += x.m_paint.Count; });
		return count;
	}

    public static int GetPatternCount(PartDescription[] designParts) {
		int count = 0;
		Array.ForEach(designParts, x => { count += x.m_pattern.Count; });
		return count;
	}

    public static void ShowBlockInfo(GameObject _object, string _id) {
		var info = NGBlockInfo.GetInfo(_id);
	    if (info == null || info.Type == NGBlockInfo.BlockType.None) return;
	    
	    var iconModel = GameObject.Instantiate(_object); 
	    var rarityPlane = iconModel.GetComponentInChildren<RarityPlane>();
	    if (rarityPlane != null) rarityPlane.gameObject.SetActive(false);
	    var icon = CaptureObjectImage.Me.Capture(iconModel, CaptureObjectImage.Use.Product);
//		    BlockInfoPanel.Create(NGManager.Me.m_blockInfoPanelPrefab, DesignTableManager.Me.m_designTableUI, info, DesignTableManager.Me.IsInBuildingMode, icon, _object);
	    BlockInfoPanelV2.Create(info, DesignTableManager.Me.IsInBuildingMode, icon, _object);
	    GameObject.Destroy(iconModel);
    }

	/*public static float DesignCost(GameState_Design _design)
	{
		float cost = 0;
		var partInfo = GetDesignData(_design);

		foreach (var part in partInfo)
		{
			if (NGBlockInfo.s_allBlocks.ContainsKey(part.m_blockID))
			{
				var details = NGBlockInfo.s_allBlocks[part.m_blockID];
				cost += details.m_materialCost;
			}
		}

		return cost;
	}*/

    public class PartDescription {
	    public string m_blockID;
		public List<int> m_paint = new List<int>();
		public List<int> m_pattern = new List<int>();
		public List<int> m_stickers = new List<int>();
		public List<int> m_links = new List<int>();
		
		public float TotalCost(Dictionary<MAComponentInfo,int> _componentCounts)
		{
			var info = NGBlockInfo.GetInfo(m_blockID);
			float cost = info.Price;
			
			if(_componentCounts != null)
			{
				foreach(var c in info.GetComponentInfos())
				{
					var count = ++_componentCounts[c];
					cost += c.GetPrice(count) * info.MarketInfo.m_currentBlockCostMultiplier;
				}
			}
			
			foreach(var i in m_paint) cost += PaintPotData.s_entries[i].m_cost;
			foreach(var i in m_pattern) cost += PatternData.s_entries[i].m_cost;
			foreach(var i in m_stickers) cost += StickerData.s_entries[i].m_cost;
			return cost;
		}
		
		public string UniqueID()
		{
			m_paint.Sort();
			m_pattern.Sort();
			string block = m_blockID;
			foreach (var p in m_paint) block += $"p{p}";
			foreach (var p in m_pattern)  block += $"t{p}";
			
			int paintUId = -1;  foreach(var p in m_paint) paintUId += p;
			int patternUId = -1;  foreach(var p in m_paint) patternUId += p;
			
			return block; 
		}
    }
	public static PartDescription[] GetDesignData(GameState_Design _design) {
		return GetDesignData(_design.m_design);
	}

	//where we add to the design string
	public static string DesignWithAddBlock(string _original, string _added)
	{
		if (string.IsNullOrEmpty(_original)) return $"1{DesignTableManager.c_designSeparator}{_added}{DesignTableManager.c_designSeparator}0{DesignTableManager.c_designSeparator}";
		var bits = _original.Split(DesignTableManager.c_designSeparator);
		int numParts = int.Parse(bits[0]);
		string res = $"{numParts + 1}";
		for (int i = 0; i < numParts; ++i) res += $"{DesignTableManager.c_designSeparator}{bits[i + 1]}";
		res += $"{DesignTableManager.c_designSeparator}{_added}";
		for (int i = 1 + numParts; i < bits.Length; ++i)
			res += $"{DesignTableManager.c_designSeparator}{bits[i]}";
		return res;
	}

/*
	public static NGBuffSet GetDesignBuffSet(string _design)
	{
		var res = new NGBuffSet();
		var parts = DesignUtilities.GetDesignData(_design);
		foreach (var p in parts)
		{
			var info = NGBlockInfo.GetInfo(p.m_blockID);
			if (info != null)
				res.Add(info);
			foreach (var i in p.m_paint) res.Add(PaintPotData.s_entries[i]);
			foreach (var i in p.m_pattern) res.Add(PatternData.s_entries[i]);
			foreach (var i in p.m_stickers) res.Add(StickerData.s_entries[i]);
		}

		return res;
	}*/

	public static float GetDesignBuildTimeOneWorker(string _design)
	{
		float timeToBuild = 0;
		var partInfo = GetDesignData(_design);
		foreach (var part in partInfo)
		{
			if (NGBlockInfo.s_allBlocks.TryGetValue(part.m_blockID, out var details))
			{
				timeToBuild += details.m_workerTimeToMake;
			}
		}

		return timeToBuild;
	}

	public static PartDescription[] GetDesignData(string _design, bool _invertLinks = false)
	{
		if (string.IsNullOrEmpty(_design)) return new PartDescription[0];
		var bits = _design.Split(DesignTableManager.c_designSeparator);
		int numParts = int.Parse(bits[0]);
		var res = new PartDescription[numParts];
		for (int p = 0; p < numParts; ++p) {
			var bits2 = bits[1 + p].Split(DesignTableManager.c_decorationSeparator);
			res[p] = new PartDescription();
			res[p].m_blockID = bits2[0];
			if (bits2.Length > 1) {
				var decs = Decoration.GetDecorationEntries(bits2[1]);
				if (decs.Length == 0 || string.IsNullOrEmpty(decs[0])) continue;
				int next = 0;
				int groupCount = int.Parse(decs[next++]);
				for (int i = 0; i < groupCount; ++i) {
					++next;
					int entryCount = int.Parse(decs[next++]);
					for (int j = 0; j < entryCount; ++j) {
						var thisDec = decs[next++].Split('=');
						var dataSplit = thisDec[1].Split(';');
						if (!string.IsNullOrEmpty(dataSplit[0])) {
							if (dataSplit[0][0] == DesignTableManager.c_paintId) res[p].m_paint.Add(int.Parse(dataSplit[0].Substring(1)));
							else if (dataSplit[0][0] == DesignTableManager.c_patternId) res[p].m_pattern.Add(int.Parse(dataSplit[0].Substring(1)));
						}
						if (dataSplit.Length > 1) {
							for (int k = 1; k < dataSplit.Length; ++k) {
								if (!string.IsNullOrEmpty(dataSplit[k])) {
									res[p].m_stickers.Add(int.Parse(dataSplit[k].Substring(1).Split(',')[0]));
								}
							}
						}
					}
				}
			}
		}

		int numLinks = int.Parse(bits[1 + numParts]);
		for (int i = 0; i < numLinks; ++i) {
			var linkInfo = bits[1 + numParts + 1 + i].Split('.');
			int subPart = int.Parse(linkInfo[0]), subHinge = int.Parse(linkInfo[1]), mainPart = int.Parse(linkInfo[2]), mainHinge = int.Parse(linkInfo[3]);
			int localRot = linkInfo.Length >= 5 ? int.Parse(linkInfo[4]) : 0;
			if (_invertLinks) {
				res[mainPart].m_links.Add(mainHinge); res[mainPart].m_links.Add(subPart); res[mainPart].m_links.Add(subHinge); res[mainPart].m_links.Add(localRot);
			} else {
				res[subPart].m_links.Add(subHinge); res[subPart].m_links.Add(mainPart); res[subPart].m_links.Add(mainHinge); res[subPart].m_links.Add(localRot);
			}
		}

		return res;
	}
	public static string[] GetDesignPartsFromDesign(string _design) {
		var details = GetDesignData(_design);
		var parts = new string[details.Length];
		for (int i = 0; i < details.Length; ++i) {
			parts[i] = details[i].m_blockID;
		}

		return parts;
	}
	public static void GetDesignDecorationCountsFromDesign(string _design, out int _stickers, out int _patterns, out int _paints) {
		_paints = _patterns = _stickers = 0;
		var details = GetDesignData(_design);
		foreach (var entry in details) {
			_paints += entry.m_paint.Count;
			_patterns += entry.m_pattern.Count;
			_stickers += entry.m_stickers.Count;
		}
	}
	public static int GetDesignDecorationTotalFromDesign(string _design) {
		int paints, patterns, stickers;
		GetDesignDecorationCountsFromDesign(_design, out stickers, out patterns, out paints);
		return paints + patterns + stickers;
	}

	private static DebugConsole.Command s_dumpboredom = new DebugConsole.Command("dumpboredom", _s =>
	{
		Debug.LogError($"Current block boredom levels");
		foreach (var kvp in GameManager.Me.m_state.m_fitnessData.m_blockBoredom.Base)
			Debug.LogError($"Block {kvp.Key} boredom {kvp.Value}");
	});

	/*public static bool IsInProductLine(string _block, string _productLine)
	{
		if (_productLine == null) return true;
		var block = NGBlockInfo.GetInfo(_block);
		if (block == null)
		{
			Debug.LogError($"NGBlockInfo.GetBlock({_block}) is null on product '{_productLine}");
			return false;
		}
		var types = block.TypeIDs;
		return types.Contains(_productLine);
		return false;
	}
	public static void AddBlockBoredom(string _design, string _productLine, float _increment = 1) // productLine = null for "ignore product line"
	{
		var partInfo = GetDesignData(_design);
		var state = GameManager.Me.m_state;
		
		var addBoredom = new Dictionary<string, float>();
		foreach (var info in partInfo)
		{
			info.m_paint.Sort();
			info.m_pattern.Sort();
			info.m_stickers.Sort();
			string block = info.m_blockID;
			foreach (var p in info.m_paint)
			{
				var id = $"p{p}";
				addBoredom.AddCount(id, _increment);
				block += id;
			}
			foreach (var p in info.m_pattern)
			{
				var id = $"t{p}";
				addBoredom.AddCount(id, _increment);
				block += id;
			}
			foreach (var p in info.m_stickers)
			{
				var id = $"s{p}";
				addBoredom.AddCount(id, _increment);
				block += id;
			}
			addBoredom.AddCount(block, _increment);
		}
		
		float reduceBoredom = NGManager.Me.m_boredomReduceRate;
		var newBoredom = new SDictionary<string, float>();
		foreach (var kvp in state.m_fitnessData.m_blockBoredom.Base)
		{
			var value = kvp.Value;
			if (addBoredom.ContainsKey(kvp.Key) == false && IsInProductLine(kvp.Key, _productLine))
				value = Mathf.Max(0, value - reduceBoredom);
			if (value > 0)
				newBoredom[kvp.Key] = value;
		}
		foreach (var kvp in addBoredom)
		{
			float boredom = newBoredom.GetValue(kvp.Key);
			newBoredom[kvp.Key] = boredom + kvp.Value;
		}
		state.m_fitnessData.m_blockBoredom = newBoredom;
	}*/

    public static float GetBlockBoredom(string _id) {
		return GameManager.Me.m_state.m_fitnessData.m_blockBoredom.GetValue(_id, 0);
	}
    
	// Check whether part connections in a loaded design are "correct"
	// Currently "correct" means not rotated and left/right only connects to left/right of same type
    public static ulong CheckConnections(Block[] _blocks, string _design)
    {
	    var blocksIndexed = new Block[_blocks.Length];
	    for (int i = 0; i < _blocks.Length; ++i)
		    blocksIndexed[_blocks[i].m_indexInDesign] = _blocks[i];
	    var details = GetDesignData(_design);
	    ulong badConnectionBm = 0;
	    for (int i = 0; i < details.Length; ++i)
	    {
		    bool isBad = false;
		    for (int j = 0; isBad == false && j < details[i].m_links.Count; j += 4)
		    {
			    int subPart = i;
			    int subHinge = details[i].m_links[j + 0];
			    int mainPart = details[i].m_links[j + 1];
			    int mainHinge = details[i].m_links[j + 2];
			    int rot = details[i].m_links[j + 3];
			    if (rot != 0)
				    isBad = true;
			    else
			    {
				    var mainBlock = blocksIndexed[mainPart];
				    var subBlock = blocksIndexed[subPart];
				    var mainSnap = mainBlock.m_toHinges.GetChild(mainHinge)?.GetComponent<SnapHinge>();
				    var subSnap = subBlock.m_toHinges.GetChild(subHinge)?.GetComponent<SnapHinge>();
				    if (mainSnap == null || subSnap == null)
					    isBad = true;
				    else
				    {
					    if (subSnap.HingeDirection == SnapHinge.EType.Left || subSnap.HingeDirection == SnapHinge.EType.Right)
					    {
						    if (mainSnap.HingeDirection != SnapHinge.EType.Left && mainSnap.HingeDirection != SnapHinge.EType.Right)
							    isBad = true;
						    else if (mainSnap.HingeCategory != subSnap.HingeCategory)
							    isBad = true;
					    }
				    }
			    }
		    }
		    if (isBad) badConnectionBm |= 1UL << i;
	    }
	    return badConnectionBm;
    }    

//	private static Dictionary<string, float> s_designUniquenessCache = new Dictionary<string, float>();

/*	public static void ClearDesignUniquenessCache()
	{
		s_designUniquenessCache.Clear();
	}
    static float GetCachedDesignUniquenessScore(string _design) {
		if (s_designUniquenessCache.TryGetValue(_design, out var _score)) return _score;
		return 0;
	}
*/
/*    public static bool CheckCachedDesignUniquenessScore(string _design, string _productLine, Action<string, float> _cb) {
		if (_design == null) return true;
		if (s_designUniquenessCache.ContainsKey(_design)) return true;
		s_designUniquenessCache[_design] = 0; // stop re-entrance during server query 
	    GameManager.Me.GetDesignUniquenessScore(_design, _productLine, _score => {
			AddUniquenessScoreToCache(_design, _score);
			_cb(_design, _score);
		});
		return false;
	}

    public static void AddUniquenessScoreToCache(string _design, float _score) {
		s_designUniquenessCache[_design] = _score;
	}
*/
 /*   public static float GetRankingScore(float _baseScore, string _productLine) {
	    if (GameManager.Me.IsDesignTable) {
			var allDesigns = DesignTableManager.Me.LastAllDesignsData;
		    if (allDesigns != null) {
			    for (int i = 0; i < allDesigns.Count; ++i) {
				    if (allDesigns[i].Design.Score < _baseScore) {
						return 1 - (float)i / (float)allDesigns.Count;
					}
				}
			}
		}

		return 0;
	}*/

/*	public static float GetPriceModifier(int _partCount, int _idealParts, int _maxScoredParts)
	{
		if (NGManager.Me.m_applyPartCountModifierToPrice)
		{
			const float c_priceModifierForIdealParts = .9f;
			if (_partCount <= _idealParts)
			{
				return c_priceModifierForIdealParts * (float)_partCount / (float)_idealParts;
			}
			else
			{
				int excessParts = _partCount - _idealParts;
				int excessAllowance = _maxScoredParts - _idealParts;
				float excessPartFraction = Mathf.Min(1, excessParts / excessAllowance);
				return c_priceModifierForIdealParts + (1 - c_priceModifierForIdealParts) * excessPartFraction;
			}
		}

		return 1;
	}*/

/*    private static float GetCostMultiplier(int _rarity, float[] _rarityMultipliers) {
		if (_rarityMultipliers == null) return 1;
		return _rarityMultipliers[_rarity];
	}
	*/
/*	public static float  GetDesignScoreOLD(GameState_Design _design, string _productLine,
		float[] _competitionMultipliers, int _idealParts, int _maxScoredParts, float _playerLevelScoreCap,
		float[] _individualScores, Dictionary<NGCarriableResource, float> _materials, bool _isBuilding, out float _taps,
		out float _timeToBuild)
	{
		Debug.Assert(NGManager.Me.m_useOldScoreSystem == false, "DesignUtilities - GetDesignScore called with NGManager.Me.m_useOldScoreSystem flag set to true");
		
		var partInfo = GetDesignData(_design);
		var blockUsages = new Dictionary<string, int>();
		float partScore = 0;
		if (_materials != null)
			_materials.Clear();
		_taps = 0;
		_timeToBuild = 0;
		float totalScore = 0, paintScore = 0, stickerScore = 0, patternScore = 0, playerLevelScore = 0, boredomScore = 0;
		float rankingScore = 0, qualityScore = 0, serverScore = 0, compatibilityScore = 0, demandScore = 0, costModifier = 1;
		List<Tuple<NGBlockInfo, float>> partScores = new List<Tuple<NGBlockInfo, float>>();
		List<Tuple<NGBlockInfo, float>> debugPartBoredomScores = new List<Tuple<NGBlockInfo, float>>();

		float duplicateLoss = 0;
		foreach (var part in partInfo)
		{
			if (NGBlockInfo.s_allBlocks.TryGetValue(part.m_blockID, out var details))
			{
				var baseScore = details.m_partScore;
				var cap = details.m_usageCap;
				var dupMod = NGManager.Me.m_designUsesModifier;
				var rarityMultiplier = RarityInfo.GetPartScoreMultiplier(details.m_rarity);
				var matList = _isBuilding ? details.BuildingMaterials : details.ProductMaterials;
				int usages = 0;
				if (!blockUsages.TryGetValue(part.m_blockID, out usages)) usages = 0;
				++usages;
				blockUsages[part.m_blockID] = usages;

				float thisScore = baseScore * (1 + rarityMultiplier);
				int blockExcess = usages - cap;
				float excessMultiplier = 1;
				if (blockExcess > 0)
					excessMultiplier = Mathf.Pow(1 + dupMod, blockExcess);
				duplicateLoss += thisScore - thisScore / excessMultiplier;
				thisScore /= excessMultiplier;
				if (_materials != null)
					foreach (var mat in matList)
						_materials.AddCount(mat, details.m_materialCost * excessMultiplier * GetCostMultiplier((int)details.Rarity - 1, _competitionMultipliers));
				//Boredom would reduce score
				
				partScore += thisScore;
				_taps += details.m_numTapsToMake;
				_timeToBuild += details.m_workerTimeToMake;
				partScores.Add(new Tuple<NGBlockInfo, float>(details, thisScore));
			}

			int bestPaint = 0;
			foreach (var e in part.m_paint)
			{
				var paintInfo = PaintPotData.s_entries[e];
				int rarity = (int)NGBalance.StringToRarity(paintInfo.m_rarity);
				bestPaint = Mathf.Max(bestPaint, rarity);
				if (_materials != null)
					foreach (var mat in paintInfo.Materials)
						_materials.AddCount(mat, paintInfo.m_materialCost * GetCostMultiplier(rarity, _competitionMultipliers));
			}

			paintScore += (float)bestPaint;
			int bestPattern = 0;
			foreach (var e in part.m_pattern)
			{
				var patternInfo = PatternData.s_entries[e];
				int rarity = (int)NGBalance.StringToRarity(patternInfo.m_rarity);
				bestPattern = Mathf.Max(bestPattern, rarity);
				if (_materials != null)
					foreach (var mat in patternInfo.Materials)
						_materials.AddCount(mat, patternInfo.m_materialCost * GetCostMultiplier(rarity, _competitionMultipliers));
			}

			patternScore += (float)bestPattern;
			int bestSticker = 0;
			foreach (var e in part.m_stickers)
			{
				var stickerInfo = StickerData.s_entries[e];
				int rarity = (int)NGBalance.StringToRarity(stickerInfo.m_rarity);
				bestSticker = Mathf.Max(bestSticker, rarity);
				if (_materials != null)
					foreach (var mat in stickerInfo.Materials)
						_materials.AddCount(mat, stickerInfo.m_materialCost * GetCostMultiplier(rarity, _competitionMultipliers));
			}

			stickerScore += (float)bestSticker;
			boredomScore += 1.0f / (1.0f + GetBlockBoredom(part.m_blockID));
			debugPartBoredomScores.Add(new Tuple<NGBlockInfo, float>(details, boredomScore));
		}

		//what about parts weight? NGManager.Me.m_weightPartsScore;
		int partCount = partInfo.Length == 0 ? 1 : partInfo.Length;
		paintScore = Mathf.Clamp01(paintScore / (float)partCount) * NGManager.Me.m_weightPaintScore;
		stickerScore = Mathf.Clamp01(stickerScore / (float)partCount) * NGManager.Me.m_weightStickerScore;
		patternScore = Mathf.Clamp01(patternScore / (float)partCount) * NGManager.Me.m_weightPatternScore;
		boredomScore = Mathf.Clamp01(boredomScore / (float)partCount) * NGManager.Me.m_weightBoredomScore;
		
		playerLevelScore = ((_playerLevelScoreCap == 0) ? 0 : Mathf.Clamp01(NGBusinessDecisionManager.Me.PlayerLevel / _playerLevelScoreCap)) * NGManager.Me.m_weightPlayerLevelScore;

		totalScore = GetRelevantTotalScoreFromList(partScore, paintScore, stickerScore, patternScore, playerLevelScore, boredomScore);//, rankingScore, qualityScore, serverScore, compatibilityScore, demandScore, costModifier);
	
		if (_individualScores != null)
		{
			DesignTableManager.Me.CurrentDesignIndividualPartScores = new List<Tuple<NGBlockInfo, float>>(partScores);
			
			_individualScores[(int)DesignGaugeScore.Rows.Part] = partScore;
			_individualScores[(int)DesignGaugeScore.Rows.Paint] = paintScore;
			_individualScores[(int)DesignGaugeScore.Rows.Sticker] = stickerScore;
			_individualScores[(int)DesignGaugeScore.Rows.Pattern] = patternScore;
			_individualScores[(int)DesignGaugeScore.Rows.BusinessLevel] = playerLevelScore;
			_individualScores[(int)DesignGaugeScore.Rows.Boredom] = boredomScore;
			
			Debug.Log($"DesignUtilities - GetDesignScore - partsTotal {partScore}\n parts {string.Join(',', partScores.ConvertToStringList(x => $"{x.Item1.m_displayName} {x.Item2}"))} \npaintScore {paintScore} \nstickerScore {stickerScore} \npatternScore {patternScore} \nplayerLevelScore {playerLevelScore}\nboredomScoreTotal {boredomScore} \nboredomScores {string.Join(',', debugPartBoredomScores.ConvertToStringList(x => $"{x.Item1.m_displayName} {x.Item2 * NGManager.Me.m_weightBoredomScore}"))}");
			// _individualScores[(int)DesignGaugeScore.Rows.Ranking] = rankingScore;
			// _individualScores[(int)DesignGaugeScore.Rows.Quality] = qualityScore;
			// _individualScores[(int)DesignGaugeScore.Rows.Uniqueness] = serverScore;
			// _individualScores[(int)DesignGaugeScore.Rows.Compatibility] = compatibilityScore;
			// _individualScores[(int)DesignGaugeScore.Rows.Demand] = demandScore;
			// _individualScores[(int)DesignGaugeScore.Rows.PriceModifier] = costModifier;
		}

		//Previous factors not included in new scheme, always confirm in version control if intending to re-add these.
		//int partCount = partInfo.Length == 0 ? 1 : partInfo.Length;
		//costModifier = GetPriceModifier(partCount, _idealParts, _maxScoredParts);
		//rankingScore = GetRankingScore(totalScore, _productLine) * NGManager.Me.m_weightRankingScore;//OLD
		//totalScore += rankingScore;
		//serverScore = GetCachedDesignUniquenessScore(_design.m_design) * NGManager.Me.m_weightServerScore; //OLD uniqueness - live score, probably means this whole system needs to go async
		//demandScore *= demandScore * NGManager.Me.m_weightDemandScore;///OLD
		//compatibilityScore = Mathf.Clamp01(compatibilityScore / (float)partCount) * NGManager.Me.m_weightCompatibilityScore;///OLD & no longer summed up
		//qualityScore = Mathf.Clamp01(qualityScore / (float)partCount) * NGManager.Me.m_weightQualityScore;//old & no longer summed with 'rarityscore'

		return totalScore;
	}
	*/
	/*public static float GetRelevantTotalScoreFromList(params float[] _scoreParts)
	{
		float totalScore = 0;
		totalScore += _scoreParts[(int)DesignGaugeScore.Rows.Part];
		totalScore += _scoreParts[(int)DesignGaugeScore.Rows.Paint];
		totalScore += _scoreParts[(int)DesignGaugeScore.Rows.Pattern];
		totalScore += _scoreParts[(int)DesignGaugeScore.Rows.Sticker];
		totalScore += _scoreParts[(int)DesignGaugeScore.Rows.Boredom];
		totalScore += _scoreParts[(int)DesignGaugeScore.Rows.BusinessLevel];
		return totalScore;
	}*/

//	public static float GetProductDesignInfo(GameState_Product _product, float[] _competitionMultipliers, out float _price, out float _market, float[] _individualScores, Dictionary<NGCarriableResource, float> _materials, out float _taps, out float _timeToBuild) {
//		return NGProductInfo.GetProductInfo(_product.m_productLine).GetDesignInfo(_product.Design, _product.m_productLine, _competitionMultipliers, out _price, out _market, _individualScores, _materials, out _taps, out _timeToBuild);
//	}

/*	public static float GetBuildingDesignScore(NGCommanderBase _commander) {
		float[] currentDesignIndividualScores = new float[16];
		Dictionary<NGCarriableResource, float> currentDesignMaterials = new Dictionary<NGCarriableResource, float>();
		return NGBuildingInfoManager.NGBuildingInfo.GetBuildingInfo(_commander.Name).GetDesignInfo(GameManager.Me.m_state.m_designs[_commander.m_stateData.m_design-1], null, out _, out _, out _, currentDesignIndividualScores, currentDesignMaterials, out _, out _);
	}*/
}
