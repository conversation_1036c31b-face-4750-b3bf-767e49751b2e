using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class TintWindowAPI {
	public enum DecorationType {
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		Pattern,
		
		None
	}

	public struct SubmeshWindow {
		public int MeshId;
		public int SubMesh;
		public int Window;
		public SubmeshWindow(int _meshId, int _submesh, int _window) {
			MeshId = _meshId;
			SubMesh = _submesh;
			Window = _window;
		}
		public override bool Equals (object obj) {
			return obj is SubmeshWindow window &&
				   MeshId == window.MeshId &&
				   SubMesh == window.SubMesh &&
				   Window == window.Window;
		}
		public override string ToString () {
			return $"[MeshId={MeshId} SubMesh={SubMesh} Window={Window}]";
		}
	}
	//===
	public static SubmeshWindow GetTintWindowFromCollider(Transform _root, Collider _obj, Ray _ray, DecorationType _type, ref Vector3 _hitPoint) {
		if (_type != DecorationType.Sticker)
		{
			var dh = _obj.GetComponentInParent<DecorationHolder>();
			if (dh != null)
				return new SubmeshWindow(dh.transform.GetTopologicalIDFromTransform(_root), 0, 1);
		}
		if (TintWindowAPI.GetMeshAndRenderer(_obj, out Mesh[] mesh, out MeshRenderer[] rend)) {
			int meshId = _obj.transform.GetTopologicalIDFromTransform(_root);
			for (int i = 0; i < mesh.Length; ++i) {
				var smw = GetTintWindowFromObject(meshId, _obj.transform, _ray, mesh[i], rend[i], _type, out _hitPoint);
				if (smw.MeshId != -1) return smw;
			}
		}
		_hitPoint = Vector3.zero;
		return new SubmeshWindow(-1, -1, -1);
	}
	public static bool GetMeshAndRenderer(Collider _collider, out Mesh[] _mesh, out MeshRenderer[] _renderer) {
		// Get initial components
		MeshFilter[] mf = null;
		_renderer = null;
		var lod = _collider.GetComponent<LODGroup>();

		if(lod != null) {
			mf = _collider.GetComponentsInChildren<MeshFilter>();
			_renderer = _collider.GetComponentsInChildren<MeshRenderer>();
		} else {
			var meshFilter = _collider.GetComponent<MeshFilter>();
			if(meshFilter != null) {
				mf = new MeshFilter[1]{ meshFilter };
			}
			_renderer = new MeshRenderer[1];
			_renderer[0] = _collider.GetComponent<MeshRenderer>();
		}

		_mesh = null;
		if(mf != null) {
			_mesh = new Mesh[mf.Length];
			for(int i = 0; i < mf.Length; i++)
				_mesh[i] = mf[i].sharedMesh;
		}

		if(_renderer == null || _mesh == null)
			return false;

		return true;
	}
#if UNITY_EDITOR
	private static HashSet<string> s_logShown = new HashSet<string>();
	public static void LogError(string _s)
	{
		if (s_logShown.Contains(_s)) return;
		s_logShown.Add(_s);
		Debug.LogError(_s);
	}
#endif
	public static SubmeshWindow GetTintWindowFromObject(int _meshId, Transform _objectTransform, Ray _ray, Mesh _mesh, MeshRenderer _renderer, DecorationType _type, out Vector3 _hitPoint) {
		_hitPoint = Vector3.zero;
		if (_mesh == null || _renderer == null)
			return new SubmeshWindow(-1, -1, -1);
		// Error if there are more submeshes than materials
		if (_renderer.materials.Length < _mesh.subMeshCount) {
			Debug.LogError("Less materials than submeshes detected on highlight!");
			return new SubmeshWindow(-1, -1, -1);
		}

		_ray.origin = _objectTransform.InverseTransformPoint(_ray.origin);
		_ray.direction = _objectTransform.InverseTransformDirection(_ray.direction);

		Vector3[] vertices = _mesh.vertices;
			
		// Iterate through all submeshes
		for (int sm = 0; sm < _mesh.subMeshCount; sm++) {
			Material tintMaterial = _renderer.materials[sm];
			switch (_type) {
				case DecorationType.Sticker:
					if (tintMaterial.name.Contains("_NoSticker")) continue;
					break;
				case DecorationType.Pattern:
					if (tintMaterial.name.Contains("_NoPattern")) continue;
					break;
				case DecorationType.Paint:
					if (tintMaterial.name.Contains("_NoPaint")) continue;
					break;
			}

			// Get mesh params
			int[] triangles = _mesh.GetTriangles(sm);

			Vector2[] uvs = _mesh.uv;

			// Closest triangle params
			int[] closestTriangle = new int[3];
			float closestIntersectionSqrDistance = float.MaxValue;
			Vector3 closestHitPoint = Vector3.zero;
			bool foundTriangle = false;

			// Find closest triangle to ray origin
			for (int i = 0; i < triangles.Length; i+=3) { 
				Vector3 p1 = vertices[triangles[i]];
				Vector3 p2 = vertices[triangles[i+1]];
				Vector3 p3 = vertices[triangles[i+2]];

				Vector3 intersectionPoint;
				if (GeometryUtilities.TriangleRayIntersect(p1, p2, p3, _ray, out intersectionPoint)) {
					float dist = (intersectionPoint - _ray.origin).sqrMagnitude;

					if(dist < closestIntersectionSqrDistance)
					{
						closestTriangle[0] = triangles[i];
						closestTriangle[1] = triangles[i+1];
						closestTriangle[2] = triangles[i+2];

						closestHitPoint = intersectionPoint;
						closestIntersectionSqrDistance = dist;

						foundTriangle = true;
					}
				}
			}

			if (foundTriangle)
				return new SubmeshWindow(_meshId, sm, 1);
		}
		return new SubmeshWindow(-1, -1, -1);
	}
}
