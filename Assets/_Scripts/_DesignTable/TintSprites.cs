using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TintSprites : MonoBehaviour {
	public UnityEngine.UI.Image[] m_toTint;
	public void Tint(Color _c) {
		foreach (var t in m_toTint) t.color = _c;
	}
	public void Replace(Texture2D _t) {
		var sprite = (_t == null) ? null : Sprite.Create(_t, new Rect(0,0,_t.width,_t.height), new Vector2(.5f,.5f), 100f);
		foreach (var t in m_toTint) {
			t.type = UnityEngine.UI.Image.Type.Simple;
			t.color = Color.white;
			t.sprite = sprite;
		}
	}
}
