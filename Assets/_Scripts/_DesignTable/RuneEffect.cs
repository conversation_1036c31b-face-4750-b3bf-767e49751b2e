using UnityEngine;

public class RuneEffect : MonoBehaviour
{
    public ParticleSystem[] m_systems;
    void Start()
    {
        for (int i = 0; i < m_systems.Length; i++)
        {
            var system = m_systems[i];
            if (system != null && system.shape.shapeType == ParticleSystemShapeType.Mesh)
            {
                var shape = system.shape;
                shape.mesh = GetComponentInParent<MeshFilter>().sharedMesh;
                system.transform.localScale = transform.parent.lossyScale;
            }
        }
    }
}
