#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using UnityEditor;

public class MAFunctionWindow: EditorWindow
{
    private Vector2 _scrollPosition;
    private int m_selectedFlowIndex = -1;
    int m_selectPopup = 0;

    class TempMethodInfo
    {
        public MethodInfo m_methodInfo;
        public bool m_selected = false;
        public string m_special = "";
        public string m_testValue = "";
    }

    class TempCatagoryInfo
    {
        public List<TempMethodInfo> m_infos = new ();
        public bool m_selected = false;
    }
    List<TempMethodInfo> m_tempMethodInfos = new ();
    Dictionary<string, TempCatagoryInfo> m_catagorizedMethods = new ();
    string m_testString = "";

    [MenuItem("22Cans/Debug Windows/Function Window")]
    public static void ShowWindow()
    {
        var window = GetWindow<MAFunctionWindow>("Function Window") as MAFunctionWindow;
        MAFunctionWindow ddd = window;
        ddd.minSize = new Vector2(400, 300);
        window.titleContent = new GUIContent("Function Window");
        Setup();
        window.Show();
    }

    public static void Setup()
    {
        var window = GetWindow<MAFunctionWindow>("Function Window") as MAFunctionWindow;
        window.m_catagorizedMethods.Clear();
        window.m_catagorizedMethods.Add("General", new TempCatagoryInfo());
        window.m_catagorizedMethods.Add("Commands", new TempCatagoryInfo());
        window.m_catagorizedMethods.Add("Triggers", new TempCatagoryInfo());
        window.m_catagorizedMethods.Add("Decisions", new TempCatagoryInfo());
        window.m_catagorizedMethods.Add("DebugCommands", new TempCatagoryInfo());
        window.m_catagorizedMethods.Add("ParserCommands", new TempCatagoryInfo());
        var methords = typeof(MAParser).GetMethods();
        foreach(var m in methords)
        {
            if (m.IsStatic == false  || m.IsPublic == false) continue;
            string catagory = "General";
            var attributes = m.GetCustomAttributes();
            foreach(var a in attributes)
            {
                if (a is MAParser.TutorialCommands)
                    catagory="Commands";
                else if (a is MAParser.TutorialTrigger)
                    catagory="Triggers";
                else if (a is MAParser.ParserCommands)
                    catagory="ParserCommands";
            }
            window.m_catagorizedMethods[catagory].m_infos.Add(new TempMethodInfo(){m_methodInfo = m});
        } 
        methords = typeof(NGBusinessDecision).GetMethods();
        foreach(var m in methords)
        {
            if (m.IsStatic || m.IsPublic == false) continue;
            bool isDecision = false;
            foreach (var a in m.GetCustomAttributes())
            {
                var aa = a as NGBusinessDecision.MADecision;
                if (aa != null)
                {
                    isDecision = true;
                    break;
                }
            }
            if(isDecision == false) continue;
            string catagory = "Decisions";
            window.m_catagorizedMethods[catagory].m_infos.Add(new TempMethodInfo(){m_methodInfo = m});
        }
        window.m_catagorizedMethods["Decisions"].m_infos.Sort((a,b) => a.m_methodInfo.Name.CompareTo(b.m_methodInfo.Name));
        window.m_catagorizedMethods["Decisions"].m_infos.Add(new TempMethodInfo(){m_special = "NOT IMPLEMENTED"});
        window.m_catagorizedMethods["Commands"].m_infos.Sort((a,b) => a.m_methodInfo.Name.CompareTo(b.m_methodInfo.Name));

        foreach (var dc in DebugConsole.s_consoleActions)
        {
            window.m_catagorizedMethods["DebugCommands"].m_infos.Add(new TempMethodInfo(){m_special = dc.Key});
        } 
        window.m_catagorizedMethods["DebugCommands"].m_infos.Sort((a,b) => a.m_special.CompareTo(b.m_special));
    }
    bool m_availableSelected = false;
    private void OnGUI()
    {
        if (m_catagorizedMethods.Count == 0) Setup();
        GUIStyle richFoldoutStyle = new GUIStyle(EditorStyles.foldout) {richText = true};

        EditorGUILayout.LabelField("Function Window", EditorStyles.boldLabel);
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Test Function:"))
        {
            MAParserSupport.TryParse(m_testString, out var result,"Test");

        }

        m_testString = EditorGUILayout.TextField("", m_testString);
        EditorGUILayout.EndHorizontal();

        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
        var methords = typeof(MAParser).GetType().GetMethods();
        foreach (var cat in m_catagorizedMethods)
        {
            var vv = cat.Value.m_infos;
            var foldoutText = $"<b>{cat.Key} [{cat.Value.m_infos.Count}]</b>";
            cat.Value.m_selected = EditorGUILayout.Foldout(cat.Value.m_selected, foldoutText, cat.Value.m_selected, richFoldoutStyle);
            if (cat.Value.m_selected)
            {
                EditorGUI.indentLevel++; 

                foreach (var m in cat.Value.m_infos)
                {
                    if (cat.Key == "DebugCommands")
                    {
                        m.m_selected = EditorGUILayout.Foldout(m.m_selected, m.m_special, true, richFoldoutStyle);
                        if (m.m_selected)
                        {
                            m.m_testValue =  EditorGUILayout.TextField("Pass String: ", m.m_testValue);
                            if (GUILayout.Button("Call"))
                            {
                                DebugConsole.s_consoleActions[m.m_special].Item1(m.m_testValue);
                            }
                        }
                        continue;

                    }
                    else if (cat.Key == "Decisions")
                    {
                        if (m.m_special.IsNullOrWhiteSpace() == false)
                        {
                            foldoutText = $"<b>{m.m_special}:</b>(";
                            if (NGBusinessDecision.s_decisions != null && NGBusinessDecision.s_decisions.Count > 0)
                            {
                                foreach (var d in NGBusinessDecision.s_decisions)
                                {
                                    bool found = false;
                                    foreach (var i in cat.Value.m_infos)
                                    {
                                        if(i.m_methodInfo == null) continue;
                                        var decisionName = i.m_methodInfo.Name.Replace("Value", "");
                                        if (d.m_type == decisionName)
                                        {
                                            found = true;
                                            break;
                                        }
                                        
                                    }
                                    if (found == false)
                                    {
                                        foldoutText += $"{d.m_name},";
                                    }                                    
                                }
                                foldoutText = foldoutText.TrimEnd(',')+")";
                            }
                        }
                        else
                        {
                            var decisionName = m.m_methodInfo.Name.Replace("Value", "");
                            foldoutText = $"<b>{decisionName}</b>(";
                            if (NGBusinessDecision.s_decisions != null && NGBusinessDecision.s_decisions.Count > 0)
                            {
                                var usedDecision = NGBusinessDecision.s_decisions.FindAll(x => x.m_type == decisionName);
                                if (usedDecision.Count > 0)
                                {
                                    foldoutText += $"Used [{usedDecision.Count}] ";
                                    foreach (var decision in usedDecision)
                                        foldoutText += $"{decision.m_name},";
                                    foldoutText = foldoutText.TrimEnd(',')+")";
                                }
                            }
                        }
                    }
                    else
                    {
                        foldoutText = $"<b>{m.m_methodInfo.Name}</b>(";
                        foreach (var p in m.m_methodInfo.GetParameters())
                        {
                            foldoutText += $"{p.ParameterType.Name} {p.Name} , ";
                        }

                        foldoutText = foldoutText.TrimEnd(',', ' ') + ")";
                    }
                    m.m_selected = EditorGUILayout.Foldout(m.m_selected, foldoutText, m.m_selected, richFoldoutStyle);
                    if(m.m_selected)
                    {
                        EditorGUI.indentLevel++;
                        foreach (var a in m.m_methodInfo.GetCustomAttributes())
                        {
                            var aa = a as MAParser.FunctionDescriptionAttribute;
                            if (aa != null)
                            {
                                EditorGUILayout.LabelField(aa.Description);
                            }
                        }
                        var labelText = $"<b>{m.m_methodInfo.Name}</b>(";
                        foreach (var p in m.m_methodInfo.GetParameters())
                        {
                            if (MAParserSupport.m_specialConverters.TryGetValue(p.ParameterType, out var converter))
                            {
                                labelText += $"{converter.m_example} , ";
                            }
                            else
                                labelText += $"{p.ParameterType.Name} {p.Name} , ";
                        }
                        labelText = labelText.TrimEnd(',', ' ') + ")";
                        GUIStyle richLabelStyle = new GUIStyle(EditorStyles.label) {richText = true};

                        EditorGUILayout.LabelField(labelText, richLabelStyle);
                        m.m_testValue = EditorGUILayout.TextField("Test", m.m_testValue);
                        if (GUILayout.Button("Test"))
                        {
                            var pass = m.m_testValue;
                            if(pass.Contains(m.m_methodInfo.Name) == false)
                            {
                                if(pass.Contains('(') == false)
                                    pass = $"({pass})";
                                pass = $"{m.m_methodInfo.Name}{pass}";
                            }
                            MAParserSupport.TryParse(pass, out var result,"Test");
                        }       
                        EditorGUI.indentLevel--;
                    }
                }
                EditorGUI.indentLevel--;
            }
        }
  
        EditorGUILayout.EndScrollView(); // End scroll view
    }

    void ShowHelp()
    {
        GUIStyle richFoldoutStyle = new GUIStyle(EditorStyles.foldout) {richText = true};

        m_availableSelected = EditorGUILayout.Foldout(m_availableSelected, "Converter Help", m_availableSelected, richFoldoutStyle);
        if (m_availableSelected)
        {
            EditorGUI.indentLevel++;
            foreach (var d in MAParserSupport.m_specialConverters)
            {
                var ss = d.Key.ToString();
                EditorGUILayout.LabelField($"{d.Value.m_name} is {d.Key.ToString()}", EditorStyles.boldLabel);
            }

            EditorGUI.indentLevel--;
        }
    }
    void OnInspectorUpdate()
    {
        Repaint();
    }
}
#endif
