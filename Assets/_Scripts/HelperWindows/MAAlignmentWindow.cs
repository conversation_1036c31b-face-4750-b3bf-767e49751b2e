#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class MAAlignmentWindow : EditorWindow
{
    private Vector2 m_scrollPosition;
    
    [MenuItem("22Cans/Debug Windows/Alignment")] 
    public static void ShowWindow()
    {
        var window = GetWindow<MAAlignmentWindow>("Alignment");
        
        window.titleContent = new GUIContent("Alignment");
        window.Show();
    }

    private void OnInspectorUpdate()
    {
        Repaint();
    }

    private void DestroyUIObject(Graphic _toDestroy)
    {
        if (_toDestroy != null)
        {
            Destroy(_toDestroy.gameObject);
        }
    }
    
    void OnGUI()
    {
        if (GlobalData.Me == null || NGManager.Me == null || GameManager.Me == null || GameManager.Me.LoadComplete == false)
        {
            EditorGUILayout.LabelField("Game Not Loaded", EditorStyles.boldLabel);
            return;
        }
        
        GUIStyle style = new GUIStyle ();
        
        style.normal.textColor = Color.white;
        style.richText = true;
        
        int col1Width = 50;
        int col2Width = 70;
        
        EditorGUILayout.LabelField("<b>Alignment</b>", style);
        EditorGUILayout.LabelField($"{AlignmentManager.Me.GetAlignmentType()} | {AlignmentManager.Me.Alignment:F5}", style);
        EditorGUILayout.Space();
        EditorGUILayout.BeginHorizontal();
        GUILayout.Button("<b>Count</b>", style, GUILayout.Width(col1Width));
        GUILayout.Button("<b>Value</b>", style, GUILayout.Width(col2Width));
        EditorGUILayout.LabelField("<b>Action</b>", style);
        EditorGUILayout.EndHorizontal();
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition, true, true);
        
        foreach (var action in GameManager.Me.m_state.m_alignment.m_actionHistory.m_keys)
        {
            var data = GameManager.Me.m_state.m_alignment.m_actionHistory[action];
            EditorGUILayout.BeginHorizontal();
            GUILayout.Button($"{data.m_count}", style, GUILayout.Width(col1Width));
            GUILayout.Button($"{data.m_value:F5}", style, GUILayout.Width(col2Width));
            EditorGUILayout.LabelField($"{action}", style);
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();
    }
}
#endif
