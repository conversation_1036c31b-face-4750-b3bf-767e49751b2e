#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class MACharacterWindow: EditorWindow
{
    private Vector2 m_scrollPosition;
    int m_selectPopup = 0;
    int m_statePopup = 0;
    public GameObject m_cameraFollowing = null;
    Dictionary<Type, CharacterDict> characterDict = new Dictionary<Type, CharacterDict>();

    [MenuItem("22Cans/Debug Windows/Characters")]
    public static void ShowWindow()
    {
        var window = GetWindow<MACharacterWindow>("Show Characters");
        
        window.titleContent = new GUIContent("Characters");
        window.Show();
    }

    public class CharacterDict
    {
        public List<NGMovingObject> m_movingObjects;
        public bool m_foldOutFlag;
    }

    void RefreshCharacterDict()
    {
        var characters = GlobalData.Me.m_characterHolder.GetComponentsInChildren<NGMovingObject>(true);
        bool isValid = true;
        foreach (var c in characters)
        {
            var t = c.GetType();
            if(characterDict.TryGetValue(t, out var value))
            {
                var f = value.m_movingObjects.Find(o=> o.m_ID == c.m_ID);
                if(f == null)
                {
                    isValid = false;
                    break;
                }
            }
            else
            {
                isValid = false;
                break;
            }
        }

        if (isValid)
        {
            int count = 0;
            foreach(var d in characterDict)
            {
                count+= d.Value.m_movingObjects.Count;
            }
            if(count == characters.Length)
                return;
        }
        //characterDict.Clear();
        foreach (var c in characters)
        {
            var t = c.GetType();
            if (characterDict.ContainsKey(t))
            {
                if (characterDict[t].m_movingObjects.Contains(c) == false)
                    characterDict[t].m_movingObjects.Add(c);
            }
            else
                characterDict.Add(t, new CharacterDict {m_movingObjects = new List<NGMovingObject>(){c}});
        }
        foreach(var d in characterDict)
        {
            for (var i = d.Value.m_movingObjects.Count - 1; i >= 0; i--)
            {
                var c = d.Value.m_movingObjects[i];
                if (characters.Find(o => o.m_ID == c.m_ID) == null)
                {
                    d.Value.m_movingObjects.Remove(c);
                }
            }
        }
    }
    private void OnGUI()
    {
        if (GlobalData.Me == null)
        {
            EditorGUILayout.LabelField("No Characters:", EditorStyles.boldLabel);
            return;
        }
        RefreshCharacterDict();
        if(m_cameraFollowing)
        {
            if (GUILayout.Button("Stop Camera Following"))
            {
                GameManager.Me.StopCameraTrackingObject();
                m_cameraFollowing = null;
            }
        }
        EditorGUILayout.LabelField($"Characters List[{characterDict.Count}]:", EditorStyles.boldLabel);
        
        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout){fontStyle = FontStyle.Bold};
        GUIStyle normalFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        var labelStyle = new GUIStyle(EditorStyles.textArea) {richText = true};
        
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition);
        foreach (var d in characterDict)
        {
            d.Value.m_foldOutFlag = EditorGUILayout.Foldout(d.Value.m_foldOutFlag, $"{d.Key.ToString()}[{d.Value.m_movingObjects.Count}]", true, boldFoldoutStyle);
            if (d.Value.m_foldOutFlag)
            {
                EditorGUI.indentLevel++;
                foreach (var c in d.Value.m_movingObjects)
                {
                    c.m_debugSelectedForWindow = EditorGUILayout.Foldout(c.m_debugSelectedForWindow, c.name, c.m_debugSelectedForWindow, normalFoldoutStyle);
                    if (c.m_debugSelectedForWindow)
                    {
                        EditorGUI.indentLevel++;
                        c.DebugShowGUIDetails(this, labelStyle);
                        EditorGUI.indentLevel--;
                    }
                }
                EditorGUI.indentLevel--;
            }
        }
        EditorGUILayout.EndScrollView(); 
    }
    void OnInspectorUpdate()
    {
        Repaint();
    }
}
#endif
