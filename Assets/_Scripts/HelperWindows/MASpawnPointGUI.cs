using UnityEngine;
#if UNITY_EDITOR
using UnityE<PERSON>or;
#endif

public partial class MASpawnPoint
{
	private Transform m_editorIndicatorDummy;
	private Transform m_editorRootSpawnPointPositionIndicator = null;
	
	public void ShowDebug(bool _flag)
	{
		#if UNITY_EDITOR
		if (m_triggerRadius != null)
		{
			Transform sphere = m_triggerRadius.transform.Find("Sphere");
			if (sphere)
			{
				sphere.gameObject.SetActive(_flag);
			}
		}

		UpdateEditorDummies(_flag);
		#endif
	}
	
#if UNITY_EDITOR
	private void UpdateEditorDummies(bool _flag)
	{
		if (m_editorIndicatorDummy == null)
		{
			m_editorIndicatorDummy = Resources.Load<Transform>("_Prefabs/Spawns/Editor/EditorIndicatorSphere");
		}
		
		if (m_editorIndicatorDummy != null)
		{
			if (m_editorRootSpawnPointPositionIndicator == null)
				m_editorRootSpawnPointPositionIndicator = Instantiate(m_editorIndicatorDummy, transform);
			m_editorRootSpawnPointPositionIndicator.gameObject.name = $"SpawnPoint Indicator - {name}";
			m_editorRootSpawnPointPositionIndicator.localPosition = Vector3.zero;
			m_editorRootSpawnPointPositionIndicator.position = m_editorRootSpawnPointPositionIndicator.position.GroundPosition();
			m_editorRootSpawnPointPositionIndicator.localScale = Vector3.one * 2f;
			m_editorRootSpawnPointPositionIndicator.gameObject.SetActive(_flag);
			for(int i = 0; i < m_spawnPositions.Count; i++)
			{
				Transform yes = null;
				foreach(var child in m_spawnPositions[i])
				{
					Transform childTr = child as Transform;
					if (childTr.name.Contains("Indicator"))
					{
						yes = childTr;
						break;
					}
				}
				
				if (yes == null) yes = Instantiate(m_editorIndicatorDummy, m_spawnPositions[i]);
				
				yes.gameObject.name = $"SpawnPosition Indicator [{i}] - {name}";
				yes.localPosition = Vector3.zero;
				yes.position = yes.position.GroundPosition();
				yes.gameObject.SetActive(_flag);
			}
		}
		
		if (m_debugCreatureHolder)
		{
			m_debugCreatureHolder.gameObject.SetActive(_flag);	
			if (_flag)
			{
			}
			else
			{
				m_debugCreatureHolder.DestroyChildren();
			}
		}
	}
	
	private void OnDrawGizmos()
	{
		if (Application.isPlaying == false) return;
		
		Vector3 origin = transform.position;
		origin = origin.GroundPosition();
		Handles.color = new Color(Color.yellow.r, Color.yellow.g, Color.yellow.b, 0.35f);
		if (m_triggerRadius == null)
		{
			m_triggerRadius = gameObject.GetComponentInChildren<SphereCollider>(true);
		}
	
		if (m_triggerRadius != null && /*m_triggerRadius.gameObject.activeSelf &&*/ PhaseCreatureSpawnInfo != null && PhaseCreatureSpawnInfo.SpawnPointType == MACreatureSpawnInfo.CreatureSpawnPointType.Proximity)
		{
			Handles.DrawSolidDisc(origin, new Vector3(0, 1, 0), m_triggerRadius.radius);
		}
	}
	
	private float m_editorSliderRadius = 0;
	private float m_editorSliderSpawnDelay = 0;
	private SerializedObject selfSerializedObject = null;
	
	public void ShowSpawnPointGUI(string[] _spawnPointInfoNames, int[] _spawnPointInfoInts, string[] _linkedBuildingsNames, int[] _linkedBuildingsInts, bool _showDebug)
	{
		if (selfSerializedObject == null)
			selfSerializedObject = new SerializedObject(this);
		EditorGUI.indentLevel++;
		var name = EditorGUILayout.TextField($"Name: ", m_name);
		if (name != m_name)
			m_name = name;
		
		m_editorSliderSpawnDelay = m_spawnDelay;
		
		EditorGUI.BeginDisabledGroup(true);

		var Id = EditorGUILayout.TextField($"Id: {m_ID}");
		EditorGUI.EndDisabledGroup();
		
		ShowDebug(_showDebug);
		
		var vector2Field = new Vector2(transform.position.x, transform.position.z);
		var pos = EditorGUILayout.Vector2Field("Position:",vector2Field);
		if (pos != vector2Field)
		{
			var pos3 = new Vector3(pos.x, 0, pos.y);
			transform.position = pos3.GroundPosition();
		}
		
		var selectedPopupBuilding = 0;
		if (m_linkedBuilding != null)
		{
			selectedPopupBuilding = NGManager.Me.m_maBuildings.IndexOf(m_linkedBuilding)+1;
		}
		var newSelectedPopupBuilding = EditorGUILayout.IntPopup($"Linked Building:", selectedPopupBuilding, _linkedBuildingsNames, _linkedBuildingsInts);
		if (newSelectedPopupBuilding != selectedPopupBuilding)
		{
			selectedPopupBuilding = newSelectedPopupBuilding;
			if (selectedPopupBuilding == 0)
				m_linkedBuilding = null;
			else
				m_linkedBuilding = NGManager.Me.m_maBuildings[selectedPopupBuilding-1];
		}

		//if (m_spawnPositionsHolder.childCount != m_spawnPositions.Count)
		{
			m_spawnPositions.Clear();
			for(int i = 0; i < m_spawnPositionsHolder.childCount; i++)
			{
				Transform spawnPos = m_spawnPositionsHolder.GetChild(i);
				//
				// if (m_deSpawnArea != null)
				// {
				// 	GameObject clonedDeSpawnArea = Instantiate(m_deSpawnArea, spawnPos, false);
				// 	clonedDeSpawnArea.transform.localPosition = Vector3.zero;
				// }
				//
				m_spawnPositions.Add(spawnPos);
			}
		}

		EditorGUILayout.Space();
		if (GUILayout.Button("v Add new Spawn Position below v"))
		{
			selfSerializedObject.ApplyModifiedProperties();
			int count = m_spawnPositionsHolder.childCount;
			Transform tr = transform;
			AddSpawnPosition(new SaveLoadSpawnPoint.BasicTransformDetail()
			{
				m_name = $"SpawnPosition {count}",
				m_position = tr.position.GetXZVector2(),
				m_rotation = tr.rotation.eulerAngles,
			});
			selfSerializedObject = new SerializedObject(this);
		}
		
		foreach(var spawnPos in m_spawnPositions)
		{
			if (spawnPos != null)
			{					
				GUILayout.BeginHorizontal();
				GUILayoutOption option = GUILayout.Width(26);
				GUILayoutOption option2 = GUILayout.Width(52);
				Vector2 posXZ = spawnPos.position.GetXZVector2();
				GUILayout.Label("X,Z", option);
				posXZ.x = EditorGUILayout.FloatField($"", posXZ.x, option2);
				posXZ.y = EditorGUILayout.FloatField($"", posXZ.y, option2);
				//spawnPos.position = EditorGUILayout.Vector2Field($"", spawnPos.position.GetXZVector2()).GetVector3XZ().GroundPosition();
				spawnPos.position = posXZ.GetVector3XZ().GroundPosition();
				var objectSpawnPos = EditorGUILayout.ObjectField($"", spawnPos,
					typeof(Transform), true);
				GUILayout.EndHorizontal();
			}
		}
		
		selfSerializedObject.ApplyModifiedProperties();

		m_editorSliderSpawnDelay = EditorGUILayout.Slider("Spawn Delay After Trigger:", m_spawnDelay >= 0f ? m_spawnDelay : 0f, 0f, 320f);
		if (m_spawnDelay != m_editorSliderSpawnDelay)
		{
			m_spawnDelay = m_editorSliderSpawnDelay;
		}

		EditorGUILayout.Space();
		if (GUILayout.Button("v Add New Spawn Phase below v "))
		{
			selfSerializedObject.ApplyModifiedProperties();
			AddSpawnPhase(CreateStandardSpawnPhaseInfo());
			selfSerializedObject = new SerializedObject(this);
		}
		
		if (m_spawnPhases.Count == 0)
		{
			AddSpawnPhase(CreateStandardSpawnPhaseInfo());
		}
		
		foreach(var spawnPhase in m_spawnPhases)
		{
			spawnPhase.GetGUI().Draw(spawnPhase, this, _showDebug, _spawnPointInfoNames, _spawnPointInfoInts);
		}
		
		selfSerializedObject.ApplyModifiedProperties();
			
		EditorGUILayout.Space();
		GUILayout.BeginHorizontal();
		if (enabled && GUILayout.Button($"{(m_paused ? "UnPause" : "Pause")} point '{m_ID}'"))
		{
			MAParser.MAPauseSpawnPoint(m_ID, !m_paused);
		}

		EditorGUILayout.Space();
		if (enabled && GUILayout.Button($"Delete point '{m_ID}'"))
		{
			DestroyMe();
		}
		GUILayout.EndHorizontal();
		
		EditorGUI.indentLevel+=2;
		if (m_currentCharacters.Count > 0 || m_phaseSpawnCountSoFar > 0 || m_timeActive >= 0)
		{
			GUIStyle style = new GUIStyle(EditorStyles.foldout);
			if (m_iCurrentSpawnPhase == m_spawnPhases.Count)
			{
				style.normal.textColor = Color.red;
				m_showCurrentInfo = EditorGUILayout.Foldout(m_showCurrentInfo, "Triggered & Sequence Complete: ",true, style);
				if (GUILayout.Button("Reset This Spawn Point"))
                {
                    ResetSpawnPoint();
                }
			}
			else
			{
				style.normal.textColor = Color.yellow;
				m_showCurrentInfo =
					EditorGUILayout.Foldout(m_showCurrentInfo, "Triggered. Show Live Info: ", true, style);
			}

			if (m_showCurrentInfo)
			{
				EditorGUI.BeginDisabledGroup(true);

				EditorGUILayout.LabelField($"Total Spawn Count: {m_totalSpawnCountSoFar}");
				EditorGUILayout.LabelField($"Current Phase Spawn Count: {m_phaseSpawnCountSoFar}");

				foreach(var currentCharacter in m_currentCharacters)
				{
					MACharacterBase characterBase = m_currentCharacters[currentCharacter.Key];
					if (characterBase != null)
					{
						var activeCharacter = EditorGUILayout.ObjectField($"{characterBase.m_ID}", characterBase,
							typeof(MACharacterBase), true);
					}
				}

				if (m_spawnDelay > 0)
				{
					GUIStyle green = new GUIStyle();
					green.normal.textColor = Color.green;
					if (m_spawnDelay - Mathf.Clamp(m_timeActive, 0, m_timeActive) >= 0)
					{
						var timer = EditorGUILayout.TextField(
							$"Spawn Delay Timer: {m_spawnDelay - Mathf.Clamp(m_timeActive, 0, m_timeActive)}s / {m_spawnDelay}s",
							green);
					}
					else
					{
						var timer = EditorGUILayout.TextField(
							$"Spawn Delay Timer Elapsed! ({m_spawnDelay}s)", green);
					}
				}

				EditorGUI.EndDisabledGroup();
			}
		}
		
		EditorGUI.indentLevel-=3;
	}

	private bool m_showCurrentInfo = false;
#endif
}
