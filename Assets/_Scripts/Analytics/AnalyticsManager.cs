using System.Collections.Generic;
using UnityEngine;
using System;
using UnityEngine.Assertions;

namespace Cans.Analytics
{
	public class AnalyticsManager : MonoSingleton<AnalyticsManager>
	{
		public const string c_GDPRStatusPref = "GDPRStatus"; //TODO: TS Remove/Use/Adjust when gdpr/age gate is available (or once we know there wont be any) //TODO: add callback for when consent is revoked, re-init analytics to apply new consent flag
		public const string c_AgeGateStatusPref = "AgeGateStatus";
		
		private Dictionary<Type, IAnalyticsHandler> m_analyticsHandlers = new Dictionary<Type, IAnalyticsHandler>();
		
		private EventFactory m_PredefinedEvents = null;

		private bool IsAgeGatePassed => PlayerPrefs.GetInt(c_AgeGateStatusPref) > 0;
		private bool IsGDPRConsentGiven => PlayerPrefs.GetInt(c_GDPRStatusPref) > 0;
		public EventFactory Events => m_PredefinedEvents == null ? m_PredefinedEvents = new EventFactory() : m_PredefinedEvents;
		
		public void Start ()
		{
			DontDestroyOnLoad (gameObject);
			InitAnalytics();
		}

		public void LogEvent(AnalyticsEvent analyticsEvent)
		{
			Assert.IsTrue(analyticsEvent != null, $"AnalyticsManager - LogEvent - null analyticsEvent");

			analyticsEvent.AddParamList(EventFactory.SystemParameters);
			
			if (analyticsEvent.UnusedParameterList != null)
			{
				Debug.LogError($"AnalyticsManager - LogEvent - Found {analyticsEvent.UnusedParameterList.Count} Unused/null parameters: {new Func<string>(() => { string outStr = ""; foreach (string str in analyticsEvent.UnusedParameterList) outStr += $"{str}\n"; return outStr; })()}");
				return;
			}
	
#if UNITY_ANALYTICS_EVENT_LOGS
			Debug.Log($"Analytics.EventFactory - LogEvent '{analyticsEvent.EventName}' - Parameter List:\n {new System.Func<string>(() => { string output = ""; foreach (var pair in analyticsEvent.Parameters) { output += $"key: {pair.Key} - {pair.Value}\n"; } return output; })()}");
#endif
			
			foreach (IAnalyticsHandler analyticsHandler in m_analyticsHandlers.Values)
			{
				analyticsHandler.LogEvent(analyticsEvent);
			}
		}

		public void UpdateUserProperty(string name, object value) //TODO: TS THIS CAN BE USED IF OLD SYSTEM CALLS ARE RECYCLED
		{
			foreach (IAnalyticsHandler analyticsHandler in m_analyticsHandlers.Values)
			{
				analyticsHandler.UpdateUserProperty(name, value);
			}
		}

		private void InitAnalytics()
		{
			Type analyticsHandlerType = null;
			IAnalyticsHandler analyticsHandler = null;
			
#if ENABLE_CLOUD_SERVICES_ANALYTICS && USE_UNITY_ANALYTICS
			analyticsHandlerType = typeof(Cans.Analytics.UnityAnalyticsHandler);
			if(m_analyticsHandlers.TryGetValue(analyticsHandlerType, out analyticsHandler) == false)
			{
				analyticsHandler = new Cans.Analytics.UnityAnalyticsHandler();
				analyticsHandler.Init();
				m_analyticsHandlers.Add(analyticsHandlerType, analyticsHandler);
			}
#endif
			
#if ENABLE_CLOUD_SERVICES_ANALYTICS && USE_UNITY_ANALYTICS_LEGACY_VERSION
			analyticsHandlerType = typeof(Cans.Analytics.UnityLegacyVersionAnalyticsHandler);
			if(m_analyticsHandlers.TryGetValue(analyticsHandlerType, out analyticsHandler) == false)
			{
				analyticsHandler = new Cans.Analytics.UnityLegacyVersionAnalyticsHandler();
				analyticsHandler.Init();
				m_analyticsHandlers.Add(analyticsHandlerType, analyticsHandler);
			}
#endif
			
#if USE_FIREBASE_ANALYTICS
			analyticsHandlerType = typeof(Cans.Analytics.FirebaseAnalytics);
			if(m_analyticsHandlers.TryGetValue(analyticsHandlerType, out analyticsHandler) == false)
			{
				analyticsHandler = new Cans.Analytics.FirebaseAnalytics();
				analyticsHandler.Init();
				m_analyticsHandlers.Add(analyticsHandlerType, analyticsHandler);
			}
#endif

			bool giveConsent = IsAgeGatePassed && IsGDPRConsentGiven;
			string userId = GameManager.UserId;
			for (int i = 0; i < m_analyticsHandlers.Count; i++)
			{
				analyticsHandler.SetUserId(userId);//TODO: when do we have valid unique user id? consider putting this in a more general setUserId which can be called when such an id is actually established.
				analyticsHandler.ApplyUserConsent(giveConsent);
			}
		}
	}
}