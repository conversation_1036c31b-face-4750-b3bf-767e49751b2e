using System.Collections.Generic;
using Cans.Analytics;

public partial class DesignTableManager
{
	private DTDragBlock.DragObjectState m_lastGrabbedObjectDataOnDragStart = null;
	private DTDragBlock.DragObjectState m_lastGrabbedObjectDataOnDragEnd = null;
	
	private class DesignTableAnalytics
	{
		public const string kNoAction = "No Action";
		public const string kPartRemoved = "Remove Part";
		public const string kAddPart = "Add Part";
		public const string kManualUndoAll = "Manual Undo All";
		public const string kChangePartPosition = "Change Position";
		public const string kChangePartRotation = "Change Rotation";
		public const string kAddPaint = "Add Paint";
		public const string kChangePaint = "Change Paint";
		public const string kAddPattern = "Add Pattern";
		public const string kChangePattern = "Change Pattern";
		public const string kAddSticker = "Add Sticker";
		public const string kChangeSticker = "Change Sticker";
		public const string kUndo = "Undo";
		public const string kRedo = "Redo";
		public const string kNewDesignConfirmed = "New Design Confirmed";

		public static string m_lastAction = string.Empty;
		public static string m_lastActionPerformedOnObject = string.Empty;

		public static void ClearHistory()
		{
			m_lastAction = string.Empty;
			m_lastActionPerformedOnObject = string.Empty;
		}
		
		public string productLine = string.Empty;
		public int timeSpent = 0;
		public bool isNewDesign = false;
		public int numParts = 0;
		public string lastActionPerformed = string.Empty;
		public string blockTypeLastHandled = "N/A";
		public float currentPrice = 0;
		public float currentScore = 0;
		public int stickerCount = 0;
		public int patternCount = 0;
		public int paintCount = 0;
		public string materialTypeRequired = string.Empty;
		public float materialCountRequired = 0;

		public Dictionary<string, object> Parameters => new()
		{
			{ EventParams.designTableProductLine, productLine },
			{ EventParams.designTableInteractionPartCount, numParts },
			{ EventParams.designTableInteractionPaintCount, paintCount },
			{ EventParams.designTableInteractionPatternCount, patternCount },
			{ EventParams.designTableInteractionStickerCount, stickerCount },
			{ EventParams.designTableInteractionDesignTimeSecs, timeSpent },
			{ EventParams.designTableInteractionDesignType, isNewDesign ? "New Design" : "Redesign" },
			{ EventParams.designTableInteractionType, lastActionPerformed },
			{ EventParams.designTableInteractionDetail, blockTypeLastHandled },
			{ EventParams.designTableInteractionPrice, currentPrice },
			{ EventParams.designTableInteractionScore, currentScore },
			{ EventParams.designTableInteractionMaterialType, materialTypeRequired },
			{ EventParams.designTableInteractionMaterialsCount, materialCountRequired },
		};
	}

	private DesignTableAnalytics CreateDesignTableAnalytics()
	{
#if UNITY_ANALYTICS_EVENT_LOGS
		Debug.Log($"DesignTableManager - CreateDesignTableAnalytics");
#endif
		DesignTableAnalytics analytics = new DesignTableAnalytics();
		/*
		string designOnTable = m_undo < 0 ? m_startingDesign.m_design : m_undoBuffer[m_undo].Item1;
		
		DesignUtilities.PartDescription[] designParts = DesignUtilities.GetDesignData(designOnTable);
		

		analytics.productLine = IsDesigningProduct() ? m_product.m_productLine : m_currentDesignType;
		analytics.timeSpent = GameStateTimer.Me.TimeDesignTableOpen;
		analytics.numParts = DesignUtilities.GetDesignPartCountFromDesign(designOnTable);
		analytics.lastActionPerformed = DesignTableAnalytics.kNoAction;
		analytics.blockTypeLastHandled = "N/A";

		analytics.stickerCount = DesignUtilities.GetStickerCount(designParts);
		analytics.patternCount = DesignUtilities.GetPatternCount(designParts);
		analytics.paintCount = DesignUtilities.GetPaintCount(designParts);

		if (m_lastGrabbedObjectDataOnDragStart != null && this.m_lastGrabbedObjectDataOnDragEnd != null)
		{
			bool positionChanged = !m_lastGrabbedObjectDataOnDragStart.m_GlobalPosition.Approximately(m_lastGrabbedObjectDataOnDragEnd.m_GlobalPosition);
			bool rotationChanged = !m_lastGrabbedObjectDataOnDragStart.m_GlobalRotation.Approximately(m_lastGrabbedObjectDataOnDragEnd.m_GlobalRotation, 0.001f);
			
			if (m_lastGrabbedObjectDataOnDragEnd.m_isFromTable)
			{
				if (m_lastGrabbedObjectDataOnDragEnd.m_isValidResult == false)
					analytics.lastActionPerformed = DesignTableAnalytics.kPartRemoved;
				else if (positionChanged)
					analytics.lastActionPerformed = DesignTableAnalytics.kChangePartPosition;
				else if (rotationChanged)
					analytics.lastActionPerformed = DesignTableAnalytics.kChangePartRotation;
			}
			else
			{
				if (AreDesignsDifferent(designOnTable, previousDesign))
					analytics.lastActionPerformed = DesignTableAnalytics.kAddPart;
				else
					analytics.lastActionPerformed = DesignTableAnalytics.kManualUndoAll;
			}
			
#if UNITY_ANALYTICS_EVENT_LOGS
			Debug.Log($"DesignTableManager - CreateDesignTableAnalytics - lastDraggedBlock available");
#endif
			analytics.blockTypeLastHandled = m_lastGrabbedObjectDataOnDragEnd.m_name;
		}
		else
		{
			if (m_currentHighlightId != null && !string.IsNullOrEmpty(DesignTableAnalytics.m_lastActionPerformedOnObject) && !string.IsNullOrEmpty(previousDesign))
			{
				DesignUtilities.PartDescription[] designPartsPrevious = DesignUtilities.GetDesignData(previousDesign);
				
				switch (m_currentHighlightId[0])
				{ 
					case c_paintId:
					{
						if (analytics.paintCount > DesignUtilities.GetPaintCount(designPartsPrevious))
							analytics.lastActionPerformed = DesignTableAnalytics.kAddPaint;
						else
							analytics.lastActionPerformed = DesignTableAnalytics.kChangePaint;
						break;
					}
					case c_patternId:
					{
						if (analytics.patternCount > DesignUtilities.GetPatternCount(designPartsPrevious))
							analytics.lastActionPerformed = DesignTableAnalytics.kAddPattern;
						else
							analytics.lastActionPerformed = DesignTableAnalytics.kChangePattern;
						break;
					}
					case c_stickerId:
					{
						if (analytics.stickerCount > DesignUtilities.GetStickerCount(designPartsPrevious))
							analytics.lastActionPerformed = DesignTableAnalytics.kAddSticker;
						else
							analytics.lastActionPerformed = DesignTableAnalytics.kChangeSticker;
						break;
					}
				}
				
				analytics.blockTypeLastHandled = DesignTableAnalytics.m_lastActionPerformedOnObject;
			}
			else if ((DesignTableAnalytics.m_lastAction == DesignTableAnalytics.kRedo ||
			          DesignTableAnalytics.m_lastAction == DesignTableAnalytics.kUndo ||
			          DesignTableAnalytics.m_lastAction == DesignTableAnalytics.kNewDesignConfirmed))
			{
#if UNITY_ANALYTICS_EVENT_LOGS
				Debug.Log($"DesignTableManager - CreateDesignTableAnalytics - lastAction - {DesignTableAnalytics.m_lastAction}");
#endif
				analytics.lastActionPerformed = DesignTableAnalytics.m_lastAction;
			}
			else
			{
				DesignTableAnalytics.ClearHistory();
				return null;
			}
		}

		analytics.currentPrice = m_currentDesignPrice;
		analytics.currentScore = m_currentDesignScore;

		if (m_currentDesignMaterials.Count > 0)
		{
			Dictionary<NGCarriableResource, float>.KeyCollection.Enumerator keyEnumerator =
				m_currentDesignMaterials.Keys.GetEnumerator();
			keyEnumerator.MoveNext();
			NGCarriableResource requiredResource = keyEnumerator.Current;
			analytics.materialTypeRequired = requiredResource.m_title;
			analytics.materialCountRequired = m_currentDesignMaterials[requiredResource];
			keyEnumerator.Dispose();
		}

		DesignTableAnalytics.ClearHistory();*/

		return analytics;
	}
	
	private void UpdateGrabData(DTDragBlock dtDragBlock, bool isFromTable)
	{
		if (dtDragBlock == null)
			return;
		m_lastGrabbedObjectDataOnDragStart = new DTDragBlock.DragObjectState(dtDragBlock.ObjectStateOnDragStart);
		m_lastGrabbedObjectDataOnDragEnd = new DTDragBlock.DragObjectState();
		m_lastGrabbedObjectDataOnDragEnd.m_GlobalPosition = dtDragBlock.transform.position;
		m_lastGrabbedObjectDataOnDragEnd.m_GlobalRotation = dtDragBlock.transform.rotation;
		m_lastGrabbedObjectDataOnDragEnd.m_isFromTable = m_lastGrabbedObjectDataOnDragStart.m_isFromTable = isFromTable;
		m_lastGrabbedObjectDataOnDragEnd.m_isValidResult = dtDragBlock.IsLastReleaseValid;
		NGBlockInfo blockInfo = NGBlockInfo.s_allBlocks[dtDragBlock.GetComponent<Block>().BlockID];
		m_lastGrabbedObjectDataOnDragEnd.m_name = blockInfo.m_displayName;
	}
	
	private void SendAnalyticsEvent()
	{
		if(GameManager.IsVisitingInProgress == false)
		{
#if UNITY_ANALYTICS_EVENT_LOGS
		Debug.Log($"DesignTableManager - SendAnalyticsEvent");
#endif
			DesignTableAnalytics analytics = CreateDesignTableAnalytics();

			if(analytics != null)
			{
				AnalyticsManager.Me.LogEvent(
					AnalyticsManager.Me.Events.DesignTableInteraction.AddParamList(analytics.Parameters));
			}
		}
	}
}
