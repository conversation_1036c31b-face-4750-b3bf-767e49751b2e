using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
//----------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------    
[CustomPropertyDrawer(typeof(FloatRange))]
class FloatRangeDrawer : PropertyDrawer
{    
    // Draw the property inside the given rect
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        var min = property.FindPropertyRelative("m_min");
        var max = property.FindPropertyRelative("m_max");

        Rect contentPosition = EditorGUI.PrefixLabel(position, new GUIContent(property.displayName));

        //Check if there is enough space to put the name on the same line (to save space)
        if (position.height > 16f)
        {
            position.height = 16f;
            EditorGUI.indentLevel += 1;
            contentPosition = EditorGUI.IndentedRect(position);
            contentPosition.y += 18f;
        }

        float half = contentPosition.width / 2;
        GUI.skin.label.padding = new RectOffset(3, 3, 6, 6);

        //show the X and Y from the point
        EditorGUIUtility.labelWidth = 30f;
        contentPosition.width *= 0.5f;
        EditorGUI.indentLevel = 0;

        // Begin/end property & change check make each field
        // behave correctly when multi-object editing.
        EditorGUI.BeginProperty(contentPosition, label, min);
        {
            EditorGUI.BeginChangeCheck();
            var newVal = EditorGUI.FloatField(contentPosition, new GUIContent("Min"), min.floatValue);
            if (EditorGUI.EndChangeCheck()) min.floatValue = newVal;
        }
        EditorGUI.EndProperty();

        contentPosition.x += half;

        EditorGUI.BeginProperty(contentPosition, label, max);
        {
            EditorGUI.BeginChangeCheck();
            var newVal = EditorGUI.FloatField(contentPosition, new GUIContent("Max"), max.floatValue);
            if (EditorGUI.EndChangeCheck()) max.floatValue = newVal;
        }
        EditorGUI.EndProperty();
    }
}
#endif

//----------------------------------------------------------------------------------------    
//----------------------------------------------------------------------------------------    
[System.Serializable]
public class FloatRange
{
    public FloatRange() { }
    public FloatRange(float _min, float _max) { m_min = _min; m_max = _max; }
    public float m_min;
    public float m_max;
     
    public float Lerp(float _speed)
    {
        return Mathf.Lerp(m_min, m_max, _speed);
    }
    public float Smooth(float _speed)
    {
        return Lerp(_speed * 2f - _speed * _speed);
    }
    public float SmoothStep(float _speed)
    {
        return Lerp(_speed * _speed * (3 - _speed - _speed));
    }
    public float Random()
    {
        return UnityEngine.Random.Range(m_min, m_max);
    }
}