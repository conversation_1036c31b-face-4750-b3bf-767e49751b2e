//#define PREWARM_LOADING
//#define _DEMO_MODE
using System;
using System.Collections;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Cans.Analytics;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEngine.Rendering.PostProcessing;
using UnityEngine.SceneManagement;
using Object = UnityEngine.Object;
#if UNITY_EDITOR
using UnityEditor;
#endif
using Steamworks;
using TMPro;
using Product = GameState_Product;
using Random = UnityEngine.Random;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Unity.Jobs;

#if (COMBAT_TESTING_ENABLED && !UNITY_EDITOR)
#error Please disable COMBAT_TESTING_ENABLED macro in Release build! 22Cans->Testing->Disable Combat Testing
#endif

public enum EManagerType {
	NONE = -1,
	COUNTRYSIDE,
	FACTORY,
	DESIGN,
	HOVER,
	LAST,
}

public partial class GameManager : MonoSingleton<GameManager> {
	private class GroupState
	{
		public List<MACharacterGroupBehaviour.GroupCharacterInfo> characters = new List<MACharacterGroupBehaviour.GroupCharacterInfo>();
		public Vector3 patrolPos = Vector3.zero;
		public float patrolRadius = 0f;
	};

	public GameState m_state { get; set; } = new GameState();
	public Camera m_camera;
	public bool DataReady = false;
	public bool LoadStarted => IsInTitleScreen() == false;
	public bool LoadComplete = false;
	public bool LoadCompleteEarly = false;
	public bool LoadCompletePreFadeIn = false;
	public bool PathsLoaded = false;
	public bool PeopleLoaded = false;
	public bool VerboseBalloons = false;
	public bool EmploymentBalloons = false;
	public bool InOutBalloons = false;
	public bool DisableCharacterDamage = false;
	
	public bool BuildingDataLoaded = false;
	public bool NamedPointDataLoaded = false;
	
	private bool m_timeLoaded = false;
	public bool TimeLoaded => m_timeLoaded;

	public GameObject m_lightsAndCameras;
	public EManagerType SceneType => IsDesignTable ? EManagerType.DESIGN : EManagerType.COUNTRYSIDE;

	public TMPro.TextMeshProUGUI m_versionDisplay;
	
	public float m_dragThreshold = .005f;

	public bool IsUnderground => UndergroundManager.Me?.gameObject?.activeSelf ?? false;

	public static bool IsInCountryside => Me?.IsCountryside ?? false;
	public bool IsCountryside => m_lightsAndCameras.activeSelf; //m_camera.isActiveAndEnabled;
	public bool IsDesignTable => DesignTableManager.Me.isActiveAndEnabled || DesignTableManager.Me.IsInDesignInPlaceActively || (DesignTableManager.Me.m_isInDesignGlobally && DesignTableManager.Me.m_designSingly == false);
	public bool IsInArcadium => MAResearchManagerUI.m_isActive;
	public bool IsInTownView => IsCountryside && DesignTableManager.Me.IsInDesignInPlace == false && IsSubScene == false && IsInCavelike == false && IsInArcadium == false;
	public static bool IsDesignTableActively => DesignTableManager.Me.IsInDesignInPlaceActively || DesignTableManager.Me.IsInDesignGloballyActively;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public bool IsProductTestingScene => ProductTestingManager.Me.m_root.activeSelf;
#else
	public bool IsProductTestingScene => false;
#endif
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public bool IsCharacterSelectionScene => CharacterSelectionSubscene.Me.m_root.activeSelf;
#else
	public bool IsCharacterSelectionScene => false;
#endif
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public bool IsFrescoScene => CryptManager.Me.IsFrescoActiveOrLeaving;
#else
	public bool IsFrescoScene => false;
#endif
	public bool IsCloseableSubScene => IsProductTestingScene || IsUnderground;
	public bool IsSubScene => IsDesignTable || IsCharacterSelectionScene || IsCloseableSubScene;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public bool IsInterior => (IsDesignTable && !DesignTableManager.Me.IsInBuildingMode) || IsProductTestingScene || IsInCavelike || RoadManager.Me.InPathEdit;
	public bool IsInteriorForAudio => IsInterior || CryptManager.Me.IsActive || IsCharacterSelectionScene || IsInTitleScreen();
#else
	public bool IsInterior => false;
	public bool IsInteriorForAudio => false;
#endif
	public bool IsBuildingPlacement => BuildingPlacementManager.Me.isActiveAndEnabled;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public bool IsRoadEdit => GlobalData.Me.m_roadEditMode || RoadManager.Me.InPathEdit;
#else
	public bool IsRoadEdit => false;
#endif
	public bool IsRoadConsuming => IsRoadEdit && RoadManager.Consuming;
	public bool IsTownMode => IsCountryside && !IsSubScene && !IsInterior && !IsRoadEdit; 
    private static bool CameraRotated;
    private static Vector3 CameraRotatedEulars;
    private static Vector3 CameraRotatedPosition;
    private int m_ambientSoundTown;
    private int m_ambientSoundWater;
    private int m_ambientSoundCountry;
	private int m_ambientSoundMenu;
	private int m_ambientSoundBoardroom;
    private bool m_cameraIsInTown = true;
    public bool m_saveOnStopPlay = true; // in Editor, should we save and upload on stop play?

    public string TokenRarity { get; set; }

    public GameObject m_subSceneExitButton;

    private Light m_mainLight = null;
    public Light MainLight
    {
	    get
	    {
		    if (m_mainLight == null)
		    {
			    m_mainLight = m_lightsAndCameras.transform.FindChildRecursiveByName("SunLight").GetComponent<Light>();
		    }
		    return m_mainLight;
	    }
    }
	public Transform CurrentCanvas {
		get {
			if (m_titlePage.activeSelf) return m_titleScreen.transform;
			if (IsCountryside) return m_mainGameUI;
			if (IsDesignTable) return DesignTableManager.Me.m_designTableUI;
			return m_mainGameUI;
		}
	}
	public Transform TopLevelCanvas => m_titleScreen.transform.parent;
	public Transform m_fullScreenCanvas;
	public Canvas TownCanvas { get; private set; }

	public int CurrentLayer => IsDesignTable ? c_layerDesignTable : 0;

	public long ServerTimeMs => (long)(Time.time * 1000f);

	public static bool HasLoadedFromSeed { get; set; } = false;
	public static bool HasLoadedFromRestoredSave { get; set; }
    private static GameObject CameraResetIcon;
	private static bool m_cameraResetActive;

	public static bool BranchingCombosEnabled = false;

	public static bool m_cropRegenerationAtDawn = false;

	//private static readonly int HASH_OWN_MASK = Shader.PropertyToID("_OwnLandMask");

	// Used by Hoover game, placeholder for future implementation if required
	public void LoadAndPush(string name, string _returnTo = null) {}
	public void LoadPrevious() {}
	// Used by tutorial system, implement if required
	public bool IsSceneLoading => false;
	// "Scene" stuff - simulates old scene-based flow
	public static string TownScene => "Town";
	public static string DesignTableScene => "DesignTable";
	public static bool IsSaveableScenes (string _scene) {
		return string.IsNullOrEmpty(_scene) || _scene == TownScene;
	}
	private string m_currentScene = TownScene;
	public string CurrentScene { get { return m_currentScene; } private set { PreviousScene = m_currentScene; m_currentScene = value; } }
	public string PreviousScene { get; private set; }
	public static event System.Action OnSceneLoad;
	public static event System.Action OnSceneLoadComplete;
	public static void SceneLoadCompleted(string _scene) {
		GameManager.Me.CurrentScene = _scene;
		OnSceneLoad?.Invoke();
		OnSceneLoadComplete?.Invoke();
	}



	//===== STEAM =====
	
	protected Callback<GameOverlayActivated_t> m_GameOverlayActivated;

	override protected void _OnEnable()
	{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (SteamManager.Initialized)
		{
			m_GameOverlayActivated = Callback<GameOverlayActivated_t>.Create(OnGameOverlayActivated);
		}
#endif
	}

	public bool CameraExceededMaxInteractionDistance()
	{
		return TerrainPopulation.Me.IsInMapMode;// Camera.main.transform.position.y >= 300;
	}
	
	private void OnGameOverlayActivated(GameOverlayActivated_t pCallback)
	{
		if (pCallback.m_bActive != 0)
		{
			Debug.Log("Steam Overlay has been activated");
			Pause(true);
		}
		else
		{
			Debug.Log("Steam Overlay has been closed");
			Pause(false);
		}
	}

	//===== /STEAM =====

	public bool Paused { get; private set; }

	void Pause(bool _pause)
	{
		Paused = _pause;
		Time.timeScale = _pause ? 0 : s_timeScale;
		if (_pause)
			StartCoroutine(Co_PumpSteam());
	}

	IEnumerator Co_PumpSteam()
	{
		while (Paused)
		{
			yield return null;
			SteamAPI.RunCallbacks();
		}
	}
	
#if UNITY_EDITOR || DEVELOPMENT_BUILD
	public static float s_timeScale = 1;
	private static void DebugSetTimeScale(float _f)
	{
		s_timeScale = _f;
		Time.timeScale = s_timeScale;
		Time.fixedDeltaTime = s_timeScale * .02f; 
	}
#else
	public static float s_timeScale => 1;
	private static void DebugSetTimeScale(float _f) {}
#endif
	
	public int m_numObjects = 0; //Camouflage variable set to 1 through reflection when timescale was changed
	public bool m_objectsRegistered = false;

	public float[] m_playerActivity = new float[256];
	private int m_lastPlayerActivityBlock;
	private int ActivityBlockIndex => ((int)(Time.realtimeSinceStartupAsDouble / (double)NGManager.Me.m_secondsPerActivityBlock)) 
	                                  % (NGManager.Me.m_numberOfActivityBlocks + 1);

	public float ActivityBlockFractionToNext => Mathf.Repeat(Time.realtimeSinceStartup / NGManager.Me.m_secondsPerActivityBlock, 1);

	private float m_simplePlayerActivity = 0;
	private float m_simplePlayerActivityGracePeriod = 0;
	public float SimplePlayerActivityGraceWindow => NGManager.Me.m_simplePlayerActivityGracePeriod > 0 ? m_simplePlayerActivityGracePeriod / NGManager.Me.m_simplePlayerActivityGracePeriod : 0;
	public void RecordPlayerActivity(float _amount)
	{
		m_simplePlayerActivity += _amount;
		m_simplePlayerActivityGracePeriod = NGManager.Me.m_simplePlayerActivityGracePeriod;
		
		int index = ActivityBlockIndex;
		m_playerActivity[index] += _amount;
	}
	public void RecordPlayerTapActivity(float _amount, bool _wasHold)
	{
		// ignore amount, record based on seconds held or clicks at a rate of n per second being equal to the hold rate 
		if (_wasHold) RecordPlayerActivity(Time.deltaTime * NGManager.Me.m_playerActivityHoldMultiplier);
		else RecordPlayerActivity(NGManager.Me.m_playerActivityTapMultiplier);
	}
	public void RecordPlayerDragActivity(float _amount)
	{
		RecordPlayerActivity(_amount * NGManager.Me.m_playerActivityDragMultiplier);
	}

	private static DebugConsole.Command s_debugPlayerActivity = new DebugConsole.Command("playeractivity", _s => Me.TogglePlayerActivityDebug());
	public void TogglePlayerActivityDebug()
	{
		if (m_playerActivityDebug == null)
		{
			m_playerActivityDebug = new GameObject("PlayerActivity");
			m_playerActivityDebug.transform.SetParent(m_mainGameUI);
			for (int i = 0; i < NGManager.Me.m_numberOfActivityBlocks + 2; ++i)
			{
				for (int j = 0; j < 2; ++j)
				{
					var go = new GameObject($"PA{i + 1}_{j+1}");
					go.transform.SetParent(m_playerActivityDebug.transform);
					var image = go.AddComponent<UnityEngine.UI.Image>();
					var rt = image.rectTransform;
					rt.anchoredPosition = new Vector2(i * 50 + 25 + 20, 25 + 20);
					rt.anchorMin = rt.anchorMax = rt.pivot = Vector2.zero;
					rt.sizeDelta = new Vector2(50, 50);
					image.color = (j == 0) ? Color.black : (i == NGManager.Me.m_numberOfActivityBlocks + 1 ? Color.blue : Color.green);
				}
			}
		}
		else
		{
			Destroy(m_playerActivityDebug);
			m_playerActivityDebug = null;
		}
	}
	private GameObject m_playerActivityDebug;

	private void TickPlayerActivity()
	{
		if (m_simplePlayerActivityGracePeriod > 0)
		{
			m_simplePlayerActivityGracePeriod = Mathf.Max(0, m_simplePlayerActivityGracePeriod - Time.deltaTime);
		}
		else
		{
			if (NGManager.Me.m_simplePlayerActivityDecayIsFraction)
				m_simplePlayerActivity *= NGManager.Me.m_simplePlayerActivityDecay;
			else
				m_simplePlayerActivity = Mathf.Max(0, NGManager.Me.m_simplePlayerActivityDecay * Time.deltaTime);
		}
		
		int index = ActivityBlockIndex;
		if (index != m_lastPlayerActivityBlock)
		{
			m_lastPlayerActivityBlock = index;
			if (NGManager.Me.m_numberOfActivityBlocks+1 > m_playerActivity.Length)
				Array.Resize(ref m_playerActivity, NGManager.Me.m_numberOfActivityBlocks+1);
			m_playerActivity[index] = 0;
		}

		if (m_playerActivityDebug != null)
		{
			for (int i = 0; i < NGManager.Me.m_numberOfActivityBlocks + 1; ++i)
			{
				var go = m_playerActivityDebug.transform.GetChild(i * 2 + 1);
				var img = go.GetComponent<UnityEngine.UI.Image>();
				img.color = (i == m_lastPlayerActivityBlock) ? Color.red : Color.green;
				var rt = go.transform as RectTransform;
				rt.sizeDelta = new Vector2(50, m_playerActivity[i] * 50);
			}
			var goT = m_playerActivityDebug.transform.GetChild(NGManager.Me.m_numberOfActivityBlocks * 2 + 2 + 1);
			var rtT = goT.transform as RectTransform;
			rtT.sizeDelta = new Vector2(50, GetPlayerActivity() * 50);
		}
	}
	public float GetPlayerActivity(bool _next = false)
	{
		if (NGManager.Me.m_simplePlayerActivity)
			return Mathf.Min(1, m_simplePlayerActivity);
		
		float score = 0;
		int baseBlock = m_lastPlayerActivityBlock + 1;
		if (_next) --baseBlock;
		for (int i = 0; i < NGManager.Me.m_numberOfActivityBlocks; ++i)
		{
			int index = (i + baseBlock) % (NGManager.Me.m_numberOfActivityBlocks + 1);
			score += Mathf.Clamp01(m_playerActivity[index]);
		}
		score /= NGManager.Me.m_numberOfActivityBlocks;
		return score;
	}

	public Transform m_mainGameUI;
	public GameObject m_buildingHighlightPrefab;

	private static bool s_isInCriticalShutdown = false;
	
	public class SLog : ILogHandler {
		static bool s_initialRun = true;
		private static string s_suffix = "";
		string LogPath => Application.persistentDataPath + $"/log{s_suffix}.txt";
		public static string PersistentLogFile => Application.persistentDataPath + $"/persistentLog{s_suffix}.txt";
		public static void SetSuffix(string _suffix) { s_suffix = _suffix; }
		public SLog() {
			if (s_initialRun)
				System.IO.File.WriteAllText(LogPath, "");
			else
				System.IO.File.AppendAllText(LogPath, "===== --- =====");
			s_initialRun = false;
			Debug.unityLogger.logHandler = this;
		}
		void HandleLogMessage(string logString, string stackTrace, LogType type)
		{
			if (logString.StartsWith("CRC Mismatch") && logString.Contains("AssetBundle"))
			{
				// bad install - a file is broken, need a re-install
				if (!s_isInCriticalShutdown)
				{
					s_isInCriticalShutdown = true;
					var msg = Utility.ShowDialog("Critical Error", "A file is damaged in your installation.\nPlease try re-installing Legacy.\nLegacy will now quit.\n", false, "Ok", null, _n =>
					{
						Quit();
					});
					msg.transform.SetParent(Me.m_titleScreen.transform.parent);
				}
			}
			if (type == LogType.Assert || type == LogType.Error || type == LogType.Exception) logString += "\n" + stackTrace; 
			System.IO.File.AppendAllText(LogPath, $"[{type.ToString()[0]}] {logString}\n");
		}
		public void LogFormat(LogType logType, Object context, string format, params object[] args) {
			HandleLogMessage(string.Format(format, args), "", logType);
		}
		public void LogException(System.Exception exception, Object context) {
			HandleLogMessage(exception.ToString(), exception.StackTrace, LogType.Exception);
		}
	}

	public static void PersistentLog(string _s)
	{
#if !UNITY_EDITOR && !UNITY_IOS
		if (GameManager.Me.IsClicker == false) return;
		var finalString = $"[{DateTime.UtcNow.ToShortDateString()} {DateTime.UtcNow.ToShortTimeString()}]\n{_s}\n";
		System.IO.File.AppendAllText(SLog.PersistentLogFile, finalString);
		Debug.Log(finalString);
#endif
	}
	private static Dictionary<string, float> s_persistentLogCooloffs = new Dictionary<string, float>();
	public static void PersistentLog(string _id, string _s)
	{
#if !UNITY_EDITOR && !UNITY_IOS
		float lastTime = 0;
		if (s_persistentLogCooloffs.TryGetValue(_id, out var t)) lastTime = t;
		const float c_minTimeBetweenPersistentMessages = 5;
		if (Time.time < lastTime + c_minTimeBetweenPersistentMessages) return;
		s_persistentLogCooloffs[_id] = Time.time;
		PersistentLog(_s);
#endif
	}
	
#if UNITY_EDITOR
    private const string LocaleTestKey = "LocaleTest";
    private const string LocaleTestLabel = "22Cans/Misc/Locale Test";
    public static bool LocaleTest { get => EditorPrefs.GetBool(LocaleTestKey, false); private set => EditorPrefs.SetBool(LocaleTestKey, value); }

    [MenuItem(LocaleTestLabel)] private static void LocaleTestDev() { LocaleTest = !LocaleTest; }
    [MenuItem(LocaleTestLabel, true)] private static bool LocaleTestDevCheck() { Menu.SetChecked(LocaleTestLabel, LocaleTest); return true; }
#else
    public static bool LocaleTest => false;  
#endif

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	private int m_windowOverrideW = -1;
	private int m_windowOverrideH = -1;
	private bool m_clickerActive = false;
	private string m_clickerPlayerName = null;
	private int m_clickerIndex = -1;
	private bool m_clickerNewGame = false;
	private bool m_creatingClicker = false;
	public bool IsClicker => m_clickerActive;
#else
	private int m_windowOverrideW => -1;
	private int m_windowOverrideH => -1;
	private bool m_clickerActive => false;
	private string m_clickerPlayerName => null;
	private int m_clickerIndex => -1;
	private bool m_clickerNewGame => false;
	private bool m_creatingClicker => false;
	public bool IsClicker => false;
#endif
	void CheckCommandLine()
	{
		string[] args = System.Environment.GetCommandLineArgs();
		foreach (var s in args)
		{
			if (s.StartsWith("-"))
			{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
				const string c_clicker = "-clicker=";
				if (s.StartsWith(c_clicker))
				{
					var bits = s.Substring(c_clicker.Length).Split(',');
					if (bits.Length >= 2)
					{
						m_clickerActive = true;
						if (int.TryParse(bits[0], out var w) && int.TryParse(bits[1], out var h))
						{
							m_windowOverrideW = w;
							m_windowOverrideH = h;
						}
						if (bits.Length > 2)
						{
							m_clickerPlayerName = bits[2];
							if (m_clickerPlayerName.StartsWith("New#"))
							{
								m_clickerPlayerName = m_clickerPlayerName.Substring(4);
								m_clickerNewGame = true;
							}
							if (bits.Length > 3)
							{
								m_clickerIndex = int.Parse(bits[3]);
#if !UNITY_EDITOR
								SLog.SetSuffix(bits[3]);
#endif
								/*if (bits.Length > 4)
								{
									if (bits[4] != "default")
									{
										BuildDetails.ServerEnv = bits[4];
									}
								}*/
							}
						}
					}
				}
#endif
			}
		}
	}

	SLog m_logger;
	protected override void _Awake() {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		bool localeTestOverride = System.IO.File.Exists(Application.persistentDataPath + "/OverrideLocale.txt");
#else
		bool localeTestOverride = false;
#endif
		if (localeTestOverride || LocaleTest)
		{
			System.Globalization.CultureInfo ci = new System.Globalization.CultureInfo("fr-FR");
			System.Threading.Thread.CurrentThread.CurrentCulture = ci;
			System.Threading.Thread.CurrentThread.CurrentUICulture = ci;
		}
		
		UnityEngine.Rendering.DebugManager.instance.enableRuntimeUI = false;
		
		CheckCommandLine();

#if !UNITY_EDITOR && !UNITY_IOS
		m_logger = new SLog();
		PersistentLog("Session Start");
#endif
		
#if false//UNITY_EDITOR
		Application.logMessageReceived += (_err, _cs, _type) => {
			if (_err.Contains("Coroutine continue failure"))
			{
				if (System.Diagnostics.Debugger.IsAttached)
					System.Diagnostics.Debugger.Break();
			}
		};
#endif
		
		SettingsUIController.LoadResolution(m_windowOverrideW, m_windowOverrideH, m_clickerIndex);
		TownCanvas = m_mainGameUI.GetComponentInParent<Canvas>();
		CameraResetIcon = GameObject.Find("CameraResetButton");
        if (CameraResetIcon != null)
            CameraResetIcon.SetActive(false);
        ReadCameraControlSpeeds();
		m_cameraResetActive = true;
		CameraRotated = false;
		m_debugCursorInfoActive = null;
	}

	static int s_divideByMe = 0;
	static DebugConsole.Command s_crash = new DebugConsole.Command("crash", _s => 
	{
		Debug.Log($"{100 / s_divideByMe}");
		GameObject crash = null;
		crash.transform.position = Vector3.zero;
	});

	private void SetCrashlyticsId(string _id)
	{
#if (UNITY_IOS || UNITY_ANDROID) && !UNITY_EDITOR
		Firebase.Crashlytics.Crashlytics.SetUserId(_id);
#endif
	}
	private static long s_sessionStartTicks = 0;
	private static long s_sessionPlayStartTicks = 0;
	private static long s_sinceForegroundTicks = 0;
	private static long s_nextPeriodicValueSet = 0;
	
	private void SetCrashlyticsPeriodicValues()
	{
#if (UNITY_IOS || UNITY_ANDROID) && !UNITY_EDITOR
		/*var now = WallClock.Ticks();
		if (s_sessionStartTicks == 0 && now != 0) s_sessionStartTicks = now;
		if (now > s_nextPeriodicValueSet + WallClock.SecondsToTicks(10))
		{
			s_nextPeriodicValueSet = now;
			var sessionTime = s_sessionStartTicks == 0 ? "-" : $"{(int)WallClock.TicksToSeconds(now - s_sessionStartTicks)}";
			var playTime = s_sessionPlayStartTicks == 0 ? "-" : $"{(int)WallClock.TicksToSeconds(now - s_sessionPlayStartTicks)}";
			var sinceForeground = s_sinceForegroundTicks == 0 ? "-" : $"{(int)WallClock.TicksToSeconds(now - s_sinceForegroundTicks)}";
			Firebase.Crashlytics.Crashlytics.SetCustomKey("SessionTime", $"s:{sessionTime}  p:{playTime}  f:{sinceForeground}");
		}*/
#endif
	}
	public static void SendToCrashlyticsAsException(string _message)
	{
#if (UNITY_IOS || UNITY_ANDROID) && !UNITY_EDITOR
		Firebase.Crashlytics.Crashlytics.LogException(new Exception($"Caught: {_message}"));
#endif
		Debug.LogError($"Caught: {_message}");
	}
	

	public void RefreshVersionAndInfo(string _idOverride = null) {
		var serverId = _idOverride ?? "";
		SetCrashlyticsId(serverId);
		var knackId = Serverless ? "" : NGKnack.ReadCachedData("uploadId.json")?.Trim() ?? "KNACK DATA BROKEN";
		m_versionDisplay.text = $"{BuildInfo.VersionString}:{knackId}:{((NGManager.Me?.m_knackVersion ?? 0) == 0 ? "-" : NGManager.Me?.m_knackVersion.ToString())}:{BuildInfo.GitRevision}";

		//m_versionDisplay.text += string.Format(":{0}", BuildDetails.ServerEnv);
		m_versionDisplay.text += $"\n{serverId}";
		GetComponent<LocalizationUI>().RemoveEntry(m_versionDisplay);
		//m_versionDisplay.text = $"{BuildInfo.VersionString}:{((NGManager.Me?.m_knackVersion ?? 0) == 0 ? "-" : NGManager.Me?.m_knackVersion.ToString())}:{BuildInfo.GitRevision}\n<size=24>{GalaID ?? ""}</size>";
		//m_versionDisplay.text = $"{BuildInfo.VersionString}:{((NGManager.Me?.m_knackVersion ?? 0) == 0 ? "-" : NGManager.Me?.m_knackVersion.ToString())}\n<size=24>{BuildInfo.GitRevision} B:{NGKnack.BalanceSet}</size>\n<size=24>{GalaID ?? ""}</size>";
	}

#if true//UNITY_EDITOR
	public static bool IsVerified1 => true;
	public static bool IsVerified2 => true;
#else
	public static bool IsVerified1 => s_galaId != null;
	public static bool IsVerified2 => Me.m_sessionInfo != null && Me.m_sessionInfo.id != null;
#endif

	public static bool Serverless => true;

	private static bool s_haveCompletedHandshake = false;

	public static void Quit(bool _noShutdown = false)
	{
		if (_noShutdown)
		{
			s_saveBeforeQuitState = SaveBeforeQuitState.Done;
		}
#if UNITY_EDITOR
		UnityEditor.EditorApplication.isPlaying = false;
#elif !UNITY_IOS
		Application.Quit();
#endif
	}
	
	IEnumerator Co_Out() {
		yield return new WaitForSeconds(Random.Range(.2f, .4f));
		Application.OpenURL("https://games.gala.com/games/legacy");
		Application.Quit();
	}

	public static string UserId;
	public static string UserName;
	public void RequestZendeskSupportPage() {
		string requestURL = "https://mastersofalbion.com?";//"https://legacyhelp.zendesk.com/hc/en-us/requests/new?";
		string info = ("userid=" + UnityEngine.Networking.UnityWebRequest.EscapeURL(UserId));
		info += ("&devicetype=" + UnityEngine.Networking.UnityWebRequest.EscapeURL(SystemInfo.deviceType.ToString()));
		info += ("&devicemodel=" + UnityEngine.Networking.UnityWebRequest.EscapeURL(SystemInfo.deviceModel));
		info += ("&deviceos=" + UnityEngine.Networking.UnityWebRequest.EscapeURL(SystemInfo.operatingSystem));
		info += ("&version=" + UnityEngine.Networking.UnityWebRequest.EscapeURL(BuildInfo.VersionString));
		info += ("&gitrevision=" + UnityEngine.Networking.UnityWebRequest.EscapeURL(BuildInfo.GitRevision));

		Application.OpenURL(requestURL + info);
	}
    public void TitleScreenSettings()
    {
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		TitleScreenSettings(true);
    }

    private SettingsUIController m_settingsPane = null;
    public bool SettingsPaneOpen => m_settingsPane != null;
	public void TitleScreenSettings(bool showNewGameButton = true) {
        if(MPlayerPrefs.HasKey("Subtitles") == false)
    		MPlayerPrefs.SetInt("Subtitles", 1);

		if (m_settingsPane != null) {
			CloseSettings();
		} else {
			if(showNewGameButton)
				m_settingsPane = SettingsUIController.Open(RequestZendeskSupportPage, null, null, OnClearSave);
			else
				m_settingsPane = SettingsUIController.Open(RequestZendeskSupportPage, null, null, null);
			m_settingsPane.onClose += () => m_settingsPane = null;
		}
	}

	public void CloseSettings()
	{
		if (m_settingsPane != null)
		{
			m_settingsPane.ClosedFromBackground();
			m_settingsPane.Close();
			m_settingsPane = null;
		}
	}

#if UNITY_EDITOR
	[MenuItem("22Cans/Save/Write Seed", false, -52)]
	private static void WriteSeed() => Me.PrepareAndWriteSeed();

	[MenuItem("22Cans/Save/Write Seed", true)]
	private static bool WriteSeedTest() => Application.isPlaying;

	[MenuItem("22Cans/Save/Write Seed Ch2", false, -51)]
	private static void WriteSeedCh2() => Me.PrepareAndWriteSeed($"Assets/Resources/{c_startFromAdvancedSave}.bytes");

	[MenuItem("22Cans/Save/Write Seed Ch2", true)]
	private static bool WriteSeedTestCh2() => Application.isPlaying;
	
	[MenuItem("22Cans/Save/Write Seed To File", false, -50)]
	private static void WriteSeedToFile()
	{
		string path = EditorUtility.SaveFilePanel("Save Seed", "", "SeedSave.txt", "txt");
		if (string.IsNullOrEmpty(path) == false)
			Me.PrepareAndWriteSeed(path);
	}
	
	private static DebugConsole.Command s_showBuildingStatsCmd = new ("showbuildingstats", _s => Debug.LogError($"Taps:{Me.m_state.m_gameInfo.m_numBuildingClicks}  Holds: {Me.m_state.m_gameInfo.m_numBuildingHolds}  Drags:{Me.m_state.m_gameInfo.m_numBuildingDrags}"));

	[MenuItem("22Cans/Save/Write Seed To File", true)]
    private static bool WriteSeedTestToFile() => Application.isPlaying;

	public static bool s_isWritingSeedSave = false;
	
	void PrepareAndWriteSeed(string _path = null)
	{
		s_isWritingSeedSave = true;
		var savePath = _path == null ? $"Assets/Resources/{SeedName}.bytes" : _path;

        var seedBackupGameInfo = m_state.m_gameInfo;
        m_state.m_gameInfo = new();
		
		var seedBackupUnlocks = m_state.m_unlocks;
		m_state.m_unlocks = new();
		var seedBackupDesignTableState = m_state.m_designTableDetails;
		m_state.m_designTableDetails = new();

		GameState_MovementBlocker.Enable("initial", false);
		
		for (int i = m_state.m_pickups.Count - 1; i >= 0; --i)
			if (m_state.m_pickups[i].m_quantity == 0)
				m_state.m_pickups.RemoveAt(i);
		
		for (int i = 0; i < m_state.m_buildings.Count; ++i)
        {
            var b = m_state.m_buildings[i];
            if (b.m_plantLevel > 0)
				b.m_plantLevel = 1;
        }
		for (int i = 0; i < m_state.m_wildBlocks.Count; ++i)
		{
			var wb = m_state.m_wildBlocks[i];
			wb.m_hasEverBeenUnlocked = false;
			if (wb.m_plantLevel > 0)
				wb.m_plantLevel = 1;
		}
		for (int i = 0; i < m_state.m_decorations.Count; ++i)
		{
			var dec = m_state.m_decorations[i];
			dec.m_wasEverInOwnedDistrict = false;
		}

		m_state.m_currentSpawnWaveThisDayByLocation = new();
		
		m_state.m_creaturesDefeated = 0;
		m_state.m_powerMana = NGManager.Me.m_defaultPowerManaMax;
		
		m_state.m_explodeInteractions.Clear();
		
		m_state.m_orders.Clear();
		m_state.PreSaveBuildings(true);

		m_state.m_akTriggers.Clear();
		
		DistrictManager.Me.LockDistrictByID("*");

		m_state.m_orderDataHistory.Clear();
		m_state.m_gateStates.Clear();
		m_state.m_manaBalls.Clear();
		m_state.m_namedPoints.Clear();
		m_state.m_ringsState.Clear();
		m_state.m_alignment.Clear();
		m_state.m_marketForces.Clear();
		
		DeleteAnimalHouseAnimalSaves();
		
		MABuilding.ResetComponentIDs();
		
		m_state.m_gameTime = new GameState_GameTime();

		OptimiseSaveData();
		
		foreach (var wb in m_state.m_wildBlocks) wb.m_hasEverBeenUnlocked = false;

		//===
		System.IO.File.WriteAllText(savePath, GetSaveData());
		//===
		
		AssetDatabase.ImportAsset(savePath);
		m_state.m_gameInfo = seedBackupGameInfo;
		m_state.m_unlocks = seedBackupUnlocks;
		m_state.m_designTableDetails = seedBackupDesignTableState;
		MASpawnPoint.WriteSeedSave();

        m_state.m_currentSpawnWaveThisDayByLocation.Clear();

		foreach (GameState_NamedPoint gameStateNamedPoint in m_state.m_namedPoints)
		{
			gameStateNamedPoint.ResetWorkingData();
		}
		s_isWritingSeedSave = false;
	}

	private void OptimiseSaveData()
	{
		m_state.m_terrainPopulationState.Clear();
	}
	
	private void DeleteAnimalHouseAnimalSaves()
	{
		for (var i = m_state.m_minorCharacterTypes.Count - 1; i >= 0; i--)
		{
			GameState_MovingObject gs = m_state.m_minorCharacterTypes[i];
			if (gs.m_homeComponentId > 0)
			{
				foreach (MABuilding meMaBuilding in NGManager.Me.m_maBuildings)
				{
					int iC = meMaBuilding.m_components.FindIndex(x => x.m_uid == gs.m_homeComponentId);
					if (iC > -1)
					{
						MAAnimalHouse aHouse = meMaBuilding.m_components[iC].GetComponent<MAAnimalHouse>();
						if (aHouse != null && meMaBuilding.m_components[iC].GetComponent<BCActionAnimalHouse>() != null)
						{
							m_state.m_minorCharacterTypes.RemoveAt(i);
						}
					}
				}
			}
		}
	}

	static DebugConsole.Command s_seedEditMode = new DebugConsole.Command("seed", _s => {
		bool wasSeedEdit = Me.IsSeedEditMode;
		Me.IsSeedEditMode = Utility.SetOrToggle(Me.IsSeedEditMode, _s);
		Debug.LogError($"Seed Edit Mode: {Me.IsSeedEditMode}");
		if (Me.IsSeedEditMode == false && wasSeedEdit == true)
		{
			Me.PrepareAndWriteSeed();
		}
	});
	public bool IsSeedEditMode { get; set; }
#else
	public bool IsSeedEditMode => false;
#endif

	private static DebugConsole.Command s_workerSpeed = new DebugConsole.Command("workerspeed", _s => {
		if(string.IsNullOrEmpty(_s) || string.IsNullOrWhiteSpace(_s)) {
			Debug.Log($"GameManager - Console Command 'workerSpeed' - Current: {NGManager.Me.m_defaultObjectSpeed}");
			return;
		}
		if(float.TryParse(_s, out float newSpeed)) {
			Debug.Log($"GameManager - Console Command 'workerSpeed' - Changing from {NGManager.Me.m_defaultObjectSpeed} to {newSpeed}");
			NGManager.Me.m_defaultObjectSpeed = newSpeed;
		}
	});
	
	private static DebugConsole.Command s_weaponDamage = new DebugConsole.Command("weapondamage", _s => {
		if (string.IsNullOrEmpty(_s) || string.IsNullOrWhiteSpace(_s))
		{
			NGManager.Me.m_defaultWeaponDamage = 1f;
			return;
		}

		if (float.TryParse(_s, out float newDmg))
		{
			NGManager.Me.m_defaultWeaponDamage = newDmg * 10f;
		}
	});

	private static DebugConsole.Command s_sweepingWeapons = new DebugConsole.Command("sweepweapons", _s => {
		if (string.IsNullOrEmpty(_s) || string.IsNullOrWhiteSpace(_s))
		{
			NGManager.Me.m_enableSweepingAttacks = !NGManager.Me.m_enableSweepingAttacks;
			return;
		}

		if (bool.TryParse(_s, out var newSweep))
		{
			NGManager.Me.m_enableSweepingAttacks = newSweep;
		}
	});

	public GameObject m_titleScreen; // title/loading image
	public GameObject m_titlePage; // title UI elements
	public GameObject m_titleLoading; // loading UI elements
	public GameObject m_debugPane;
	public ProgressImage m_titleLoadingProgress;
	public TMPro.TextMeshProUGUI m_titleLoadingProgressMessage;
	public TMPro.TextMeshProUGUI m_startButton;
	public TMPro.TextMeshProUGUI m_keyManagerButton;
	public UnityEngine.UI.Button m_startButtonButton;
	public GameObject m_startAdvancedButton;
	public TMPro.TextMeshProUGUI[] m_debugSlotButtons;
	public GameObject[] m_debugDemoButtons;
	public int m_debugDemoButtonsEnable = 1 | 4;
	const string c_selectedSaveSlotPrefsKey = "MOASaveSlot";
	int m_selectedSaveSlot = 0;
	public static int SelectedSaveSlot => MPlayerPrefs.GetInt(c_selectedSaveSlotPrefsKey, 0);
	void DebugRefreshSlotButtons()
	{
		RefreshSlotButtonsWork();
	}

	void RefreshSlotButtonsWork()
	{
		int slot = MPlayerPrefs.GetInt(c_selectedSaveSlotPrefsKey, 0);
		m_selectedSaveSlot = slot;
		for (int i = 0; i < m_debugSlotButtons.Length; ++i) {
			m_debugSlotButtons[i].color = (slot == i) ? new Color(.5f,.5f,1f,1f) : new Color(.75f,.75f,.75f,1f);
		}
		foreach (var b in m_debugSlotButtons) b.transform.parent.gameObject.SetActive(!BuildDetails.DemoMode);
		for (int i = 0; i < m_debugDemoButtons.Length; ++i)
		{
			var b = m_debugDemoButtons[i];
			b.SetActive(BuildDetails.DemoMode && (m_debugDemoButtonsEnable & (1 << i)) != 0);
		}
	}
	
	public void DebugSelectSaveSlot(int _index) {
		MPlayerPrefs.SetInt(c_selectedSaveSlotPrefsKey, _index);
		DebugRefreshSlotButtons();
		RefreshStartButton();
	}
	void DebugStart() {
#if DEVELOPMENT_BUILD || UNITY_EDITOR
		m_debugPane.SetActive(true);
		DebugRefreshSlotButtons();
#else
		m_debugPane.SetActive(false);
#endif
	}
	
	public void UpdateKeyManagerButton()
	{
		var keyManagerButton = m_keyManagerButton.transform.parent.parent.gameObject;
		keyManagerButton.GetComponent<ContextMenuButton>().SetButtonInteractable(false);
	}
	
	private void InitStartButton()
	{
		if (Serverless)
		{
			RefreshStartButton();
			return;
		}
		var button = m_startButton.transform.parent.parent.gameObject;
		button.GetComponent<ContextMenuButton>().SetButtonInteractable(false);
		m_startButton.text = "";
    }
	public enum SaveSlotState
	{
		Indeterminate,
		New,
		Existing,
	}
	
	private int m_preWarmSlot = -1;
	private SaveSlotState m_preWarmState;

	public GameState LoadFromFile(string _path)
	{
		var json = ReadSaveDataFile(_path);
		return LoadFromJson(json, _path);
	}
	public GameState LoadFromJson(string _json, string _path)
	{
		var state = new GameState();
		state.Invalidate();

#if UNITY_EDITOR
		try
		{
			JsonUtility.FromJsonOverwrite(_json, state);
		}
		catch (Exception e)
		{
			throw new ArgumentException($"Failed to load save data at path, delete file, or analyse '{_path}'", e);
		}
#else
		JsonUtility.FromJsonOverwrite(_json, state);
#endif
		state.PostLoad();
		return state;
	}
	
	List<MACalenderInfo> m_earlyCalendarInfo;
	public MACalenderInfo GetCalendarInfo(GameState _state)
	{
		if (m_earlyCalendarInfo == null)
		{
			NGManager.NGSettings.LoadInfo(); // make sure NGManager.m_duskLength and m_dawnLength is up to date
			m_earlyCalendarInfo = MACalenderInfo.LoadInfo();
		}
		var dayFrac = _state.m_gameTime.m_gameTime;
		var day = (int) dayFrac;
		dayFrac -= day;
		MACalenderInfo thisInfo = null;
		for (int i = 0; i < m_earlyCalendarInfo.Count; ++i)
		{
			var dayInfo = m_earlyCalendarInfo[i];
			if (dayInfo.m_dayNumber == day)
			{
				thisInfo = dayInfo;
				break;
			}
		}
		return thisInfo;
	}

	public string GetCalendarInfoString(GameState _state)
	{
		var info = GetCalendarInfo(_state);
		return GetCalendarInfoString(info);
	}

	public string GetCalendarInfoString(MACalenderInfo _info)
	{
		if (_info == null) return "";
		return $"Day {_info.m_dayNumber} - {_info.m_dawnHeading}";
	}


	private SaveSlotState m_currentSlotState = SaveSlotState.Indeterminate;
	public bool IsSaveButtonReady => m_currentSlotState != SaveSlotState.Indeterminate;
	private static string[] s_slotStateButtonTexts = { "Loading", "New Game", "Continue" };
	public void RefreshStartButton()
	{
		var button = m_startButton.transform.parent.parent.gameObject;
		button.GetComponent<ContextMenuButton>().SetButtonInteractable(false);

		UpdateKeyManagerButton();
		if (Serverless)
		{
			m_currentSlotState = System.IO.File.Exists(SavePath) ? SaveSlotState.Existing : SaveSlotState.New;
			string currentSaveInfo = "";
			if (m_currentSlotState == SaveSlotState.Existing)
			{
				var state = LoadFromFile(SavePath);
				currentSaveInfo = GetCalendarInfoString(state);
			}
			UIManager.Me.m_currentSlotInfo.text = currentSaveInfo;
		}

		m_startButton.text = LocalizeKnack.TranslateLocalisedString(s_slotStateButtonTexts[(int)m_currentSlotState]);
        if (UseLocalSave) m_startButton.text += " <size=70%><color=#ff8080>Local</color></size>";
		button.SetActive(!BuildDetails.DemoMode);
		button.GetComponent<ContextMenuButton>().SetButtonInteractable(m_currentSlotState != SaveSlotState.Indeterminate);
		SetTitlesWaiting(m_currentSlotState == SaveSlotState.Indeterminate);
		
		bool showAdvancedButton = !SaveExists();
		var buttonTexts = m_startAdvancedButton.GetComponentsInChildren<TMPro.TextMeshProUGUI>(true);
#if _DEMO_MODE
		buttonTexts[0].text = "Restart Demo";
		showAdvancedButton = !showAdvancedButton;
#else
		buttonTexts[0].text = "Restart Game";//"Start Chapter 2";
		showAdvancedButton = !showAdvancedButton;
#endif
		buttonTexts[1].gameObject.SetActive(false);
		m_startAdvancedButton.GetComponent<LayoutElement>().enabled = true;
		m_startAdvancedButton.SetActive(showAdvancedButton);
		
#if false//!UNITY_EDITOR
		m_startButton.transform.parent.parent.gameObject.SetActive(SaveExists());
#endif
		
#if PREWARM_LOADING
		StartLateInitialise(true);
#endif
	}
	string m_startFromRestoredSave = null;
	const string c_startFromAdvancedSave = "seedmoach2";
	public void TitleScreenContinue() {
		m_startFromRestoredSave = null;
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        AudioClipManager.Me.PlayUISound("PlaySound_TitleScreenStart");

        LoadSaveAndStart();
	}

	private void ConfirmAndClearSave(bool _startGameAfterwards)
	{
		AudioClipManager.Me.PlayUISound("PlaySound_ClearSave");
		var msg = Utility.ShowDialog("Clear Save?", "This will delete all progress and start the game again.\n\n<size=125%>Are you sure?</size>", false, "Yes", "No", _n =>
		{
			if (_n == 0)
			{
				float delayBeforeDelete = 0.01f;
				if (_startGameAfterwards)
				{
					ShowLoading(true);
					delayBeforeDelete = 1f;
				}
				this.DoAfter(delayBeforeDelete, () =>
				{
					DeleteSaveDataFromServer((b) =>
					{
						AudioClipManager.Me.PlayUISound("PlaySound_ClearSave");
						if (_startGameAfterwards)
						{
							LoadSaveAndStart();
						}
						else
						{
							RefreshStartButton();
#if PREWARM_LOADING
							//ResetToTitles();
#endif
						}
					});
				});
			}
		});
	}

	public void TitleScreenStartAdvanced() {
#if true//_DEMO_MODE
		ConfirmAndClearSave(true);
#else
		m_startFromRestoredSave = c_startFromAdvancedSave;
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		AudioClipManager.Me.PlayUISound("PlaySound_StartChapter2");
		LoadSaveAndStart();
#endif
	}
	public void TitleScreenRestart() {
		TitleScreenSettings(true);
	}
	void OnClearSave() 
	{
		ConfirmAndClearSave(false);
	}

	private bool m_visitWorldAbandon = false;
	private static int s_visitWorldIndex = -1;
	private static int s_visitWorldFrom = -1;
	private static bool s_visitWorldFromTitles = false;
	public static string s_visitWorldTheirID;
	private static bool s_visitWorldHadError = false;
	public static bool IsVisiting => IsVisitingInProgress && Me.LoadComplete;
	public static bool IsVisitingInProgress => s_visitWorldFrom != -1;

	public static HashSet<GameObject> s_allSingletons = new HashSet<GameObject>();

	private static DebugConsole.Command s_spinner = new DebugConsole.Command("spinner", _s => Me.ShowSpinner(!Me.m_spinner.activeSelf)); 
	public GameObject m_spinner;
	public void ShowSpinner(bool _show) {
		m_spinner.SetActive(_show);
	}
	
	public void VisitPlayerWorld(string _playerId) {
		s_visitWorldTheirID = _playerId;
		s_visitWorldHadError = false;
		VisitWorld(c_visitSaveIndex);
	}

	public static void ExitToTitleScreen(bool _cleanly = true)
	{
		VisitWorld(_cleanly ? -1 : -2); // cleanly means save first, otherwise just ditch (network error etc)
	}

	static bool s_loadSaveOnStart = false;
	private void ResetAndLoadSave()
	{
		m_titleScreen.SetActive(false);
		s_loadSaveOnStart = true;
		VisitWorld(-2);
	}

	private void ResetToTitles()
	{
		m_titleScreen.SetActive(false);
		VisitWorld(-2);
	}

	private static void VisitWorld(int _index) {
		Me.StartCoroutine(Me.Co_VisitWorld(_index));
	}
	IEnumerator Co_VisitWorld(int _index) {
		if (_index == -2)
		{
			yield return AudioClipManager.Me.FadeOut();
			yield return null;
		}

		bool inTitles = IsInTitleScreen();
		
		yield return Co_StartLoadingScreen();

		if (_index == -2) // -2 means "same as -1 but don't save first"
			_index = -1;
		else
			CleanUpForShutdown();
		
		if (_index == -1) {
			_index = s_visitWorldFromTitles ? -1 : s_visitWorldFrom;
			s_visitWorldFrom = -1;
		} else {
			s_visitWorldFrom = Me.m_selectedSaveSlot;
			s_visitWorldFromTitles = inTitles;
		}
		s_visitWorldIndex = _index;
		LoadComplete = false;
		LoadCompletePreFadeIn = false;
		LoadCompleteEarly = false;

		MASpawnPoint.CleanUp();
		DistrictManager.CleanUp();
		SubSceneStack.CleanUp();
		PathBlock.CleanUp();
		CameraSnapPoint.CleanUp();
		foreach (var s in s_allSingletons) Destroy(s.gameObject);
			s_allSingletons.Clear();
			ItemDetailsHelper.Cleanup();
		s_haveCompletedHandshake = false;
		ReactPickupPersistent.HaveServerPickups = false;
		SceneManager.LoadScene("Town", LoadSceneMode.Single);
	}

	public void CleanUpForShutdown()
	{
		if(!LoadComplete) return;

		ForceSaveAndUpload();
	}

	public void ReturnFromVisiting()
	{
		ReturnFromVisiting(false);
	}
	public void ReturnFromVisiting(bool _hadError)
	{
		s_visitWorldHadError = _hadError;
		m_visitWorldAbandon = _hadError;
		VisitWorld(-1);
	}

	public void QuitToTitleScreen()
    {
		SceneManager.LoadScene(SceneManager.GetActiveScene().name);
	}

	void InitialiseTranslationSettings()
    {
		NGLanguages.LoadInfo();
        string tlang = MPlayerPrefs.GetString("Language", "English");
        LocalizeKnack.m_language = tlang;
        LocalizeKnack.ReloadNGTranslated(tlang);
    }

	void FadeTo3DLogo() => StartCoroutine(Co_FadeTo3DLogo());

	IEnumerator Co_FadeTo3DLogo()
	{
		Show3DLogo();

		UIManager.Me.m_titleScreenOverlay.gameObject.SetActive(true);
		const float c_fadeLength = .5f;
		float dt = 1.0f / (30.0f * c_fadeLength);
		for (float t = 1; t >= 0 && m_titleLoading.activeSelf == false; t -= dt)
		{
			UIManager.Me.m_titleScreenOverlay.color = new Color(0, 0, 0, t);
			yield return null;
		}
		UIManager.Me.m_titleScreenOverlay.color = Color.clear;
		UIManager.Me.m_titleScreenOverlay.gameObject.SetActive(false);
	}

	void StartTitles() {
		m_keyManagerButton.GetComponentInParent<UnityEngine.UI.Button>(true).gameObject.SetActive(false); // hide until auth tells us we're on a keyholder server
		if (s_visitWorldIndex != -1) {
			m_selectedSaveSlot = s_visitWorldIndex;
			return;
		}
		m_titleScreen.SetActive(true);
		m_titlePage.SetActive(true);
		m_titleLoading.SetActive(false);
		DebugStart();
		if (Serverless) EarlyInitialise();
		InitStartButton();
	}

	void EndTitles()
	{
		m_titlePage.SetActive(false);
		ShowLoading(true);
		StartLateInitialise(false);
	}
	
	bool m_lateInitialiseInProgress = false;
	void StartLateInitialise(bool _isPreWarm)
	{
		m_granularLoading = _isPreWarm;
		
		if (m_lateInitialiseInProgress) return;
		m_lateInitialiseInProgress = true;

		m_preWarmSlot = m_selectedSaveSlot;
		m_preWarmState = m_currentSlotState;
		
		StartCoroutine(Co_LateInitialise());

		if (s_loadSaveOnStart)
		{
			s_loadSaveOnStart = false;
			EndTitles();
		}
	}
	
	void ShowLoading(bool _show) {
		if (s_loadSaveOnStart) return;
		m_titleScreen.SetActive(_show);
		m_titleLoading.SetActive(_show);
		m_debugPane.SetActive(false);
	}
	public bool IsInTitleScreen()
	{
		return m_titleScreen.activeSelf;
	}

	public bool IsIn3DTitleScreen()
	{
		return CameraRenderSettings.Me.m_titleObject3D != null ? CameraRenderSettings.Me.m_titleObject3D.activeSelf : false;
	}

	public bool IsNewGame()
	{
		return m_currentSlotState == SaveSlotState.New;
	}
	
	bool m_RHeldOnLoad = false; public bool RHeldOnLoad => m_RHeldOnLoad;
	GameObject m_saveBankButtonHolder = null;

	void ClearSaveBankButtons()
	{
		if (m_saveBankButtonHolder == null) return;
		m_saveBankButtonHolder.SetActive(false);
		Destroy(m_saveBankButtonHolder);
		m_saveBankButtonHolder = null;
	}

	private static DebugConsole.Command s_bankSaveCmd = new("banksave", _s => Me.BankSave(_s));
	private static DebugConsole.Command s_unbankSaveCmd = new("unbanksave", _s => Me.UnbankSave(_s));
	private static DebugConsole.Command s_saveToSlotCmd = new("saveToSlot", _s => Me.SaveToSlot(_s));
	private static DebugConsole.Command s_currentSlotCmd = new("currentSlot", _s => Debug.LogError($"Currently using save slot {Me.m_selectedSaveSlot}"));

	void SaveToSlot(string _s)
	{
		if (int.TryParse(_s, out var slot) == false)
		{
			Debug.LogError($"Invalid save slot index '{_s}'");
			return;
		}
		bool isInTitles = IsInTitleScreen();
		string json = null;
		if (isInTitles == false) json = GetSaveData();
		var savePath = GetSavePath(slot);
		var outFile = $"{savePath}{_s}.json";
		if (json == null)
			System.IO.File.Copy(Me.SavePath, outFile, true);
		else
			System.IO.File.WriteAllText(outFile, json);
		Debug.LogError($"Saved to slot {slot}");
	}
	
	void BankSave(string _s)
	{
		bool isInTitles = IsInTitleScreen();
		string json = null;
		if (isInTitles == false) json = GetSaveData();
		var savePath = $"{Application.persistentDataPath}/SaveBank/";
		if (System.IO.Directory.Exists(savePath) == false) System.IO.Directory.CreateDirectory(savePath);
		var outFile = $"{savePath}{_s}.json";
		if (json == null)
			System.IO.File.Copy(Me.SavePath, outFile, true);
		else
			System.IO.File.WriteAllText(outFile, json);
		Debug.LogError($"Saved to save bank as {_s}");
		if (isInTitles) CheckSaveBank();
	}

	void UnbankSave(string _s)
	{
		bool isInTitles = IsInTitleScreen();
		var savePath = $"{Application.persistentDataPath}/SaveBank/{_s}.json";
		if (System.IO.File.Exists(savePath))
			System.IO.File.Delete(savePath);
		Debug.LogError($"Removed from save bank as {_s}");
		if (isInTitles) CheckSaveBank();
	}

	private bool m_forceLoadedFromSeed = false;
	void AddSaveButton(string _label, string _path, int _y)
	{
		const float c_saveButtonH = 48;
		float scale = Screen.height / 1080.0f;
		var buttonGO = new GameObject("Button_" + _label);
		buttonGO.transform.SetParent(m_saveBankButtonHolder.transform);
		var button = buttonGO.AddComponent<UnityEngine.UI.Button>();
		button.onClick.AddListener(() =>
		{
			// restore and start save _path
			if (_path == null)
			{
#if UNITY_EDITOR
				// load from arbitrary file
				string readPath = EditorUtility.OpenFilePanel("Load Seed", "", "txt");
				if (string.IsNullOrEmpty(readPath) == false)
				{
					System.IO.File.Copy(readPath, SavePath, true);
					m_forceLoadedFromSeed = true;
				}
#endif
			}
			else
			{
				if (Input.GetKey(KeyCode.D))
				{
					System.IO.File.Delete(_path);
					CheckSaveBank();
					return;
				}
				m_RHeldOnLoad = Input.GetKey(KeyCode.R);
				System.IO.File.Copy(_path, SavePath, true);
				var lastSlashIndex = _path.LastIndexOf('/');
				var fileName = _path.Substring(lastSlashIndex + 1);
				if (fileName.ToLower().StartsWith("prf"))
				{
					ProfileMode = true;
#if UNITY_EDITOR
					Selection.activeObject = PerformanceManager.Me.gameObject;
#endif
				}
				else if (fileName.ToLower().StartsWith("cap"))
				{
					ProfileMode = true;
					this.DoPostLoad(() => StartFPSCapture());
				}
				m_currentSlotState = SaveSlotState.Existing;
			}
			Dewarm();
			LoadSaveAndStart();
			ClearSaveBankButtons();
		});
		var img = buttonGO.AddComponent<UnityEngine.UI.Image>();
		img.color = new Color(0.1f, 0.5f, 0.2f, .5f);
		button.targetGraphic = img;
		//buttonGO.AddComponent<CanvasRenderer>();
		var rt = buttonGO.GetComponent<RectTransform>();
		rt.anchorMin = new Vector2(0, 1);
		rt.anchorMax = new Vector2(0, 1);
		rt.pivot = new Vector2(0, 1);
		rt.sizeDelta = new Vector2(c_saveButtonH * 10, c_saveButtonH) * scale;
		rt.anchoredPosition = new Vector2(0, -_y * (c_saveButtonH * 1.1f));
		var buttonLabel = new GameObject("Label");
		buttonLabel.transform.SetParent(buttonGO.transform);
		var label = buttonLabel.AddComponent<UnityEngine.UI.Text>();
		label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
		label.fontSize = (int)(c_saveButtonH * .9f * scale);
		label.color = Color.white;
		label.text = _label;
		label.alignment = TextAnchor.MiddleCenter;
		label.horizontalOverflow = HorizontalWrapMode.Overflow;
		label.verticalOverflow = VerticalWrapMode.Overflow;
		label.raycastTarget = false;
		var lrt = buttonLabel.GetComponent<RectTransform>();
		lrt.anchoredPosition = Vector2.zero;
		lrt.sizeDelta = rt.sizeDelta;
	}

	private int m_screenHeightOnCheckSaveBank = 0;
	void CheckSaveBankNeedsRefresh()
	{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if (IsInTitleScreen() == false)
			return;
		if (m_screenHeightOnCheckSaveBank != Screen.height)
			CheckSaveBank();
#endif
	}

	void CheckSaveBank()
	{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		m_screenHeightOnCheckSaveBank = Screen.height;
		
		ClearSaveBankButtons();
		m_saveBankButtonHolder = new GameObject("SaveBankButtons");
		m_saveBankButtonHolder.transform.SetParent(m_titleScreen.transform, false);
		var dummy = m_saveBankButtonHolder.AddComponent<Image>();
		dummy.enabled = false;
		var holderRT = m_saveBankButtonHolder.transform as RectTransform;
		holderRT.anchorMin = holderRT.anchorMax = holderRT.pivot = new Vector2(0, 1);
		
		int y = 0;
		AddSaveButton("Load Seed From File", null, y++);
		
		var savePath = $"{Application.persistentDataPath}/SaveBank/";
		if (System.IO.Directory.Exists(savePath) == false) return;
		var saveBank = System.IO.Directory.GetFiles(savePath);
		if (saveBank == null || saveBank.Length == 0) return;
		foreach (var save in saveBank)
		{
			var name = System.IO.Path.GetFileName(save);
			int ext = name.LastIndexOf('.');
			if (ext == 0) continue;
			if (ext != -1) name = name.Substring(0, ext);
			AddSaveButton(name, save, y++);
		}
#endif
	}

	void Dewarm()
	{
		m_preWarmSlot = -1;
	}

	private static DebugConsole.Command s_setphysicsdtcmd = new ("physdt", _s => Time.fixedDeltaTime = floatinv.Parse(_s));
	private static DebugConsole.Command s_toggleprofilemodecmd = new ("profilemode", _s => Me.ProfileMode = Utility.SetOrToggle(Me.ProfileMode, _s));
	private bool m_isInProfileMode = false; 
	public bool ProfileMode
	{
		get => m_isInProfileMode;
		set {
			if (m_isInProfileMode == value) return;
			DebugSetTimeScale(value ? .001f : 1);
			Utility.OverrideInputs(value);
			Utility.OverrideInputsMousePosition(Vector2.one * .5f, 0, 0);
			s_consoleShowFPS = true;
			m_isInProfileMode = value;
		}
	}

	void LoadSaveAndStart()
	{
#if PREWARM_LOADING
		if (m_preWarmSlot != m_selectedSaveSlot || m_preWarmState != m_currentSlotState)
			ResetAndLoadSave();
		else
#endif
			EndTitles();
	}

	//[MenuItem("22Cans/TestSS")]
	static void TestSS()
	{
		//SSerializer.Inst.TestFloats();
		
		var s = ":<:m_orderSlots:=::[::<:m_gift:=::T:MAOrder+MAOrderBusinessGift:<::>::.:m_order:=::<:m_orderId:=:176:.:m_isParsed:=:1:.:m_rewardsChosen:=::[:PeopleFavour3:.::]::.:m_orderInfoIndexer:=:chapter 1[58]0010:.:m_linkedDesignID:=:77:.:m_assignedBuildingId:=:-1:.:m_originatorBuildingId:=:50:.:m_orderQuantity:=:3:.:m_orderQuality:=:0.001:.:m_numProductsDelivered:=:0:.:m_orderNutrition:=:0:.:m_orderScore:=:0:.:m_orderBonusScore:=:0:.:m_locked:=:False:.:m_product:=:Food:.::>::.:m_lockedDuration:=:0:.::>::.::<:m_gift:=::T:MAOrder+MAOrderBusinessGift:<::>::.:m_order:=::<:m_orderId:=:177:.:m_isParsed:=:1:.:m_rewardsChosen:=::[:RoyalFavour2:.::]::.:m_orderInfoIndexer:=:chapter 1[58]0020:.:m_linkedDesignID:=:78:.:m_assignedBuildingId:=:-1:.:m_originatorBuildingId:=:50:.:m_orderQuantity:=:12:.:m_orderQuality:=:0.21:.:m_numProductsDelivered:=:0:.:m_orderNutrition:=:0:.:m_orderScore:=:0:.:m_orderBonusScore:=:0:.:m_locked:=:False:.:m_product:=:Weapons:.::>::.:m_lockedDuration:=:0:.::>::.::]::.:m_orderSequence:=::[:chapter 1[58]0030:.:chapter 1[58]0040:.:chapter 1[58]0050:.:chapter 1[58]0060:.:chapter 1[58]0070:.:chapter 1[58]0080:.:chapter 1[58]0090:.:chapter 1[58]0100:.::]::.:m_title:=:Dispatch:.::>::.:";
		//var s = ":<:m_orderSlots:=::[::<:m_gift:=::<:id:=::.:m_name:=::.:m_type:=::.:m_giftTitle:=::.:m_cardTitle:=::.:m_cardPower:=::.:m_power:=::.:m_cardPrice:=:0:.:m_quantity:=:0:.:m_spritePath:=::.:m_description:=::.:m_buildingDesign:=::.:m_canBePutInVault:=:False:.:m_giftFunction:=::.:m_rewardFunction:=::.:m_fromFlow:=:True:.:m_buildingsToUpgrade:=::.:m_componentsToUpgrade:=::.:m_buildingsToUpgradeList:=::[::]::.:m_componentsToUpgradeInfos:=::[::]::.:m_upgrades:=::[::]::.:m_upgradeImports:=::.:m_upgradeImportList:=::[::]::.::>::.:m_order:=::<:m_orderId:=:176:.:m_isParsed:=:1:.:m_rewardsChosen:=::[:PeopleFavour3:.::]::.:m_orderInfoIndexer:=:chapter 1[58]0010:.:m_linkedDesignID:=:77:.:m_assignedBuildingId:=:-1:.:m_originatorBuildingId:=:50:.:m_orderQuantity:=:3:.:m_orderQuality:=:0.001:.:m_numProductsDelivered:=:0:.:m_orderNutrition:=:0:.:m_orderScore:=:0:.:m_orderBonusScore:=:0:.:m_locked:=:False:.:m_product:=:Food:.::>::.:m_lockedDuration:=:0:.::>::.::<:m_gift:=::<:id:=::.:m_name:=::.:m_type:=::.:m_giftTitle:=::.:m_cardTitle:=::.:m_cardPower:=::.:m_power:=::.:m_cardPrice:=:0:.:m_quantity:=:0:.:m_spritePath:=::.:m_description:=::.:m_buildingDesign:=::.:m_canBePutInVault:=:False:.:m_giftFunction:=::.:m_rewardFunction:=::.:m_fromFlow:=:True:.:m_buildingsToUpgrade:=::.:m_componentsToUpgrade:=::.:m_buildingsToUpgradeList:=::[::]::.:m_componentsToUpgradeInfos:=::[::]::.:m_upgrades:=::[::]::.:m_upgradeImports:=::.:m_upgradeImportList:=::[::]::.::>::.:m_order:=::<:m_orderId:=:177:.:m_isParsed:=:1:.:m_rewardsChosen:=::[:RoyalFavour2:.::]::.:m_orderInfoIndexer:=:chapter 1[58]0020:.:m_linkedDesignID:=:78:.:m_assignedBuildingId:=:-1:.:m_originatorBuildingId:=:50:.:m_orderQuantity:=:12:.:m_orderQuality:=:0.21:.:m_numProductsDelivered:=:0:.:m_orderNutrition:=:0:.:m_orderScore:=:0:.:m_orderBonusScore:=:0:.:m_locked:=:False:.:m_product:=:Weapons:.::>::.:m_lockedDuration:=:0:.::>::.::]::.:m_orderSequence:=::[:chapter 1[58]0030:.:chapter 1[58]0040:.:chapter 1[58]0050:.:chapter 1[58]0060:.:chapter 1[58]0070:.:chapter 1[58]0080:.:chapter 1[58]0090:.:chapter 1[58]0100:.::]::.:m_title:=:Dispatch:.::>::.:";
		//var s = ":<:m_orderSlots:=::[::<:m_gift:=::<:id:=::.:m_name:=::.:m_type:=::.:m_giftTitle:=::.:m_cardTitle:=::.:m_cardPower:=::.:m_power:=::.:m_cardPrice:=:0:.:m_quantity:=:0:.:m_spritePath:=::.:m_description:=::.:m_buildingDesign:=::.:m_canBePutInVault:=:False:.:m_giftFunction:=::.:m_rewardFunction:=::.:m_fromFlow:=:False:.:m_buildingsToUpgrade:=::.:m_componentsToUpgrade:=::.:m_buildingsToUpgradeList:=::[::]::.:m_componentsToUpgradeInfos:=::[::]::.:m_upgrades:=::[::]::.:m_upgradeImports:=::.:m_upgradeImportList:=::[::]::.::>::.:m_order:=::<:m_orderId:=:176:.:m_isParsed:=:1:.:m_rewardsChosen:=::[:PeopleFavour3:.::]::.:m_orderInfoIndexer:=:chapter 1[58]0010:.:m_linkedDesignID:=:77:.:m_assignedBuildingId:=:-1:.:m_originatorBuildingId:=:50:.:m_orderQuantity:=:3:.:m_orderQuality:=:0.001:.:m_numProductsDelivered:=:0:.:m_orderNutrition:=:0:.:m_orderScore:=:0:.:m_orderBonusScore:=:0:.:m_locked:=:False:.:m_product:=:Food:.::>::.:m_lockedDuration:=:0:.::>::.::<:m_gift:=::<:id:=::.:m_name:=::.:m_type:=::.:m_giftTitle:=::.:m_cardTitle:=::.:m_cardPower:=::.:m_power:=::.:m_cardPrice:=:0:.:m_quantity:=:0:.:m_spritePath:=::.:m_description:=::.:m_buildingDesign:=::.:m_canBePutInVault:=:False:.:m_giftFunction:=::.:m_rewardFunction:=::.:m_fromFlow:=:False:.:m_buildingsToUpgrade:=::.:m_componentsToUpgrade:=::.:m_buildingsToUpgradeList:=::[::]::.:m_componentsToUpgradeInfos:=::[::]::.:m_upgrades:=::[::]::.:m_upgradeImports:=::.:m_upgradeImportList:=::[::]::.::>::.:m_order:=::<:m_orderId:=:177:.:m_isParsed:=:1:.:m_rewardsChosen:=::[:RoyalFavour2:.::]::.:m_orderInfoIndexer:=:chapter 1[58]0020:.:m_linkedDesignID:=:78:.:m_assignedBuildingId:=:-1:.:m_originatorBuildingId:=:50:.:m_orderQuantity:=:12:.:m_orderQuality:=:0.21:.:m_numProductsDelivered:=:0:.:m_orderNutrition:=:0:.:m_orderScore:=:0:.:m_orderBonusScore:=:0:.:m_locked:=:False:.:m_product:=:Weapons:.::>::.:m_lockedDuration:=:0:.::>::.::]::.:m_orderSequence:=::[:chapter 1[58]0030:.:chapter 1[58]0040:.:chapter 1[58]0050:.:chapter 1[58]0060:.:chapter 1[58]0070:.:chapter 1[58]0080:.:chapter 1[58]0090:.:chapter 1[58]0100:.::]::.:m_title:=:Dispatch:.::>::.:";
		
		var g = new GameObject("TestSS");
		var bc = g.AddComponent<BCActionDispatch>();
		var blk = g.AddComponent<Block>();
		SSerializer.DeserializeInPlace(bc, s);
		var ll = bc.Save();
		if (s != ll.m_saveString) Debug.LogError($"Save string mismatch:\n===\n{s}\n\n===\n{ll.m_saveString}\n===");
		else
		{
			Debug.LogError("Save match");
			//SSerializer.Inst.DumpStats();
			var sw = System.Diagnostics.Stopwatch.StartNew();
			for (int i = 0; i < 5000; ++i)
			{
				bc.Save();
			}
			sw.Stop();
			Debug.LogError($"5000 saves in {sw.ElapsedMilliseconds}ms");
			Debug.LogError($"Json: \n{JsonUtility.ToJson(bc)}");
			sw = System.Diagnostics.Stopwatch.StartNew();
			for (int i = 0; i < 5000; ++i)
			{
				JsonUtility.ToJson(bc);
			}
			sw.Stop();
			Debug.LogError($"5000 saves in {sw.ElapsedMilliseconds}ms");
		}
		DestroyImmediate(g);
	}
	
	void Start()
	{
		Time.maximumDeltaTime = 1.0f / 10.0f;
		Time.maximumParticleDeltaTime = 1.0f / 15.0f;
		
		CheckSaveBank();
		
		StartUnityGameServices();
		Shader.SetGlobalColor("_AmbientColour", new Color(0.5f, 0.5f, 0.3f, 1f));

		//Shader.SetGlobalTexture(HASH_OWN_MASK, (Texture)Texture2D.whiteTexture);
		
		s_saveBeforeQuitState = SaveBeforeQuitState.None;
		Application.wantsToQuit += SaveBeforeQuit;

		//Instantiate(Resources.Load<GameObject>("_GUI/ClickerWarning"), m_titlePage.transform);
		
		gameObject.AddComponent<TouchManager>();

		NGManager.SetSSerializerTransforms();
		
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		StartTitles();
		FadeTo3DLogo();
#endif
	}

	public void AddDebug(string _s) => m_state.Debug(_s);
	
#if UNITY_EDITOR
	[MenuItem("22Cans/Misc/Edit AutoExec")]
	static void EditAutoExec()
	{
		var autoExecFile = Application.persistentDataPath + "/autoexec.txt";
		if (System.IO.File.Exists(autoExecFile) == false)
			System.IO.File.WriteAllText(autoExecFile, "# MOA AutoExec file\n# Add debug console commands here to have them run post-load\n\n# Add sections [new] and [load] to define commands to run\n# only on a new game or only when loading an existing game,\n# [always] to go back to always\n\n");
		System.Diagnostics.Process.Start(autoExecFile);
	}
#endif
	
	void DebugAutoExec()
	{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		var autoExecFile = Application.persistentDataPath + "/autoexec.txt";
		if (System.IO.File.Exists(autoExecFile))
		{
			var lines = System.IO.File.ReadAllLines(autoExecFile);
			bool inhibit = false;
			string newClr = "<color=#c0ffff>[new] ";
			string loadClr = "<color=#ffc0ff>[load] ";
			string alwaysClr = "<color=#ffffc0>[always] ";
			var clr = alwaysClr;
			foreach (var line in lines)
			{
				if (string.IsNullOrEmpty(line)) continue;
				if (line.StartsWith("#")) continue;
				if (line.StartsWith("//")) continue;
				if (line.StartsWith("["))
				{
					// meta
					if (line.ToLower().StartsWith("[new]"))
					{
						inhibit = HasLoadedFromSeed == false;
						clr = newClr;
					}
					else if (line.ToLower().StartsWith("[load]"))
					{
						inhibit = HasLoadedFromSeed;
						clr = loadClr;
					}
					else if (line.ToLower().StartsWith("[always]"))
					{
						inhibit = false;
						clr = alwaysClr;
					}
					continue;
				}
				if (inhibit) continue;
				Debug.LogError($"<color=#ffffff>Autoexec: </color>{clr}{line}</color>");
				DebugConsole.Me.ExecuteConsole(line, true);
			}
		}
#endif
	}

	void StartUnityGameServices()
	{
		Unity.Services.Core.UnityServices.InitializeAsync();
	}

	enum SaveBeforeQuitState
	{
		None,
		SaveInProgress,
		Done,
	}
	private static SaveBeforeQuitState s_saveBeforeQuitState = SaveBeforeQuitState.None;

	public static void CloseSessionAndQuit()
	{
		CloseSession(() =>
		{
			s_saveBeforeQuitState = SaveBeforeQuitState.Done;
			Quit();
		});
	}

	private static void CloseSession(System.Action _cb)
	{
		_cb();
	}
	private static void CheckSaveBeforeQuit()
	{
		if (s_saveBeforeQuitState == SaveBeforeQuitState.SaveInProgress)
		{
			Debug.Log("Save before quit complete");
			CloseSessionAndQuit();
		}
	}
	private static bool IsSavingForQuit => s_saveBeforeQuitState == SaveBeforeQuitState.SaveInProgress;
	private static bool SaveBeforeQuit()
	{
		switch (s_saveBeforeQuitState)
		{
			case SaveBeforeQuitState.None:
				Debug.Log("Save before quit");
				s_saveBeforeQuitState = SaveBeforeQuitState.SaveInProgress;
#if UNITY_EDITOR
				bool doSave = Me.m_saveOnStopPlay;
#else
				bool doSave = true;
#endif
				if (Me.IsInTitleScreen() || IsVisitingInProgress || !doSave)
					CloseSessionAndQuit();
				else
					Me.ForceSaveAndUpload();
				float timeout = 3;
				/*while (s_saveBeforeQuitState == SaveBeforeQuitState.SaveInProgress)
				{
					timeout -= .001f;//Time.unscaledDeltaTime;
					if (timeout < 0) s_saveBeforeQuitState = SaveBeforeQuitState.Done;
					BestHTTP.HTTPManager.OnUpdate();
					System.Threading.Thread.Sleep(1);
				}*/
				return true;
			case SaveBeforeQuitState.SaveInProgress:
				return false;
			case SaveBeforeQuitState.Done:
			default:
				return true;
		}
	}

    public static IEnumerator LoadDeviceInfo(System.Action _apply)
    {
	    int loadStage = -1;
	    float startTime = Time.time;
		 
	    NGDeviceID.LoadInfoStage((_info) => { loadStage++; return true; });
	    NGIdiom.LoadInfoStage((_info) => { loadStage++; return true; });
	    NGPowerLevel.LoadInfoStage((_info) => { loadStage++; return true; });

	    yield return new WaitUntil(() => (loadStage == 2) || (Time.time - startTime > 2f));
		
	    _apply?.Invoke();
    }

	public void EarlyInitialise()
    {
#if UNITY_IOS || UNITY_ANDROID
	    StartCoroutine(LoadDeviceInfo(() =>
	    {
		    if(NGIdiom.Idiom != null)
		    {
			    GameSettings.HUDScale(NGIdiom.Idiom.m_hUDScale);
			    GameSettings.GUIScale(NGIdiom.Idiom.m_uIScale);
		    }
	    }));
#endif
	    RefreshVersionAndInfo();
		NGManager.NGSettings.LoadInfo();

		InitialiseTranslationSettings();
    }

	const bool c_profileLoading = false;
	private System.Diagnostics.Stopwatch m_progressStopwatch;
	private long m_progressLastLabelTime = 0;
	private int m_progressLastLabelFrame = 0;
	private string m_lastProgressLabel = "";

	private long m_lastFrameTimeMS = 0;
	private long m_worstFrameTimeMS = 0;
	private void TrackWorstFrameTime()
	{
		if (c_profileLoading == false) return;
		var time = Time.unscaledTime;
		var timeMS = m_progressStopwatch?.ElapsedMilliseconds ?? 0;
		if (m_lastFrameTimeMS > 0)
		{
			var dtMS = timeMS - m_lastFrameTimeMS;
			if (dtMS > m_worstFrameTimeMS) m_worstFrameTimeMS = dtMS;
		}
		m_lastFrameTimeMS = timeMS;
	}

	private void PrefixProgress(string _prefix)
	{
		var label = $"{_prefix}_{m_lastProgressLabel}";
		var f = m_titleLoadingProgress.Progress;
		SetProgress(f, label);
	}

	private void SetProgress(float _f, string _t)
    {
	    if (c_profileLoading)
	    {
		    var label = _t.Split(':')[0];
		    if (m_lastProgressLabel != label)
		    {
			    if (m_progressStopwatch == null)
			    {
				    m_progressStopwatch = new System.Diagnostics.Stopwatch();
				    m_progressStopwatch.Restart();
				    m_progressLastLabelTime = 0;
				    m_progressLastLabelFrame = Time.frameCount;
			    }
			    var t = m_progressStopwatch.ElapsedMilliseconds;
			    var dt = t - m_progressLastLabelTime;
			    var frames = Time.frameCount - m_progressLastLabelFrame;
			    TrackWorstFrameTime();
			    Debug.LogError($"{m_lastProgressLabel}: {dt}ms over {frames} frames [avg {(frames > 0 ? dt / frames : 0)}ms worst {m_worstFrameTimeMS}]");
			    m_worstFrameTimeMS = 0;
			    m_progressLastLabelTime = t;
			    m_lastProgressLabel = label;
			    m_progressLastLabelFrame = Time.frameCount;
		    }
	    }
	    m_titleLoadingProgress.Progress = _f;
	    m_titleLoadingProgressMessage.text = $"Loading\n<size=50%>{_t}</size>";
    }

    float m_nextProgressStop = 0;
    float m_nextProgressStep = 0;
    private void SetNextProgressStage(float _to, int _estimatedSteps)
    {
	    m_nextProgressStop = _to;
	    m_nextProgressStep = (_to - m_titleLoadingProgress.Progress) / _estimatedSteps;
    }

    private void StepProgress(string _t, int _step = 1)
    {
	    SetProgress(Mathf.Min(m_titleLoadingProgress.Progress + m_nextProgressStep * _step, m_nextProgressStop), _t);
    }
    
    private void RunProgressSteps(int _steps)
    {
		m_titleLoadingProgress.Step(_steps);
    }
    
    private bool m_granularLoading = false;

    IEnumerator WaitForSettle()
    {
	    if (c_profileLoading == false) yield break;
	    PrefixProgress("Settle");
	    for (int i = 0; i < 100; ++i) yield return null;
    }

    private bool LoadAbandoned { get; set; } = false;
	private static bool s_haveKnack = false;
	IEnumerator Co_LateInitialise() {
		float loadStartTime = Time.unscaledTime;
		
		yield return null; // settle, allow starts
		
		Time.timeScale = .001f;
		
		DesignTableManager.Me.InitialseDesignMode();
		
		ReactPickupPersistent.PickupsLoaded = false;
		m_objectsRegistered = false; //Clear flag so timescale change can be reported to tpp again
		m_titleLoadingProgress.Progress = 0;
		m_titleLoadingProgressMessage = m_titleLoadingProgress.transform.parent.parent.GetComponentInChildren<TMPro.TextMeshProUGUI>();
		m_titleLoadingProgressMessage.textWrappingMode = TMPro.TextWrappingModes.NoWrap;
		m_timeLoaded = false;
		
		const float c_knackEndLoading = .2f;
		const float c_postLoading = .95f;
		const int c_otherSteps = 8, c_buildSteps = 8, c_pathSteps = 8;

		GameManager.SceneLoadCompleted(GameManager.TownScene);

		yield return null;

		NGManager.NGSettings.LoadInfo(); // pre-load the settings to get the seed name
		yield return ReadSaveData();
		if (IsVisitingInProgress && m_visitWorldAbandon)
		{
			m_visitWorldAbandon = false;
			yield break;
		}
		if (LoadAbandoned)
		{
			StartTitles();
			yield break;
		}
		
		RefreshVersionAndInfo();
		
		if (!s_haveKnack) {
			s_haveKnack = true;
			NGManager.s_loadInfoProgress += (_f, _s) => SetProgress(Mathf.Lerp(0, c_knackEndLoading, _f), _s);
			yield return NGManager.LoadInfos();
		} else {
			//NGTutorial.LoadMasterInfo(); // reset tutorial data

			RoadManager.RoadImport.LoadInfo();
		}
		SetNextProgressStage(c_postLoading, c_otherSteps+c_buildSteps+c_pathSteps);

		StepProgress("Managers"); yield return null;

		// TerrainPopulation.Me.CheckAllInstances();

		ItemDetailsHelper.ActivateUnlocks();
		
		NGBusinessDecisionManager.Me.Activate();

		NGBusinessDecisionManager.Me.EarlyLoad(m_state.m_oldStyleData);
		
		yield return WaitForSettle();
		StepProgress("WildBlocks"); yield return null;
		DesignTableManager.PreLoadWildBlocks();
		
		yield return WaitForSettle();
		StepProgress("Buildings", c_buildSteps); RunProgressSteps(c_buildSteps); yield return null;
		GlobalData.Me.BeginBatchTerrainOperations();
		if(HasLoadedFromSeed) //TS - MUST BE REMOVED AS SOON AS SEED SAVE CREATURES ARE CLEARED
		{
			m_state.m_creatures.Clear();
			m_state.m_heroes.Clear();
			m_state.m_akTriggers.Clear();
		}
		yield return CreateBuildings();
		yield return WaitForSettle();
		
		StepProgress("Paths", c_pathSteps); RunProgressSteps(c_pathSteps); yield return null;
		PathBreak.LoadAll();
		GameState_MovementBlocker.Initialise();
		RoadManager.Me.GeneratePathVisuals(); // has to happen after we load buildings so we can remove fences etc around buildings
		ReseatBuildings(true); // after we create paths we need to reseat buildings so they are at the correct height
		GlobalData.Me.EndBatchTerrainOperations();
		yield return WaitForSettle();

		PathsLoaded = true;
		StepProgress("Objects"); yield return null;
		CreateVehicles();
		//CreateBoats();
		yield return CreatePeople();
		yield return CreateCreatures();
		PeopleLoaded = true;
		StepProgress("OldStructs"); yield return null;
		LoadFromOldStyleSaveStructures();
		StepProgress("Pre"); yield return null;
		
		DesignTableManager.LoadWildBlocks();	// Moving until after people as due to save order dependancy
		while(DesignTableManager.IsRestoring)
			yield return null;
		DesignTableManager.PostLoadWildBlocks();

		TreeHolder.PostLoad();
		
		MAOrderDataManager.Me.Load();
		LoadMABuildingData();
		LoadMANamedSpawnPointData();
		
		MAOrderDataManager.Me.PostLoad();
		
		LoadMASpawnPoints();

		LoadInteractionPoints();
		PostLoadMovingObjects();
		
		MAMarketForcesManager.Me.PostLoad();
		
		CreateLocks();
		StepProgress("CamSeq"); yield return null;
		if (HasLoadedFromSeed) 
		{
			m_state.m_gameInfo.m_chosenGender = "";
			NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Given, NGManager.Me.SetStartingMoney, "Starting cash");
		}
		else
		{
			MAQuestManager.Me.LoadAllQuests(m_state.m_questSaveData);
		}
		yield return new WaitForEndOfFrame();
		
		// Deal with out-of-range buildings; subsequently do any processing that requires buildings to be partitioned by in/out-of-range 
		// workers/boats etc are loaded afterwards. Their saved destinations can be commanders or they'll need to find new valid destinations
		StepProgress("LateInit"); yield return null;
		NGManager.Me.UpdateOutOfRangeCommanders();
	
		NGBusinessDecisionManager.Me.Load(m_state.m_oldStyleData);
		
		LoadTime();
		
		if(!HasLoadedFromSeed)
			ReactPickupPersistent.CreateSavedPickups(GlobalData.Me.m_pickupsHolder);

        //NGTutorialManager.Me.LatePostLoad();
		NGBusinessGiftsPanel.OnPostLoad();
		
		PopulateStoryCharacters();
		
#if ENABLE_ADVERTS
		//MAVehicleControl.Me.SpawnAdvertVehicle(); //TODO: TS - comment-in when the MOA flag issue is resolved, also MOA map is different, navigation paths to off-map cause vans to get stuck in ditch
#endif
		
		yield return null;
		LateInitialise();
		StepProgress("Tutorial"); yield return null;
		//NGTutorialManager.Me.SetSavedSceneLocation();
		MAGameFlow.LateInitialise();

		SetNextProgressStage(1.0f, 3);
		StepProgress("Post"); yield return null;
		
		NGDecorationInfoManager.PostLoad();		
		DEBUG_PostLoad();

		if (BuildDetails.DemoMode)
		{
			NGUnlocks.PayBills = false;
		}

		MAManaBall.LoadAll();
		MATreasurePit.LoadAll();
		TreasureChestInteraction.LoadAll();
		ExplodeInteraction.LoadAll();

		//StepProgress("PathBreak"); yield return null;
		//PathBreak.LoadAll();
		
		//CheckGuildBuildings();

		yield return WaitForSettle();
		StepProgress("Prg"); yield return null;
		
		m_titleLoadingProgress.Progress = 1;
		m_titleLoadingProgress.WorkComplete();
        while (m_titleLoadingProgress.ProgressVisual < .999f)
            yield return null;

        StepProgress("Final"); yield return null;

        TurnOnCountrysideAmbientSounds();

        KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.Town);

        LoadDesignTableState();
        
        if(HasLoadedFromSeed)
        {
	        m_state.m_gameStats = new GameState_Stats();
	        m_state.m_gameStats.Init();
	        m_state.m_parserCommands.Clear();
        }
        
        CalendarUIManager.Me.Refresh();
        GameState_ChallengeIcons.Load();
        
        m_state.m_subSceneState.Load();

        GameState_BuildHelper.LoadForBuildings();
        FailSequenceController.Me.LoadFailState();
        
        // Reload parser commands
        foreach(var func in m_state.m_parserCommands.m_values)
        {
	        func.Execute();
        }

        float loadTime = Time.unscaledTime - loadStartTime;
        Debug.Log($"TLT {loadTime}s");
        
        DebugAutoExec();

        StepProgress(" "); yield return null;
		yield return Co_EndLoadingScreen(3, () => LoadingComplete());

		if(TrySendStartupCurrencyAnalytics() == false)
		{
			Debug.LogError("GameManager - Attempt to Log Startup Cash Analytics failed -> currencies <= 0 or values received yet.");
		}
	}

	private void LoadTime()
	{
		var timeInfo = m_state.m_gameTime;
		if (HasLoadedFromSeed == false)
		{
#if UNITY_EDITOR
			if (timeInfo.m_gameTime > 1e6f)
			{
				timeInfo.m_gameTime = Mathf.Repeat(timeInfo.m_gameTime, 1.0f); // degenerate amount of time has passed, probably an old bug 
			}
#endif
			if (timeInfo.m_gameTimeLocked)
				DayNight.Me.m_overrideDayFraction = Mathf.Repeat(timeInfo.m_gameTime, 1.0f);
			else
				DayNight.Me.m_overrideDayFraction = -1;
		}
		else
		{
			timeInfo.m_gameTime = Mathf.Repeat(timeInfo.m_gameTime, 1.0f) + 1; // seed load, discard number of days passed, leave just fraction of day (but start on day 1)
		}
		m_timeLoaded = true;
	}
	
	private void LoadDesignTableState()
	{
		int buildingID = m_state.m_designTableDetails.m_currentBuildingDesignID;
		if (buildingID == 0) return;
		DesignTableManager.Me.SetNextDesignDrawerState(m_state.m_designTableDetails.m_drawerSelection);
		if (buildingID < 0)
		{
			DesignTableManager.Me.StartDesignGloballyUnfocused();
			return;
		}
		var building = NGManager.Me.FindBuildingByID(buildingID);
		if(building == null)
			return;
		if (m_state.m_designTableDetails.m_isDesigningProductOrderId >= 0)
		{
			bool hasFactory = false;
			var factories = building.BuildingComponents<BCFactory>();
			foreach(var factory in factories)
			{
				hasFactory = true;
				if(factory.GetOrder().IsValid)
				{
					factory.DesignProduct(); //TS: depending on save system init sequence, if [Save] data is loaded early enough this will be available
					return;
				}
			}
			
			if(hasFactory)
			{
				MAOrder _order = MAOrder.FindOrderById(m_state.m_designTableDetails.m_isDesigningProductOrderId);
				if(_order.IsValid)
				{
					building.ReceiveOrder(_order); //TS: depending on save system init sequence, if [Save] data is loaded later this will ensure order is re-applied tob uidling
				}
			}
			return;
		}
		DesignTableManager.Me.StartDesignGlobally(building);
	}
	
	[NonSerialized]
	public MACharacterBase m_possessedCharacterBeforeGameOver = null;
	
	public IEnumerator Co_ExitToTown()
	{
		var wasLoadComplete = LoadComplete;
		if (wasLoadComplete == false)
		{
			while (LoadCompleteEarly == false)
				yield return null;
			Crossfade.Me.StartFadeOverride(true);
			while (LoadComplete == false)
				yield return null;
		}
		// something catestrophic has happened (day failed etc), break out to town for end sequence
		while (CryptManager.Me.IsFrescoActiveOrLeaving) // don't abandon fresco but finish showing it before playing the fail sequence
			yield return null;
		if (ProductTestingManager.Me.IsActive)
		{
			ProductTestingManager.Me.Abort();
			while (ProductTestingManager.Me.IsActive)
				yield return null;
			while (DesignTableManager.IsDesignInPlace)
				yield return null;
		}
		if (DesignTableManager.IsDesignInPlace)
		{
			DesignTableManager.Me.Close(false);
			while (DesignTableManager.IsDesignInPlace)
				yield return null;
		}
		if (IsInSubScene || m_state.m_subSceneState.Current != null)
		{
			// in case we're still loading the subscene, wait for it to finish loading before exiting
			while (m_state.m_subSceneState.Current == null)
				yield return null;
			// exit sub-scene, returning possessed character to original entrance
			GenericSubScene.Exit(null);
			while (m_state.m_subSceneState.Current != null)
				yield return null;
			yield return new WaitForSecondsRealtime(.5f);
		}
		if (CryptManager.Me.IsActive)
		{
			IntroControl.Me.LeaveCrypt();
			while (CryptManager.Me.IsActive)
				yield return null;
			yield return new WaitForSecondsRealtime(.5f);
		}
		if (IsPossessing) //TS - Comment back in if need to unpossess upon game over
		{
			m_possessedCharacterBeforeGameOver = PossessedCharacter;
			Unpossess();
			while (IsPossessing)
				yield return null;
		}
		if (IsInArcadium)
		{
			MAResearchManagerUI.Me.ClickedClose();
			while (IsInArcadium)
				yield return null;
		}
		if (BuildingPlacementManager.Me.IsActive)
			BuildingPlacementManager.Me.ToggleOff();

		DragHolder.DropAllHeldItems();
		
		BlockInfoPanelV2.DestroyPreviousInstance();

		if (wasLoadComplete == false)
			Crossfade.Me.EndFadeOverride();
	}

	public void CheckRePossess()
	{
		if (IsPossessing == false &&
		    m_possessedCharacterBeforeGameOver != null &&
		    m_possessedCharacterBeforeGameOver.IsAlive) //TS - Comment out in if need to prevent re-possess after game over
		{
			PossessObject(m_possessedCharacterBeforeGameOver);
		}
		m_possessedCharacterBeforeGameOver = null;
	}

	void SetTitlesWaiting(bool _waiting) {
		if (_waiting) InitStartButton();
		m_titlesSpinner.SetActive(_waiting);
	}

	public GameObject m_titlesSpinner;
	public TMPro.TextMeshProUGUI m_tokenType;
	public UnityEngine.UI.Image m_tokenImage;
	
	static string[] s_currentRoles;

	public static bool DisableAuthentication { get; set; }

	public static void DownloadSprite(string _url, System.Action<Sprite> _cb) {
		Me.StartCoroutine(Me.Co_DownloadSprite(_url, _cb));
	}
	IEnumerator Co_DownloadSprite(string _url, System.Action<Sprite> _cb) {
		UnityEngine.Networking.UnityWebRequest request = UnityEngine.Networking.UnityWebRequestTexture.GetTexture(_url);
		yield return request.SendWebRequest();
		if (request.isNetworkError || request.isHttpError) {
			Debug.LogError(request.error);
			_cb(null);
		} else {
			var data = request.downloadHandler.data;
			if (data[0] == 'G' && data[1] == 'I' && data[2] == 'F')
			{
				yield return UniGif.GetTextureListCoroutine(data,
					(texList, loopCount, width, height) =>
					{
						var tex = texList[0].m_texture2d;
						var sprite = Sprite.Create(tex, new Rect(0, 0, tex.width, tex.height), new Vector2(0.5f, 0.5f), 100.0f);
						_cb(sprite);
					}, FilterMode.Bilinear, TextureWrapMode.Clamp, 1
				);
			}
			else
			{
				var tex = ((UnityEngine.Networking.DownloadHandlerTexture)request.downloadHandler).texture;
				var sprite = Sprite.Create(tex, new Rect(0, 0, tex.width, tex.height), new Vector2(0.5f, 0.5f), 100.0f);
				_cb(sprite);
			}
		}
	} 
	
	bool m_holdLoadingScreen = false;
	public bool HoldLoadingScreen {
		set {
			if (m_holdLoadingScreen != value) {
				m_holdLoadingScreen = value;
				if (!value) StartCoroutine(Co_EndLoadingScreen(5,() => LoadingComplete())); 
			}
		}
	}
	
	int m_mainCameraLayerMask = 0;
	IEnumerator Co_EndLoadingScreen(int _framesBeforeFade, System.Action _finishedCb) {
		LoadCompleteEarly = true;
		
		while (m_titlePage.activeSelf) yield return null;

		Time.timeScale = s_timeScale;

		if (HasLoadedFromSeed == false && IsInTownView && IsPossessing == false)
			IntroControl.Me.StartShortCameraZoom(1.75f);
		
		UIManager.Me.m_titleScreenEndClouds.SetActive(true);
		var endTrigger = UIManager.Me.m_titleScreenEndClouds.transform.Find("Trigger").gameObject;
		while (endTrigger.activeSelf) yield return null;
		Show3DLogo(false);
		LoadCompletePreFadeIn = true;

		//if (HasLoadedFromSeed)
		//	CameraPanNode.StartCameraPanSequence("Seed");

		if (m_holdLoadingScreen) 
            yield break;
		while (--_framesBeforeFade >= 0) 
            yield return null;
		
		var uiCG = UIManager.Me.GetComponent<CanvasGroup>();
		uiCG.alpha = 0;

		float t = 1;
		const float c_fadeTime = .5f * 2;
		while (t > 0) {
			t -= Time.unscaledDeltaTime / c_fadeTime;
			m_titleScreen.GetComponent<CanvasGroup>().alpha = t;
			yield return null;
		}
		ShowLoading(false);
		if (_finishedCb != null) 
            _finishedCb();
		
		for (float ut = -4; ut < 1; ut += Time.unscaledDeltaTime / .2f) {
			uiCG.alpha = Mathf.Clamp01(ut);
			yield return null;
		}
		uiCG.alpha = 1;
	}
	
	void Show3DLogo(bool _show = true)
	{
		if (_show)
		{
			CameraRenderSettings.Me.m_titleObject3D.SetActive(true);
			if (m_mainCameraLayerMask == 0)
				m_mainCameraLayerMask = Camera.main.cullingMask;
			Camera.main.cullingMask = 1 << CameraRenderSettings.Me.m_titleObject3D.layer;
		}
		else
		{
			CameraRenderSettings.Me.m_titleObject3D.SetActive(false);
			if (m_mainCameraLayerMask != 0)
				Camera.main.cullingMask = m_mainCameraLayerMask; 
		}
	}
	
	IEnumerator Co_StartLoadingScreen(int _framesBeforeFade = 0, System.Action _readyCb = null) {
		var uiCG = UIManager.Me.GetComponent<CanvasGroup>();
		for (float ut = 1; ut >= 0; ut -= Time.unscaledDeltaTime / .2f) {
			uiCG.alpha = ut;
			yield return null;
		}
		uiCG.alpha = 0;
		if (_readyCb != null) _readyCb();

		Show3DLogo();

		m_titleScreen.GetComponent<CanvasGroup>().alpha = 0;
		m_titleLoadingProgress.Progress = 0;
		ShowLoading(true);
		float t = 0;
		const float c_fadeTime = .5f;
		while (t < 1) {
			t += Time.unscaledDeltaTime / c_fadeTime;
			m_titleScreen.GetComponent<CanvasGroup>().alpha = t;
			yield return null;
		}
		m_titleScreen.GetComponent<CanvasGroup>().alpha = 1;
	}
	
	void DEBUG_PostLoad() {
		/*foreach (var kvp in ReactObjectBase.s_idLookup) {
			Debug.LogError($"ROB:{kvp.Key} {kvp.Value.GetType()} {kvp.Value.name}");
		}*/
	}
		
	public void PostProcessFactory(NGCommanderBase _factory) {
		// Fix up old-style factory data
		// TODO  - once it's safe to make deep changes to NG code this should be changed to use state structures directly
		foreach(var ng in m_state.m_oldStyleData.m_saveNGBuildings) {
			if (ng.m_UID == _factory.m_linkUID) {
				_factory.SetupFromLoad(ng);
				_factory.Load(_factory.m_stateData.m_typeSpecificData ?? "");
				_factory.SetupFromLoad();
				break;
			}
		}
	}

	public void LateInitialise() {
		GlobalData.Me.UpdateNavGrid();
		//update init money from saved data here
		m_camera.transform.position = m_state.m_cameraPosition;
		if (m_state.m_cameraEulers.sqrMagnitude > 0) {
			m_camera.transform.eulerAngles = m_state.m_cameraEulers;
		}
		
		DataReady = true;
	}
	
	private static DebugConsole.Command s_animAllUnlocksCmd = new ("animallunlocks", _s => Utility.SetOrToggle(ref Me.m_animatePreviousUnlocks, _s), "Animate all unlocked blocks even if they were previously unlocked", "<bool>");
	private bool m_animatePreviousUnlocks = false;
	public void AddBlockUnlockAnimation(string _id, Vector3 _unlockLocation)
	{
		UnlockDrawersForBlock(_id);
		if (m_animatePreviousUnlocks == false && IsUnlocked(_id)) return;
		AddUnlock(_id);
		var info = NGBlockInfo.GetInfo(_id);
		if (info != null && info.m_hideFromUser == false && _id.StartsWith("MABase", StringComparison.InvariantCultureIgnoreCase) == false)
			DesignTableManager.Me.AddToUnlockShelf(_id, _unlockLocation);
	}

	public static bool IsUnlocked(string _blockId) {
		// non-zero (negative = infinite)
		return GetUnlockCount(_blockId) != 0;
	}

	private static DebugConsole.Command s_startpancmd = new("startpan", _s => CameraPanNode.StartCameraPanSequence(_s));

	private static DebugConsole.Command s_unlockblock = new DebugConsole.Command("giveunlock", _s =>
	{
		var all = _s.Split('|', StringSplitOptions.RemoveEmptyEntries);
		foreach (var s in all)
		{
			var bits = s.Split(',');
			int value = 1;
			if (bits.Length > 1 && int.TryParse(bits[1], out var v)) value = v;
			AddUnlock(bits[0], value);
		}
	});
	
	private static Dictionary<string, int> s_deferredUnlocks = new Dictionary<string, int>();
	private static bool s_deferredUnlocksActive = false;

	public static void ClearDeferredUnlocks()
	{
		s_deferredUnlocks.Clear();
	}
	public static void BeginDeferredUnlockOperations()
	{
		s_deferredUnlocks.Clear();
		s_deferredUnlocksActive = true;
	}

	public static void EndDeferredUnlockOperations()
	{
		if (s_deferredUnlocksActive)
		{
			s_deferredUnlocksActive = false;
			var adds = new Dictionary<string, int>();
			var consumes = new Dictionary<string, int>();
			foreach (var kvp in s_deferredUnlocks)
			{
				if (kvp.Value > 0) adds[kvp.Key] = kvp.Value;
				else if (kvp.Value < 0) consumes[kvp.Key] = -kvp.Value;
			}
			if (adds.Count > 0)
			{
				AddUnlocks(adds);
			}
			if (consumes.Count > 0)
			{
				AddUnlocks(consumes, true);
			}
		}
		s_deferredUnlocks.Clear();
	}
	
	public static event System.Action UnlocksChanged;
	public static bool s_debugUnlockDraws = false;
	private static DebugConsole.Command s_unlockDraws = new ("toggledraws", _s => s_debugUnlockDraws = !s_debugUnlockDraws);
	public static void UnlockDraw(MADrawerInfo _info, bool _flag)
	{
		if(_info == null)
		{
			Debug.LogError($"Invalid drawer to unlock");
			return;
		}
		
		Me.m_state.m_designTableDetails.m_unlockedDrawers[_info.m_drawerIndex] = _flag;
	}
	
	public static void UnlockDrawersForBlock(string _blockID)
	{
		var blockInfo = NGBlockInfo.GetInfo(_blockID);
		if (blockInfo != null)
		{
			var drawerInfos = MADrawerInfo.GetInfos(blockInfo.m_mADrawerInfos);
			foreach (var drawerInfo in drawerInfos)
				UnlockDraw(drawerInfo, true);
		}
	}

	public static void AddUnlock(string _blockId, int _value = -1)
	{
		if (Me == null) return;
		if (s_deferredUnlocksActive)
		{
			s_deferredUnlocks.AddCount(_blockId, _value);
			UnlocksChanged?.Invoke();
			return;
		}
		var block = NGBlockInfo.GetInfo(_blockId);
		if (block != null)
		{
			if (s_showUnlocks && (Me.m_state.m_designTableDetails.m_unlockedDrawers.TryGetValue(block.m_mADrawerInfos, out var isUnlocked) == false || isUnlocked == false))
				Debug.LogError($"Unlock {_blockId} unlocked set {block.m_mADrawerInfos}");
		}
		var newVal = Me.m_state.m_unlocks.AddValue(_blockId, _value, AddValuesNegInf);
		if (_value >= 0 && Mathf.Abs(newVal - _value) < .001f) // non-negative value and value equals new value means new unlock
			UnlocksChanged?.Invoke();
	}

	public static int GetUnlockCount(string _id)
	{
		int count = Me.m_state.m_unlocks.GetValue(_id);
		if (s_deferredUnlocksActive && count > -1 && s_deferredUnlocks.TryGetValue(_id, out int addition)) count += addition;
		return count;
	}

	public static int AddValueToCurrent(int _add, int _current)
	{
		if (_current < 0) return -1; // current is infinity, return infinity
		if (_add + _current < 0) return 0; // new total goes negative, something went wrong, don't allow it to go to infinity
		return _add + _current;
	}
	public static int AddValuesNegInf(int _a, int _b)
	{
		if (_a < 0 || _b < 0) return -1;
		return _a + _b;
	}
	public static int AddValues(int _a, int _b)
	{
		return _a + _b;
	}

	private static DebugConsole.Command s_showUnlocksCmd = new ("showunlocks", _s => Utility.SetOrToggle(ref s_showUnlocks, _s));
	private static bool s_showUnlocks = false;
	
	public static void AddUnlocks(Dictionary<string, int> _blockValues, bool _isConsume = false)
	{
		foreach (var kvp in _blockValues)
		{
			if (s_showUnlocks && _isConsume == false) Debug.LogError($"Unlock {kvp.Key}");
			
			if (_isConsume)
				ConsumeUnlock(kvp.Key, kvp.Value);
			else
				AddUnlock(kvp.Key, kvp.Value);
		}
	}
	
	public static bool ConsumeUnlock(string _blockId, int _value = 1)
	{
		if (_blockId == null) return false;
		if (s_deferredUnlocksActive)
		{
			s_deferredUnlocks.AddCount(_blockId, -_value);
			UnlocksChanged?.Invoke();
			return true;
		}
		
		if (!Me.m_state.m_unlocks.ContainsKey(_blockId)) return false;
		if (Me.m_state.m_unlocks.AddValue(_blockId, -_value, AddValueToCurrent) == 0)
		{
			Me.m_state.m_unlocks.RemoveKey(_blockId);
			UnlocksChanged?.Invoke();
		}
		return true;
	}

	// return all the types of things that can be unlocked (independent of what's available to unlock to any particular player)
	public static IEnumerable<string> GetAllUnlockableIds()
	{
		foreach (var blockID in NGBlockInfo.s_allBlocks.Keys)
			yield return blockID;
		foreach (var paint in PaintPotData.s_entries)
			yield return paint.m_name;
		foreach (var pattern in PatternData.s_entries)
			yield return pattern.m_name;
		foreach (var sticker in StickerData.s_entries)
			yield return sticker.m_name;
	}

	public const bool c_buildingPartsNonConsumable = true;
	public static bool IsConsumableUnlock(string _id)
	{
		// Design have requested all unlocks are infinite / non-consume
		if (c_buildingPartsNonConsumable)
			return false;
		var blockInfo = NGBlockInfo.GetInfo(_id);
		return blockInfo != null && blockInfo.Type == NGBlockInfo.BlockType.Building;
	}
	
//	public bool IsReadyForBusinessFlow() {
	//	foreach (var b in NGManager.Me.m_NGFactoryList) {
	//		if (b.IsProductProducer && b.m_productMade != null && b.m_productMade.HasDesign) {
	//			return true;
	//		}
	//	}
//		return NGTutorialManager.Me.TutorialEnded;
//	}

    /*public string DistrictThatSupportResource(NGCarriableResource _resource)
    {
        foreach (var b in NGManager.Me.FactoryList)
        {
            if (b.OutputIs == _resource)
            {
                return b.GetDistrictName(b.DoorPosInner);
            }
        }
        return "";
    }*/

	public bool DoesTownSupportResource(NGCarriableResource _resource) {
		return true;	// Not using for now
		/*if (_resource.m_name == "RawResourceClay") return true; // clay no longer used so always supported
		// see if we have a building that contains or produces this
		foreach (var b in NGManager.Me.FactoryList) {
			if (b.IsWithinOpenDistrict() == false) continue;
			if (b.OutputIs == _resource) {
				return true;
			}
			if (b.OutputIs.IsAny && (b.InputsAre.Find(_resource)?.Stock ?? 0) > 0)
				return true;
		}
		return false;*/
	}

	public MAVehicle GetMAVehicle(int _id) {
	    foreach (var d in NGManager.Me.MAVehicles) if (d.m_ID == _id) return d;
	    return null;
	}

	Dictionary<int, NGCommanderBase> m_linkToCommander = new Dictionary<int, NGCommanderBase>();
	public T GetNGCommander<T>(int _id) where T : NGCommanderBase {
		if (m_linkToCommander.ContainsKey(_id) == false) return null;
		return m_linkToCommander[_id] as T;
	}

	public T GetMACommander<T>(int _id) where T : MABuilding
	{
		if(_id < 0) return null;
		if (m_linkToCommander.ContainsKey(_id) == false) return null;
		return m_linkToCommander[_id] as T;
	}
	IEnumerator CreateBuildings() {
		GlobalData.Me.BeginDeferTerrainHeightsUpdate();
		int buildingsCompleted = 0;
		for (int i = 0; i < m_state.m_buildings.Count; ++i) {
			var b = m_state.m_buildings[i];
			if (b.IsSetPiece)
			{
				var setPiece = BuildingPlacementManager.CreateSetPiece(b);
				if(setPiece == null)
				{
					m_state.m_buildings.RemoveAt(i);
					--i;
				}
				else
					++buildingsCompleted;
				continue;
			}
			if (b.m_buildingDesign == null)
			{
				m_state.m_buildings.RemoveAt(i);
				--i;
				continue;
			}
#if UNITY_EDITOR
			if (HasLoadedFromSeed && b.m_shouldLoadSeedData) Debug.LogError($"<color=yellow>Building {b.m_id} is loading component data in seed mode</color>");
#endif
			var go = BuildingPlacementManager.CreateBuilding(b, false, (_g) => {
				++buildingsCompleted;
			}, true, true);
			if (go != null) {
				m_linkToCommander[b.m_id] = go.GetComponent<NGCommanderBase>();
				if (m_granularLoading) for (var complete = buildingsCompleted;  complete == buildingsCompleted; ) yield return null;
			} else {
				m_state.m_buildings.RemoveAt(i);
				--i;
			}
		}
		while (buildingsCompleted < m_state.m_buildings.Count) {
			yield return null;
		}
		GlobalData.Me.EndDeferTerrainHeightsUpdate();
	}
	
	public void LinkBuildingId(int _newId, NGCommanderBase _commander)
	{
		if(_commander == null || _newId < 0) return;
		m_linkToCommander[_newId] = _commander;
	}

	private void ReseatBuildings(bool _noNavUpdate)
	{
		for (int i = 0; i < NGManager.Me.m_maBuildings.Count; ++i)
			BuildingPlacementManager.ReapplyFlatten(NGManager.Me.m_maBuildings[i], _noNavUpdate);
	}

	private void CreateLocks()
	{
		for (int i = 0; i < m_state.m_locks.Count; ++i)
		{
			MALock.Create(m_state.m_locks[i]);
		}
		for (int i = 0; i < m_state.m_keyContainers.Count; ++i)
		{
			MAKeyDrag.Create(m_state.m_keyContainers[i]);
		}
	}
	

	private void CreateVehicles()
	{
		if(HasLoadedFromSeed) m_state.m_vehicles.Clear();
		MAVehicleControl.Me.LoadSavedVehicles(m_state.m_vehicles);
	}

	IEnumerator CreateCreatures()
	{
		for(int i = 0; i < m_state.m_heroes.Count; ++i)
		{
			MACreatureControl.Me.SpawnCharacterWithData(m_state.m_heroes[i]);
			if (m_granularLoading) yield return null;
		}
		for(int i = 0; i < m_state.m_creatures.Count; ++i)
		{
			MACreatureControl.Me.SpawnCharacterWithData(m_state.m_creatures[i]);
			if (m_granularLoading) yield return null;
		}
		for(int i = m_state.m_minorCharacterTypes.Count - 1; i >= 0; i--)
		{
			if (m_state.m_minorCharacterTypes[i] == null)
			{
				m_state.m_minorCharacterTypes.RemoveAt(i);
				continue;
				;
			}

			MACreatureControl.Me.SpawnCharacterWithData(m_state.m_minorCharacterTypes[i]);
		}
	}
	
	IEnumerator CreatePeople()
	{
		for (int i = m_state.m_people.Count - 1; i >= 0; i--)
		{
			var person = m_state.m_people[i];
			if(person == null || person.m_workerInfo.IsNullOrWhiteSpace() || person.m_state == (int)NGMovingObject.STATE.MA_DEAD)
			{
				m_state.m_people.RemoveAt(i);
				continue;
			}

			const float mapBounds = 800.0f;
			
			if (person.m_pos.x < -mapBounds || person.m_pos.z < -mapBounds || person.m_pos.x > mapBounds || person.m_pos.z > mapBounds)
			{
				Debug.LogWarning("GameManager::CreatePeople: removing out of bounds person: " + person.m_workerInfo);
				m_state.m_people.RemoveAt(i);
				continue;
			}
			
			var workerInfo = MAWorkerInfo.GetInfo(person.m_workerInfo);
			if(workerInfo == null)
			{
				m_state.m_people.RemoveAt(i);
				continue;
			}
			
			if(HasLoadedFromSeed && (workerInfo.WorkerType == MAWorkerInfo.WorkerTypeEnum.Player || 
									workerInfo.WorkerType == MAWorkerInfo.WorkerTypeEnum.Tourist ||
									workerInfo.WorkerType == MAWorkerInfo.WorkerTypeEnum.QuestGiver))
			{
				m_state.m_people.RemoveAt(i);
				continue;
			}
        
			person.Create();
			if (m_granularLoading) yield return null;
		}
	}

	private void PostLoadMovingObjects()
	{
		foreach(var vehicle in NGManager.Me.m_maVehicles)
		{
			vehicle.PostLoad();
		}
		
		foreach(var worker in NGManager.Me.m_MACharacterList)
		{
			worker.PostLoad();
		}
		foreach(var hero in NGManager.Me.m_MAHeroList)
		{
			hero.PostLoad();
		}
		var groups = new Dictionary<int, GroupState>();
		foreach(var creature in NGManager.Me.m_MACreatureList)
		{
			creature.PostLoad();
			
			var state = creature.CharacterGameState;
			if (state != null)
			{
				int id = state.m_groupId;
				if (id >= 0)
				{
					if (!groups.ContainsKey(id))
						groups[id] = new GroupState();
					groups[id].characters.Add(new MACharacterGroupBehaviour.GroupCharacterInfo
						{
							character = creature,
							stationaryPos = state.m_groupStationaryPosition
						});
					groups[id].patrolPos = state.m_groupPatrolPosition;
					groups[id].patrolRadius = state.m_groupPatrolRadius;
				}
			}
		}
		
		var prefab = Resources.Load<GameObject>("_Prefabs/Characters/CharacterGroupBehaviour");
		foreach(var group in groups)
		{
			var go = Instantiate(prefab, Vector3.zero, Quaternion.identity, GlobalData.Me.m_characterHolder);
			var cgb = go.GetComponent<MACharacterGroupBehaviour>();
			MACharacterGroupBehaviour.GroupPatrollingInfo patrollingInfo = null;
			var groupState = group.Value;
            if (groupState.patrolRadius > 0f)
                patrollingInfo = new MACharacterGroupBehaviour.GroupPatrollingInfo
                {
                    centerPos = groupState.patrolPos,
                    radius = groupState.patrolRadius
                };
			cgb.SetupGroup(groupState.characters, patrollingInfo, group.Key);
		}
	}

	void PopulateStoryCharacters()
	{
	//	c_characterManager.InitAllCharacters();
	}
	
	private void ForcePlayerDetailsGUI(System.Action _cb = null, Transform _parent = null) {
#if true//_DEMO_MODE
		_cb(); return; // DEMO - no player details
#endif
		NGPlayer.Me.PlayerName = "";
		NGPlayer.Me.PlayerFirstname = "";
		NGPlayer.Me.PlayerSurname = "";
		NGPlayer.Me.m_companyName = "";
		NGPlayer.Me.m_missionStatement = "";
		PlayerDetailsGUI.Create(false, _cb, _parent);
	}
	
	static DebugConsole.Command s_showplayerdetails = new ("showplayerdetails", _s => {
		PlayerDetailsGUI.Create(false);
	});

	private static DebugConsole.Command s_spawnPointActivate = new DebugConsole.Command("triggerSpawn", _s => 
	{
		if(GlobalData.Me == null) return;
		if(int.TryParse(_s, out int id))
		{
			MASpawnPoint[] maSpawnPoints = GlobalData.Me.m_spawnPointHolder.GetComponentsInChildren<MASpawnPoint>();
			foreach(var spawnPoint in maSpawnPoints) if(spawnPoint.ID == id) spawnPoint.MacroTrigger();
		}
	});

	public void LoadingComplete()
	{
		NGManager.NGSettings.LoadInfo();
		LoadComplete = true;

		Conveyor.SearchAllFactories();

		// initial cash now comes from server through knack data - NGSettings.SetStartingMoney
		
        m_cameraIsInTown = true;
		
		GameStateTimer.TimeStampLastLoad = DateTime.Now.Ticks;

		AnalyticsManager.Me.LogEvent(new AnalyticsEvent("Test").AddParamList(EventFactory.PlayerParameters));

		if (s_visitWorldHadError)
		{
			s_visitWorldHadError = false;
			Utility.ShowDialog("An Error Occurred", "Something went wrong when visiting, please try again", true, "Ok", null, null);
		}
		
		DayNight.Me.RegisterRestartDayCallback(BCChopObject.RestartDay);
	}

	public bool IsOKToPlayUISound()
	{
		if (AudioClipManager.Me == null)
			return false;

		return true;
	}
	public bool PlaySoundIfOkay(string _audio)
	{
		if (_audio.IsNullOrWhiteSpace()) return false;
		if (IsOKToPlayUISound() == false) return false;
		// if (NGTutorial.m_audioClip != 0)
		// {
		// 	if(AudioClipManager.Me.IsPlaying(NGTutorial.m_audioClip))
		// 		AudioClipManager.Me.StopVO(NGTutorial.m_audioClip);
		// }
		// string clipName = NGTutorialManager.Me.DecodeAudio(_audio);
		// string clip = "PlaySound_" + clipName;
		// NGTutorial.m_audioClip = AudioClipManager.Me.PlayVO(clip);
		// NGTutorial.m_lastAudioPlayed = _audio;
		return true;
	}
	public string PlaySoundIfCan(string _audio)
	{
		if (_audio.IsNullOrWhiteSpace()) return null;
		if (IsOKToPlayUISound() == false) return null;
	
		string clipName = DecodeAudio(_audio);
		string clip = "PlaySound_" + clipName; 

		return clip;
	}
	public static string   DecodeAudio(string _clip)
    {
        NGCommanderBase cb;
        //NGTutorialInterface.TutorialDataType t;

        string output = "";
        var parts = _clip.Split('[');
        if (parts.Length == 1)
        {
            output = _clip;
        }
        else
        {
            output = parts[0];
            for (int i = 1; i < parts.Length; i++)
            {
                var p = parts[i];
                var subPart = p.Split(']');
                // if (Enum.TryParse(subPart[0], true, out t) != false)
                // {
                // }
                if (subPart.Length > 1)
                {
                    output += subPart[1];
                }
            }
        }

        return output;
    }
	public bool IsOKToPlayHUDSound()
	{
		if (AudioClipManager.Me == null)
			return false;
		if (DesignTableManager.Me.IsEnabled)
			return false;

		return true;
	}

	public bool IsOKToPlayTownSound()
	{
		if (AudioClipManager.Me == null)
			return false;
		if (DesignTableManager.Me.IsEnabled)
			return false;

		return true;
	}

	public bool IsOKToPlayDesignTableSound()
	{
		if (AudioClipManager.Me == null)
			return false;
		if (!DesignTableManager.Me.IsEnabled)
			return false;

		return true;
	}

	GameObject m_runningWaterAudioHolder;
	public void TurnOnCountrysideAmbientSounds()
    {
        AudioClipManager.Me.SetSoundState("Town");
        if (GameManager.Me.IsOKToPlayTownSound())
        {
            if (m_ambientSoundWater == 0)
            {
	            if (m_runningWaterAudioHolder == null)
		            m_runningWaterAudioHolder = new GameObject("RunningWaterAudioHolder");
	            m_ambientSoundWater = AudioClipManager.Me.PlaySound("PlaySound_Brook", m_runningWaterAudioHolder, true);
	            AudioClipManager.Me.PlaySound("PlaySound_Stratosphere", gameObject, true);
            }
        }
    }
	
	public void StartDemoSave(int _index) {
		// demos always start from scratch, so delete any old user for this device.
		DeleteSaveDataFromServer((bool success) => {
			if(success) {
				if (_index != 0)
					m_startFromRestoredSave = $"demo{_index}";
				LoadSaveAndStart();
			}
		});
	}

	private const int c_visitSaveIndex = 5;
	static string[] s_saveNames = new string[] {"save.dat", "save2.dat", "save3.dat", "save4.dat", "save5.dat", "saveVisit.dat"};
	string SavePath => Application.persistentDataPath + "/" + s_saveNames[m_selectedSaveSlot];

	string GetSavePath(int _index) { return Application.persistentDataPath + "/" + s_saveNames[_index]; }

	public void CopySaveFromResourceAndLoad(string _fromResource, int _index)
	{
		m_selectedSaveSlot = _index;
		m_currentSlotState = SaveSlotState.Existing;
		var data = Resources.Load<TextAsset>(_fromResource);
		System.IO.File.WriteAllBytes(SavePath, data.bytes);
		EndTitles();
	}

	bool SaveExists() {
		if (!IsVerified2) return false;
		return System.IO.File.Exists(SavePath);
	}

	void DeleteSaveDataFromServer(Action<bool> callback) { StartCoroutine(DeleteSaveDataFromServerAsync(callback)); }
	IEnumerator DeleteSaveDataFromServerAsync(Action<bool> callback)
	{
		m_currentSlotState = SaveSlotState.Indeterminate;
		RefreshStartButton();
		
		if (System.IO.File.Exists(SavePath)) {
			System.IO.File.Delete(SavePath);
		}
		yield return null;
		RefreshStartButton();
		callback?.Invoke(true);
	}

	IEnumerator RestoreSaveFromResource(string _name) {
		var t = ResManager.Load<TextAsset>(_name);
		if (t != null && t.text != null) // && t.bytes != null
		{
			HasLoadedFromSeed = _name.StartsWith("seed") || _name.StartsWith("Seed");
			HasLoadedFromRestoredSave = !_name.StartsWith("demo");
			//System.IO.File.WriteAllBytes(SavePath, t.bytes);
			string saveData = t.text;
			
			ReadSaveDataInternal(saveData);
			MASpawnPoint.ReadSeedSave();
			//MAQuestSpawnPoint.ReadSeedSave();
		}
		yield break;
	}
	public static bool IsPathSeed = false;
	public static string SeedName => $"seedmoa{NGManager.Me.m_seedSuffix}";
	public static string AdvancedName => NGManager.Me.m_advancedName;

#if UNITY_EDITOR
    //*********** Cache Enabled Menu Toggle
    private const string UseLocalSaveName = "UseLocalSave";
    private const string UseLocalSaveMenuName = "22Cans/Save/Use local save";
    private const string ForceInGameSaveName = "22Cans/Save/Force-Save Game";
    public static bool UseLocalSave
    {
	    get => EditorPrefs.GetBool(UseLocalSaveName, false);
	    private set { EditorPrefs.SetBool(UseLocalSaveName, value); }
    }

    [MenuItem(UseLocalSaveMenuName)]
    private static void ToggleLocalSave()
    {
        UseLocalSave = !UseLocalSave;
    }
    [MenuItem(UseLocalSaveMenuName, true)]
    private static bool ToggleLocalSaveValidate()
    {
	    Menu.SetChecked(UseLocalSaveMenuName, UseLocalSave);
	    return true;
    }

    [MenuItem(ForceInGameSaveName, false, -50)]
    private static void ForceInGameSave()
    {
	    if (Me != null && Me.SaveEnabled && Me.LoadComplete)
	    {
		    Debug.LogError("Menu - Force Save");
			Me.ForceSave();//MOA
		    //Me.ForceSaveAndUpload();//legacy debug
	    }
    }
    
    [MenuItem(ForceInGameSaveName, true)]
    private static bool ForceInGameSaveValidate()
    {
	    return Me != null && Me.SaveEnabled && Me.LoadComplete;
    }
#else
    public static bool UseLocalSave = false;
#endif

	private void CreateIntroDialogue(Action _onClose)
	{
		if (IsClicker)
		{
			if (_onClose != null) _onClose();
			return;
		}
		var introDialogue = GameObject.Instantiate(m_introDialoguePrefab, m_titleScreen.transform, false);
		bool useCallback = true;
		introDialogue.Show();
		introDialogue.onClose += () => { m_introDialogue = null; if(useCallback && _onClose != null) _onClose();  };
		introDialogue.AddButton("Read Design Notes", _ped =>
		{
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			useCallback = false;
			introDialogue.Close();			
			CreateDesignNotesDialogue(() => { if(_onClose != null) _onClose(); }, m_titleScreen.transform);
		});
		introDialogue.AddButton(LocalizeKnack.TranslateLocalisedString("Continue"), _ped => {
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			introDialogue.Close(); });
	}

	public void CreateDesignNotesDialogue(Action _onClose, Transform _parent = null)
	{
		var dialogue = GameObject.Instantiate(m_designNotesDialoguePrefab, _parent == null ? CurrentCanvas : _parent, false);
		dialogue.Show();
		dialogue.onClose += _onClose;
	}

	public NotificationUIController CreateEndGameDialogue(string _title, string _message, string _feedbackURI, Action _onClose, Transform _parent = null)
	{
		var dialogue = Utility.ShowDialog(_title, _message, true, "Feedback", null,
		_button =>
		{
			if (_button == 0) Application.OpenURL(_feedbackURI);
			if (_button == -1) _onClose();
		});
		dialogue.transform.SetParent(_parent == null ? CurrentCanvas : _parent, false);
		/*var dialogue = GameObject.Instantiate(m_outroDialoguePrefab, _parent == null ? CurrentCanvas : _parent, false);
		dialogue.Show();
		dialogue.onClose += _onClose;*/
		return dialogue;
	}

	NotificationUIController m_introDialogue;
	public NotificationUIController m_introDialoguePrefab;
	public NotificationUIController m_designNotesDialoguePrefab;
	public NotificationUIController m_outroDialoguePrefab;

	IEnumerator ReadSaveData() {
		HasLoadedFromSeed = false;
		LoadAbandoned = false;
		
		if (m_forceLoadedFromSeed)
		{
			HasLoadedFromSeed = true;
			m_forceLoadedFromSeed = false;
		}

		string saveData = null;
		bool abort = false;

		if (!IsVisitingInProgress && m_currentSlotState == SaveSlotState.New)
		{
			//Debug.LogError("no save game data!");
		}
        else
		{
			string savePath = SavePath;
			if (System.IO.File.Exists(savePath))
				saveData = ReadSaveDataFile(savePath);//System.IO.File.ReadAllText(savePath);
			else
				Debug.LogError("file not found " + savePath);
        }

		if (abort)
		{
			LoadAbandoned = true;
			yield break;
		}
		if (saveData != null)
			ReadSaveDataInternal(saveData);
		else
			;//Debug.LogError("save data is null");

		if (saveData == null || !m_state.Confirm())
		{
			if (IsVisitingInProgress)
			{
				ReturnFromVisiting(true);
				yield break;
			}

			// if saveData is an empty json "{}" (as opposed to null) the save doesn't exist yet but the player has been created
			// in this case we need to use the seed save but -not- delete the player on the server
			if (saveData != null && saveData.Length > 2)
			{
				// bad save, delete it
				bool finishedDeleting = false;
				yield return DeleteSaveDataFromServerAsync(_b => finishedDeleting = true);
				while (!finishedDeleting) yield return null;
			}
			
			m_state = new GameState();
			string file = m_startFromRestoredSave;
			//if(file == c_startFromAdvancedSave)
			//	file = AdvancedName;
			yield return RestoreSaveFromResource(file ?? SeedName);
			// ReadSaveDataInternal(); // restore handles this now

			/* ** Taking out intro notes for now **
			 bool doneIntroDialogue = false;

			CreateIntroDialogue(() => { doneIntroDialogue = true; });

			while (!doneIntroDialogue)
				yield return null;*/

			bool done = false;
			ForcePlayerDetailsGUI(() => { done = true; }, m_titleScreen.transform);
			while (!done)
				yield return null;
		}
		m_startFromRestoredSave = null;
	}
	void ReadSaveDataInternal(string json) {
		if (!IsVerified1) return;
		m_state = new();
		m_state.Invalidate();
		JsonUtility.FromJsonOverwrite(json, m_state);
		m_state.PostLoad();
	}
	const float c_secondsBetweenUploads = 60;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	private static bool s_bypassTpp = true;
	private static DebugConsole.Command s_bypasstppcmd = new DebugConsole.Command("bypasstpp", _s => Utility.SetOrToggle(ref s_bypassTpp, _s));
	private string UploadSuffix => s_bypassTpp ? "?bypassTpp=true" : "";
#else
	private string UploadSuffix => "";
#endif

	private string GetSaveData(string _fileToWrite = null)
	{
		if (m_inputRecording)
		{
			return InputRecorderSaveData();
		}
		
		m_state.Prepare();
		UpdateOldStyleSaveStructures();

		var json = JsonUtility.ToJson(m_state, prettyPrint: true);
		if (!IsVerified1) return "";

#if UNITY_EDITOR || DEVELOPMENT_BUILD
		// In Editor, we'll keep the save data on disk for easier debugging.
		if (!IsVisitingInProgress)
#else
		if (!IsVisitingInProgress && Serverless)
#endif
		{
			if (m_state.m_inputRecording.Valid == false)
				WriteSaveDataFile(json, _fileToWrite);
		}

		return json;
	}

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	private static bool s_saveAsJson = true;
	private static DebugConsole.Command s_saveAsJsonCmd = new DebugConsole.Command("saveasjson", _s => Utility.SetOrToggle(ref s_saveAsJson, _s));
	private static bool SaveAsJson => s_saveAsJson;
	private static bool AllowReadAsJson => true;
#else
	private static bool SaveAsJson => false;
	private static bool AllowReadAsJson => true; // for final tests
#endif

	private static int s_header => 0x30a755;
	private void WriteSaveDataFile(string _json, string _fileToWrite = null)
	{
		var path = _fileToWrite ?? SavePath;
		ThreadPool.QueueUserWorkItem(_ =>
		{
			if (SaveAsJson)
			{
				System.IO.File.WriteAllText(path, _json);
				return;
			}
			var bytes = PackSaveToBytes(_json);
			System.IO.File.WriteAllBytes(path, bytes);
		});
	}

	private static byte[] PackSaveToBytes(string _json)
	{
		var bytes = Utility.LZMACompressString(_json);
		return Utility.SimpleXorWithHeader(bytes, s_header, 0x57);
	}

	private static string ReadSaveDataFile(string _s)
	{
		var bytes = System.IO.File.ReadAllBytes(_s);
		var unxor = Utility.SimpleXorRemovingHeader(bytes, s_header, 0x57);
		if (unxor == null)
		{
			if (SaveAsJson || AllowReadAsJson)
				return System.Text.Encoding.UTF8.GetString(bytes);
			return null;
		}
		return Utility.LZMADecompressString(unxor);
	}
	
#if UNITY_EDITOR
	[UnityEditor.MenuItem("22Cans/Save/Encode Save To Release")]
	private static void EncodeSaveToRelease()
	{
		var jsonFile = EditorUtility.OpenFilePanelWithFilters("Choose save file", Application.persistentDataPath, new string[] {"DAT files", "dat", "All files", "*"});
		if (jsonFile == null) return;
		var json = System.IO.File.ReadAllText(jsonFile);
		if (json.Length == 0 || json[0] != '{')
		{
			Debug.LogError($"Save file {jsonFile} is not JSON");
			return;
		}
		var bytes = PackSaveToBytes(json);
		System.IO.File.WriteAllBytes(jsonFile, bytes);
	}

	[UnityEditor.MenuItem("22Cans/Save/Decode Save From Release")]
	private static void DecodeSaveFromRelease()
	{
		var datFile = EditorUtility.OpenFilePanelWithFilters("Choose save file", Application.persistentDataPath, new string[] {"DAT files", "dat", "All files", "*"});
		if (datFile == null) return;
		var json = ReadSaveDataFile(datFile);
		if (json == null)
		{
			Debug.LogError($"Save file {datFile} is not a valid save file");
			return;
		}
		System.IO.File.WriteAllText(datFile, json);
	}
#endif

	public void WriteSaveData() {
		if (!SaveEnabled) return;
		if (!LoadComplete) return;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if(Block.s_overrideBlock.IsNullOrWhiteSpace() == false) return;
#endif

		var json = GetSaveData();
		
		if (Serverless) CheckSaveBeforeQuit();

		m_state.PostSave();
#if PLATFORM_WEBGL
		Application.ExternalEval("_JS_FileSystem_Sync();");
#endif
	}

	private static DebugConsole.Command s_releaseStuff = new DebugConsole.Command("releasestuff", _s =>
	{
		Resources.UnloadUnusedAssets();
		GC.Collect();
	});

	private bool m_haveUploaded = false;

	void UpdateOldStyleSaveStructures() {
		var _s = m_state.m_oldStyleData;
		NGPlayer.Me.m_cash.Save(_s.m_playerData.m_cash);
		NGPlayer.Me.m_royalFavors.Save(_s.m_playerData.m_royalFavors);
		NGPlayer.Me.m_lordsFavors.Save(_s.m_playerData.m_lordsFavors);
		NGPlayer.Me.m_mysticFavors.Save(_s.m_playerData.m_mysticFavors);
		NGPlayer.Me.m_commonersFavors.Save(_s.m_playerData.m_commonersFavors);
		_s.m_saveGameData.m_purchaseData.Clear();
		NGPlayer.Save(ref _s.m_saveGameData);
		NGBusinessDecisionDialog.SaveNFTCards(ref _s.m_saveGameData);
		NGBusinessGiftsPanel.SaveHolder(ref _s.m_saveGameData);
		NGProductInfo.Save(ref _s.m_saveGameData);
		if(NGBusinessDecisionManager.Me) NGBusinessDecisionManager.Me.Save(ref m_state.m_oldStyleData);
		_s.m_pollutionCells = ReactPollutionManager.Me.GetProto();
		DistrictManager.Me.Save(ref m_state.m_oldStyleData);
		ExplodeInteraction.SaveAll();
	}
	void LoadFromOldStyleSaveStructures() {
		if (!HasLoadedFromSeed) {
			NGPlayer.Load(m_state.m_oldStyleData.m_saveGameData);
			NGBusinessDecisionDialog.LoadNFTCards(m_state.m_oldStyleData.m_saveGameData);
			NGBusinessGiftsPanel.LoadHolder(m_state.m_oldStyleData.m_saveGameData);
			ReactPollutionManager.Me.Load(m_state.m_oldStyleData.m_pollutionCells);
			NGPlayer.Me.m_cash.Load(m_state.m_oldStyleData.m_playerData.m_cash);
			NGPlayer.Me.m_royalFavors.Load(m_state.m_oldStyleData.m_playerData.m_royalFavors);
			NGPlayer.Me.m_lordsFavors.Load(m_state.m_oldStyleData.m_playerData.m_lordsFavors);
			NGPlayer.Me.m_mysticFavors.Load(m_state.m_oldStyleData.m_playerData.m_mysticFavors);
			NGPlayer.Me.m_commonersFavors.Load(m_state.m_oldStyleData.m_playerData.m_commonersFavors);
		} else {
			m_state.m_gameTime.m_gameTime = 1;
		}
		NGPlayer.Me.m_runInBackground = true; // GL - until we decide this should be off at any point
		Application.runInBackground = true;

		NGProductInfo.Load(m_state.m_oldStyleData.m_saveGameData);
//		TutorialMaster.Me.Load(m_state.m_oldStyleData.m_saveGameData);
		DistrictManager.Me.Load(m_state.m_oldStyleData);

#if false//UNITY_EDITOR || DEVELOPMENT_BUILD
		//bool isValidStateVehicles = m_state.m_oldStyleData.m_dispatchVehicles.Count > 0; // should always be > 0
		bool isValidStateProductLines = m_state.m_movingObjects.Count == 0 || (NGProductInfo.OwnedProductLines != null && NGProductInfo.OwnedProductLines.Count > 0); // should always have at least one product line, except before we finish onboarding; for our purposes here "have one character" is sufficient 
		if (/*!isValidStateVehicles || */!isValidStateProductLines)
		{
			ShowCriticalError($"Your save has been corrupted by an old bug [{/*(isValidStateVehicles?0:1)|*/(isValidStateProductLines?0:2)}]\nUnfortunately this is non-correctable - you will need to start a new save");
		}
#endif
	}
	
	void LoadMASpawnPoints()
	{
		var spawnPointManagers = MASpawnPointManager.LoadAllSpawnPointLocations();
		foreach (MASpawnPointManager maSpawnPointManager in spawnPointManagers)
		{
			maSpawnPointManager.LoadAll();
		}
		MASpawnPoint.LoadAll(m_state.m_spawnPointData);
	}


	void LoadInteractionPoints()
	{
		MACharacterInteract.LoadAll();
	}

	void LoadMABuildingData()
	{
		MABuilding.LoadAll(m_state.m_buildings);
		BuildingDataLoaded = true;
	}
	
	void LoadMANamedSpawnPointData()
    {
     	NamedPoint.LoadAll();
        NamedPointDataLoaded = true;
    }
	
	void UpdatePickupSync()
	{
	}

	private static NotificationUIController s_ticketConversionUI;
	public static bool ShowingTicketConversion => s_ticketConversionUI != null;
	public static void CloseTicketConversion()
	{
		if (s_ticketConversionUI != null)
		{
			s_ticketConversionUI.Close();
			s_ticketConversionUI = null;
		}
	}
	
	private void OnApplicationPause(bool pauseStatus)
	{
		if (pauseStatus == false && LoadComplete)
		{
			//WallClock.Me.UpdateServerTime();
			//FlagForeground();
		}
	}

	private static bool m_isShuttingDown;
	public static bool IsShuttingDown => m_isShuttingDown;
	private void OnApplicationQuit() { m_isShuttingDown = true; }
#if UNITY_EDITOR
	[InitializeOnLoadMethod]
	private static void InitializeEditorShutdownHook()
	{
		EditorApplication.playModeStateChanged += state => { if (state == PlayModeStateChange.ExitingPlayMode) m_isShuttingDown = true; };
	}

	private static DebugConsole.Command s_captureProfileCmd = new ("profile", _s =>
	{
		float t = floatinv.TryParse(_s, out var f) ? f : 1;
		UnityEngine.Profiling.Profiler.enabled = true;
		UnityEditorInternal.ProfilerDriver .enabled = true;
		UnityEngine.Profiling.Profiler.BeginSample($"Capture for {t} seconds");
		Me.DoAfter(t, () => {
			UnityEngine.Profiling.Profiler.EndSample();
			UnityEditorInternal.ProfilerDriver.enabled = false;
			UnityEngine.Profiling.Profiler.enabled = false;
		});
	});
#endif
	
#if UNITY_EDITOR
	interface IGizmoEntry
	{
		void Draw();
	}
	class GizmoEntryLine : IGizmoEntry
	{
		Vector3 m_start, m_end;
		Color m_color;

		public GizmoEntryLine(Vector3 _start, Vector3 _end, Color _clr)
		{
			m_start = _start; m_end = _end; m_color = _clr;
		}

		public void Draw()
		{
			Gizmos.color = m_color;
			Gizmos.DrawLine(m_start, m_end);
		}
	}
	class GizmoEntryCone : IGizmoEntry
	{
		Vector3 m_tip, m_base;
		float m_angle;
		Color m_color;

		public GizmoEntryCone(Vector3 _tip, Vector3 _base, float _angle, Color _clr)
		{
			m_tip = _tip;
			m_base = _base;
			m_angle = _angle;
			m_color = _clr;
		}

		public void Draw()
		{
			var basePos = m_base;
			var dir = m_tip - m_base;
			var length = dir.magnitude;
			dir /= length;
			Gizmos.color = m_color;
			DrawWireCone(basePos, dir, length, m_angle, 20); 
		}

		public float height = 5f;
		public float angle = 30f;
		public int resolution = 20; // Number of lines to draw the base circle

		private void DrawWireCone(Vector3 _position, Vector3 _direction, float _height, float _angle, int _resolution)
		{
			float radius = _height * Mathf.Tan(_angle * Mathf.Deg2Rad / 2);
			Vector3 tip = _position + _direction * _height;
			for (int i = 0; i < _resolution; i++)
			{
				float theta = 2 * Mathf.PI * i / _resolution;
				Vector3 basePoint = _position + new Vector3(Mathf.Cos(theta) * radius, Mathf.Sin(theta) * radius, 0);
				Gizmos.DrawLine(basePoint, tip);
			}
			for (int i = 0; i < _resolution; i++)
			{
				float theta1 = 2 * Mathf.PI * i / _resolution;
				float theta2 = 2 * Mathf.PI * (i + 1) / _resolution;
				Vector3 point1 = _position + new Vector3(Mathf.Cos(theta1) * radius, Mathf.Sin(theta1) * radius, 0);
				Vector3 point2 = _position + new Vector3(Mathf.Cos(theta2) * radius, Mathf.Sin(theta2) * radius, 0);
				Gizmos.DrawLine(point1, point2);
			}
		}
	}
	class GizmoEntryPoint : IGizmoEntry
	{
		Vector3 m_center;
		float m_radius;
		Color m_color;

		public GizmoEntryPoint(Vector3 _center, float _radius, Color _clr)
		{
			m_center = _center;
			m_radius = _radius;
			m_color = _clr;
		}

		public void Draw()
		{
			Gizmos.color = m_color;
			Gizmos.DrawSphere(m_center, m_radius);
		}
	}
	class GizmoEntryCube : IGizmoEntry
	{
		Vector3 m_center;
		Vector3 m_extent;
		Color m_color;
		bool m_showWire;

		public GizmoEntryCube(Vector3 _center, Vector3 _extent, Color _clr, bool _showWire)
		{
			m_center = _center;
			m_extent = _extent;
			m_color = _clr;
			m_showWire = _showWire;
		}

		public void Draw()
		{
			Gizmos.color = m_color;
			Gizmos.DrawCube(m_center, m_extent);
			if (m_showWire)
			{
				Gizmos.color = Color.white;
				Gizmos.DrawWireCube(m_center, m_extent);
			}
		}
	}

	class GizmoEntryOCube : IGizmoEntry
	{
		Vector3 m_center;
		Vector3 m_extent1, m_extent2, m_extent3;
		Color m_color;
		bool m_showWire;

		public GizmoEntryOCube(Vector3 _center, Vector3 _extent1, Vector3 _extent2, Vector3 _extent3, Color _clr, bool _showWire)
		{
			m_center = _center;
			m_extent1 = _extent1;
			m_extent2 = _extent2;
			m_extent3 = _extent3;
			m_color = _clr;
			m_showWire = _showWire;
		}

		public void Draw()
		{
			Gizmos.color = m_color;
			var mat = Gizmos.matrix;
			Gizmos.matrix = Matrix4x4.LookAt(m_center, m_center + m_extent3, m_extent2.normalized);
			var size = new Vector3(m_extent1.magnitude, m_extent2.magnitude, m_extent3.magnitude) * 2; 
			Gizmos.DrawCube(Vector3.zero, size);
			if (m_showWire)
			{
				Gizmos.color = Color.white;
				Gizmos.DrawWireCube(Vector3.zero, size);
			}
			Gizmos.matrix = mat;
		}
	}
	
	class GizmoEntryLabel : IGizmoEntry
	{
		Vector3 m_pos;
		string m_label;
		GUIStyle m_style;

		public GizmoEntryLabel(Vector3 _pos, string _label, Color _clr)
		{
			m_pos = _pos;
			m_label = _label;
			m_style = new GUIStyle();
			m_style.normal.textColor = _clr;
		}

		public void Draw()
		{
			Handles.Label(m_pos, m_label, m_style);
		}
	}	
	private Dictionary<string, List<IGizmoEntry>> m_gizmoLists = new();

	private void AddGizmoEntry(string _listId, IGizmoEntry _entry)
	{
		if (m_gizmoLists.TryGetValue(_listId, out var list) == false)
		{
			list = new List<IGizmoEntry>();
			m_gizmoLists[_listId] = list;
		}
		list.Add(_entry);
	}

	public void ClearGizmos(string _id)
	{
		if (m_gizmoLists.TryGetValue(_id, out var list)) list.Clear();
	}

	public void AddGizmoLine(string _id, Vector3 _a, Vector3 _b, Color _c, float _thickness = 0)
	{
		if (_thickness > 0)
		{
			var along = (_b - _a).normalized;
			var side = Vector3.Cross(Vector3.up, along).normalized;
			var up = Vector3.Cross(along, side);
			AddGizmoEntry(_id, new GizmoEntryOCube((_a + _b) * .5f, (_b - _a) * .5f, side * _thickness, up * _thickness, _c, false)); 
		}
		else
			AddGizmoEntry(_id, new GizmoEntryLine(_a, _b, _c));
	}

	public void AddGizmoCone(string _id, Vector3 _tip, Vector3 _base, float _angle, Color _clr)
	{
		AddGizmoEntry(_id, new GizmoEntryCone(_tip, _base, _angle, _clr));
	}

	public void AddGizmoPoint(string _id, Vector3 _a, float _r, Color _c)
	{
		AddGizmoEntry(_id, new GizmoEntryPoint(_a, _r, _c));
	}

	public void AddGizmoLabel(string _id, Vector3 _a, string _label, Color _c)
	{
		AddGizmoEntry(_id, new GizmoEntryLabel(_a, _label, _c));
	}

	public void AddGizmoCube(string _id, Vector3 _center, Vector3 _ext, Color _c, bool _addWire = false)
	{
		AddGizmoEntry(_id, new GizmoEntryCube(_center, _ext, _c, _addWire));
	}

	public void AddGizmoCubeMinMax(string _id, Vector3 _min, Vector3 _max, Color _c, bool _addWire = false)
	{
		var center = (_max + _min) * .5f;
		var extent = _max - _min;
		AddGizmoEntry(_id, new GizmoEntryCube(center, extent, _c, _addWire));
	}

	public void AddGizmoOCube(string _id, Vector3 _center, Vector3 _ext1, Vector3 _ext2, Vector3 _ext3, Color _c, bool _showWire = false)
	{
		AddGizmoEntry(_id, new GizmoEntryOCube(_center, _ext1, _ext2, _ext3, _c, _showWire));
	}

	void OnDrawGizmos()
	{
		foreach (var kvp in m_gizmoLists)
		{
			foreach (var e in kvp.Value) e.Draw();
		}
	}
#else
	public void ClearGizmos(string _id) {}
	public void AddGizmoLine(string _id, Vector3 _a, Vector3 _b, Color _c, float _thickness = 0) {}
	public void AddGizmoCone(string _id, Vector3 _tip, Vector3 _base, float _angle, Color _clr) {}
	public void AddGizmoPoint(string _id, Vector3 _a, float _r, Color _c) {}
	public void AddGizmoLabel(string _id, Vector3 _a, string _label, Color _c) {}
	public void AddGizmoCube(string _id, Vector3 _center, Vector3 _ext, Color _c, bool _addWire = false) { }
	public void AddGizmoOCube(string _id, Vector3 _center, Vector3 _ext1, Vector3 _ext2, Vector3 _ext3, Color _c, bool _showWire = false) {}
    public void AddGizmoCubeMinMax(string _id, Vector3 _min, Vector3 _max, Color _c, bool _addWire = false) {}
#endif

	static Dictionary<string, long> m_timers = new();
	static Dictionary<string, int> m_numTimes = new();
#if UNITY_EDITOR
	public static void ToggleTimer(string _id)
	{
		if (m_timers.ContainsKey(_id))
		{
			var num = m_numTimes[_id];
			m_numTimes[_id] = num + 1;
			m_timers[_id] += ((num & 1) * 2 - 1) * DateTime.UtcNow.Ticks; //If num is odd (the timer was running), add the end time, otherwise we should subtract the start time
		}
		else
		{
			m_numTimes.Add(_id, 1);
			m_timers.Add(_id, -DateTime.UtcNow.Ticks);
		}
	}

	public static void DisplayResults()
	{
		foreach (var kvp in m_timers)
		{
			var key = kvp.Key;
			var val = kvp.Value;
			Debug.LogError($"{m_numTimes[key] / 2} repeats of {key}: Tot = {val * 0.0000001f}s, Avg = {val * 0.0000002f / m_numTimes[key]}s");
		}
	}

#else
	public static void ToggleTimer(string _id) { }
	public static void DisplayResults() { }
#endif

	void UpdateDesignChecks()
	{
		if (InputConsuming || KeyboardConsuming) return;
		if (Utility.GetKeyDown(KeyboardController.Me.GetKey(EKeyboardFunction.DesignMode)) && !DesignTableManager.Me.IsDesignGloballyClosing && DesignTableManager.Me.IsShowingUnlockShelf == false && MAUnlocks.CanDesignBuildings)
		{
			if (DesignTableManager.Me.m_isInDesignGlobally)
			{
				DesignTableManager.Me.Close();
			}
			else
			{
				DesignTableManager.Me.StartDesignGlobally(null);
			}
		}
		if (DesignTableManager.Me.m_isInDesignGlobally && DesignTableManager.Me.DesignGloballyFocusMode)
		{
			if (Utility.GetKeyDown(c_leaveFocusKey) && MAGameInterface.CanLeaveFocusDesignMode())
			{
				m_escapeConsumed = true;
				DesignTableManager.Me.SetDesignGloballyFocus(null);
			}
		}
	}

	void UpdateXray()
	{
		bool xrayActive = KeyboardConsuming == false && (Utility.GetKey(KeyCode.LeftShift) || Utility.GetKey(KeyCode.RightShift));
		
		Block hitBlock = null;
		if (xrayActive && Physics.Raycast(Camera.main.RayAtMouse(), out var hitInfo))
		{
			hitBlock = hitInfo.collider.gameObject.GetComponentInParent<Block>();
			if (hitBlock != null && hitBlock.gameObject.GetComponent<DTDragBlock>() == null)
				hitBlock = null;
		}
		DesignTableManager.Me.HoverBlock(hitBlock);
	}

	void UpdateDayNight()
	{
		var dayNight = UIManager.Me.m_dayNightSprite;
		const bool c_hideDayNightWhenFrozen = false;
		if (c_hideDayNightWhenFrozen)
		{
			var rt = dayNight.transform.parent as RectTransform;
			var target = DayNight.Me.m_overrideDayFraction >= 0 ? 0 : 1;
			rt.anchoredPosition = Vector2.Lerp(rt.anchoredPosition, new Vector2(0, (1 - target) * -96), .1f);
			dayNight.color = new Color(1,1,1, Mathf.Lerp(dayNight.color.a, target, .1f));
		}
		//float angle = DayNight.Me.m_timeStage * (360.0f / DayNight.c_timeStages) + 90;
		float angle = 90.0f - DayNight.Me.m_timeOfDay * 360.0f;
		dayNight.transform.localEulerAngles = new Vector3(0, 0, angle);
		var mat = dayNight.material;
		float innerAngle = DayNight.Me.m_nightPMFraction * .5f * Mathf.PI * 2;
		float outerAngle = (1 - DayNight.Me.m_dayFraction) * .5f * Mathf.PI * 2;
		mat.SetVector("_BlendAngles", new Vector4(innerAngle, outerAngle, 0, 0));
		//PDM Bodge to get in time
		var text = dayNight.transform.parent.GetComponentInChildren<TMP_Text>();
		if (text)
		{
			text.text = DayNight.Me.CurrentTimeString;
		}
	}

	void CheckStartUprootTree()
	{
		if (GetMouseButtonDown(0))
		{
			if (RaycastAtPoint(Utility.mousePosition, out var hit))
			{
				var tree = hit.collider.GetComponent<TreeHolder>();
				if (tree != null && BCChopObject.CanUproot(tree))
				{
					var chop = tree.GetComponent<BCChopObject>();
					if (chop == null) chop = tree.gameObject.AddComponent<BCChopObject>();
					chop.StartUproot();
				}
			}
		}
	}

	private static DebugConsole.Command s_entercavecmd = new ("entercave", _s => {
		if (Me.IsPossessing)
		{
			var bits = _s.Split(",");
			if (bits.Length < 2)
			{
				Debug.LogError($"Usage: entercave=<name>,<entranceLabel>");
				return;
			}
			GenericSubScene.Open(bits[0], bits[1]);
		}
		else
			Debug.LogError($"Caves can only be entered in posssess mode");
	});
	private static DebugConsole.Command s_exitcavecmd = new("exitcave", _s =>
	{
		if (Me.m_state.m_subSceneState.Current != null)
		{
			if (string.IsNullOrEmpty(_s))
			    Debug.LogError($"Usage: exitcave=<exitLabel>");
			else
				GenericSubScene.Exit(_s);
		}
		else
			Debug.LogError($"No cave to exit");
	});
	
	private static DebugConsole.Command s_skipBuildHelpers = new ("skipbuilds", _s => Me.SkipBuildHelpers());
	private void SkipBuildHelpers() => StartCoroutine(Co_SkipBuildHelpers());
	private IEnumerator Co_SkipBuildHelpers()
	{
		for (int keepGoing = 5; keepGoing > 0; --keepGoing)
		{
			for (int i = 0; i < 3; ++i)
				yield return null;
			for (int i = 0; i < m_state.m_wildBlocks.Count; ++i)
			{
				var w = m_state.m_wildBlocks[i];
				var wildObj = w.Obj;
				if (wildObj == null) continue;
				var helper = wildObj.GetComponent<BuildHelper>();
				if (helper == null) continue;
				if (DistrictManager.Me.IsWithinDistrictBounds(wildObj.transform.position, true) == false) continue;
				if (DesignTableManager.Me.TrySnapBuildHelper(helper))
				{
					++keepGoing;
					break;
				}
			}
		}
	}
	
#if UNITY_EDITOR
	static DebugConsole.Command s_buildhelperscmd = new ("buildhelper", _s => Me.SetBuildHelper(_s));
	static DebugConsole.Command s_debugdecorationscmd = new("debugdecorations", _s => Me.GenerateDecorationDebug());
	static DebugConsole.Command s_debugchestscmd = new("debugchests", _s => Me.GenerateChestDebug());
	static DebugConsole.Command s_debuggatescmd = new("debuggates", _s => Me.GenerateGateDebug());
	static DebugConsole.Command s_moveblockerscmd = new("moveblockers", _s => Me.EditMoveBlockers());

	List<Canvas> m_buildHelpDebugCanvas = new();
	List<LineRenderer> m_buildHelpDebugLines = new();

	void MakeDebugLabel(Transform t, string labelText, Color clr)
	{
		var canvas = new GameObject("BuildHelpDebugCanvas").AddComponent<Canvas>();
		canvas.transform.SetParent(t, false);
		canvas.renderMode = RenderMode.WorldSpace;
		canvas.transform.localScale = Vector3.one * 0.03f;
		canvas.transform.localPosition = Vector3.forward * 0.3f;
		canvas.transform.localEulerAngles = Vector3.up * 180;
		var afc = canvas.gameObject.AddComponent<AlwaysFaceCamera>();
		afc.m_lockTo2D = true;
		afc.m_freezeXZ = false;
		afc.m_pushForward = 8;
		for (int i = 0; i < 5; ++i)
		{
			var label = new GameObject($"BuildHelp_{labelText}").AddComponent<TMPro.TextMeshProUGUI>();
			label.transform.SetParent(canvas.transform, false);
			if (i < 4)
			{
				float dx = ((i & 1) * 2 - 1) * 2f;
				float dy = ((i & 2) - 1) * 2f;
				label.transform.localPosition = new Vector3(dx, dy, 0);
			}
			label.text = labelText;
			label.textWrappingMode = TMPro.TextWrappingModes.NoWrap;
			label.overflowMode = TMPro.TextOverflowModes.Overflow;
			label.alignment = TMPro.TextAlignmentOptions.Center;
			label.color = i == 4 ? clr : Color.black;
		}
		m_buildHelpDebugCanvas.Add(canvas);
	}

	void MakeDebugLine(Transform _from, Transform _to, int _height, Color _clr)
	{
		if (_from == null || _to == null) return;
		var line = new GameObject("BuildHelpDebugLine").AddComponent<LineRenderer>();
		line.transform.SetParent(_from, false);
		line.useWorldSpace = true;
		line.positionCount = 2;
		var fromPos = _from.position;
		var toPos = _to.position + Vector3.up * (3.5f * _height);
		var points = BezierLine.GetPoints(new Vector3[] { fromPos, fromPos + Vector3.up * 6, (fromPos + toPos) * .5f + Vector3.up * 8, toPos + Vector3.up * 6, toPos });
		line.positionCount = points.Length;
		line.SetPositions(points);
		line.startWidth = line.endWidth = 0.25f;
		line.material = new Material(Shader.Find("Unlit/Color"));
		line.material.color = _clr;
		m_buildHelpDebugLines.Add(line);
	}

	void DestroyGeneratedHelps()
	{
		foreach (var c in m_buildHelpDebugCanvas)
			Destroy(c.gameObject);
		m_buildHelpDebugCanvas.Clear();
		foreach (var l in m_buildHelpDebugLines)
			Destroy(l.gameObject);
		m_buildHelpDebugLines.Clear();
	}

	void GenerateBuildHelpDebug(bool _show)
	{
		if (_show)
		{
			void AddBuildingDebug(NGCommanderBase b)
			{
				var block = b.GetComponentInChildren<BaseBlock>();
				if (block == null) return;
				var hinges = block.GetHinges();
				for (int i = 0; i < hinges.Count; ++i)
				{
					var t = hinges[i];
					var labelText = $"{b.m_linkUID},{i}";
					MakeDebugLabel(t, labelText, new Color(.75f, 1f, .75f));
				}
			}

			foreach (var b in NGManager.Me.m_NGCommanderList)
				AddBuildingDebug(b);
			foreach (var b in NGManager.Me.m_NGCommanderListOutOfRange)
				AddBuildingDebug(b);
			foreach (var w in m_state.m_wildBlocks)
			{
				var t = w.Obj?.transform;
				if (t == null) continue;
				var labelText = $"{w.m_id}";
				MakeDebugLabel(t, labelText, new Color(1f, .75f, .75f));
				var buildHelper = w.Obj.GetComponent<BuildHelper>();
				if (buildHelper != null)
					MakeDebugLine(t, buildHelper.Pad, buildHelper.Height, new Color(1f, 1f, .7f)); 
			}
		}
		else
		{
			DestroyGeneratedHelps();
		}
	}

	void GenerateDecorationDebug()
	{
		var _show = m_buildHelpDebugCanvas.Count == 0;
		if (_show)
		{
			for (int i = 0; i < m_state.m_decorations.Count; ++i)
			{
				var d = m_state.m_decorations[i];
				var obj = d.Decoration;
				var labelText = $"{i}\n<size=50%>{d.m_name}</size>";
				MakeDebugLabel(obj.transform, labelText, new Color(.75f, 1f, .75f));
			}
		}
		else
		{
			DestroyGeneratedHelps();
		}
	}

	void GenerateChestDebug()
	{
		var _show = m_buildHelpDebugCanvas.Count == 0;
		if (_show)
		{
			for (int i = 0; i < m_state.m_treasureChests.Count; ++i)
			{
				var d = m_state.m_treasureChests[i];
				var obj = d.Object;
				var labelText = $"Chest {i}";
				MakeDebugLabel(obj.transform, labelText, new Color(.75f, 1f, .75f));
			}
			for (int i = 0; i < m_state.m_treasurePits.Count; ++i)
			{
				var d = m_state.m_treasurePits[i];
				var obj = d.Object;
				var labelText = $"Pit {i}";
				MakeDebugLabel(obj.transform, labelText, new Color(.75f,  .75f, 1f));
			}
		}
		else
		{
			DestroyGeneratedHelps();
		}
	}
	void GenerateGateDebug()
	{
		var _show = m_buildHelpDebugCanvas.Count == 0;
		if (_show)
		{
			for (int i = 0; i < GateOpener.GateOpeners.Count; ++i)
			{
				var g = GateOpener.GateOpeners[i];
				var labelText = $"<size=200%>{(int)g.transform.position.x},{(int) g.transform.position.z}</size>";
				MakeDebugLabel(g.transform, labelText, new Color(.75f, 1f, .75f));
			}
		}
		else
		{
			DestroyGeneratedHelps();
		}
	}

	bool m_showBuildHelpDebug = false;
	void SetBuildHelper(string _s)
	{
		if (string.IsNullOrEmpty(_s))
		{
			m_showBuildHelpDebug = !m_showBuildHelpDebug;
			GenerateBuildHelpDebug(m_showBuildHelpDebug);
			return;
		}
		var bits = _s.Split(",");
		if (bits.Length >= 4)
		{
			var wildID = int.Parse(bits[0]);
			var buildingID = int.Parse(bits[1]);
			var padID = int.Parse(bits[2]);
			var height = int.Parse(bits[3]);
			var direction = bits.Length >= 5 ? (int)Enum.Parse(typeof(SnapHinge.EType), bits[4]) : 0;
			GameState_BuildHelper.Add(wildID, buildingID, padID, height, direction);
			if (m_showBuildHelpDebug)
			{
				GenerateBuildHelpDebug(false);
				GenerateBuildHelpDebug(true);
			}
		}
	}
#endif
	
	private int m_lastFixedUpdateFrame;

	// public List<MAWorker> GetWorkers()
	// {
	// 	List<MAWorker> actualWorkers = new();
	// 	foreach (var workerType in NGManager.Me.m_MAWorkerList)
	// 	{
	// 		if (workerType.GetType() == typeof(MAWorker))
	// 		{
	// 			actualWorkers.Add(workerType);
	// 		}
	// 	}
	//
	// 	return actualWorkers;
	// }
	
	void FixedUpdate()
	{
		//var w = GetWorkers();
		if (m_lastFixedUpdateFrame == Time.frameCount) return;
		m_lastFixedUpdateFrame = Time.frameCount;
		UpdatePossessionCharacterControl();
		EarlyUpdate();
	}


	// do stuff in here before all object updates
	void EarlyUpdate()
	{
		Utility.InputUpdate();
	}

	bool m_escapeConsumed = false;
	void Update() {
		TrackWorstFrameTime();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        if (Utility.IsShuttingDown) return;
        UpdateInputRecorder();
        m_escapeConsumed = false;
        TrackFPS();
		Debug.developerConsoleVisible = false;
		ManagedBlock.UpdateLazyObjects();
		CheckPlayerActivity();
#endif
		CheckSaveBankNeedsRefresh();
		Utility.UpdateSmoothedMouse();
		if(Utility.IsInputFieldActive() == false && PauseManager.IsPaused == false)
			UpdateCamera();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		UpdateCurrentFeel();
#endif
		UpdateAudio();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		UpdateVignette();
		UpdateXray();
		CheckStartUprootTree();
		UpdateDayNight();
#endif
		UpdateCameraZoomState();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		UpdateDebug();
		CheckBookmarks();
		UpdateDesignChecks();
		DesignTableManager.Me.UpdateDesignGloballyBuildingHide(); // after UpdateCamera and CheckBookmarks
#endif
		UpdatePickupSync();
		UpdateCharactersAboveTerrain();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		Conveyor.UpdateVisuals();
		TickPlayerActivity();
		
		ShowInDesign.RefreshAll();

		GlobalData.Me.SetCloudPostProcessOn(IsInSubScene == false && IsDesignTable == false && DesignTableManager.Me.IsInDesignInPlaceOnLocation == false);
		
		SetCrashlyticsPeriodicValues();

        // If Dragging		
        if (!m_escapeConsumed && SkipManager.Me.IsEnabled == false && IsPossessing == false && BuildingPlacementManager.Consuming == false && GetKeyDown(LegacyKeyCode.ShowSettings))
		{
			if (!KeyboardConsuming && !DragHolder.AnyDragging && PlayerHandManager.Me.RadialMenuActive == false)
			{
	            if(EscapeShowsSettings)
		    		TitleScreenSettings(m_titlePage.activeSelf);
				
			}
		}
		if (m_numObjects > 0 && !m_objectsRegistered) //WE'VE DETECTED TIMESCALE CHANGE
		{
			m_objectsRegistered = true; //protection against refiring every frame, cleared in lateInitialise
		}
		CheckAutosave();

		UpdateEndGame();
		
		if(LoadComplete)
			m_state.m_gameStats.Update();
#endif
	}

	List<Transform> m_charactersAboveTerrainList = new();
	float[] m_charactersAboveTerrainResults = null; 
	void UpdateCharactersAboveTerrain()
	{
		var movingObjects = m_charactersAboveTerrainList;
		movingObjects.Clear();

		foreach (var character in NGManager.Me.m_MACharacterList)
		{
			if (character == null)
				continue;
			if (character.Health <= 0)
				continue;
			if (!character.isActiveAndEnabled)
				continue;
			if (character.GameState.m_isUnderground)
				continue;
			movingObjects.Add(character.transform);
		}

		foreach (var vehicle in NGManager.Me.MAVehicles)
		{		
			//ToDo: TS - if vehicles get health etc, must add check here
			if (vehicle == null)
				continue;
			if (!vehicle.isActiveAndEnabled)
				continue;
			movingObjects.Add(vehicle.transform);
		}
		foreach (var wildBlock in m_state.m_wildBlocks)
		{
			if (wildBlock.Obj == null)
				continue;
			if (!wildBlock.Obj.activeSelf)
				continue;
			movingObjects.Add(wildBlock.Obj.transform);
		}
		foreach (var dec in m_state.m_decorations)
		{
			if (dec.Decoration == null)
				continue;
			if (!dec.Decoration.gameObject.activeSelf)
				continue;
			if (dec.Decoration.m_canPickupAndThrow == false)
				continue;
			movingObjects.Add(dec.Decoration.transform);
		}

		var positions = new Unity.Mathematics.float2[movingObjects.Count];
		for (int i = 0; i < movingObjects.Count; ++i)
			positions[i] = movingObjects[i].position.GetXZVector2();
		var job = new GlobalData.CheckHeightsJob(positions, GlobalData.Me.Heights);
		job.Schedule().Complete();
		if (m_charactersAboveTerrainResults == null || m_charactersAboveTerrainResults.Length != movingObjects.Count)
			m_charactersAboveTerrainResults = new float[movingObjects.Count];
		var output = m_charactersAboveTerrainResults;
		job.GetOutput(ref output);

		for (int i = 0; i < movingObjects.Count; ++i)
		{
			var character = movingObjects[i];
			var pos = character.position;
			if (pos.y >= output[i] - .5f)
				continue;

			pos.y = output[i];
			character.position = pos + Vector3.up * 2;
			if (character.up.y < 0) character.up = -character.up;

			var body = character.GetComponentInChildren<Rigidbody>();
			if ((body != null) && !body.isKinematic)
				body.linearVelocity = body.linearVelocity.NewY(0);
		}
	}
	
	private NotificationUIController m_finishedGameDialogue;
	private void UpdateEndGame()
	{
		if(!LoadComplete) return;
		
		if(m_state.m_haveFinishedGame && m_finishedGameDialogue == null)
		{
			m_finishedGameDialogue = CreateEndGameDialogue(NGManager.Me.m_endOfContentTitle, NGManager.Me.m_endOfContentMessage, NGManager.Me.m_endOfContentFeedbackURI,
				() => { m_finishedGameDialogue = null; ExitToTitleScreen(); });
		}
	}
	
	public enum LegacyKeyCode
	{
		CancelDrag,
		ShowSettings,
		AssignJob,
		AssignSite,
		AssignHome,
	}

	public bool GetKeyDown(LegacyKeyCode _lkc)
	{
		return Input.GetKeyDown(MapKeyCode(_lkc));
	}

	public bool GetKey(LegacyKeyCode _lkc)
	{
		return Input.GetKey(MapKeyCode(_lkc));
	}

	public bool GetKeyUp(LegacyKeyCode _lkc)
	{
		return Input.GetKeyUp(MapKeyCode(_lkc));
	}

	public KeyCode MapKeyCode(LegacyKeyCode _lkc)
	{
		switch(_lkc)
		{
			case LegacyKeyCode.CancelDrag:
			case LegacyKeyCode.ShowSettings:
				return KeyCode.Escape;
			case LegacyKeyCode.AssignJob:
				return KeyCode.J;
			case LegacyKeyCode.AssignSite:
				return KeyCode.K;
			case LegacyKeyCode.AssignHome:
				return KeyCode.H;
		}
		return KeyCode.None;
	}

	//static DebugConsole.Command s_killtutorial = new DebugConsole.Command("killtutorial", _s => NGTutorialManager.Me.TutorialHasEnded());
    public bool EscapeShowsSettings => !DesignTableManager.Me.m_dragInProgress;

	public bool SaveEnabled = true;
	float m_lastAutosaveTime = 0;
	void CheckAutosave() {
		if (LoadComplete) {
			if (m_lastAutosaveTime == 0) {
				m_lastAutosaveTime = Time.time;
			}
			const float c_timeBetweenAutosaves = 5f;
			if (Time.time - m_lastAutosaveTime > c_timeBetweenAutosaves) {
				ForceSave();
			}
		}
	}
	public void ForceSave() {
		m_lastAutosaveTime = Time.time;
		WriteSaveData();
	}

	public void ForceSaveAndUpload()
	{
		if (!LoadComplete)
		{
			Debug.Log("FSAU fired early");
			return;
		}
		ForceSave();
	}
	
	public void SaveDay() 
	{
		var fileToWrite = $"{Application.persistentDataPath}/Day{DayNight.Me.CurrentWorkingDay}.dat";
		GetSaveData(fileToWrite);
	}

	public void Load(string _name, bool _isJson)
	{
		if (_isJson) System.IO.File.WriteAllText(SavePath, _name);
		else System.IO.File.Copy(_name, SavePath, true);
		m_currentSlotState = SaveSlotState.Existing;
		LoadSaveAndStart();
	}

	public void SubSceneExit() {
		if (UndergroundManager.Me?.gameObject?.activeSelf ?? false)
			UndergroundManager.Me.gameObject.SetActive(false);
		else
			SubSceneStack.CleanExit();
	}

	public void GoUnderground()
	{
		if (UndergroundManager.Me != null)
			UndergroundManager.Me.gameObject.SetActive(true);
		else
			UndergroundManager.Create(GlobalData.Me.m_undergroundManagerPrefab);
	}

	public void Tidy() {
		Resources.UnloadUnusedAssets();
	}

	public void GetDesignSpriteAsync(string _design, CaptureObjectImage.Use _use, System.Action<Sprite> _onComplete) {
		var o = new GameObject("Capture");
		DesignTableManager.Me.RestoreDesign(DesignTableManager.RestoreType.ImageCapture, _design, o.transform, (_o,componentsChanged) => {
			var sprite = CaptureObjectImage.Me.Capture(o, _use);
			o.SetActive(false);
			Destroy(o);
			try
			{
				_onComplete(sprite);
			}
			catch (Exception _e)
			{
				Debug.LogError($"GetDesignSpriteAsync caught exception\n{_e}");
			}
		});
	}
	Sprite m_placeholder = null;
	public void SetDesignSprite(GameState_Design _design, Sprite _sprite) {
		GlobalData.Me.m_designSprites[_design] = _sprite;
	}
	public void GetCarriableResourceSprite(NGCarriableResource _resource, NGCommanderBase _factory, System.Action<Sprite> _onComplete) {
		if ((_resource == null || _resource.IsNone)) {
			_onComplete(NGCarriableResource.RawMaterialSpriteImage("worker"));
			/*if (_factory == null || _factory.OutputIs.IsNone || _factory.OutputIs.m_carriableType == "Heal") {
				_onComplete(NGCarriableResource.RawMaterialSpriteImage("worker"));
			} else {
				_onComplete(NGCarriableResource.RawMaterialSpriteImage(_factory.OutputIs.m_rawMaterial));
			}*/
		/*} else if (_resource.m_carriableType == "Heal") {
			_onComplete(NGCarriableResource.RawMaterialSpriteImage("rest"));*/
		} else if (_resource.IsProduct) {
			GameState_Product productMade = _factory.ProductMade;
			if(productMade == null) return;
			var s = GetDesignSprite(productMade.m_design, CaptureObjectImage.Use.Product, _onComplete);
			if (s != null) _onComplete(s);
		} else {
			_onComplete(_resource.SpriteImage());
		}
	}

	private Dictionary<GameState_Design, List<System.Action<Sprite>>> m_designSpriteCallbacks = new ();

	private void AddSpriteCallback(GameState_Design _design, System.Action<Sprite> _cb)
	{
		List<System.Action<Sprite>> cbs;
		if (!m_designSpriteCallbacks.TryGetValue(_design, out cbs))
		{
			cbs = new List<System.Action<Sprite>>();
			m_designSpriteCallbacks[_design] = cbs;
		}
		cbs.Add(_cb);
	}

	private List<System.Action<Sprite>> GetSpriteCallbacks(GameState_Design _design)
	{
		if (m_designSpriteCallbacks.TryGetValue(_design, out var list))
		{
			m_designSpriteCallbacks.Remove(_design);
			return list;
		}
		return null;
	}
	
	public Sprite GetDesignSprite(GameState_Design _design, CaptureObjectImage.Use _use, System.Action<Sprite> _onComplete) {
		if (_design == null) {
			_onComplete(null);
			return null;
		}
		if(GlobalData.Me.m_designSprites.ContainsKey(_design) == false)
		{
			if (m_placeholder == null) m_placeholder = Sprite.Create(Texture2D.whiteTexture, new Rect(0,0,Texture2D.whiteTexture.width,Texture2D.whiteTexture.height), Vector2.one * .5f);
			GlobalData.Me.m_designSprites[_design] = m_placeholder;
			GetDesignSpriteAsync(_design.m_design, _use, (_sprite) => {
				GlobalData.Me.m_designSprites[_design] = _sprite;
				_onComplete(_sprite);
				var list = GetSpriteCallbacks(_design);
				if (list != null)
					foreach (var cb in list)
						cb(_sprite);
			});
		}
		
		var _sprite = GlobalData.Me.m_designSprites[_design];
		if (_sprite == m_placeholder)
			AddSpriteCallback(_design, _onComplete);
		else
			_onComplete(_sprite);
		return _sprite;
	}

	public Vector3? RaycastTerrain(float _screenFractionX, float _screenFractionY)
	{
		var cam = m_camera;
		if (IsPossessing)
			return (cam.transform.position + cam.transform.forward * 5f).GroundPosition();
		var ray = cam.RayAtScreenPosition(new Vector3(Screen.width * _screenFractionX, Screen.height * _screenFractionY, 0));
		if (Physics.Raycast(ray, out var hit, 1000, GameManager.c_layerTerrainBit))
			return hit.point;
		return null;
	}

	public bool Raycast(Vector3 _from, Vector3 _fwd, out RaycastHit _hit, int _mask = -1) {
		float dist = _fwd.magnitude;
		_fwd /= dist;
		return Physics.Raycast(_from, _fwd, out _hit, dist, _mask);
	}
	public bool RaycastAtPoint(Vector3 _pos, out RaycastHit _hit, int _mask = -1, bool _ignoreTriggers = false) {
		var ray = m_camera.ScreenPointToRay(_pos);
		return Physics.Raycast(ray, out _hit, 1e23f, _mask, _ignoreTriggers ? QueryTriggerInteraction.Ignore : QueryTriggerInteraction.Collide);
	}

	public bool RaycastAtPointWithFallback(Vector3 _pos, out RaycastHit _hit, int _mask = -1, bool _ignoreTriggers = false)
	{
		var ray = m_camera.ScreenPointToRay(_pos);
		if (Physics.Raycast(ray, out _hit, 1e23f, _mask, _ignoreTriggers ? QueryTriggerInteraction.Ignore : QueryTriggerInteraction.Collide))
			return true;
		if ((new Plane(Vector3.up, Vector3.up * GlobalData.c_seaLevel)).Raycast(ray, out float dist))
		{
			_hit.distance = dist;
			_hit.point = ray.GetPoint(dist);
			return true;
		}
		return false;
	}
	
	public bool RaycastAtPoint(Vector3 _pos, GameObject _ignore, out RaycastHit _hit, int _mask = -1) {
		var ray = m_camera.ScreenPointToRay(_pos);
		var hits = Physics.RaycastAll(ray, 1e23f, _mask);
		_hit = default(RaycastHit); _hit.distance = 1e23f;
		var rc = _ignore.GetComponentInChildren<RagdollController>()?.BoneHipsRagdoll.gameObject;
		foreach (var h in hits) {
			var hitObject = h.collider.gameObject;
			if (hitObject.IsChildOf(_ignore)) continue;
			if ((rc != null) && hitObject.IsChildOf(rc)) continue;
			if (h.distance < _hit.distance) _hit = h;
		}
		return _hit.distance < 1e22f;
	}

	public bool RaycastAtPoint(Vector3 _pos, GameObject _ignore, GameObject _ignore2, out RaycastHit _hit, int _mask = -1)
	{
		var ray = m_camera.ScreenPointToRay(_pos);
		return RaycastWithExclude(ray, _ignore, _ignore2, out _hit, _mask);
	}

	public bool RaycastWithExclude(Ray _ray, GameObject _ignore, GameObject _ignore2, out RaycastHit _hit, int _mask = -1, bool _ignoreTriggers = false)
	{
		var hits = Physics.RaycastAll(_ray, 1e23f, _mask);
		_hit = default(RaycastHit);
		_hit.distance = 1e23f;
		var rc = _ignore == null ? null : _ignore.GetComponentInChildren<RagdollController>()?.BoneHipsRagdoll;
		var rc2 = _ignore2 == null ? null : _ignore2.GetComponentInChildren<RagdollController>()?.BoneHipsRagdoll;
		foreach (var h in hits)
		{
			if (_ignoreTriggers && h.collider.isTrigger) continue;
			var hitObject = h.collider.gameObject;
			if (hitObject.IsChildOf(_ignore)) continue;
			if (hitObject.IsChildOf(_ignore2)) continue;
			if ((rc != null) && hitObject.IsChildOf(rc.gameObject)) continue;
			if ((rc2 != null) && hitObject.IsChildOf(rc2.gameObject)) continue;
			if (h.distance < _hit.distance) _hit = h;
		}
		return _hit.distance < 1e22f;
	}

	public Vector3 VectorAtPoint(Vector3 _p) {
		_p.y = HeightAtPoint(_p);
		return _p;
	}
	public float HeightAtPoint(Vector3 _p) {
		return GlobalData.Me.GetRawHeight(_p);
	}

	public Vector3 GetTerrainPointAtCenterOfScreen()
	{
		RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * .5f, 0), out var hit, c_layerTerrainBit);
		return hit.point;
	}


	public void ShowMessage(string _title, string _body, bool _showClose = false, System.Action _cb = null) {
		NotificationUIController uiController = InfoPlaqueManager.Me.LoadUI<NotificationUIController>();
		uiController.titleText.text = _title;
		uiController.descriptionText.text = _body;
		uiController.ToggleCloseButtonX(_showClose);
		uiController.Show();
		if (_cb != null)
			uiController.onClose += _cb;
	}

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	private bool AdvancedEditMode => Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
#else
	private bool AdvancedEditMode => false;
#endif

	public TMPro.TextMeshProUGUI m_debugText;
	void SelectDebugItem(int _type) {
		if (GlobalData.Me.m_roadEditMode) {
			RoadManager.Me.m_currentRoadSet = _type;
		}
		RefreshSelectionDebug();
	}
	void RefreshSelectionDebug() {
		return;
		if (GlobalData.Me.m_roadEditMode) {
			m_debugText.text = $"{RoadManager.Me.m_roadSets[RoadManager.Me.m_currentRoadSet].name} - {RoadManager.Me.m_currentRoadSet}";
		}
		m_lastDebugTrigger = Time.time;
	}
	
	private string m_autoBackupPath = null;
	private bool m_autoBackupChecked = false;
	private float m_autoBackupNextTime = 0;
	private int m_autoBackupPeriod = 0;
	private string AutoBackupConfigFile => Application.persistentDataPath + "/" + "AutoBackup.txt"; 
	private void DebugAutoBackup()
	{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if (!m_autoBackupChecked)
		{
			if (System.IO.File.Exists(AutoBackupConfigFile))
			{
				var text = System.IO.File.ReadAllText(AutoBackupConfigFile);
				if (!string.IsNullOrEmpty(text))
				{
					var bits = text.Split(",");
					m_autoBackupPath = $"{Application.persistentDataPath}/{bits[0]}";
					m_autoBackupPeriod = int.Parse(bits[1]);
					if (!System.IO.Directory.Exists(m_autoBackupPath))
						System.IO.Directory.CreateDirectory(m_autoBackupPath);
				}
			}
			m_autoBackupChecked = true;
		}
		if (m_autoBackupPath == null) return;
		if (IsVisitingInProgress) return;
		if (!LoadComplete) return;
		if (HasLoadedFromSeed && !m_haveUploaded) return;
		if (Time.realtimeSinceStartup > m_autoBackupNextTime)
		{
			m_autoBackupNextTime = Time.realtimeSinceStartup + m_autoBackupPeriod;
		}
#endif
	}
	
	public string m_debugConsoleCommand;
	public bool m_debugConsoleExecute = false;

	public string m_debugData;
	float m_lastDebugTrigger = -100;
	const int c_firstBuildingDesign = 10;
	const int c_avatarDesign = c_firstBuildingDesign - 1;
	private bool m_shouldShowMemory = false;
	
	Unity.Profiling.ProfilerRecorder m_systemUsedMemoryRecorder;
	
	public static bool s_screenGrabMode = false;
	static DebugConsole.Command s_screenGrabModeToggle = new DebugConsole.Command("screengrabmode", _s => Utility.SetOrToggle(ref s_screenGrabMode, _s));
	public static bool s_lockPodium = false;
	static DebugConsole.Command s_lockPodiumToggle = new DebugConsole.Command("lockpodium", _s => Utility.SetOrToggle(ref s_lockPodium, _s));
	static DebugConsole.Command s_toggleCardHolderVisible = new DebugConsole.Command("cardholders", _s => { Utility.SetOrToggle(ref s_cardHoldersVisible, _s); UpdateCardHoldersVisible();});
	public static bool s_cardHoldersVisible = true;
	
	private static void UpdateCardHoldersVisible()
	{
		if(s_cardHoldersVisible)
		{
			foreach(var building in NGManager.Me.m_maBuildings)
			{
				building.RefreshOrderBoard();
			}
		}
		else
		{
			foreach(var building in NGManager.Me.m_maBuildings)
			{
				building.CardHolder?.DestroyMe();
				building.CardHolder = null;
			}
		}
	}
	
	static DebugConsole.Command s_showcutouts = new DebugConsole.Command("cutouts", _s => s_consoleDisplay = () =>
	{
		string s = "SC:";
		foreach (var r in Screen.cutouts)
		{
			s += $"{r}\n";
		}
		return s;
	});

	void SnapCameraNew(bool _canJumpBack)
	{
		if (IsDesignTable)
		{
			DesignTableManager.Me.SnapCamera();
			return;
		}
		// snap to oakridge or building under attack if relevant
		bool playSound = false;
		var cam = Camera.main.transform;
		Vector3 oldPos = cam.position, oldEuler = cam.eulerAngles;
		var underAttack = GetBuildingUnderAttack();
		if (underAttack != null)
		{
			var pos = underAttack.DoorPosInner;
			var buildingFwd = (underAttack.DoorPosOuter - pos).normalized;
			var focus = pos + Vector3.up * 2;
			cam.position = focus + buildingFwd * -20 + Vector3.up * 15f;
			cam.LookAt(focus, Vector3.up);
			playSound = true;
		}
		else
		{
			cam.position = m_cameraResetPosition;
			cam.eulerAngles = m_cameraResetRotation;
			playSound = true;
		}
		var delta = oldPos - cam.position;
		if (delta.sqrMagnitude < .1f * .1f)
		{
			// jumped to current pos, jump back to last position if there is one
			if (_canJumpBack && m_cameraResetFromPosition.sqrMagnitude > .001f * .001f)
			{
				cam.position = m_cameraResetFromPosition;
				cam.eulerAngles = m_cameraResetFromRotation;
				m_cameraResetFromPosition = Vector3.zero;
			}
			else
				playSound = false;
		}
		else if (delta.sqrMagnitude > 50 * 50)
		{
			m_cameraResetFromPosition = oldPos;
			m_cameraResetFromRotation = oldEuler;
		}
		if (playSound && NGManager.Me.m_cameraJumpAudio != null) NGManager.Me.m_cameraJumpAudio.Play(gameObject); 
		ClearCameraSmoothing();
	}
	

	public void SnapCamera()
	{
		var cam = Camera.main.transform;
		if (m_cameraResetIsInside && m_cameraResetFromPosition.sqrMagnitude > .001f * .001f)
		{
			// snap to last position we snapped from
			cam.position = m_cameraResetFromPosition;
			cam.eulerAngles = m_cameraResetFromRotation;
			m_cameraResetFromPosition = Vector3.zero;
		}
		else
		{
			m_cameraResetFromPosition = cam.position;
			m_cameraResetFromRotation = cam.eulerAngles;
			cam.position = m_cameraResetPosition;
			cam.eulerAngles = m_cameraResetRotation;
		}
	}
	
	public void FocusCamera(Transform _t, float _blend = 1) => FocusCamera(_t.position, _blend);

	public void FocusCamera(Vector3 _p, float _blend = 1)
	{
		var cam = m_camera.transform;
		var camY = cam.position.y;
		var k = (camY - _p.y) / cam.forward.y;
		var newPos = _p + cam.forward * k;
		cam.position = Vector3.Lerp(cam.position, newPos, _blend);
	}
	
	private int m_mapTarget = -1;
	private void ToggleMap()
	{
		if (m_mapTarget == -1) m_mapTarget = 1 - (int)TerrainPopulation.Me.MapMode();
		else m_mapTarget = m_mapTarget == 1 ? 0 : 1;
	}

	public void LeaveMap(Transform _focusOn)
	{
		if (_focusOn == null)
			m_mapTarget = 0;
		else
			FocusCameraOn(_focusOn);
	}
	
	public void FocusCameraOn(Transform _focus, float _focusTime = .8f) => StartCoroutine(Co_FocusCameraOn(_focus, _focusTime));

	private IEnumerator Co_FocusCameraOn(Transform _focus, float _focusTime)
	{
		StartCameraPanSequence();
		var camXform = Camera.main.transform;
		var startPos = camXform.position;
		var startFwd = camXform.forward;
		Vector3 endFocus, endFwdXZ;
		var bld = _focus.GetComponent<NGCommanderBase>();
		if (bld != null)
		{
			endFocus = bld.DoorPosInner;
			endFwdXZ = bld.DoorPosInner - bld.DoorPosOuter;
		}
		else
		{
			endFocus = _focus.position;
			endFwdXZ = startFwd;
		}
		const float c_endDistance = 20;
		var endFwd = endFwdXZ.GetXZNorm() + Vector3.up * -.8f;
		var endPos = endFocus - endFwd * c_endDistance;
		for (float t = 0; t < _focusTime + .1f; t += Time.deltaTime)
		{
			var k = Mathf.Clamp01(t / _focusTime);
			k = k * k * (3 - k - k);
			if (bld == null)
			{
				endFocus = _focus.position;
				endPos = endFocus - endFwd * c_endDistance;
			}
			camXform.position = Vector3.Lerp(startPos, endPos, k);
			camXform.forward = Vector3.Slerp(startFwd, endFwd, k);
			yield return null;
		}
		EndCameraPanSequence();
	}

	private float GetMapZoom()
	{
		if (m_mapTarget == -1) return 0;
		var target = TerrainPopulation.Me.MapMode(.15f, 1f);
		if (m_mapTarget == target)
		{
			m_mapTarget = -1;
			return 0;
		}
		const float c_mapZoomSpeed = 10;
		return (m_mapTarget * -2 + 1) * c_mapZoomSpeed;
	}

	private Vector3 m_middleClickDownAt;
	public string m_cameraResetLabel = "Initial";
	public bool m_cameraResetIsInside = true;
	public Vector3 m_defaultCameraResetPosition = new Vector3(-100, 160, 10);
	public Vector3 m_defaultCameraResetRotation = new Vector3(40, 30, 0);
	public Vector3 m_cameraResetPosition = new Vector3(-100, 160, 10);
	public Vector3 m_cameraResetRotation = new Vector3(40, 30, 0);
	public bool m_cameraResetDisableSnapPoints = false;
	private Vector3 m_cameraResetFromPosition;
	private Vector3 m_cameraResetFromRotation;
	void CheckBookmarks()
	{
		if (KeyboardConsuming) return;
		if (IsCloseableSubScene) return;
		if (IsPossessing) return;
		if (PlayerHandManager.Me.RadialMenuActive) return;
		if (DistrictManager.Me.IsEditing) return;
		if (FailSequenceController.Me.IsActive) return;

		if (m_cameraResetDisableSnapPoints == false)
		{
			RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * .5f, 0), out var camFocus, GameManager.c_layerTerrainBit);
			var nearestSP = CameraSnapPoint.GetNearest(camFocus.point, 250);
			if (nearestSP != null)
			{
				(m_cameraResetPosition, m_cameraResetRotation) = nearestSP.GetCamera();
				m_cameraResetLabel = nearestSP.m_label;
				const float c_isInsideSnapDistance = 80;
				m_cameraResetIsInside = (nearestSP.transform.position - camFocus.point).xzSqrMagnitude() < c_isInsideSnapDistance * c_isInsideSnapDistance;
			}
		}
		
		bool isInDesignTable = IsDesignTable;
		
		if (EKeyboardFunction.ShowMap.IsDown() && isInDesignTable == false) ToggleMap();
		if (EKeyboardFunction.JumpToOakridge.IsDown())
		{
			SnapCameraNew(false);
		}

		int middleState = -1;
		if (Utility.GetMouseButtonDown(2)) middleState = 0;
		else if (Utility.GetMouseButton(2)) middleState = 1;
		else if (Utility.GetMouseButtonUp(2)) middleState = 2;
		if (middleState == 0)
		{
			m_middleClickDownAt = Input.mousePosition;
		}
		else if (middleState != -1)
		{
			var dMouse = m_middleClickDownAt - Input.mousePosition;
			var threshold = Screen.height * .02f;
			if (dMouse.sqrMagnitude < threshold * threshold)
			{
				if (middleState == 2)
				{
					// mouse up and haven't moved too far, middle click
					SnapCameraNew(true);
				}
			}
			else
			{
				if (middleState == 1)
				{
					// moved too far, this can't be a middle click
					m_middleClickDownAt = Vector3.one * 1e23f;
				}
			}
		}

		if (isInDesignTable == false && !Utility.GetKey(KeyCode.LeftAlt) && !Utility.GetKey(KeyCode.RightAlt))
		{
			for (KeyCode k = KeyCode.Alpha0; k <= KeyCode.Alpha9; ++k)
			{
				if (Utility.GetKeyDown(k))
				{
					int id = (int) k - (int) KeyCode.Alpha0;
					if (Utility.GetKey(KeyCode.LeftControl) || Utility.GetKey(KeyCode.RightControl))
					{
						MPlayerPrefs.SetString($"Bookmark{id}", $"{Camera.main.transform.position.x.ToStringInv()},{Camera.main.transform.position.y.ToStringInv()},{Camera.main.transform.position.z.ToStringInv()},{Camera.main.transform.eulerAngles.x.ToStringInv()},{Camera.main.transform.eulerAngles.y.ToStringInv()},{Camera.main.transform.eulerAngles.z.ToStringInv()}");
						ShowCriticalError($"Bookmark {id} set", 2);
					}
					else
					{
						var s = MPlayerPrefs.GetString($"Bookmark{id}");
						var bits = s.Split(',');
						if (bits.Length >= 6)
						{
							Vector3 pos, euler;
							if (bits[0].TryFloatInv(out pos.x) && bits[1].TryFloatInv(out pos.y) && bits[2].TryFloatInv(out pos.z) &&
							    bits[3].TryFloatInv(out euler.x) && bits[4].TryFloatInv(out euler.y) && bits[5].TryFloatInv(out euler.z))
							{
								Camera.main.transform.position = pos;
								Camera.main.transform.eulerAngles = euler;
								ShowCriticalError($"Bookmark {id} restored", 2);
							}
						}
					}
				}
			}
		}
	}

	public (float, Vector3?, bool, float, float) GetDepthOfFieldOverride()
	{
		if (IsInTitleScreen())
			return (0, null, false, 0, 0);
		if (PlayerHandManager.Me.UnlockPowerSequenceLevel > 0)
			return (PlayerHandManager.Me.UnlockPowerSequenceLevel, PlayerHandManager.Me.Fingertip.position, true, 2, 2);
		if (IsPossessing)
			return (0, null, false, 0, 0);
		if (IsDesignTable || DistrictPolygon.IsUnlockSequenceInProgress)
		{
			if (DesignTableManager.Me.IsInDesignInPlaceActively)
				return (1, DesignTableManager.Me.TurntableVisual.transform.position, true, .04f, 2.2f);
			return (0, null, false, 0, 0);
		}
		if (RoadManager.Me.InPathEdit || Pickup.AnyDragging || UndergroundManager.IsActive || TerrainPopulation.Me.IsInMapMode)
			return (0, null, false, 0, 0);
		return (-1, null, false, 0, 0);
	}

	private static DebugConsole.Command s_showtreescmd = new ("trees", _s => {
		var terrain = Terrain.activeTerrain;
		terrain.drawTreesAndFoliage = Utility.SetOrToggle(terrain.drawTreesAndFoliage, _s);
	});
	private static DebugConsole.Command s_setrescmd = new ("res", _s => {
		var bits = _s.Split(',');
		if (bits.Length < 2) return;
		var w = int.Parse(bits[0]);
		var h = int.Parse(bits[1]);
		Utility.SetGameViewSize(w, h);
	});
	private static DebugConsole.Command s_fpsCaptureCmd = new ("fpscapture", _s => Me.StartFPSCapture());
	private static DebugConsole.Command s_gocameraCmd = new ("cam", _s => {
		var bits = _s.Split(",");
		if (bits.Length >= 7)
		{
			var floats = new float[7];
			for (int i = 0; i < 7; ++i) floats[i] = floatinv.Parse(bits[i]);
			Camera.main.transform.position = new Vector3(floats[0], floats[1], floats[2]);
			Camera.main.transform.rotation = new Quaternion(floats[3], floats[4], floats[5], floats[6]);
		}
		else
		{
			Debug.LogError($"Camera at [{Camera.main.transform.position.ToString("0.0").Replace("(", "").Replace(")", ",")}\t{Camera.main.transform.rotation.ToString("0.000").Replace("(", "").Replace(")", ",")}]");
		}
	});
	bool m_fpsCaptureActive = false, m_fpsCaptureSkipFrame;
	List<(float, Vector3, Quaternion)> m_fpsCaptureTimes = new();
	float m_fpsCaptureLastTime;
	bool m_fpsCaptureWasInProfileMode;
	Texture2D m_fpsCaptureGraph;
	Unity.Collections.NativeArray<Color32> m_fpsCaptureGraphData;
	RawImage m_fpsCaptureGraphDisplay;
	int m_fpsCaptureGraphColour = 0;

	private void InitFPSGraph()
	{
		m_fpsCaptureGraphColour = 1 - m_fpsCaptureGraphColour;
		if (m_fpsCaptureGraph == null)
		{
			m_fpsCaptureGraph = new Texture2D(c_fpsGraphW, c_fpsGraphH, TextureFormat.RGBA32, false);
			var clear = new Color(0, 0, 0, 32f / 255f);
			for (int i = 0; i < c_fpsGraphW * c_fpsGraphH; ++i) m_fpsCaptureGraph.SetPixel(i % m_fpsCaptureGraph.width, i / m_fpsCaptureGraph.width, clear);
			m_fpsCaptureGraph.Apply(false, false);
			m_fpsCaptureGraphData = m_fpsCaptureGraph.GetRawTextureData<Color32>();
			var canvasObj = new GameObject("FPSCaptureCanvas");
			canvasObj.transform.SetParent(UIManager.Me.transform.parent);
			var canvas = canvasObj.AddComponent<Canvas>();
			var rt = canvas.GetComponent<RectTransform>();
			rt.anchorMin = new Vector2(0, 0);
			rt.anchorMax = new Vector2(1, 0);
			rt.pivot = new Vector2(0.5f, 0.5f);
			rt.sizeDelta = new Vector2(0, 256);
			rt.anchoredPosition = new Vector2(0, 128);
			m_fpsCaptureGraphDisplay = canvasObj.AddComponent<RawImage>();
			m_fpsCaptureGraphDisplay.texture = m_fpsCaptureGraph;
		}
	}

	const int c_fpsGraphW = 2048;
	const int c_fpsGraphH = 128;
	private void UpdateFPSGraph()
	{
		int col = m_fpsCaptureTimes.Count - 1;
		if (col < 0) return;
		const float c_maxGraphTimepF = .15f;
		var ms = (int)(m_fpsCaptureTimes[col].Item1 * (c_fpsGraphH / c_maxGraphTimepF));
		var cFill = m_fpsCaptureGraphColour == 0 ? new Color32(255, 0, 0, 128) : new Color32(0, 0, 255, 160);
		var cDot = m_fpsCaptureGraphColour == 0 ? new Color32(255, 0, 0, 255) : new Color32(0, 0, 255, 255);
		var cClear = m_fpsCaptureGraphColour == 0 ? new Color32(255, 0, 0, 32) : new Color32(0, 0, 255, 32);
		for (int y = 0, index = col; y < c_fpsGraphH; ++y, index += c_fpsGraphW)
			m_fpsCaptureGraphData[index] = y < ms ? cFill : (y == ms ? cDot : cClear);
		for (int y = 1; y <= 6; ++y)
		{
			var msf = (int) (.01666666f * y * (c_fpsGraphH / c_maxGraphTimepF));
			var index = col + msf * c_fpsGraphW;
			var c = m_fpsCaptureGraphData[index];
			c.g = 160; c.a = c.a < (byte)180 ? (byte)180 : c.a;
			m_fpsCaptureGraphData[index] = c;
		}
		m_fpsCaptureGraph.Apply(false, false);
	}
	

	public void StartFPSCapture()
	{
		if (Screen.width != 1920 || Screen.height != 1080)
			Utility.SetGameViewSize(1920, 1080);

		InitFPSGraph();

		m_fpsCaptureActive = true;
		m_fpsCaptureLastTime = Time.unscaledTime;
		m_fpsCaptureTimes.Clear();
		m_fpsCaptureSkipFrame = true;
		m_fpsCaptureWasInProfileMode = ProfileMode;
		if (m_fpsCaptureWasInProfileMode)
			ProfileMode = false;
		Time.captureDeltaTime = 1.0f / 30.0f;
		SaveEnabled = false;
		CameraPanNode.StartCameraPanSequence("FPSCapture", true, () => EndFPSCapture());
	}
	void EndFPSCapture()
	{
		Time.captureDeltaTime = 0;
		SaveEnabled = true;
		m_fpsCaptureActive = false;
		var s = "Capture results:,MS/F,CamX,CamY,CamZ,CamQX,CamQY,CamQZ,CamQW\n";
		for (int i = 0; i < m_fpsCaptureTimes.Count; ++i) s += $"{i},{m_fpsCaptureTimes[i].Item1 * 1000 : 0.00},{m_fpsCaptureTimes[i].Item2.ToString("0.0").Replace("(", "").Replace(")", ",")} {m_fpsCaptureTimes[i].Item3.ToString("0.000").Replace("(", "").Replace(")", ",")}\n";
		var file = $"{Application.persistentDataPath}/FPSCapture_{PerformanceManager.Me.GetDisabledLayers().Replace(" ", "_")}{DayNight.Me.PartOfDay()}_{m_fpsCaptureTimes.Count}_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.csv";
		System.IO.File.WriteAllText(file, s);
		Debug.LogError($"FPSCapture complete, {m_fpsCaptureTimes.Count} frames");
		if (m_fpsCaptureWasInProfileMode)
			ProfileMode = true;
	}
	void UpdateFPSCapture()
	{
		if (m_fpsCaptureActive == false) return;
		#if UNITY_EDITOR
		Selection.activeObject = PerformanceManager.Me.gameObject;
		#endif
		var t = Time.unscaledTime;
		var dt = t - m_fpsCaptureLastTime;
		m_fpsCaptureLastTime = t;
		if (dt > .00001f && m_fpsCaptureSkipFrame == false)
			m_fpsCaptureTimes.Add((dt, Camera.main.transform.position, Camera.main.transform.rotation));
		m_fpsCaptureSkipFrame = false;
		
		UpdateFPSGraph();
	}

	private bool m_wasRecording = false;
	private float m_originalLODBias = 0;
	void UpdateDebug() {
		bool showVersion = SettingsPaneOpen || IsInTitleScreen();
		if (s_screenGrabMode) showVersion = false;
		if (m_versionDisplay.gameObject.activeSelf != showVersion)
			m_versionDisplay.gameObject.SetActive(showVersion);
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		UpdateFPSCapture();
		UpdateEditMoveBlockers();

		if (IsInTitleScreen())
			if (Utility.ModifiedKey(KeyCode.N, false, true, false, "New Game", false))
				DeleteSaveDataFromServer((b) => { });
#if UNITY_IOS
		if (s_screenGrabMode)
		{
			s_consoleDisplay = null;
			m_shouldShowMemory = true;
		}
		else if (m_shouldShowMemory)
		{
			m_systemUsedMemoryRecorder = Unity.Profiling.ProfilerRecorder.StartNew(Unity.Profiling.ProfilerCategory.Memory, "System Used Memory");
			if (DebugConsole.Me == null) new DebugConsole();
			m_shouldShowMemory = false;
			s_consoleDisplay = () => $"<size=32>M:{m_systemUsedMemoryRecorder.LastValue/1024/1024} F:{m_averageTenthMSPerFrame * 0.1f:n1} [P:{GlobalData.Me.ActivePathThreads} {GlobalData.Me.ActivePathCallbacks}]            </size>";
		}
#endif
		if (LoadComplete && NGCarriableResource.GetInfo("None").Name != "None")
			ShowCriticalError($"NGCarriableResource None is broken - {NGCarriableResource.GetInfo("None").Name}");
		
		const float c_timeToShowDebug = 5f;
		m_debugText.gameObject.SetActive(Time.time < m_lastDebugTrigger + c_timeToShowDebug);
		
		m_debugData = $"Drag:{DragHolder.s_activeDrags.Count}";

		DebugAutoBackup();
		
		DebugConsoleTick();

		if (!DataReady) return;
		if (KeyboardConsuming) return;

		DebugConsole.Me?.CheckKeyboardShortcuts();
		
		bool shiftHeld = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
		bool controlHeld = Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl);
		bool altHeld = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt);

		if (shiftHeld && controlHeld && !altHeld && Input.GetKeyDown(KeyCode.L))
			ToggleLocoStage();

		if (BuildDetails.DemoMode && !shiftHeld && controlHeld && !altHeld && Input.GetKeyDown(KeyCode.B))
		{
			NGUnlocks.PayBills = true;
			m_state.m_gameInfo.m_timeSinceLastPay = 10000;
		}
		
		if (!controlHeld && !altHeld)
		{
			/*if (Input.GetKeyDown(KeyCode.T)) ToggleDesignTableMode(GlobalData.c_productId);
			if (Input.GetKeyDown(KeyCode.Y)) ToggleDesignTableMode(m_currentBuilding);
			if (Input.GetKeyDown(KeyCode.B))
			{
				ToggleBuildingPlaceMode(m_currentBuilding);
				RefreshSelectionDebug();
			}*/
			if ((Input.GetKeyDown(KeyCode.LeftShift) || Input.GetKeyDown(KeyCode.RightShift)) && Input.GetKeyDown(KeyCode.Minus))
			{
				Debug.LogError($"Save");
				WriteSaveData();
			}
		}
		/*for (int i = 0; i < 10; ++i) {
			if (Input.GetKeyDown((KeyCode)((int)KeyCode.Alpha0 + i))) {
				int index = (i-1+10)%10;
				if (shiftHeld) index += 10;
				if (controlHeld) index += 20;
				if (altHeld) index += 40;
				SelectDebugItem(index);
			}
		}*/

		if (Input.GetKeyDown(KeyCode.Q) && controlHeld && altHeld)
			VisitWorld(-1);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if (Input.GetKeyUp(KeyCode.Backslash))
		{
			BranchingCombosEnabled = !BranchingCombosEnabled;
		}
#endif //DEVELOPMENT_BUILD

		var recording = Utility.IsVideoRecording;
		if (recording != m_wasRecording)
		{
			m_wasRecording = recording;
			if (recording)
			{
				// into recording mode
				m_originalLODBias = QualitySettings.lodBias; 
				QualitySettings.lodBias = 100;
				//QualitySettings.terrainQualityOverrides |= TerrainQualityOverrides.TreeDistance;
				//QualitySettings.terrainTreeDistance = 10000;
			}
			else
			{
				// leave recording mode
				foreach(var building in NGManager.Me.m_maBuildings)
				{
					building.RefreshOrderBoard();
				}
				QualitySettings.lodBias = m_originalLODBias;
			}
		}
#endif
	}

	static DebugConsole.Command s_jiggle = new DebugConsole.Command("jiggle", (_s) => {
		if (!string.IsNullOrWhiteSpace(_s)) {
			foreach (var b in NGManager.Me.FactoryList) if (b.Name.ToLower() == _s) b.ShowBuildingAnimation();
		}
	});

	private static DebugConsole.Command s_gift = new DebugConsole.Command("gift", (_s) => {
		string searchStr = _s.ToLower();
		int iGift = NGBusinessGift.s_gifts.FindIndex(o => o.m_name.ToLower().Equals(searchStr));
		if (iGift > -1)
		{
			NGBusinessGiftsPanel.CreateOrCall(new List<NGBusinessGift>() { NGBusinessGift.s_gifts[iGift] });
		}
	});

	private static DebugConsole.Command s_rest = new DebugConsole.Command("rest", _s => 
	{
		if(string.IsNullOrWhiteSpace(_s))
		{
			foreach(NGMovingObject character in NGManager.Me.m_MAWorkerList)
			{
				MAWorker worker = character as MAWorker;
				if(worker != null) worker.Energy = 0f;
			}
		}
		else
		{
			if(int.TryParse(_s, out int iWorker))
			{
				int iW = NGManager.Me.m_MAWorkerList.FindIndex(x => x.GameState.m_id == iWorker);
				if(iW > -1)
				{
					MAWorker worker = NGManager.Me.m_MAWorkerList[iW] as MAWorker;
					if(worker != null) worker.Energy = 0f;
				}
			}
		}
	});

	private static DebugConsole.Command s_time = new DebugConsole.Command("timescale", (_s) => { SetTimeScale(_s); });
	private static DebugConsole.Command s_timeShort = new DebugConsole.Command("t", (_s) => { SetTimeScale(_s); });
	private static void SetTimeScale(string _s)
	{
		float[] allowed = { 0f, 0.1f, 0.3f, 0.5f, 1f, 2f, 3f, 5f };
		float time = float.Parse(_s), finalTime = allowed[allowed.Length - 1];
		for (int i = 0; i < allowed.Length; i++)
		{
			float a1 = allowed[i];
			if (a1 >= time)
			{
				float a0 = allowed[i > 0 ? i - 1 : i];
				finalTime = time >= (a0 + (a1 - a0) * 0.5f) ? a1 : a0;
				break;
			}
		}
		Debug.Log($"Debug Command: 'timescale = {finalTime}'");
		Time.timeScale = finalTime;
	}

	static System.Func<string> s_consoleDisplay = null;

	public static void SetConsoleDisplay(Func<string> _cb)
	{
		s_consoleDisplay = _cb;
	}

	public static void ShowCriticalError(string _error, float _showForSeconds = -1)
	{
		if (DebugConsole.Me == null) new DebugConsole();
		if (_showForSeconds > 0)
			Me.StartCoroutine(Co_ShowCriticalError(
				() => { s_consoleDisplay = () => _error.PulseColour(0xFF8080, 0xFFFFFF); },
				_showForSeconds,
				() => { s_consoleDisplay = null; }));
		else
			s_consoleDisplay = () => _error.PulseColour(0xFF8080, 0xFFFFFF);
	}

	private static IEnumerator Co_ShowCriticalError(Action _cb1, float _seconds, Action _cb2)
	{
		_cb1();
		yield return new WaitForSeconds(_seconds);
		_cb2();
	} 
	
	private static DebugConsole.Command s_showPointLightShadowsWithNoDisableCmd = new ("checkpointshadows", _s =>
	{
		bool showPrefabs = string.IsNullOrEmpty(_s) == false;
		foreach (var light in Resources.FindObjectsOfTypeAll<Light>())
		{
			if ((light.gameObject.scene.name == null) != showPrefabs) continue;
			if (light.type == LightType.Point && light.shadows != LightShadows.None)
			{
				if (light.gameObject.GetComponent<DisableLightWithDistance>() == null)
				{
					Debug.LogError($"Point Light {light.name} has shadows but no DisableLightWithDistance", light.gameObject);
				}
			}
		}
	});
	private static DebugConsole.Command s_showBuildingComplexitiesCmd = new ("buildingstats", _s =>
	{
		var res = new List<(MABuilding, int, int)>();
		foreach (var b in NGManager.Me.m_maBuildings)
		{
			var rnds = b.GetComponentsInChildren<Renderer>(false);
			int verts = 0, activeRenderers = 0;;
			foreach (var rnd in rnds)
			{
				if (rnd is MeshRenderer)
					verts += rnd.GetComponent<MeshFilter>()?.sharedMesh?.vertexCount ?? 0;
				else if (rnd is SkinnedMeshRenderer smr)
					verts += smr.sharedMesh.vertexCount;
				if (rnd.enabled && rnd.gameObject.activeInHierarchy) ++activeRenderers;
			}
			res.Add((b, activeRenderers, verts));
		}
		res.Sort((a, b) => b.Item2.CompareTo(a.Item2));
		var s = "";
		for (int i = 0; i < res.Count; ++i)
			s += $"{res[i].Item1.name} rnds:{res[i].Item2} verts:{res[i].Item3}\n";
		Debug.LogError(s);
	}, "Show how many renderers are present in each building, sorted by highest count");

	private static DebugConsole.Command s_clearerror = new DebugConsole.Command("clearerror", _s => s_consoleDisplay = null);

	static string s_consoleShowOccluders = "";
	static DebugConsole.Command _toggleOccluders = new DebugConsole.Command("occluders", (_s) => { s_consoleShowOccluders = _s; });

	public static string s_fpsExtra = "";
	static bool s_consoleShowFPS = false;
	static DebugConsole.Command _toggleFPS = new DebugConsole.Command("fps", (_s) => {
		Utility.SetOrToggle(ref s_consoleShowFPS, _s);
	}, "Show the ms per frame and frames per second counter", "<bool>");

	private static float s_frameCountEndTime = -1f;
	private static int s_frameCountDuration = -1;
	private static int s_initialFrameCount = 0;
	static DebugConsole.Command _startFPSCum = new DebugConsole.Command("countFrames", (_s) => {
		s_initialFrameCount = Time.frameCount;
		s_frameCountDuration = Utility.GetIntFromString(_s, 60);
		s_frameCountEndTime = Time.unscaledTime + s_frameCountDuration;
		CameraPanNode.StartCameraPanSequence("FPSCapture", true);
	});
	
	
	static bool s_consoleShowTOD = false;
	static DebugConsole.Command _toggleTOD = new DebugConsole.Command("tod", (_s) => {
		Utility.SetOrToggle(ref s_consoleShowTOD, _s);
	}, "Show the debug Time Of Day  display", "<bool>");
	static bool s_consoleShowAudioDebug = false;
	static DebugConsole.Command _toggleAudioDebug = new DebugConsole.Command("debugaudio", (_s) => {
		Utility.SetOrToggle(ref s_consoleShowAudioDebug, _s);
	}, "Show a debug display of various global audio systems", "<bool>");

	static bool s_noManaCost = false;
	static DebugConsole.Command s_togglenomanacost = new("nomana", _s => Utility.SetOrToggle(ref s_noManaCost, _s), "Stop hand powers costing mana", "<bool>");
	#if UNITY_EDITOR || DEVELOPMENT_BUILD
	public static bool NoManaCost => s_noManaCost;
	#else
	public static bool NoManaCost => false;
	#endif
	
	
	private static DebugConsole.Command s_sleepercmd = new DebugConsole.Command("sleeper", _s => s_sleeper = int.Parse(_s));
	
	private static DebugConsole.Command s_thermals = new ("thermals", _s => s_consoleDisplay = () => $"Thermal State: <color=yellow>{ThermalMonitoring.GetThermalState()}</color>");
	
	private static DebugConsole.Command s_showtouch = new ("touches", _s => s_consoleDisplay = () =>
	{
		var s = "";
		int z = (int)(Me.GetZoom() * 4);
		for (int i = -20; i <0; ++i) s += (z > 0 || i <= z) ? " " : ".";
		s += "|";
		for (int i = 0; i < 20; ++i) s += (z < 0 || i >= z) ? " " : ".";
		return $"{TouchManager.DebugStr()}\n{s}";
	});
	private static DebugConsole.Command s_showdrags = new ("drags", _s => s_consoleDisplay = () => $"{DragHolder.s_activeDrags.Count} {DragHolder.s_activeLongPresses.Count}       ssa:{SubSceneActive} ad:{DragHolder.AnyDragging} rm:{RoadManager.Consuming} pik:{Pickup.AnyDragging}     ");

	private static bool s_showBlockDragDebug = false;
	private static DebugConsole.Command s_showBlockDragDebugCmd = new DebugConsole.Command("debugblock", _s => Utility.SetOrToggle(ref s_showBlockDragDebug, _s));

	private static string DEBUGPossessAudio
	{
		get
		{
			var chr = Me.PossessedCharacter;
			if (chr == null) return "";
			var index = CameraRenderSettings.Me.GetStrongestSplatPoint(chr.transform.position);
			var name = CameraRenderSettings.Me.GetSplatName(index);
			var type = GlobalData.GetTerrainSurfaceAtPoint(chr.transform.position);
			return $"{index}: {name} - {type}";
		}
	}
	
	void RunPowerLevel()
	{
#if UNITY_IOS || UNITY_ANDROID
		var sleepMs = (100 - GameSettings.PowerLevel) / 2;
		if (sleepMs > 0) Thread.Sleep(sleepMs);
#endif
	}
	
	private static int s_sleeper = 0;
	private long m_averageTenthMSPerFrame, m_lastFrameTimeTenthMs;
	void TrackFPS()
	{
		RunPowerLevel();
		
		if (s_frameCountEndTime > -1f && Time.unscaledTime > s_frameCountEndTime)
		{
			var frames = Time.frameCount - s_initialFrameCount;
			Debug.LogError($"mSPF: {s_frameCountDuration * 1000f / frames}\nFPS: {(float)frames / s_frameCountDuration}");
			s_frameCountEndTime = -1f;
		}
		
		long nextTime = System.DateTime.UtcNow.Ticks;
		m_lastFrameTimeTenthMs = (nextTime - s_lastFPSTimes[(s_nextFPSTimeIndex - 1) & (s_lastFPSTimes.Length - 1)]) / 1000;
		m_averageTenthMSPerFrame = (nextTime - s_lastFPSTimes[s_nextFPSTimeIndex & (s_lastFPSTimes.Length - 1)]) / (1000 * s_lastFPSTimes.Length);
		s_lastFPSTimes[s_nextFPSTimeIndex & (s_lastFPSTimes.Length - 1)] = nextTime;
		s_nextFPSTimeIndex++;
		if (s_nextFPSTimeIndex < s_lastFPSTimes.Length) return;
		if (IsDesignTable) m_state.m_gameInfo.m_designTablePerf.Track(m_averageTenthMSPerFrame);
		else if (IsCountryside) m_state.m_gameInfo.m_townPerf.Track(m_averageTenthMSPerFrame);
		else m_state.m_gameInfo.m_otherPerf.Track(m_averageTenthMSPerFrame);
	}
	
	static long[] s_lastFPSTimes = new long[16];
	static int s_nextFPSTimeIndex = 0;
	void DebugConsoleTick() {
		if (DebugConsole.Me == null) new DebugConsole();
		bool shift = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
		bool control = Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl);
		bool alt = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt);
		if ((!shift && control && alt && (Input.GetKeyDown(KeyCode.D) || Input.GetKeyDown(KeyCode.Home))) || TouchManager.TouchInputThreeFingerTap) {
			DebugConsole.Me.Activate();
		}
		if (DebugConsole.Me != null)
		{
			string label = "";
			if (s_consoleShowFPS)
			{
				label = $"<size=36>{((float) m_averageTenthMSPerFrame * 0.1f):N1}ms/f ({(10000.0f / (float) m_averageTenthMSPerFrame):N1}fps) {(Application.targetFrameRate == -1 ? "" : $"[{Application.targetFrameRate}]")}</size>\n<size=24>MS:{m_lastFrameTimeTenthMs * .1f:N1}</size>{GlobalData.Me.PhysicsTimeString}{s_fpsExtra}";
			}
			else if (s_showCameraDetails)
			{
				label = GetCameraDetailsString();
			}
			else if (Pickup.PickupPlaneDebugActive)
				label = $"<size=24>{Pickup.PickupPlaneDebug}</size>";
			else if (s_consoleShowAudioDebug) {
				label = $"<size=24>Ambient: {m_lastLocationAmbientType} [sea:{m_seaFraction:0.00}]\nMusicName: {m_lastMusicSwitch}\nMusic: {m_lastLocationMusic}\n{m_lastLocationType}: {m_lastLocationString}\nDistrict: {m_lastLocationDistrict}\nFeel: {m_lastLocationFeel}\nOverlay: {m_lastOverlayType}\nSplat: {m_surroundingSplatDebug}\nPossessIsOn: {(PossessedCharacter == null ? "<not possessed>" : PossessedCharacter.SurfaceTypeTracker.DebugInfo)}\nOcclusion Raycasts: {AudioClipManager.Me.RaycastSounds}\n{(m_wasInInteriorMode ? "WwiseSetMode_Interior" : "WwiseSetMode_Exterior")}\n{GenericAudioSwitch.DEBUGStates()}\nPossess:{DEBUGPossessAudio}\nCameraHeight: {m_audioCameraHeightParameter:n1}\nTOD Speed: {DayNight.Me.TimePassingSpeedMultiplier}\nAlignment: {m_currentAlignment}</size>";
			} else if (s_consoleShowTOD)  {
				label = $"<size=48>{DayNight.Me.CurrentTimeString} {(m_state.m_gameTime.m_gameTimeLocked ? "(frozen)" : "")} [{DayNight.Me.m_timeStage:0.00} {DayNight.Me.Darkness():0.00}] day {DayNight.Me.m_day} {DayNight.Me.CurrentWorkingDay}</size>\n<size=24>Speed: {DayNight.Me.TimePassingSpeedMultiplier} - {DayNight.Me.IsTimePassingFast}</size>";
			} else if (string.IsNullOrEmpty(s_consoleShowOccluders) == false) {
				label = $"<size=24>{AudioClipManager.Me.DebugOccluders(s_consoleShowOccluders)}</size>";
			} else if (TerrainPopulation.ShowGrassDebug) {
				label = $"<size=24>{TerrainPopulation.GrassDebug}</size>";
			} else if (s_showBlockDragDebug) {
				label = $"<size=24>{Utility.DebugGet("BlockDrag")}</size>";
#if UNITY_EDITOR
			} else if (DesignTableManager.DebugHingeSnaps) {
				label = $"<size=16>{DesignTableManager.Me.GetDebugSnapData()}</size>";
			} else if (PossessionFootstrikeDebugActive) {
				label = PossessionFootstrikeDebug();
#endif
			} else if (s_consoleDisplay != null)  {
				label = s_consoleDisplay();
			}
			DebugConsole.Me.ShowLabelIfInactive(label);
		}
		
		if (m_debugConsoleExecute) {
			m_debugConsoleExecute = false;
			DebugConsole.Me.ExecuteConsole(m_debugConsoleCommand);
		}
	}

	static DebugConsole.Command _cameraReset = new DebugConsole.Command("CameraReset", (_s) => {
		if (_s == "true")
		{
			m_cameraResetActive = true;
		}
		else
		{
			m_cameraResetActive = false;
			CameraResetIcon.SetActive(false);
		}
	});

	static DebugConsole.Command _employmentBalloons = new DebugConsole.Command("EmploymentBalloons", (_s) => {
		if (_s == "true")
		{
			GameManager.Me.EmploymentBalloons = true;
		}
		else
		{
			GameManager.Me.EmploymentBalloons = false;
		}
	});

	static DebugConsole.Command _inOutBalloons = new DebugConsole.Command("InOutBalloons", (_s) => {
		if (_s == "true")
		{
			GameManager.Me.InOutBalloons = true;
		}
		else
		{
			GameManager.Me.InOutBalloons = false;
		}
	});

	static DebugConsole.Command _verboseBalloons = new DebugConsole.Command("VerboseBalloons", (_s) => {
		if (_s == "true")
		{
			GameManager.Me.VerboseBalloons = true;
		}
		else
		{
			GameManager.Me.VerboseBalloons = false;
		}
	});

	static DebugConsole.Command _unlockRoads = new DebugConsole.Command("UnlockRoads", (_s) => NGUnlocks.Roads = Utility.SetOrToggle(NGUnlocks.Roads, _s));
	static DebugConsole.Command _unlockBuildingDesign = new DebugConsole.Command("UnlockBuildingDesign", (_s) => MAUnlocks.Me.m_designBuildings = Utility.SetOrToggle(MAUnlocks.Me.m_designBuildings, _s));

	static DebugConsole.Command _giveCash = new DebugConsole.Command("cash", (_s) => {
        long f;
        if (long.TryParse(_s, out f))
        {
            NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Earned, f, "Cheat", PlayerHandManager.Me.Fingertip);
        }
    });
    //favours=royal,100
    private static DebugConsole.Command _giveFavours = new DebugConsole.Command("favours", (_s) => 
    {
		string[] favours = _s.Split(',');
		if (favours.Length == 2)
		{
			if (Enum.TryParse(favours[0], out MAFactionInfo.FactionType faction))
			{
				if (int.TryParse(favours[1], out int f))
				{
					NGPlayer.Me.AddFavors(faction, f, "Debug");
				}
			}
		}
		else if (int.TryParse(favours[1], out int f))
		{
			for(var i = MAFactionInfo.FactionType.Royal; i < MAFactionInfo.FactionType.Last; i++)
			{
				NGPlayer.Me.AddFavors(i, f, "Debug");
			}
		}
    });
    // static DebugConsole.Command _activateBusinessStageCelebration = new DebugConsole.Command("celebration", (_s) => {
    //     NGTutorialManager.Me.TestTriggerBusinessStageCelebration();
    // });

    static DebugConsole.Command _activeCharacter = new DebugConsole.Command("character", (_s) => {
        NGUnlocks.CharacterName = _s;
    });

    static DebugConsole.Command _activeStory = new DebugConsole.Command("storyindex", (_s) => {
        int f;
        if (int.TryParse(_s, out f))
        {
			NGUnlocks.Story = f;
        }
    });
    static DebugConsole.Command _giveRoyalFavours = new DebugConsole.Command("royalfavours", (_s) => {
		if(int.TryParse(_s, out int _amount)) {
			NGPlayer.Me.m_royalFavors.Add(CurrencyContainer.TransactionType.InGame, _amount, "Cheat");
		}
	});
	static DebugConsole.Command _giveLordsFavours = new DebugConsole.Command("lordsfavours", (_s) => {
		if(int.TryParse(_s, out int _amount)) {
			NGPlayer.Me.m_lordsFavors.Add(CurrencyContainer.TransactionType.InGame, _amount, "Cheat");
		}
	});
	static DebugConsole.Command _giveCommonerFavours = new DebugConsole.Command("commonerfavours", (_s) => {
		if(int.TryParse(_s, out int _amount)) {
			NGPlayer.Me.m_commonersFavors.Add(CurrencyContainer.TransactionType.InGame, _amount, "Cheat");
		}
	});
	static DebugConsole.Command _giveMysticFavours = new DebugConsole.Command("mysticfavours", (_s) => {
		if(int.TryParse(_s, out int _amount)) {
			NGPlayer.Me.m_mysticFavors.Add(CurrencyContainer.TransactionType.InGame, _amount, "Cheat");
		}
	});

#if UNITY_EDITOR
	static DebugConsole.Command _toggleCharacterDebugHelper = new DebugConsole.Command("characterhelper", (_s) => {
        PlayerHandManager.Me.showCharacterDebugHelper = !PlayerHandManager.Me.showCharacterDebugHelper;
    });

	static DebugConsole.Command _toggleDisableCharacterDamage = new DebugConsole.Command("disablecharacterdamage", (_s) => {
        GameManager.Me.DisableCharacterDamage = _s == "true" ? true : false;
    });
#endif
	
	public const int c_layerIgnoreRaycast = 2;
	public const int c_layerIgnoreCamera = 3;
	public const int c_layerDesignHarness = 7;
	public const int c_layerTerrain = 8;
	public const int c_layerRoads = 9;
	public const int c_layerBodyToBody = 12;
	public const int c_layerDistrict = 19;
	public const int c_layerImageCapture = 30;
	public const int c_layerDesignTable = 31;
	public const int c_layerIgnoreRaycastBit = 1 << c_layerIgnoreRaycast;
	public const int c_layerRoadsBit = 1 << c_layerRoads;
	public const int c_layerTerrainBit = 1 << c_layerTerrain;
	public const int c_layerBodyToBodyBit = 1 << c_layerBodyToBody;
	public const int c_layerDistrictBit = 1 << c_layerDistrict;
	public const int c_layerIgnoreCameraBit = 1 << c_layerIgnoreCamera;
	public const int c_layerImageCaptureBit = 1 << c_layerImageCapture;
	public const int c_layerDesignTableBit = 1 << c_layerDesignTable;
	public float DistanceToTerrain(float _default = 50) {
		return DistanceToTerrain(new Vector3(Screen.width*.5f, Screen.height*.5f, 0), _default);
	}
	public float DistanceToTerrain(Vector3 _screenPos, float _default = 50) {
		var ray = m_camera.RayAtScreenPosition(_screenPos);
		RaycastHit hitInfo;
		if (Physics.Raycast(ray, out hitInfo, 1e23f, c_layerTerrainBit)) {
			return hitInfo.distance;
		}
		return _default;
	}

	private void UpdateCameraZoomState()
    {
		float height = m_camera.transform.position.y;
		ECameraZoomState nextState = m_cameraZoomState;

		switch(m_cameraZoomState)
        {
			case ECameraZoomState.Default:
				if (height > m_townManagementThreshold.ShowHeight)
                {
					nextState = ECameraZoomState.TownManagement;
                }
				break;
			case ECameraZoomState.TownManagement:	
				if (height < m_townManagementThreshold.HideHeight)
				{
					nextState = ECameraZoomState.Default;
				}
				else if (height > m_mapOverlayThreshold.ShowHeight)
				{
					nextState = ECameraZoomState.MapOverlay;
				}
				break;
			case ECameraZoomState.MapOverlay:
				if (height < m_mapOverlayThreshold.HideHeight)
				{
					nextState = ECameraZoomState.TownManagement;
				}
				break;
			default:
				break;
        }

		if(nextState != m_cameraZoomState)
        {
			SetCameraZoomState(nextState);
        }

		UpdateMapOverlay();
	}

	private void SetCameraZoomState(ECameraZoomState _state)
    {
		ECameraZoomState prevState = m_cameraZoomState;
		m_cameraZoomState = _state;

		OnExitCameraZoomState?.Invoke(prevState);
		OnEnterCameraZoomState?.Invoke(m_cameraZoomState);
	}

	public void SetTownManagementMode(ETownManagementMode _mode)
	{
		ETownManagementMode prevMode = m_townManagementMode;
		m_townManagementMode = _mode;

		OnExitTownManagementMode?.Invoke(prevMode);
		OnEnterTownManagementMode?.Invoke(m_townManagementMode);
	}

	void UpdateMapOverlay()
	{
		/*var mapOverlay = DistrictManager.Me;
		if (mapOverlay != null) 
		{
			float mapOverlayAlpha = (m_cameraZoomState == ECameraZoomState.MapOverlay && m_camera.isActiveAndEnabled) ? 1.0f : 0.0f;
			mapOverlay.SetTargetAlpha(mapOverlayAlpha, m_camera.isActiveAndEnabled);
		}*/
	}

	Vector3 m_lastMousePos;
	Vector3 m_draggingMouse = Vector3.zero;
	Vector3 m_dragStart;
#if UNITY_IOS || UNITY_ANDROID
	const int c_dragButton = 0;
#else
	const int c_dragButton = 1;
#endif
	const int c_rotateButton = 2;//c_dragButton + 1;
	const KeyCode c_rotateKey = KeyCode.C;
	const KeyCode c_leaveFocusKey = KeyCode.Escape;
	Vector3 m_lastMouseDownPosition = Vector3.zero;
	float m_pushSpeed = 0;
	void CheckCameraEdgeScroll() {
		if(NGDirectionCardBase.DraggingCard != null && NGDirectionCardBase.DraggingCard.BlockScreenDrag())
			return;
		if(NGBusinessGiftsPanel.Me != null && Utility.IsMouseOver(NGBusinessGiftsPanel.Me.m_background) )
			return;

		var mousePos = TouchManager.FirstInputPosition;
		if (TouchManager.TouchInputActive)
		{
			DragBase firstDrag = null;
			foreach (var kvp in DragHolder.s_activeDrags)
			{
				firstDrag = kvp.Value;
				break;
			}
			if (firstDrag == null) return;
			mousePos = firstDrag.InputPosition;
		}
		
		mousePos.x /= Screen.width;
		mousePos.y /= Screen.height;
		const float c_marginFraction = .1f;
		Vector3 push = Vector3.zero;
		if (mousePos.x < c_marginFraction) {
			push.x = -1;
		} else if (mousePos.x > 1-c_marginFraction) {
			push.x = 1;
		}
		if (mousePos.y < c_marginFraction) {
			push.z = -1;
		} else if (mousePos.y > 1-c_marginFraction) {
			push.z = 1;
		}
		if (push.sqrMagnitude > 0) {
			var pushDown = m_lastMouseDownPosition; pushDown.x /= Screen.width; pushDown.y /= Screen.height;
			float cx = .5f;//pushDown.x;
			float cy = .5f;//pushDown.y;
			if (push.x * push.x < .01f*.01f) push.x = Mathf.Clamp((mousePos.x - cx) / (.5f - c_marginFraction), -1, 1);
			else if (push.z * push.z < .01f*.01f) push.z = Mathf.Clamp((mousePos.y - cy) / (.5f - c_marginFraction), -1, 1);
			float pushSpeed = DistanceToTerrain() * .4f;
			m_pushSpeed = Mathf.Lerp(m_pushSpeed, pushSpeed, .1f);
			push *= m_pushSpeed * Time.deltaTime;
			var fwd = m_camera.transform.forward; fwd = fwd.GetXZ().normalized;
			var right = m_camera.transform.right; right = right.GetXZ().normalized;
			m_camera.transform.position += right * push.x + fwd * push.z;
		} else {
			m_pushSpeed = Mathf.Lerp(m_pushSpeed, 0, .1f);
		}
	}

	static int s_keyboardConsumeState = 0;
	public static void SetKeyboardConsumeState(bool _consuming)
	{
		s_keyboardConsumeState += _consuming ? 1 : -1;
	}
	
	public static bool IsInSubScene { get; set; }
	public static bool FullSubSceneActive => IsInSubScene;
	public static bool SubSceneActive => RoadManager.Consuming || BuildingPlacementManager.Consuming || FullSubSceneActive;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public static bool InputConsuming => SubSceneActive || DragHolder.AnyDragging || DesignTableManager.Me.IsDesignGloballyConsuming || DesignTableManager.Me.IsInDesignInPlace || MALock.s_dragging || Me.IsPossessing || UndergroundManager.IsActive || PlayerHandManager.Me.RadialMenuActive || Me.IsInArcadium;
#else
	public static bool InputConsuming => DragHolder.AnyDragging || Me.IsPossessing;
#endif
	public static bool InputConsumingTouchGestures => InputConsuming && NGManager.Me.m_pinchGestureMustCoincide == false;
	public static bool InputDragging => RoadManager.Consuming || Pickup.AnyDragging || DesignTableManager.Me.IsDesignGloballyConsuming || MALock.s_dragging;
	public static bool ClickConsumed => SubSceneActive || DesignTableManager.Me.IsDesignGloballyConsuming || DesignTableManager.Me.IsInDesignInPlace || Me.IsPossessing || UndergroundManager.IsActive || PlayerHandManager.Me.IsConsuming;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public static bool KeyboardConsuming => (PlayerDetailsGUI.IsInstanceInScene && Me.IsDesignTable == false && Me.IsCloseableSubScene == false) ||
	                                        DebugConsole.HasFocus || 
	                                        s_keyboardConsumeState > 0 ||
	                                        CryptManager.Me.IsFrescoActiveOrLeaving ||
	                                        NGRename.Me != null;
#else
	public static bool KeyboardConsuming => false;
#endif
	public static bool TownSubModeActive => IsInCountryside && (BuildMode.BuildToolActive || DesignTableManager.IsDesignInPlace || DesignTableManager.Me.m_isInDesignGlobally);
	public static bool TownStandardInteractionsActive => IsInCountryside && TownSubModeActive == false && IsVisitingInProgress == false;

	bool m_inputWasConsumed = false;
	bool m_inputWasDragging = false;
	
	public KeyCode c_zoomInKey => KeyboardController.Me.GetKey(EKeyboardFunction.DollyCamera, 0);
	public KeyCode c_zoomOutKey => KeyboardController.Me.GetKey(EKeyboardFunction.DollyCamera, 1);
	
	float m_forceZoom = 0;
	public float GetZoom(bool _includeMouseWheel = true) {
		bool usedZoom = false;
		var zoom = ReadKeyAxis(c_zoomInKey, c_zoomOutKey, c_keySpeedZoom, ref m_keySmoothZoom, ref usedZoom, true, NGManager.Me.m_zoomKeysSmoothing) * Time.deltaTime;
		if (Utility.GetKey(KeyCode.LeftCommand) || Utility.GetKey(KeyCode.RightCommand) || Utility.GetKey(KeyCode.LeftControl) || Utility.GetKey(KeyCode.RightControl)) zoom = 0;
		if (Utility.GetKey(KeyCode.LeftShift) || Utility.GetKey(KeyCode.RightShift)) zoom *= .1f;
		zoom += TouchManager.TouchInputPinchDelta * 300;
		if (InputConsumingTouchGestures)
			zoom = 0; // if we're dragging or making don't zoom
		if (_includeMouseWheel)
			 zoom += Utility.SmoothedMouseWheel * 6;
		const float c_forceZoomFalloffSpeed = 1.0f / .75f;
		const float c_forceZoomMag = -15;
		if (m_forceZoom > .9f) Utility.ClearMouseWheel();
		m_forceZoom = Mathf.Max(0, m_forceZoom - Time.deltaTime * c_forceZoomFalloffSpeed);
		return Mathf.Lerp(zoom, c_forceZoomMag, m_forceZoom);
	}

	private bool m_cameraPanSequenceInProgress = false; public bool CameraPanSequenceInProgress => m_cameraPanSequenceInProgress;
	public void StartCameraPanSequence()
	{
		m_cameraPanSequenceInProgress = true;
	}

	public void UpdateCameraPanSequence(Vector3 _position, Vector3 _forward, string _nodeData)
	{
		var camTransform = Camera.main.transform;
		camTransform.position = _position;
		camTransform.LookAt(_position + _forward, Vector3.up);
	}

	public void EndCameraPanSequence()
	{
		m_cameraPanSequenceInProgress = false;
	}

	private static DebugConsole.Command s_lookatbuildingsCmd = new ("lookatbuildings", _s => Me.VisuallyInspectBuildings(), "Visually inspect all buildings");

	private void VisuallyInspectBuildings() => StartCoroutine(Co_VisuallyInspectBuildings());

	private IEnumerator Co_VisuallyInspectBuildings()
	{
		StartCameraPanSequence();
		var allCmds = new List<NGCommanderBase>();
		allCmds.AddRange(NGManager.Me.m_NGCommanderList);
		allCmds.AddRange(NGManager.Me.m_NGCommanderListOutOfRange);
		var camTransform = Camera.main.transform;
		NGCommanderBase best = null;
		float bestXZ = 1e23f;
		foreach (var cmd in allCmds)
		{
			if (cmd.transform.position.x + cmd.transform.position.z < bestXZ)
			{
				bestXZ = cmd.transform.position.x + cmd.transform.position.z;
				best = cmd;
			}
		}
		s_consoleDisplay = () => $"Inspecting {best.name} - {allCmds.Count} remaining";
		bool nextPressed = false, autoNext = true;
		while (best != null)
		{
			var cmd = best;
			allCmds.Remove(best);
			
			var focus = cmd.DoorPosInner;
			var forward = (focus - cmd.DoorPosOuter).GetXZNorm();
			if (forward.sqrMagnitude < .01f * .01f) forward = Vector3.forward;
			var pos = focus - forward * 30 + Vector3.up * 20;
			forward = (focus - pos).normalized;

			void CheckPause()
			{
				if (Input.GetKeyDown(KeyCode.Minus)) { nextPressed = true; autoNext = false; }
				if (Input.GetKeyDown(KeyCode.Equals)) autoNext = !autoNext;
			}
			var fromPos = camTransform.position;
			var fromFwd = camTransform.forward;
			const float c_lerpTime = .3f;
			for (float t = 0; t < c_lerpTime; t += Time.deltaTime)
			{
				var f = t / c_lerpTime;
				f = f * f * (3 - f - f);
				camTransform.position = Vector3.Lerp(fromPos, pos, f);
				camTransform.forward = Vector3.Slerp(fromFwd, forward, f);
				CheckPause();
				yield return null;
			}
			camTransform.position = pos;
			camTransform.forward = forward;
			for (float t = 0; t < .4f; t += Time.deltaTime)
			{
				if (autoNext == false) t = 0;
				if (nextPressed)
				{
					t = 100;
					nextPressed = false;
				}
				CheckPause();
				yield return null;
			}

			best = null;
			bestXZ = 1e23f;
			foreach (var check in allCmds)
			{
				var xz = (check.transform.position - cmd.transform.position).xzSqrMagnitude();
				if (xz < bestXZ)
				{
					 bestXZ = xz;
					 best = check;
				}
			}
		}
		s_consoleDisplay = null;
		EndCameraPanSequence();
	}

	public void MinCameraTransition(Vector3 _focus, float _distance)
    {
		Vector3 dt = m_camera.transform.position - _focus;
		if (dt.magnitude > _distance) return;
		CameraTransition(_focus, 0.1f, _distance, CameraMoveComplete);
	}
	public void CameraMoveComplete() { }
	Coroutine m_cameraTransition;

	private static DebugConsole.Command s_cameraPanCmd = new ("pan", _s =>
	{
		var bits = _s.Split(',');
		if (bits.Length < 2) return;
		var focus = new Vector3(floatinv.Parse(bits[0]), 0, floatinv.Parse(bits[1])).GroundPosition();
		if (bits.Length == 2)
			Me.CameraTransition(focus, 2.0f, 100.0f, null);
		else
		{
			var rotY = floatinv.Parse(bits[2]);
			Me.CameraTransition(focus, 30, rotY, 2.0f, 100.0f, null);
		}
	});
	
	private static bool s_showCameraDetails = false;
	private static DebugConsole.Command s_showCamCmd = new ("showcamera", _s => Utility.SetOrToggle(ref s_showCameraDetails, _s));
	private string GetCameraDetailsString()
	{
		var cam = m_camera;
		var pos = cam.transform.position;
		var hit = RaycastTerrain(.5f, .5f);
		if (hit.HasValue)
		{
			var rot = cam.transform.rotation.eulerAngles;
			return $"Camera focus:{hit.Value.x:0.00} {hit.Value.z:0.00}  incline:{rot.x:0.00}  heading:{rot.y:0.00}  distance:{(pos - hit.Value).magnitude:0.00}";
		}
		return "";
	}

	public void CameraTransition(Vector3 _focus, float _transitionTime, float _distance, System.Action _onEnd)
	{
		CameraTransition(_focus, m_camera.transform.rotation, _transitionTime, _distance, _onEnd);
	}

	public void CameraTransition(Vector3 _focus, float _incline, float _heading, float _transitionTime, float _distance, System.Action _onEnd)
	{
		CameraTransition(_focus, Quaternion.Euler(_incline, _heading, 0), _transitionTime, _distance, _onEnd);
	}

	public void CameraTransition(Vector3 _focus, Quaternion _rotation, float _transitionTime, float _distance, System.Action _onEnd)
	{
		if (m_cameraTransition != null)
		{
			StopCoroutine(m_cameraTransition);
			m_cameraTransition = null;
		}
		m_cameraTransition = StartCoroutine(Co_CameraTransition(_focus, _rotation, _transitionTime, _distance, _onEnd));
	}
	IEnumerator Co_CameraTransition(Vector3 _focus, Quaternion _rotation, float _transitionTime, float _distance, System.Action _onEnd)
	{
		var startRot = m_camera.transform.rotation;
		var finalForward = _rotation * Vector3.forward;
		Vector3 finalPos = _focus - finalForward * _distance;
		Vector3 fromPos = m_camera.transform.position;
		for (float progress = 0; progress <= 1f; progress += Time.deltaTime / _transitionTime)
		{
			yield return null;
			var smooth = progress * progress * (3.0f - progress - progress);
			var pos = Vector3.Lerp(fromPos, finalPos, smooth);
			var rot = Quaternion.Slerp(startRot, _rotation, smooth);
			m_camera.transform.position = pos;
			m_camera.transform.rotation = rot;
		}
		m_cameraTransition = null;
		if(_onEnd != null)	
			_onEnd();
	}

	public void CameraTransition(Vector3 _focus, Vector3 _pos, float _transitionTime, System.Action _onEnd)
	{
		var tiltDist = _pos.y;
		var lerp = Mathf.Clamp01((tiltDist - m_cameraHeightToStartElevating) / (m_cameraHeightToFinishElevating - m_cameraHeightToStartElevating));
		lerp = lerp * lerp * (3.0f - lerp - lerp);
		float angle = Mathf.LerpUnclamped(m_cameraLowElevation, m_cameraHighElevation, lerp);

		Vector3 forward = _focus - _pos;
		forward.y = 0.0f;
		float adj = forward.magnitude;
		float opp = Mathf.Tan(angle * Mathf.Deg2Rad) * adj;
		_pos.y = _focus.y + opp;
		forward = _focus - _pos;
		Quaternion rot = Quaternion.LookRotation(forward, Vector3.up);

		CameraTransition(_pos, rot, _transitionTime, _onEnd);
	}

	public void CameraTransition(Vector3 _pos, Quaternion _rot, float _transitionTime, System.Action _onEnd)
	{
		if (m_cameraTransition != null)
		{
			StopCoroutine(m_cameraTransition);
			m_cameraTransition = null;
		}
		m_cameraTransition = StartCoroutine(Co_CameraTransition(_pos, _rot, _transitionTime, _onEnd));
	}
	IEnumerator Co_CameraTransition(Vector3 _pos, Quaternion _rot, float _transitionTime, System.Action _onEnd)
	{
		Vector3 fromPos = m_camera.transform.position;
		Quaternion fromRot = m_camera.transform.rotation;

		Vector3 finalPos = _pos;
		Quaternion finalRot = _rot;

		for (float progress = 0; progress <= 1f; progress += Time.deltaTime / _transitionTime)
		{
			yield return null;
			var smooth = progress * progress * (3.0f - progress - progress);
			var pos = Vector3.Lerp(fromPos, finalPos, smooth);
			var rot = Quaternion.Lerp(fromRot, finalRot, smooth);
			m_camera.transform.position = pos;
			m_camera.transform.rotation = rot;
		}
		m_cameraTransition = null;
		if (_onEnd != null)
			_onEnd();
	}

	public void CameraFade(Vector3 _pos, Quaternion _rot, System.Action _onEnd)
	{
		if (m_cameraTransition != null)
		{
			StopCoroutine(m_cameraTransition);
			m_cameraTransition = null;
		}
		m_cameraTransition = StartCoroutine(Co_CameraFade(_pos, _rot, _onEnd));
	}

	IEnumerator Co_CameraFade(Vector3 _pos, Quaternion _rot, System.Action _onEnd)
	{
		Crossfade.Me.Fade(() =>
		{
			m_camera.transform.position = _pos;
			m_camera.transform.rotation = _rot;
			return true;
		},
		() =>
		{
			m_cameraTransition = null;
			if (_onEnd != null)
				_onEnd();
		});

		yield return null;
	}

	public void ResetCamera() {
		// copy from seed:
		/*
		     "m_cameraPosition": {
        "x": -73.89474487304688,
        "y": 143.31634521484376,
        "z": -9.78622055053711
    },
    "m_cameraEulers": {
        "x": 30.00000762939453,
        "y": 14.99999713897705,
        "z": 0.0
    },
		 */
		m_camera.transform.position = new Vector3(-73.9f, 143.3f, -9.8f);
		m_camera.transform.eulerAngles = new Vector3(30f, 15f, 0);
	}

	public void ResetCameraRotation() {
		m_camera.transform.eulerAngles = CameraRotatedEulars;
        m_camera.transform.position = CameraRotatedPosition;
        if (CameraResetIcon != null)
            CameraResetIcon.SetActive(false);
        CameraRotated = false;
	}


	public Texture2D m_fingerCursor, m_fingerCursorPressed;
	bool m_isFingerIndicatorOn = false;
	private void SetFingerIndicator(bool _on)
	{
		m_isFingerIndicatorOn = _on;
		if (_on)
			StartCoroutine(Co_ShowCursor());
	}

	IEnumerator Co_ShowCursor()
	{
		while (m_isFingerIndicatorOn)
		{
			var tex = Input.GetMouseButton(0) ? m_fingerCursorPressed : m_fingerCursor;
			Cursor.SetCursor(tex, new Vector2(tex.width * .5f, tex.height * .5f), CursorMode.Auto);
			yield return null;
		}
		Cursor.SetCursor(null, Vector2.zero, CursorMode.Auto);
	}
	static DebugConsole.Command s_fingermode = new DebugConsole.Command("finger", _s => Me.SetFingerIndicator(!Me.m_isFingerIndicatorOn));
		
	public float m_cameraLowElevation = 40, m_cameraHighElevation = 80;
	public float m_cameraHeightToStartElevating = 150, m_cameraHeightToFinishElevating = 300;
	public float m_cameraHeightMaximum = 600;

	public float m_namedPointMaxSpawnDist = 100f;
	public float m_banditGroupDisableDist = 200f;
	public Vector3 m_cameraResetEulers = new Vector3(30,15,0);

	public enum ECameraZoomState
	{
		Default,
		TownManagement,
		MapOverlay
	}

	private ECameraZoomState m_cameraZoomState = ECameraZoomState.Default;
	public ECameraZoomState CameraZoomState { get { return m_cameraZoomState; } }

	private ETownManagementMode m_townManagementMode = ETownManagementMode.None;
	public ETownManagementMode TownManagementMode { get { return m_townManagementMode; } }

	[System.Serializable]
	public class ZoomTransitionThreshold
	{
		public float Height;
		[Range(1.0f, 20.0f)] public float TransitionOffset = 5.0f;
		public ZoomTransitionThreshold(float _height)
		{
			Height = _height;
		}
		public float HideHeight => Height - TransitionOffset;
		public float ShowHeight => Height + TransitionOffset;
	};

	public ZoomTransitionThreshold m_townManagementThreshold = new ZoomTransitionThreshold(280.0f);
	public ZoomTransitionThreshold m_mapOverlayThreshold = new ZoomTransitionThreshold(380.0f);
	public static event System.Action<ECameraZoomState> OnExitCameraZoomState;
	public static event System.Action<ECameraZoomState> OnEnterCameraZoomState;
	public static event System.Action<ETownManagementMode> OnExitTownManagementMode;
	public static event System.Action<ETownManagementMode> OnEnterTownManagementMode;

	public Vector3 CamPos => m_camera.transform.position;
	public Vector3 CamRot => m_camera.transform.eulerAngles;
	public Vector3 CamFwd => m_camera.transform.forward;
	public Vector3 CamFocus => CamPos + CamFwd * 100;

	public Vector3 DefaultCameraAngles => new Vector3(30, 15.1f, 0);

	public bool BuildModeDeprecated => false;

	static DebugConsole.Command s_freecamease = new DebugConsole.Command("fcease", _s => {
		var bits = _s.Split(',');
		Me.m_freeCameraRotateEase = floatinv.Parse(bits[0]);
		if (bits.Length > 1)
			Me.m_freeCameraMoveEase = floatinv.Parse(bits[1]);
	});
	static DebugConsole.Command s_freecamspeeds = new DebugConsole.Command("fcspeed", _s => {
		var bits = _s.Split(',');
		Me.m_freeCameraRotateSpeed = floatinv.Parse(bits[0]);
		if (bits.Length > 1)
			Me.m_freeCameraElevateSpeed = floatinv.Parse(bits[1]);
		if (bits.Length > 2)
			Me.m_freeCameraMoveSpeed = floatinv.Parse(bits[2]);
	});
	static DebugConsole.Command s_freecam = new DebugConsole.Command("freecam", _s => Me.FreeCameraMode = Utility.SetOrToggle(Me.FreeCameraMode, _s));
	bool m_freeCameraMode = false; public bool FreeCameraMode { get { return m_freeCameraMode; } set { m_freeCameraMode = value; } }
	bool m_wasFreeCameraMode = false;
	float m_freeCameraRotateSpeed = 1, m_freeCameraElevateSpeed = 1, m_freeCameraMoveSpeed = 1;
	float m_freeCameraRotateEase = .85f, m_freeCameraMoveEase = 0;
	Vector3 m_freeCameraEulers;
	float m_freeCameraRotKeyX, m_freeCameraRotKeyY, m_freeCameraFwdKey, m_freeCameraSideKey, m_freeCameraRaiseKey;
	Vector3 m_freeCameraLastMouse;
	bool UpdateFreeCamera() {
		if (m_freeCameraMode == false) return false;
		if (!m_wasFreeCameraMode) {
			// just entered
			m_freeCameraLastMouse = Utility.InputPos;
			m_freeCameraEulers = m_camera.transform.eulerAngles;
		}
		if (Input.GetKeyDown(KeyCode.Escape)) {
			m_freeCameraMode = false;
			m_wasFreeCameraMode = false;
			Cursor.lockState = CursorLockMode.None;
			return false;
		}
		Cursor.lockState = CursorLockMode.Locked;
		m_wasFreeCameraMode = true;
		float fwd = (Input.GetKey(KeyCode.W) ? 1 : 0) - (Input.GetKey(KeyCode.S) ? 1 : 0);
		float side = (Input.GetKey(KeyCode.D) ? 1 : 0) - (Input.GetKey(KeyCode.A) ? 1 : 0);
		float raise = (Input.GetKey(KeyCode.E) ? 1 : 0) - (Input.GetKey(KeyCode.Q) ? 1 : 0);
		m_freeCameraFwdKey = Mathf.Lerp(m_freeCameraFwdKey, fwd, 1-m_freeCameraMoveEase);
		m_freeCameraSideKey = Mathf.Lerp(m_freeCameraSideKey, side, 1-m_freeCameraMoveEase);
		m_freeCameraRaiseKey = Mathf.Lerp(m_freeCameraRaiseKey, raise, 1-m_freeCameraMoveEase);
		var dMouse = Utility.InputPos - m_freeCameraLastMouse;
		m_freeCameraLastMouse += dMouse;
		const float c_freeCameraRotSpeed = 10f;
		const float c_freeCameraElevateSpeed = 3f;
		const float c_freeCameraSpeed = 10;
		float freeCameraRotSpeed = c_freeCameraRotSpeed * m_freeCameraRotateSpeed;
		float freeCameraElevateSpeed = c_freeCameraElevateSpeed * m_freeCameraElevateSpeed;
		float freeCameraSpeed = c_freeCameraSpeed * m_freeCameraMoveSpeed;
		if (Input.GetMouseButton(2)) {
			m_freeCameraEulers.y += dMouse.x * (freeCameraRotSpeed * Time.deltaTime);
			m_freeCameraEulers.x -= dMouse.y * (freeCameraElevateSpeed * Time.deltaTime);
		}
		float rotFwd = (Input.GetKey(KeyCode.UpArrow) ? 1 : 0) - (Input.GetKey(KeyCode.DownArrow) ? 1 : 0);
		float rotSide = (Input.GetKey(KeyCode.RightArrow) ? 1 : 0) - (Input.GetKey(KeyCode.LeftArrow) ? 1 : 0);
		m_freeCameraRotKeyX = Mathf.Lerp(m_freeCameraRotKeyX, rotSide, 1-m_freeCameraRotateEase);
		m_freeCameraRotKeyY = Mathf.Lerp(m_freeCameraRotKeyY, rotFwd, 1-m_freeCameraRotateEase);
		m_freeCameraEulers.y += m_freeCameraRotKeyX * (freeCameraRotSpeed * 5 * Time.deltaTime);
		m_freeCameraEulers.x -= m_freeCameraRotKeyY * (freeCameraElevateSpeed * 5 * Time.deltaTime);
		
		if (m_freeCameraEulers.y < -180) m_freeCameraEulers.y += 360; if (m_freeCameraEulers.y > 180) m_freeCameraEulers.y -= 360;
		m_freeCameraEulers.x = Mathf.Clamp(m_freeCameraEulers.x, -80, 80);
		m_camera.transform.eulerAngles = m_freeCameraEulers;

		var fwdV = m_camera.transform.forward;
		var sideV = m_camera.transform.right;
		var upV = Vector3.up;
		if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift)) {
			fwdV = fwdV.GetXZNorm();
			sideV = sideV.GetXZNorm();
		}
		var move = fwdV * m_freeCameraFwdKey + sideV * m_freeCameraSideKey + upV * m_freeCameraRaiseKey;
		m_camera.transform.position += move * (freeCameraSpeed * Time.deltaTime);

		return true;
	}

	float ReadKeyAxis(KeyCode _neg, KeyCode _pos, float _scale, ref float _smooth, ref bool _userInput, bool _allowShift = false, float _smoothing = .8f)
	{
		float f = 0;
		if (!Utility.GetKey(KeyCode.LeftAlt) && !Utility.GetKey(KeyCode.RightAlt) &&
		    !Utility.GetKey(KeyCode.LeftControl) && !Utility.GetKey(KeyCode.RightControl) &&
		    (_allowShift || (!Utility.GetKey(KeyCode.LeftShift) && !Utility.GetKey(KeyCode.RightShift))) &&
		    !KeyboardConsuming)
		{
			if (Utility.GetKey(_neg)) { f -= _scale; _userInput = true; }
			if (Utility.GetKey(_pos)) { f += _scale; _userInput = true; }
		}
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (DistrictManager.Me.IsEditing)
			_smooth = f;
		else
#endif
		{
			_smooth = Mathf.Lerp(_smooth, f, 1 - _smoothing);
			if (_smooth * _smooth < .001f * .001f) _smooth = 0;
		}
		return _smooth;
	}
	Vector2 ReadKeyStick(KeyCode _left, KeyCode _right, KeyCode _down, KeyCode _up, float _scale, ref Vector2 _smooth, ref bool _userInput, float _smoothing = .8f)
	{
		var v = Vector2.zero;
		v.x = ReadKeyAxis(_left, _right, _scale, ref _smooth.x, ref _userInput, _smoothing: _smoothing);
		v.y = ReadKeyAxis(_down, _up, _scale, ref _smooth.y, ref _userInput, _smoothing: _smoothing);
		return v;
	}

	public static Vector3 DragPosRaycast(Camera _cam, int _mask, Vector3 _default)
	{
		RaycastHit hitMouse;
		Vector3 hitPos = _default;
		var ray = _cam.ScreenPointToRay(GetDragPosition());
		if (Physics.Raycast(ray, out hitMouse, 1e23f, -1)) hitPos = hitMouse.point;
		return hitPos;
	}

	public static Vector3 DragPosRaycastPlane(Camera _cam, Vector3 _origin, Vector3 _normal)
	{
		var ray = _cam.ScreenPointToRay(GetDragPosition());
		var plane = new Plane(_normal, _origin);
		float hitDist;
		plane.Raycast(ray, out hitDist);
		var newPos = ray.GetPoint(hitDist);
		return newPos;
	}

	private void ShowResetRotationGUI()
	{
		if (CameraResetIcon != null && m_cameraResetActive)
            CameraResetIcon.SetActive(true);

		if(CameraRotated == false)
        {
            CameraRotatedEulars = m_cameraResetEulers;
            CameraRotatedPosition = m_camera.transform.position;
            CameraRotated = true;
        }
	}

	private float m_keySmoothZoom, m_keySmoothRotate;
	private Vector2 m_keySmoothMove, m_keySmoothMove2;

	public void ClearCameraSmoothing()
	{
		m_keySmoothZoom = m_keySmoothRotate = 0;
		m_keySmoothMove = m_keySmoothMove2 = Vector2.zero;
		Utility.ClearMouseWheel();
	}

	private float m_cameraControlMoveSpeed = 1;
	private float m_cameraControlRotateSpeed = 1;
	private float m_cameraControlZoomSpeed = 1;

	private const string c_cameraMoveSpeedPref = "CamMoveSpeed";
	private const string c_cameraRotateSpeedPref = "CamRotSpeed";
	private const string c_cameraZoomSpeedPref = "CamZoomSpeed";

	private void ReadCameraControlSpeeds()
	{
		if (MPlayerPrefs.HasKey(c_cameraMoveSpeedPref))
			m_cameraControlMoveSpeed = MPlayerPrefs.GetFloat(c_cameraMoveSpeedPref);
		if (MPlayerPrefs.HasKey(c_cameraRotateSpeedPref))
			m_cameraControlRotateSpeed = MPlayerPrefs.GetFloat(c_cameraRotateSpeedPref);
		if (MPlayerPrefs.HasKey(c_cameraZoomSpeedPref))
			m_cameraControlZoomSpeed = MPlayerPrefs.GetFloat(c_cameraZoomSpeedPref);
		GameSettings.LoadSettings();
		m_cameraControlMoveSpeed = Mathf.Clamp(m_cameraControlMoveSpeed, .5f, 2f);
		m_cameraControlRotateSpeed = Mathf.Clamp(m_cameraControlRotateSpeed, .5f, 2f);
		m_cameraControlZoomSpeed = Mathf.Clamp(m_cameraControlZoomSpeed, .5f, 2f);
	}
	public float CameraMoveSpeed(float _set = 1e23f)
	{
		if (_set < 1e22f)
		{
			MPlayerPrefs.SetFloat(c_cameraMoveSpeedPref, _set);
			m_cameraControlMoveSpeed = _set;
		}
		return m_cameraControlMoveSpeed;
	}
	public float CameraRotateSpeed(float _set = 1e23f)
	{
		if (_set < 1e22f)
		{
			MPlayerPrefs.SetFloat(c_cameraRotateSpeedPref, _set);
			m_cameraControlRotateSpeed = _set;
		}
		return m_cameraControlRotateSpeed;
	}
	public float CameraZoomSpeed(float _set = 1e23f)
	{
		if (_set < 1e22f)
		{
			MPlayerPrefs.SetFloat(c_cameraZoomSpeedPref, _set);
			m_cameraControlZoomSpeed = _set;
		}
		return m_cameraControlZoomSpeed;
	}

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	private static DebugConsole.Command s_startClickController = new DebugConsole.Command("startclick", _s =>
	{
		Me.m_clickerActive = true;
		if (string.IsNullOrEmpty(_s))
		{
			ClickController.Create("Clickz Clicker");
		}
		else
		{
			Me.DeleteSaveDataFromServer(_ =>
			{
				ClickController.Create("Clickz Clicker");
			});
		}
	});
	public Vector2 DebugCameraMove = Vector2.zero;
#else
	public Vector2 DebugCameraMove { get { return Vector2.zero; } set { } }
#endif

	public KeyCode c_cameraRotateCCW => KeyboardController.Me.GetKey(EKeyboardFunction.RotateCamera, 1);
	public KeyCode c_cameraRotateCW => KeyboardController.Me.GetKey(EKeyboardFunction.RotateCamera, 0);

	public bool IsHoldingCameraControlButton()
	{
		if (Utility.GetMouseButton(c_dragButton)) return true;
		if (Utility.GetMouseButton(c_rotateButton)) return true;
		if (Utility.GetKey(c_rotateKey)) return true;
		if (Utility.GetKey(c_cameraRotateCCW)) return true;
		if (Utility.GetKey(c_cameraRotateCW)) return true;
		if (Utility.GetKey(c_zoomInKey)) return true;
		if (Utility.GetKey(c_zoomOutKey)) return true;
		return false;
	}
	
	const float c_keySpeedZoom = 100;
	const float c_keySpeedMove = 50;
	const float c_keySpeedRotate = 500;

	public float GetKeyboardCameraRotate(ref bool _didRotate)
	{
		return ReadKeyAxis(c_cameraRotateCCW, c_cameraRotateCW,  IsPossessing ? CurrentPossessionSettings.m_possessedKeyCameraSpeedRotate : c_keySpeedRotate, ref m_keySmoothRotate, ref _didRotate, true, NGManager.Me.m_rotateKeysSmoothing) * Time.deltaTime;
	}

	public float GetTotalCameraRotate(bool _mouseIsLegal, ref bool _didRotate)
	{
		float touchRotate = TouchManager.TouchInputAngleChange * -4f;
		if (InputConsumingTouchGestures)
			touchRotate = 0; // if we're dragging or making don't rotate

		float mouseRotate = 0;
		if (TouchManager.TouchInputActive == false && (Utility.GetMouseButton(c_rotateButton) || Utility.GetKey(c_rotateKey)))
		{
			if (Utility.GetMouseButtonDown(c_rotateButton) || Utility.GetKeyDown(c_rotateKey))
			{
				m_rotateMouse = Utility.InputPos;
				m_mouseRotateStartedLegally = _mouseIsLegal;
			}
			else if (m_mouseRotateStartedLegally)
			{
				mouseRotate += m_rotateMouse.x - Utility.InputPos.x;
				m_rotateMouse = Utility.InputPos;
				_didRotate = true;
			}
		}
		float keyboardRotate = -GetKeyboardCameraRotate(ref _didRotate);
		float rotate = (touchRotate + keyboardRotate + mouseRotate) * m_cameraControlRotateSpeed;
		return rotate;
	}
	
	private static DebugConsole.Command s_cameraTrack = new ("track", _s => {
		if (string.IsNullOrEmpty(_s))
		{
			Me.StopCameraTrackingObject();
		}
		switch (_s[0])
		{
			case 'o':
				var id = int.Parse(_s.Substring(1));
				Me.StartCameraTrackingObject(NGManager.Me.FindCharacterByID(id)?.gameObject, Vector3.up * -10 + Vector3.forward * 8);
				break;
		}
	});
	private GameObject m_cameraTrackingFocus = null;
	private Vector3 m_cameraTrackingOffset = Vector3.up * 10;
	bool UpdateCameraTracking()
	{
		if (m_cameraTrackingFocus == null) return false;
		m_camera.transform.position = m_cameraTrackingFocus.transform.position - m_cameraTrackingOffset;
		m_camera.transform.LookAt(m_cameraTrackingFocus.transform, Vector3.up);
		return true;
	}
	public void StartCameraTrackingObject(GameObject _object, Vector3 _offset)
	{
		m_cameraTrackingFocus = _object;
		m_cameraTrackingOffset = _offset;
	}
	public void StopCameraTrackingObject()
	{
		m_cameraTrackingFocus = null;
	}

	float m_currentCameraOffsetHorizontal = 0; public float TargetCameraOffsetHorizontal => m_currentCameraOffsetHorizontal;
	float m_currentCameraOffsetVertical = 0; public float TargetCameraOffsetVertical => m_currentCameraOffsetVertical;
	public float CurrentCameraOffsetVertical => Camera.main.projectionMatrix[1, 2];

	public void SetCameraOffset(float _h, float _v)
	{
		m_currentCameraOffsetHorizontal = _h;
		m_currentCameraOffsetVertical = _v;
	}

	void UpdateCameraOffset()
	{
		return;
		var cam = Camera.main;
		var mat = cam.projectionMatrix;
		mat[0, 2] = Mathf.Lerp(mat[0, 2], m_currentCameraOffsetHorizontal, .1f);
		mat[1, 2] = Mathf.Lerp(mat[1, 2], m_currentCameraOffsetVertical, .1f);
		cam.projectionMatrix = mat;
	}
	
	string m_currentFlowBlock = "", m_flowMusicState = "";
	public void RegisterFlowIndex(string _indexer)
	{
		m_currentFlowBlock = _indexer.ToLower().StartsWith("main:") ? "" : _indexer;
	}

	private const float c_holdFlowMusicStateForSeconds = 2;
	private float m_flowMusicStateEndAt = 0;
	public void SetFlowMusicState(string _state)
	{
		if (string.IsNullOrEmpty(_state) == false)
		{
			m_flowMusicState = _state;
			var holdDuration = m_flowMusicState.ToLower().Contains("_loop") ? 1e23f : c_holdFlowMusicStateForSeconds;
			m_flowMusicStateEndAt = Time.time + holdDuration;
		}
	}

	public bool GlobalInteractCheck(NGMovingObject _chr) => true;//_chr is not MAHoodedCharacter;

	public enum ECurrentFeel
	{
		Calm, // all good
		Unease, // attackers around
		Danger, // attack in progress
	}
	ECurrentFeel m_currentFeel = ECurrentFeel.Calm;
	public ECurrentFeel CurrentFeel => m_currentFeel;

	private void UpdateCurrentFeel()
	{
		if(LoadComplete == false) return;
		var feel = ECurrentFeel.Calm;
		foreach (var creature in NGManager.Me.m_MACreatureList)
		{
			if (creature.Health <= 0) continue;
			if (creature.IsInAttackState)
			{
				feel = ECurrentFeel.Danger;
				break;
			}
			feel = ECurrentFeel.Unease;
		}
		m_currentFeel = feel;
	}

	bool IsNight(float _timeStage)
	{
		return _timeStage < DayNight.c_timeStageDawnStart || _timeStage > DayNight.c_timeStageDuskEnd;
	}

	bool IsDusk(float _timeStage)
	{
		return _timeStage >= DayNight.c_timeStageDayEnd && _timeStage < DayNight.c_timeStageDuskEnd;
	}

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	public static bool IsEditMode = false;
	private static DebugConsole.Command s_editModeCmd = new ("editmode", _s => Utility.SetOrToggle(ref IsEditMode, _s), "Set Edit Mode, allows various player-disabled actions", "<bool>");
#else
	public static bool IsEditMode => false;
#endif
	
	void UpdateDayNightAudio()
	{
		if (LoadComplete == false) return;
		if (IntroControl.Me.InIntroPossess) return;
		
		var camPos = Camera.main.transform.position;
		var gate = GateOpener.NearestToPoint(camPos);
		if (gate != null)
		{
			var distance = (camPos - gate.transform.position).magnitude;
			const float c_maxGateDistance = 1000;
			if (distance > c_maxGateDistance) distance = c_maxGateDistance;
			AudioClipManager.Me.SetGameParameter("DistanceToNearestWatchTower", distance, null);
		}
	}


	public const float c_maxTreeSoundRange = 5f;

	enum EAmbientType
	{
		None = -1,
		TitleScreen,
		Sea,
		Locked,
		DesignTable,
		Coast,
		Cave,
		FirstDistrictType,
	}
	
	static string[] c_ambientTypeEvents = {
		"PlaySound_AmbienceNone",
		"PlaySound_Ocean",
		"PlaySound_RegionLocked",
		"PlaySound_Factory",
		"PlaySound_Shoreline",
		"PlaySound_Cave",	
		"PlaySound_RuralCountry", // oakridge/default
		"PlaySound_RuralCountry", // quarry
		"PlaySound_MineRegion", // metal mine
		"PlaySound_RuralCountry", // wood
		"PlaySound_RuralCountry", // cloth
	};
	bool m_wasInInteriorMode = false;
	string m_lastLocationAmbientType = "";
	string m_lastLocationString = "", m_lastLocationMusic = "", m_lastLocationType = "", m_lastLocationFeel = "", m_lastOverlayType = "", m_lastMusicSwitch = "";
	string m_lastLocationDistrict;
	bool m_lastLocationIsSea;
	bool m_lastLocationUnlocked;
	float m_seaFraction;
	string m_surroundingSplatMajority = "", m_surroundingSplatDebug = "";
	string m_previousGenderChoice = "";
	void UpdateGenderChoiceAudioSwitch()
	{
		var choice = m_state.m_gameInfo.m_chosenGender;
		if (string.IsNullOrEmpty(choice)) return;
		if (choice == m_previousGenderChoice) return;
		m_previousGenderChoice = choice;
		AudioClipManager.Me.SetSoundState("PlayerGender", $"PlayerGender_{choice}");
	}

	public class GenericAudioSwitch
	{
		private static List<GenericAudioSwitch> s_switches = new List<GenericAudioSwitch>();
		private static void Register(GenericAudioSwitch _switch) => s_switches.Add(_switch);
		private static void Unregister(GenericAudioSwitch _switch) => s_switches.Remove(_switch);
		public static void UpdateAll() { for (int i = 0; i < s_switches.Count; ++i) s_switches[i].Update(); }

		public static string DEBUGStates()
		{
			var s = "";
			for (int i = 0; i < s_switches.Count; ++i)
			{
				var sw = s_switches[i];
				s += $"{sw.m_switch}={(sw.m_state == 1 ? "TRUE" : "FALSE")}\n";
			}
			return s;
		}

		private int m_state = -1;
		private string m_switch;
		private Func<bool> m_activeCheckCb;
		public GenericAudioSwitch(string _switch, Func<bool> _activeCheckCb)
		{
			m_state = -1;
			m_switch = _switch;
			m_activeCheckCb = _activeCheckCb;
			Register(this);
		}

		~GenericAudioSwitch()
		{
			FLog.Log("GenericAudioSwitch");
			try {
				Unregister(this); 
			} catch (Exception e) { }
		}

		private void Update()
		{
			var state = m_activeCheckCb() ? 1 : 0;
			if (state != m_state)
			{
				AudioClipManager.Me.SetSoundState(m_switch, $"{m_switch}_{(state == 1 ? "TRUE" : "FALSE")}");
				m_state = state;
			}
		}
	}
	
	public static bool IsInCavelike => UndergroundManager.IsActive || Me.m_state.m_subSceneState.Current != null; 

	private bool m_audioSwitchesInitialised = false;
	void InitialiseAudioSwitches()
	{
		if (m_audioSwitchesInitialised) return;
		m_audioSwitchesInitialised = true;
		new GenericAudioSwitch("InArcadium", () => IsInArcadium);
		new GenericAudioSwitch("InCave", () => IsInCavelike);
	}

	private string m_lastTODPeriod = "";
	private void CheckDayNightAudio()
	{
	#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (LoadComplete == false) return;
		var dayStart = NGManager.Me.m_audioTime_DayStart;
		var dayEnd = NGManager.Me.m_audioTime_DayEnd;
		var t24 = DayNight.Me.CurrentTime24h;
		var isDay = t24 >= dayStart && t24 < dayEnd;
		var todPeriod = isDay ? "Day" : "Night";
		if (m_lastTODPeriod != todPeriod)
		{
			m_lastTODPeriod = todPeriod;
			AudioClipManager.Me.SetSoundState("TOD_Period", todPeriod);
		}
	#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	}
	
	private float m_audioCameraHeightParameter = 0;
	
	private string m_lastDistrictAudio = "Oakridge";
	
	private static DebugConsole.Command s_audioRaycastFractionCmd = new ("audioraycast", _s => floatinv.TryParse(_s, out Me.m_audioRaycastVerticalFraction), "Set the vertical screen fraction for audio raycasting", "<float,0,1>");
	private float m_audioRaycastVerticalFraction = .75f;
	

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	private static string s_playFeelOverride = null;
	private static DebugConsole.Command s_spoofPlayFeelCmd = new ("spoofplayfeel", _s => s_playFeelOverride = string.IsNullOrEmpty(_s) ? null : _s, "Override the PlayFeel with the specified value (calm, unease, danger)", "<string>");
#else
	private static string s_playFeelOverride => null;
#endif

	private string m_currentAlignment = "";
	void SetAlignmentAudio()
	{
		if (LoadComplete == false) return;

		var type = AlignmentManager.Me.GetAlignmentType();
		if (type != m_currentAlignment)
		{
			m_currentAlignment = type;
			AudioClipManager.Me.SetSoundState("PlayerAlignment", $"PlayerAlignment_{type}");
		}
	}

	private string m_holdUntilMusicIsNot = null;
	public void ClearMusic()
	{
		m_holdUntilMusicIsNot = m_lastMusicSwitch;
		m_lastMusicSwitch = "MusicStop";
		AudioClipManager.Me.SetMusicSwitch(c_musicSwitchName, m_lastMusicSwitch);
	}

	public const string c_musicSwitchName = "MusicName";
	
	void UpdateAudio()
	{
		if (AudioClipManager.Me == null || !AudioClipManager.Me.ReadyToPlaySound) return;
		bool hasLoadStarted = LoadStarted;
		
		InitialiseAudioSwitches();

		var camPos = m_camera.transform.position;
		var baseY = 100;//Mathf.Max(camPos.GroundPosition().y, GlobalData.c_seaLevel);
		var cameraHeight = camPos.y - baseY;
		if (m_camera.isActiveAndEnabled == false || LoadComplete == false) cameraHeight = 0;
		m_audioCameraHeightParameter = cameraHeight;
		AudioClipManager.Me.SetGameParameter("CameraHeight", cameraHeight, gameObject);
		
		CheckDayNightAudio();
		
		bool isInInteriorMode = IsInteriorForAudio; // includes things like title screen
		if (isInInteriorMode != m_wasInInteriorMode)
        {
            m_wasInInteriorMode = isInInteriorMode;
            AudioClipManager.Me.PlaySound(isInInteriorMode ? "WwiseSetMode_Interior" : "WwiseSetMode_Exterior", gameObject);
        }
		
		GenericAudioSwitch.UpdateAll();
		
		SetAlignmentAudio();
		
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		UpdateGenderChoiceAudioSwitch();
		
		UpdateDayNightAudio();

		Vector3 pos = camPos.GroundPosition();
		Vector3 focus;
		if (IsPossessing)
		{
			focus = PossessedObject.transform.position;
		}
		else
		{
			RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * (1-m_audioRaycastVerticalFraction), 0), out var hit, c_layerTerrainBit);
			focus = hit.point;
		}
		var testPoint = pos;
		var coastTestPoint = camPos;
		var (districtID, isUnlocked) = DistrictManager.Me.GetDistrictAtPoint(focus);
		var (districtIDPos, isUnlockedPos) = DistrictManager.Me.GetDistrictAtPoint(pos);
		if (isUnlocked == false && isUnlockedPos)
		{
			districtID = districtIDPos;
			isUnlocked = isUnlockedPos;
		}
		var district = DistrictManager.Me.GetDistrictByID(districtID);
		var districtName = district?.m_name ?? "<none>";
		var districtAudio = district?.AudioID; 
		var districtAmbience = districtAudio;
		var districtOverride = DistrictManager.Me.m_data.FindAudioOverrideAtPoint(pos);
		bool ambienceIsOverridden = false, musicIsOverridden = false;
		if (districtOverride != null)
		{
			var overrideLabel = districtOverride.Apply(districtAudio);
			if (overrideLabel.Length > 2 && overrideLabel[1] == ':')
			{
				var c = overrideLabel[0];
				if (c == 'a' || c == 'A')
				{
					districtAmbience = overrideLabel[2..];
					ambienceIsOverridden = true;
				}
				else if (c == 'm' || c == 'M')
				{
					districtAudio = overrideLabel[2..];
					musicIsOverridden = true;
				}
			}
			else
			{
				districtAudio = districtAmbience = overrideLabel;
				ambienceIsOverridden = musicIsOverridden = true;
			}
		}
		if (string.IsNullOrEmpty(districtAudio)) districtAudio = m_lastDistrictAudio;
		m_lastDistrictAudio = districtAudio;
		
		bool isInUnderground = IsInCavelike;
		bool inFresco = CryptManager.Me.IsFrescoActiveOrLeaving;
		bool inCharacterSelect = CharacterSelectionSubscene.IsOpenOrLeaving();
		// location type
		string location, locationType, feel, overlayType = "", musicSwitch = "";
		if (IsInTitleScreen())
		{
			musicSwitch = "TitleScreen";
			locationType = "";
			location = "Titles";
			feel = "";
		}
		else if (inCharacterSelect)
		{
			musicSwitch = "TitleScreen";
			locationType = "";
			location = "CharacterSelect";
			feel = "";
		}
		else if (Time.time < m_flowMusicStateEndAt && string.IsNullOrEmpty(m_flowMusicState) == false)
		{
			musicSwitch = m_flowMusicState;
			locationType = "";
			location = "";
			feel = "";
		}
		else if (inFresco)
		{
			musicSwitch = CryptManager.Me.CurrentFrescoName;
			locationType = "";
			location = "";
			feel = "";
		}
		else if (FailSequenceController.Me.CurrentFailType == FailSequenceController.EFailType.Crypt)
		{
			musicSwitch = ""; // no change
			locationType = "RegionName";
			location = "Region_Dungeon";
			feel = "PlayFeel_Danger";
		}
		else if (IntroControl.Me.InIntroPossess && IntroControl.Me.InCrypt)
		{
			musicSwitch = "Crypt";
			locationType = "InteriorName";
			location = "Interior_Crypt";
			feel = "";
		}
		/*else if (IsInArcadium)
		{
			locationType = "";
			location = "";
			feel = "";
			overlayType = "Overlay_Arcadium";
		}*/
		else if (DesignTableManager.Me.IsInDesignInPlaceActively)
		{
			musicSwitch = "DesignTable";
			locationType = "InteriorName";
			location = "Interior_DesignTable";
			feel = "";
		}
		/*else if (DesignTableManager.Me.m_isInDesignGlobally)
		{
			music = "RegionExplore";
			locationType = "";
			location = "Region_DesignTable";
			feel = "";
		}*/
		else if (IsProductTestingScene)
		{
			musicSwitch = "DesignTable";
			locationType = "InteriorName";
			location = "Interior_ProductTesting";
			feel = "";
		}
		else if (isInUnderground)
		{
			musicSwitch = "RegionExplore";
			locationType = "RegionName";
			location = "Region_Cave";
			feel = "PlayFeel_Unease";
		}
		else
		{
			musicSwitch = cameraHeight < NGManager.Me.m_skyAudioHeight ? "RegionExplore" : "Sky";
			location = $"Region_{districtAudio}";
			locationType = "RegionName";
			switch (CurrentFeel)
			{
				default: case ECurrentFeel.Calm: feel = "PlayFeel_Calm"; break;
				case ECurrentFeel.Unease: feel = "PlayFeel_Unease"; break;
				case ECurrentFeel.Danger: feel = "PlayFeel_Danger"; break;
			}
		}
#else
		string musicSwitch = "RegionExplore";
		string location = "Region_Albion";
		string locationType = "RegionName";
		string feel = "PlayFeel_Calm";
		string overlayType = "";
#endif
		
		var holdMusic = m_holdUntilMusicIsNot == musicSwitch;
		if (holdMusic == false) m_holdUntilMusicIsNot = null;

		if (s_playFeelOverride != null)
			feel = $"PlayFeel_{s_playFeelOverride}";

		if (holdMusic == false && string.IsNullOrEmpty(musicSwitch) == false && musicSwitch != m_lastMusicSwitch)
		{
			if (string.IsNullOrEmpty(m_lastMusicSwitch))
				AudioClipManager.Me.PlayMusic("PlayMusicMaster");
			m_lastMusicSwitch = musicSwitch;
			AudioClipManager.Me.SetMusicSwitch(c_musicSwitchName, m_lastMusicSwitch);
		}
		
		if (string.IsNullOrEmpty(locationType) == false && m_lastLocationString != location)
		{
			m_lastLocationType = locationType;
			m_lastLocationString = location;
			AudioClipManager.Me.SetMusicSwitch(m_lastLocationType, m_lastLocationString);
			//Debug.LogError($"*Music* {m_lastLocationType} to {m_lastLocationString}");
		}
		if (string.IsNullOrEmpty(overlayType) == false && m_lastOverlayType != overlayType)
		{
			m_lastOverlayType = overlayType;
			AudioClipManager.Me.SetMusicSwitch("OverlayType", m_lastOverlayType);
		}
		if (string.IsNullOrEmpty(feel) == false && m_lastLocationFeel != feel)
		{
			m_lastLocationFeel = feel;
			AudioClipManager.Me.SetSoundState("PlayFeel", m_lastLocationFeel);
			//Debug.LogError($"*Music* PlayFeel to {m_lastLocationFeel}");
		}

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (hasLoadStarted)
		{
			const float c_maxWaterDistance = 1000;
			var closestWater = RoadManager.Me.m_pathSet.GetClosestPathToPoint(null, true, camPos, c_maxWaterDistance, out var river, out _, (p) => p.Set.m_createSurfaceMaterial != null);
			if (m_runningWaterAudioHolder != null && river != null)
			{
				var riverSurfaceHeight = closestWater.GroundPosition().y + river.Set.m_createSurfaceLevel;
				var waterToCamera = camPos - closestWater;
				var xzWaterToCamera = waterToCamera.GetXZ();
				var xzWaterDistance = xzWaterToCamera.magnitude;
				var halfWidth = river.Set.m_heightWidthOuter * .85f;
				if (xzWaterDistance > halfWidth)
					closestWater += waterToCamera.GetXZNorm() * halfWidth;
				else
					closestWater = camPos;
				closestWater = closestWater.NewY(riverSurfaceHeight);
				var distance = (camPos - closestWater).magnitude;
				//AudioClipManager.Me.SetGameParameter("DistanceToRunningWater", distance, m_runningWaterAudioHolder);
				m_runningWaterAudioHolder.transform.position = Vector3.Lerp(m_runningWaterAudioHolder.transform.position, closestWater, .5f);
			}
		}
		
		var isSea = testPoint.y < GlobalData.c_seaLevel;
		bool isCoastal = CheckCoastal(coastTestPoint, out m_seaFraction);
		EAmbientType ambientType = EAmbientType.None;
		string ambientString = "";
		if (IsInTitleScreen() || inFresco || inCharacterSelect) ambientType = EAmbientType.TitleScreen;
		else if (DesignTableManager.Me.IsInDesignInPlace || IsProductTestingScene) ambientType = EAmbientType.DesignTable;
		else if (isInUnderground) ambientType = EAmbientType.Cave;
		else if (isCoastal && ambienceIsOverridden == false) ambientType = EAmbientType.Coast;
		else if (isSea && ambienceIsOverridden == false) ambientType = EAmbientType.Sea;
		else if (!isUnlocked) ambientType = EAmbientType.Locked;
		if (ambientType != EAmbientType.None) ambientString = c_ambientTypeEvents[(int)ambientType];
		else ambientString = $"PlaySound_Ambience_{districtAmbience}";
#else
		var testPoint = m_camera.transform.position.GroundPosition();
		EAmbientType ambientType = EAmbientType.FirstDistrictType + 0;
		string districtID = "District1";
		string districtAudio = "None";
		bool isSea = false;
		bool isUnlocked = true;
		string districtName = districtID;
		string ambientString = "";
#endif
		if (ambientString != m_lastLocationAmbientType)
		{
			m_lastLocationAmbientType = ambientString;
			AudioClipManager.Me.PlaySound(ambientString, gameObject, true);
		}
		if (m_lastLocationDistrict != districtID || m_lastLocationIsSea != isSea || m_lastLocationUnlocked != isUnlocked)
		{
			m_lastLocationDistrict = districtID;
			m_lastLocationIsSea = isSea;
			m_lastLocationUnlocked = isUnlocked;
			AudioClipManager.Me.SetSoundSwitch_Future("LocationID", districtName, gameObject);
			AudioClipManager.Me.SetSoundSwitch_Future("LocationType", isSea ? "Sea" : "Land", gameObject);
			AudioClipManager.Me.SetSoundSwitch_Future("LocationUnlocked", isUnlocked ? "Unlocked" : "Locked", gameObject);
		}
		
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		var treeSoundDetails = TerrainPopulation.Me.InstancesInRange(camPos, c_maxTreeSoundRange, true);
		foreach (var go in treeSoundDetails)
		{
			var st = go.GetComponentInChildren<SoundTrigger>();
			if (st != null) st.CheckRange();
		}
#endif

		var splats = CameraRenderSettings.Me.GetSplatInArea(testPoint, 8);
		int maxSplatIndex = 0, secondSplatIndex = 1;
		if (splats[maxSplatIndex] < splats[secondSplatIndex]) (maxSplatIndex, secondSplatIndex) = (secondSplatIndex, maxSplatIndex);
		for (int i = 2; i < splats.Length; ++i)
		{
			if (splats[i] > splats[maxSplatIndex])
				maxSplatIndex = i;
			else if (splats[i] > splats[secondSplatIndex])
				secondSplatIndex = i;
		}
		/*var treeDetails = TerrainPopulation.Me.TreeDetailsInRange(testPoint, 20, "Trees");
		string mostCommonTree = "";
		int mostCommonTreeCount = 0;
		int totalTrees = 0;
		foreach (var kvp in treeDetails)
		{
			if (kvp.Value > mostCommonTreeCount)
			{
				mostCommonTree = kvp.Key;
				mostCommonTreeCount = kvp.Value;
			}
			totalTrees += kvp.Value;
		}
		var mostCommonTreeType = CameraRenderSettings.Me.TreeAudioFromTreeName(mostCommonTree) ?? "";*/
		m_surroundingSplatMajority = CameraRenderSettings.Me.m_splatAudioTypes[maxSplatIndex];
		var typeAtPoint = CameraRenderSettings.Me.GetAudioDataFromPosition(testPoint);
		//m_surroundingSplatDebug = $"[{typeAtPoint}] {CameraRenderSettings.Me.m_splatAudioTypes[maxSplatIndex]}:{(int)(splats[maxSplatIndex] * 100)}% {CameraRenderSettings.Me.m_splatAudioTypes[secondSplatIndex]}:{(int)(splats[secondSplatIndex] * 100)}%\nTrees: {mostCommonTreeType} ({mostCommonTree}) ({mostCommonTreeCount} / {totalTrees})";
		m_surroundingSplatDebug = $"[{typeAtPoint}] {CameraRenderSettings.Me.m_splatAudioTypes[maxSplatIndex]}:{(int)(splats[maxSplatIndex] * 100)}% {CameraRenderSettings.Me.m_splatAudioTypes[secondSplatIndex]}:{(int)(splats[secondSplatIndex] * 100)}%";
	}

	private static DebugConsole.Command s_showCoastalGizmoCmd = new ("debugcoast", _s => Utility.SetOrToggle(ref s_showCoastalGizmo, _s));
	private static DebugConsole.Command s_setCoastalCheckForwardPushCmd = new("setcoastalfwd", _s => Me.m_coastalCheckForwardPush = floatinv.Parse(_s));
	static bool s_showCoastalGizmo = false;
	bool m_coastalGizmosShown = false;
	float m_coastalCheckForwardPush = 0;
	bool CheckCoastal(Vector3 _pos, out float _fraction)
    {
	    var distance = CameraRenderSettings.Me.LookupCoastDistance3D(_pos);
	    _fraction = distance;
	    return distance < NGManager.Me.m_audioCoastalDistance;
	    
	    if (m_coastalGizmosShown) ClearGizmos("Coastal");
	    m_coastalGizmosShown = s_showCoastalGizmo;
	    var camXform = Camera.main.transform;
	    var camFwd = camXform.forward.GetXZNorm();
	    var camRight = camXform.right.GetXZNorm();
	    _pos += camFwd * m_coastalCheckForwardPush;
	    const int c_numRadials = 8;
	    const int c_numRadii = 5;
		const float c_radiusBase = 15;
		int numSea = 0, numTotal = 0;
	    for (int i = 0; i < c_numRadials; ++i)
	    {
		    var theta = i * Mathf.PI * 2 / c_numRadials;
		    float sin = Mathf.Sin(theta), cos = Mathf.Cos(theta);
		    for (int j = 0; j < c_numRadii; ++j)
		    {
			    float radius = (j + 1) * c_radiusBase;
			    var testPoint = _pos + camFwd * (sin * radius) + camRight * (cos * radius);
			    var isSea = testPoint.GroundPositionFast().y < GlobalData.c_seaLevel;
			    if (isSea) ++numSea;
			    ++numTotal;
			    var gizmo = testPoint.GroundPositionFast();
			    gizmo.y = Mathf.Max(gizmo.y, GlobalData.c_seaLevel);
			    if (s_showCoastalGizmo) AddGizmoPoint("Coastal", gizmo, .5f, isSea ? Color.blue : Color.green);
		    }
	    }
	    var fractionSea = (float)numSea / numTotal;
	    _fraction = fractionSea;
	    return fractionSea > .15f && fractionSea < .85f;
    }

	float m_vignetteIntensity = 0;
	void UpdateVignette()
	{
		RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * .5f, 0), out var hit, c_layerTerrainBit);
		var checkPoint = hit.point;
		bool foundCreature = false;
		foreach (var creature in NGManager.Me.m_MACreatureList)
		{
			if (creature.Health <= 0) continue;
			float d2Check = 40 * 40;
			if (creature.IsInAttackState) d2Check = 80 * 80;
			if ((checkPoint - creature.transform.position).sqrMagnitude < d2Check)
			{
				foundCreature = true;
				break;
			}
		}
		m_vignetteIntensity = Mathf.Lerp(m_vignetteIntensity, foundCreature ? 1 : 0, .05f);
		float finalVignetteIntensity = Mathf.Lerp(NGManager.Me.m_vignetteIntensityDay, NGManager.Me.m_vignetteIntensityNight, DayNight.Me.Darkness());
		CameraRenderSettings.Me.SetVignette(NGManager.Me.m_vignetteColour * (m_vignetteIntensity * finalVignetteIntensity));
	}

	private MABuilding m_buildingUnderAttack;
	private float m_buildingUnderAttackTime;
	public void TrackBuildingDamage(MABuilding _building)
	{
		m_buildingUnderAttack = _building;
		m_buildingUnderAttackTime = Time.time;
	}

	public MABuilding GetBuildingUnderAttack()
	{
		const float c_buildingUnderAttackTimeWindow = 4;
		if (Time.time > m_buildingUnderAttackTime + c_buildingUnderAttackTimeWindow) return null;
		return m_buildingUnderAttack;
	}

	bool m_cameraControlledThisFrame = false;
	public bool CameraControlledThisFrame => m_cameraControlledThisFrame;

	private Vector3 m_overrideTownCameraPos, m_overrideTownCameraFwd;
	private void OverrideTownCamera(Vector3 _pos, Vector3 _fwd)
	{
		m_overrideTownCameraPos = _pos;
		m_overrideTownCameraFwd = _fwd;
	}

	private void ApplyTownCameraOverride()
	{
		if (m_overrideTownCameraPos.sqrMagnitude < .001f * .001f) return;
		var cam = Camera.main.transform;
		cam.position = m_overrideTownCameraPos;
		cam.LookAt(m_overrideTownCameraPos + m_overrideTownCameraFwd, Vector3.up);
	}

	public float TiltFromPosition(Vector3 _pos)
    {
    	var tiltDist = _pos.y;
    	var lerp = Mathf.Clamp01((tiltDist - m_cameraHeightToStartElevating) / (m_cameraHeightToFinishElevating - m_cameraHeightToStartElevating));
    	lerp = lerp * lerp * (3.0f - lerp - lerp);
    	return Mathf.LerpUnclamped(m_cameraLowElevation, m_cameraHighElevation, lerp);
    }

	private void TriggerDoubleClick()
	{
		//if (m_zoomBurst < 1 && PlayerHandManager.Me.AnyPowerActive == false)
		//	m_zoomBurst = 2;
		if (TerrainPopulation.Me.IsInMapMode)
		{
			DistrictManager.Me.DoDoubleClick();
		}
	}
	
	float m_zoomBurst = 0;
	float m_lastClickTime = 0;
	bool m_mouseDragStartedLegally = false, m_mouseRotateStartedLegally = false;
	Vector3 m_rotateMouse;
	private bool m_wasHoldingLMB = false;
	void UpdateCamera() {
		var (isOverUIScrollable, isOverUIAny) = Utility.UpdateMouseWheel();
		UpdateCameraOffset();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (!DataReady) return;
		if (DesignTableManager.Me.UpdateDesignInPlace()) return;
		if (!IsCountryside) return;
		NGCommanderBase.UpdateTownAge();
#endif
		Utility.UpdateMouseShake();
		if (m_cameraTransition != null) return;
		if (m_cameraPanSequenceInProgress) return;
		if (UpdateFreeCamera()) return;
		if (UpdateCameraTracking()) return;
		if (UpdatePossessionCamera()) return;
		if (UndergroundManager.IsActive && UndergroundManager.Me.m_undergroundCamera) return;
		
		float dt = Time.deltaTime;

		if (GetMouseButtonDown(0))
		{
			const float c_doubleClickTime = .5f;
			if (Time.time < m_lastClickTime + c_doubleClickTime && isOverUIAny == false)
			{
				// double click, zoom in
				TriggerDoubleClick();
			}
			m_lastClickTime = Time.time;
		}
		if (m_zoomBurst > 0)
		{
			bool justStarted = m_zoomBurst > 1;
			m_zoomBurst = 0;
			if (RaycastAtPoint(Input.mousePosition, out var hitZB, -1))
			{
				var distSqrd = (hitZB.point - Camera.main.transform.position).sqrMagnitude;
				float minDistSqrdToContinue = justStarted ? 70 * 70 : 30 * 30;
				if (distSqrd > minDistSqrdToContinue) m_zoomBurst = 1;
			}
		}

		// this shouldn't be necessary, but in case we get an exception while dragging this clears things up
		DragHolder.CheckOrphans();
		m_wasHoldingLMB = GetMouseButton(0);

		bool mouseIsLegalForMove = true, mouseIsLegalForOther = true;
		if (!TouchManager.TouchInputActive) // touch handles legality internally
		{
			mouseIsLegalForMove = !isOverUIScrollable && IsCountryside;
			mouseIsLegalForOther = mouseIsLegalForMove;
		}

		m_cameraControlledThisFrame = false;

		var startingPos = m_camera.transform.position;

		var hitPos = DragPosRaycast(m_camera, -1, Vector3.zero);
		var hitTerrain = RaycastAtPointWithFallback(new Vector3(Screen.width * .5f, Screen.height * .5f, 0), out var hit, ~c_layerIgnoreCameraBit, true);

		bool userKeyboardRotate = false;
		var zoom = GetZoom(mouseIsLegalForOther) + m_zoomBurst * 10;
		zoom += GetMapZoom();
		zoom *= m_cameraControlZoomSpeed * m_cameraControlZoomSpeed;
		if (zoom * zoom > .001f * .001f)
		{
			m_cameraControlledThisFrame = true;

			if (hitTerrain)
			{
				var zoomMag = hit.distance * .02f;
				zoom *= zoomMag;
			}
			const float c_minDistance = 1f;
			if (zoom > 0 && zoom > hit.distance - c_minDistance) zoom = hit.distance - c_minDistance;

			var newCamPos = m_camera.transform.position + m_camera.transform.forward * zoom;
			if (newCamPos.y > m_cameraHeightMaximum)
			{
				zoom = (m_cameraHeightMaximum - m_camera.transform.position.y) / m_camera.transform.forward.y;
				newCamPos = m_camera.transform.position + m_camera.transform.forward * zoom;
			}
			m_camera.transform.position = newCamPos;
			if (hitTerrain)
			{
				// Camera tilt with height (maintaining focus)
				var angle = TiltFromPosition(m_camera.transform.position);
				angle = Mathf.Lerp(m_camera.transform.eulerAngles.x, angle, .1f);
				m_camera.transform.eulerAngles = m_camera.transform.eulerAngles.NewX(angle);
				m_camera.transform.position = hit.point - m_camera.transform.forward * (hit.distance + m_camera.nearClipPlane - zoom);
				if (hitPos.sqrMagnitude > 0)
					m_camera.transform.position += hitPos - DragPosRaycastPlane(m_camera, hitPos, m_camera.transform.forward);
			}
		}
		
		if (Utility.GetMouseButtonDown(c_dragButton)) m_lastMouseDownPosition = Utility.InputPos;
		if (!Utility.GetMouseButton(c_dragButton)) {
			m_inputWasConsumed = false;
			m_inputWasDragging = false;
		}
		
		m_inputWasConsumed |= InputConsuming; // Add any other consuming checks here
		m_inputWasDragging |= InputDragging;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
		bool isClicker = ClickController.Me != null;
#else
		bool isClicker = false;
#endif
		if (m_inputWasConsumed && !isClicker) {
			if (m_inputWasDragging && IsCountryside) CheckCameraEdgeScroll();
		}
		
		float rotate = GetTotalCameraRotate(mouseIsLegalForOther, ref userKeyboardRotate);
		
		if(userKeyboardRotate)
			ShowResetRotationGUI();
		
		if (rotate * rotate > .01f*.01f) {
			m_cameraControlledThisFrame = true;
            
			var camPos = m_camera.transform.position;
			
			var ray = m_camera.RayAtClampedMouse();
			if (Physics.Raycast(ray.origin, ray.direction, out var hitRot, 2000, c_layerTerrainBit)) {
			//if (Raycast(camPos, m_camera.transform.forward * 1000, out hit, c_layerTerrainBit)) {
				var camFocus = hitRot.point;
				var toFocus = camFocus - camPos;
				toFocus = toFocus.RotateAbout(Vector3.up, rotate * -.005f);
				camPos = camFocus - toFocus;
				toFocus = m_camera.transform.forward.RotateAbout(Vector3.up, rotate * -.005f);
				m_camera.transform.position = camPos;
				m_camera.transform.forward = toFocus.normalized;
			}
		}

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		bool dragIsLegal = IsRoadConsuming == false && DesignTableManager.Me.IsDesignGloballyConsuming == false;
#else
		bool dragIsLegal = true;
#endif
		if (GetDragStarted()) {
			m_mouseDragStartedLegally = mouseIsLegalForMove;
			m_lastMousePos = GetDragPosition();
			m_draggingMouse = Vector3.zero;
		}
		if (GetDragContinued() && m_mouseDragStartedLegally && dragIsLegal) {
			m_draggingMouse = GetDragPosition();
		}
		if (m_draggingMouse.sqrMagnitude > 0) {
			var mouseAvg = Vector3.Lerp(m_lastMousePos, m_draggingMouse, .2f);
			if (RaycastAtPoint(m_lastMousePos, out var hitDrag)) {
				var oldHit = hitDrag.point;
				var plane = new Plane(Vector3.up, oldHit);
				float distance;
				var rayNew = m_camera.ScreenPointToRay(mouseAvg);
				if (plane.Raycast(rayNew, out distance)) {
					var newHit = rayNew.GetPoint(distance);
					m_camera.transform.position += oldHit - newHit;
				}
			}
			m_lastMousePos = mouseAvg;
		}

		var debugMove = DebugCameraMove;
		DebugCameraMove = Vector2.zero;
		
		bool userKeyboardMove = false;
		var movement = (ReadKeyStick(KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 1), KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 3), KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 2), KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 0), c_keySpeedMove, ref m_keySmoothMove, ref userKeyboardMove, NGManager.Me.m_moveKeysSmoothing) +
		               ReadKeyStick(KeyCode.LeftArrow, KeyCode.RightArrow, KeyCode.DownArrow, KeyCode.UpArrow,
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
					   DesignTableManager.Me.m_isInDesignGlobally || PlayerHandManager.Me.PowerActive(PlayerHandManager.c_powerTypeCuddle) || BuildingPlacementManager.Consuming ? 0 : c_keySpeedMove,
#else
					   c_keySpeedMove,
#endif
					   ref m_keySmoothMove2, ref userKeyboardMove, NGManager.Me.m_moveKeysSmoothing) +
		               debugMove * c_keySpeedMove) * 
		               m_cameraControlMoveSpeed * dt;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (DistrictManager.Me.IsEditing) movement *= 4;
#endif

		var camFwdFlat = m_camera.transform.forward; camFwdFlat.y = 0; camFwdFlat.Normalize();
		var camRightFlat = m_camera.transform.right; camRightFlat.y = 0; camRightFlat.Normalize();
		var totalMove = m_camera.transform.position + movement.x * camRightFlat + movement.y * camFwdFlat - startingPos;
		if (totalMove.sqrMagnitude > .001f * .001f)
		{
			m_cameraControlledThisFrame = true;
			
			var totalDistance = totalMove.magnitude;
			var totalDirection = totalMove / totalDistance;
			if (Physics.SphereCast(m_camera.transform.position, .01f, totalDirection, out var moveHit, 1000, ~c_layerIgnoreCameraBit, QueryTriggerInteraction.Ignore))
			{
				if (zoom < 0)
				{
					// hit something while zooming out, jump through it
					var xform = m_camera.transform;
					var fwd = xform.forward;
					while (true)
					{
						xform.position += fwd * -.5f;
						if (Physics.OverlapSphere(xform.position, 1.0f, -1, QueryTriggerInteraction.Ignore).Length == 0)
						{
							startingPos = xform.position;
							totalDistance = 0;
							break;
						}
					}
				}
                else
					totalDistance = Mathf.Min(totalDistance, moveHit.distance - 1);
			}
			m_camera.transform.position = ClampToWorldBounds(startingPos + totalDirection * totalDistance, m_camera.transform.forward);
			ClampAboveGround(m_camera.transform);
		}
		
        ApplyTownCameraOverride();
        
		if (IsInPlayground && m_camera.transform.position.y < c_playgroundHeight + 10f)
			m_camera.transform.position = m_camera.transform.position.NewY(c_playgroundHeight + 10f);
		m_state.m_cameraPosition = m_camera.transform.position;
		m_state.m_cameraEulers = m_camera.transform.eulerAngles;
		
		m_cameraFrustumPlanes = GeometryUtility.CalculateFrustumPlanes(Camera.main);
	}
	
	private Plane[] m_cameraFrustumPlanes = new Plane[6];

	public bool IsVisible(Vector3 _centre, Vector3 _size) 
	{
		return m_cameraFrustumPlanes != null && GeometryUtility.TestPlanesAABB(m_cameraFrustumPlanes, new Bounds(_centre, _size));
	}
	
	public Vector3 StateCamPos => IsPossessing ? m_camera.transform.position : m_state.m_cameraPosition;

	void ClampAboveGround(Transform _cam)
	{
		const float c_margin = .5f;
		var pos1 = _cam.position;
		var dh1 = pos1.y - (Me.HeightAtPoint(pos1) + c_margin);
		var pos2 = _cam.position + _cam.forward * .5f;
		var dh2 = pos2.y - (Me.HeightAtPoint(pos2) + c_margin);
		var minDH = Mathf.Min(dh1, dh2);
		if (minDH < 0) _cam.position -= Vector3.up * minDH;
	}
	
	private bool TrySendStartupCurrencyAnalytics()
	{
		bool result = true;
		if (HasLoadedFromSeed)
		{
			NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Given, 0, "Startup");
		}
		return result;
	}

	private static DebugConsole.Command s_portraitCmd = new DebugConsole.Command("portrait", _s =>
	{
		bool isPortrait = Screen.autorotateToPortrait;
		Utility.SetOrToggle(ref isPortrait, _s);
		Screen.autorotateToPortrait = isPortrait;
		Screen.autorotateToPortraitUpsideDown = isPortrait;
		Screen.autorotateToLandscapeLeft = !isPortrait;
		Screen.autorotateToLandscapeRight = !isPortrait;
	});

	static DebugConsole.Command s_clampCameraCmd = new DebugConsole.Command("clampcamera", _s => Utility.SetOrToggle(ref s_clampCamera, _s));
	static bool s_clampCamera = true;
	static float s_groundClampFactor = 1.0f / 800.0f;
	static float s_groundClampMax = .8f;
	static float s_groundClampOffset = 1000;
	public static Vector3 ClampToWorldBounds(Vector3 _pos, Vector3 _forward) {
		if (s_clampCamera) {
			var min = GlobalData.c_terrainMin;
			var max = GlobalData.c_terrainMax;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
			if (DistrictManager.Me.IsEditing)
			{
				min -= GlobalData.c_terrainExtent * .3f;
				max += GlobalData.c_terrainExtent * .3f;
			}
			else
			{
#endif
				var maxHeight = Me.m_cameraHeightMaximum;
				s_groundClampFactor = 1.0f / (maxHeight - 100);
				//s_groundClampOffset = maxHeight - 100;
				s_groundClampMax = Mathf.Min(1, (maxHeight + 750) / 1850.0f);
				float aboveGround = _pos.y - 100;
				var center = (min + max) * .5f;
				var extent = max - center;
				float clamp = Mathf.Clamp01(aboveGround * s_groundClampFactor) * s_groundClampMax;
				extent  *= 1 - clamp;
				center += _forward.GetXZNorm() * (-s_groundClampOffset * clamp);
				min = center - extent;
				max = center + extent;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
			}
#endif
			_pos.x = Mathf.Clamp(_pos.x, min.x, max.x);
			_pos.z = Mathf.Clamp(_pos.z, min.z, max.z);
		}
		return _pos;
	}

	//
	public static bool GetDragStarted()
	{
		if (TouchManager.TouchInputActive) return TouchManager.GetDragStarted();
		return GetMouseButtonDown(c_dragButton);
	}
	public static bool GetDragContinued()
	{
		if (TouchManager.TouchInputActive) return TouchManager.GetDragContinued();
		return GetMouseButton(c_dragButton);
	}
	public static Vector3 GetDragPosition()
	{
		if (TouchManager.TouchInputActive) return TouchManager.GetDragPosition();
		return Utility.InputPos;
	}

	public static bool GetPrimaryDragStarted()
	{
		if (TouchManager.TouchInputActive) return TouchManager.GetDragStarted();
		return GetMouseButtonDown(0);
	}
	public static bool GetPrimaryDragContinued()
	{
		if (TouchManager.TouchInputActive) return TouchManager.GetDragContinued();
		return GetMouseButton(0);
	}
	public static Vector3 GetPrimaryDragPosition()
	{
		if (TouchManager.TouchInputActive) return TouchManager.GetDragPosition();
		return Utility.InputPos;
	}
	public static int CancelDrag()
	{
		if (TouchManager.TouchInputActive) return TouchManager.CancelDrag();
		return 0;
	}
	//
	public static bool GetMouseButton(int _button) {
		if (TouchManager.TouchInputActive) return TouchManager.CheckTouchState(_button, 2);
		if (Input.touchCount > 1) return false;
		return Utility.GetMouseButton(_button) || Utility.GetMouseButtonOverride(_button);
	}
	public static bool GetMouseButtonDown(int _button) {
		if (TouchManager.TouchInputActive) return TouchManager.CheckTouchState(_button, 1);
		if (Input.touchCount > 1) return false;
		return Utility.GetMouseButtonDown(_button);
	}
	public static bool GetMouseButtonUp(int _button) {
		if (TouchManager.TouchInputActive) return TouchManager.CheckTouchState(_button, 0);
		if (Input.touchCount > 1) return false;
		return Utility.GetMouseButtonUp(_button);
	}

	public static Vector3 InputPosition(int _button)
	{
		if (TouchManager.TouchInputActive)
		{
			var touch = TouchManager.TouchData[_button];
			if (touch != null)
				return touch.m_lastPosition;
			return Vector3.zero;
		}
		return Utility.InputPos;
	}

	public static bool ReadyForNext(ref float _lastTime, float _timeBetween)
	{
		if (Time.unscaledTime < _lastTime + _timeBetween) return false;
		_lastTime = Time.unscaledTime + _timeBetween * Random.Range(1.0f, 1.4f);
		return true;
	}

	void CheckPlayerActivity()
	{
		if (Input.anyKeyDown || Input.mousePositionDelta != Vector3.zero)
		{
			GameManager.Me.m_state.m_gameTime.m_lastPlayerActionTime = Time.realtimeSinceStartup;
		}
	}
	
	private static CursorInfo m_debugCursorInfoActive = null;
	private static DebugConsole.Command s_debugCursorInfo = new DebugConsole.Command("cursorInfo", _s =>
	{
		if (m_debugCursorInfoActive == null)
		{
			Object ob = Resources.Load("_Prefabs/UI/Editor/DebugCursorInfo");
			if(ob == null) return;
			GameObject go = GameObject.Instantiate(ob) as GameObject;
			if(go == null) return;
			m_debugCursorInfoActive = go.GetComponent<CursorInfo>();
			if (m_debugCursorInfoActive == null)
			{
				Destroy(go);
				return;
			}
		}
		else
		{
			Destroy(m_debugCursorInfoActive.gameObject);
			m_debugCursorInfoActive = null;
		}
	});
}
