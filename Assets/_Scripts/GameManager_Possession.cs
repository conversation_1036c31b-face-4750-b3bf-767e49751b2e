#define THIRDPERSONCAMERA_V3

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class GameManager
{
    [Header("Possess Mode")]
    [NonSerialized]
    public MAPossessionSettings m_possessionSettingsOverride = null;
    public MAPossessionSettings CurrentPossessionSettings => m_possessionSettingsOverride ? m_possessionSettingsOverride : NGManager.Me.m_possessionSettingsDefault ??= ScriptableObject.CreateInstance<MAPossessionSettings>();
    
    private KeyCode[] c_activation = { KeyCode.P };
    private KeyCode[] c_infoPage = { KeyCode.I };
    
    bool c_canPossessHeroOutOfDistrict => NGManager.Me.m_canPossessHeroOutOfDistrict;
    bool c_canPossessDogOutOfDistrict => NGManager.Me.m_canPossessDogOutOfDistrict;
    bool c_canPossessOthersOutOfDistrict => NGManager.Me.m_canPossessOtherCharacterOutOfDistrict;

    public bool m_autoMovePossessionCamera = false; // move the camera behind the possessed character while moving
    public bool m_possessionRotateRequiresButton = false; // drag rather than simply move the mouse 

    CameraRenderSettings.VisionMode m_possessedVision;
    
    NGMovingObject m_possessedObject;
    MACharacterBase m_possessedCharacter;
    MABuilding m_possessedBuilding;

    Vector3 m_lastPossessionInput;
    Vector3 m_velocity = Vector3.zero;
    float m_inputRotation = 0;
    Vector3 m_possessedCameraBackupPos;
    Quaternion m_possessedCameraBackupRot;
    float m_possessedCameraCurrentDistance = -1f;
    bool m_possessedUnderControl = true;
    bool m_wasPossessedNavigating = false;
    float m_maxZoom = 1.5f;

    public bool IsPossessing => m_possessedObject != null;
    public bool IsPossessingBuilding => m_possessedBuilding != null;
    public MACharacterBase PossessedCharacter { get { return m_possessedCharacter; } }
    public GameObject PossessedObject => m_possessedObject == null ? null : m_possessedObject.gameObject;
    public bool IsPossessed(NGMovingObject _o) => m_possessedObject == _o;
    public bool IsControllingPossessed => IsPossessing && m_possessedUnderControl;
    
    public float m_possessModeFreeze = 0;
    
    private bool m_isPermanent;
    
    private int m_framesPossessed;
    private float m_initialPossessCameraBlend;
    private float smoothToAfterBlend = 0f;

    private PossessedObjectHeight m_possessedObjectHeight = null;

    private static bool PossessedStrafing => Me.CurrentPossessionSettings.m_possessionStrafingMode &&
                                             (Me.PossessedCharacter != null &&
                                             Me.PossessedCharacter.CharacterSettings.m_possessionModeNeverStrafe == false);
    
    public AkEventHolder m_onPossessAudio;
    public AkEventHolder m_onPossessTargetAudio;
    private int m_onPossessAudioHandle = 0;
    private int m_onPossessTargetAudioHandle = 0;

    private Vector3 m_cameraTargetFocus = Vector3.zero;

    private bool m_canInstaTurn = false;

	private bool m_actionCamTracking = false;

    [SerializeField]
    private HealthBar externalHealthBar = null;

    private void StartPossessAudio(NGMovingObject _object)
    {
        if (LoadComplete == false) return; // no audio if loading into possess mode
        if (m_onPossessAudio != null) m_onPossessAudio.Play(gameObject);
        if (m_onPossessTargetAudio != null) m_onPossessTargetAudio.Play(_object.gameObject);
    }
    private void StopPossessAudio()
    {
        if (m_onPossessAudioHandle > 0)
            AudioClipManager.Me.StopSound(m_onPossessAudioHandle);
        if (m_onPossessTargetAudioHandle > 0)
            AudioClipManager.Me.StopSound(m_onPossessTargetAudioHandle);
        m_onPossessAudioHandle = 0;
        m_onPossessTargetAudioHandle = 0;
    }
    
    public bool IsPossessedUnderground => m_possessedCharacter?.GameState.m_isUnderground ?? false;
    public bool CanEndPossession => m_isPermanent == false && IsPossessedUnderground == false;
    public bool CanStartBlock => m_possessedCharacter != null && m_possessedCharacter.HasBlockAbility && GameManager.Me.m_state.m_powerMana > m_possessedCharacter.blockManaPerSecond * 0.5f;

    public bool CanPossessAtPosition(NGMovingObject _character)
    {
        return CanPossessOutOfDistrict(_character) || DistrictManager.Me.IsWithinDistrictBounds(_character.transform.position);
    }

    public bool CanPossessOutOfDistrict(NGMovingObject _movingObject)
    {
        if (_movingObject is MAHeroBase)
        {
            return c_canPossessHeroOutOfDistrict;
        }
        else if (_movingObject is MADog)
        {
            return c_canPossessDogOutOfDistrict;
        }
        else
        {
            return c_canPossessOthersOutOfDistrict;
        }
    }

    public bool CanPossess(NGMovingObject _movingObject)
    {
        if(_movingObject == null || _movingObject.CanEnterPossessionMode == false) return false;
        
        if (_movingObject is MAHeroBase) return MAUnlocks.Me.m_possessHeroes;
        if (_movingObject is MATourist) return MAUnlocks.Me.m_possessTourists;
        if (_movingObject is MAQuestGiver) return false; 
        if (_movingObject is MAWorker) return MAUnlocks.Me.m_possessWorkers;
        if (_movingObject is MACreatureBase) return MAUnlocks.Me.m_possessCreatures;
        if (_movingObject is MAAnimal) return MAUnlocks.Me.m_possessAnimals;

        if (MAQuestManager.Me.RequiredPossessableMovingObjects().Contains(_movingObject))
            return true;
        
        return false;
    }

    public Action<bool, NGLegacyBase> m_onPossess = null;
    public void PossessObject(NGMovingObject _object, bool _control = true, bool _isPermanent = false)
    {
        m_possessedVision = _object.VisionMode;
        CameraRenderSettings.Me.SetVisionMode(m_possessedVision);
        
        ContextMenuManager.Me.RemoveCurrentMenu();
        
        if (_isPermanent == false)
            StartPossessAudio(_object);

        _object.TrySetTeleportPosition();

        m_possessedObjectHeight = new PossessedObjectHeight(_object);
        
        m_isPermanent = _isPermanent;
        m_framesPossessed = 0;
        m_initialPossessCameraBlend = 0;
        smoothToAfterBlend = 0f;
        m_cameraTargetFocus = Vector3.zero;
        if (m_possessedCameraBackupPos.sqrMagnitude < .1f * .1f)
        {
            var camXform = m_camera.transform;
            m_possessedCameraBackupPos = camXform.position;
            m_possessedCameraBackupRot = camXform.rotation;
        }
        camRot = _object.transform.rotation;
        m_possessedObject = _object;
        m_possessedCharacter = m_possessedObject as MACharacterBase;
        m_lastPossessionInput = Input.mousePosition;
        m_possessionCameraDragOff = 0;
        m_possessionCameraDistance = CurrentPossessionSettings.m_possessionCameraDistanceMin;
		turning = false;

        ResetCameraEffects();

        // RW-23-MAY-25: Disable hand power when entering possession.
        PlayerHandManager.Me.ActivatePower("");
        
        m_onPossess?.Invoke(true, _object);
        
        _control = CurrentPossessionSettings.m_useCharacterControls && _control;
        
        if (_control)
            _object.SetupPossessed(true);
        
        if (m_possessedCharacter != null)
        {
            bool isHero = m_possessedCharacter.CreatureInfo.m_creatureType == "Hero";
            bool isWorker = m_possessedCharacter.CreatureInfo.m_creatureType == "Worker";
            if (isHero || isWorker)
                m_possessedCharacter.SetExternalHealthBar(externalHealthBar);
            else
                m_possessedCharacter.SetExternalHealthBar(null);
        }

		KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.Possess);
        ControlPossessed(_control);
        m_cameraFrustumPlanes = null;
    }
    public void ControlPossessed(bool _control)
    {
        if (_control)
        {
            m_wasPossessedNavigating = m_possessedObject.m_nav.IsNavigating;
            m_possessedObject.m_nav.PushPause("Possess");
            m_possessedObject.GetComponent<Rigidbody>().isKinematic = false;
        }
        else
        {
            m_possessedObject.m_nav.PopPause("Possess");
            if (m_wasPossessedNavigating)
            {
                m_possessedObject.m_nav.RefreshNavigation();
            }
        }
        m_possessedUnderControl = _control && CurrentPossessionSettings.m_useCharacterControls;
    }

    private float m_defaultCameraFOV = 0f;
    public float DefaultCameraFOV => m_defaultCameraFOV;

    void CheckFOV(Camera _cam)
    {
        var possessing = IsPossessing;
        if (!possessing && (m_defaultCameraFOV == 0f))
            m_defaultCameraFOV = _cam.fieldOfView;
        
        var startFOV = _cam.fieldOfView;
        var targetFOV = _cam.fieldOfView;
        if (possessing)
        {
            targetFOV = m_possessedVision.m_fov;
        }
        else if (m_defaultCameraFOV > 0f)
        {
            targetFOV = m_defaultCameraFOV;
        }
        _cam.fieldOfView = Mathf.Lerp(startFOV, targetFOV, m_initialPossessCameraBlend);
    }

    static float s_possessionRotationSpeed = -.1f;
    static float s_acceleration = 2.0f;
    static float s_maxSpeed = 40;
    static float s_cameraRayRadius = 1f;
    static float s_possessionCameraZoomScale = .5f;

    float m_possessionCameraDistance;
    float m_possessionCameraDragOff = 0;
    bool m_isPossessionEnding = false;
    float m_smoothedSpeed = 0;
    
    Vector3 m_lastControllerInput = Vector3.zero;
    
    public bool PossessionFootstrikeDebugActive => m_locoAuditionPlatform != null && IsPossessing;
    public string PossessionFootstrikeDebug() => $"Smoothed speed:{m_smoothedSpeed:0.00}   Anim speed:{m_possessedObject.GetComponentInChildren<Animator>().GetFloat("Speed"):0.00}";

    public BCActionTurret GetPossessedTurret()
    {
        if (m_possessedBuilding == null)
            return null;
        return m_possessedBuilding.GetComponentInChildren<BCActionTurret>();
    }
    
    public bool IsPossessedTurretLocked() => GetPossessedTurret()?.IsLocked ?? false;
    public bool CanPossessedTurretFire() => GetPossessedTurret()?.CanFire ?? false;

    public void Unpossess(bool _zoomOut = true, float _zoomDistance = 1.0f)
    {
        if(CameraRenderSettings.Me != null) CameraRenderSettings.Me.SetVisionMode(CameraRenderSettings.Me.m_defaultVision);
        
        m_onPossess?.Invoke(false, (NGLegacyBase)m_possessedObject ?? (NGLegacyBase)m_possessedBuilding);
        StopPossessAudio();

		ToggleActionCamTracking(false);
        ResetCameraEffects();

        if (m_possessedCharacter != null)
            m_possessedCharacter.SetExternalHealthBar(null);
        
        if (m_possessedBuilding != null)
        {
            m_possessedBuilding.Unpossess();
            m_possessedBuilding = null;
            m_escapeConsumed = true;
            return;
        }

        if (m_locoAuditionPlatformStartedInPossess && m_locoAuditionPlatform != null)
            ToggleLocoStage();
        if (m_possessedObject == null)
            return;
        KeyboardShortcutManager.Me.PopShortcuts();
        if (m_possessedUnderControl)
            m_possessedObject.SetupPossessed(false);
        ControlPossessed(false);
        
        if (m_possessedObject != null)
        {
            if(m_possessedCharacter != null)
            {
                m_possessedCharacter.m_nav.GradualStop();
            }
            m_possessedObject.m_nav.Unpause();
        }

        m_possessedCharacter = null;
        m_possessedObject = null;
        m_escapeConsumed = true;
        m_isPossessionEnding = true;
        m_forceZoom = -15;
        if (_zoomOut)
        {
            m_forceZoom = _zoomDistance;
            var camXform = m_camera.transform;
            if (camXform.forward.y > -.5) camXform.forward = camXform.forward.NewY(-.5f);
        }

        m_cameraFrustumPlanes = new Plane[6];

        m_possessionSettingsOverride = null;
    }

    public bool PossessedCharacterWeaponHasPowers()
    {
        if (m_possessedCharacter == null)
            return false;
        return m_possessedCharacter.WeaponHasPowers();
    }

    void PossessedAction()
    {
        if (m_possessedObject == null)
            return;
        
        if (m_possessedObject.DoStandardInteraction() == false)
        {
            if ((m_possessedCharacter != null) && m_possessedCharacter.HasCombo() && m_possessedCharacter.IsComboInCooldown())
                m_canInstaTurn = false;
            
            m_possessedObject.DoPossessedAction();
        }
    }

    void PossessedSecondaryAction()
    {
        if (m_possessedObject == null)
            return;
        
        if ((m_possessedCharacter != null) && m_possessedCharacter.HasCombo() && m_possessedCharacter.IsComboInCooldown())
            m_canInstaTurn = false;
        
        m_possessedObject.DoPossessedSecondaryAction();
    }

    void PossessedToggleBlockAction(bool enable)
    {
        if (m_possessedObject == null)
            return;
        
        if (enable)
            m_possessedObject.DoPossessedBlockAction();
        else
            m_possessedObject.UndoPossessedBlockAction();
    }

    public void ScheduleInstaTurn()
    {
        m_canInstaTurn = true;
    }

    public float GetPossessedMoveSpeed()
    {
        if (m_possessedObject != null)
            return m_smoothedSpeed;

        return 0.0f;
    }

    void CheckSeaLevel(ref Vector3 _move)
    {
        // if we're moving downwards below sea level don't allow the move
        m_possessedObject.m_nav.DidSeaLevelCheckBlockCharacter = false;
        var chrPos = m_possessedObject.transform.position;
        var pos = chrPos.GroundPosition();
        if (chrPos.y.Nearly(pos.y, .5f) == false) return; // not on the ground, don't do the sea check
        const float c_seaLevelCheckRadius = .25f;
        var posCheck = (pos + _move.normalized * c_seaLevelCheckRadius).GroundPosition();
        if (posCheck.y > pos.y) return; // moving upwards, always ok
        if (posCheck.y < GlobalData.c_seaLevel)
        {
            m_possessedObject.m_nav.DidSeaLevelCheckBlockCharacter = true;
            _move = Vector3.zero;
        }
    }

    bool IsPossessedLocomoting()
    {
        if (m_possessedCharacter != null && m_possessedCharacter.m_ragdollController != null && !m_possessedCharacter.m_ragdollController.IsAnimated)
			return false;
        
        if (m_possessedCharacter != null && m_possessedCharacter.IsBlocking)
			return false;

        if (m_possessedCharacter != null && m_possessedCharacter.HasCombo() && (m_possessedCharacter.CurrentAttack != null))
        {
            return m_possessedCharacter.CurrentAttack.CanMove;
        }

		var ao = m_possessedObject.GetComponentInChildren<AnimationOverride>();
        if (ao == null)
            return true;
        
        return ao.GetFractionPlayed() > .9f;
    }

    bool UpdatePossessionCharacterControl()
    {
        if (m_possessedObject == null) return false;
        if (m_possessedUnderControl == false)
        {
            m_lastControllerInput = Vector3.zero;
            return false;
        }

        var cam = m_camera;
        var camXform = cam.transform;
        var move = Vector3.zero;

        var characterFwd = (Quaternion.Euler(CurrentPossessionSettings.m_possessionCameraAngleOffset) * camXform.forward).GetXZNorm();
        var characterRight = Vector3.Cross(Vector3.up, characterFwd);

        var charXform = m_possessedObject.transform;
        
		if (IsPossessedLocomoting())
        {
            var upKey = KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 0);
            var leftKey = KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 1);
            var downKey = KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 2);
            var rightKey = KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 3);
            move.x += (Utility.GetKey(rightKey) ? 1 : 0) + (Utility.GetKey(leftKey) ? -1 : 0);
            move.z += (Utility.GetKey(upKey) ? 1 : 0) + (Utility.GetKey(downKey) ? -1 : 0);
        }

#if UNITY_EDITOR
        if (Utility.GetKey(KeyCode.LeftAlt) || Utility.GetKey(KeyCode.RightAlt))
        {
            for (int i = 0; i < 10; ++i)
            {
                if (Utility.GetKeyDown(KeyCode.Alpha0 + i))
                {
                    s_acceleration = 1.0f + i * .5f;
                }
            }
        }
#endif
        
        var sprinting = Utility.GetKey(KeyCode.LeftShift) || Utility.GetKey(KeyCode.RightShift);

        if (PossessedStrafing)
        {
            move.x *= m_possessedObject.m_possessionSideWalkSpeed;
            if (move.z > 0f)
                move.z *= !sprinting ? m_possessedObject.m_possessionFwdWalkSpeed : m_possessedObject.m_possessionFwdRunSpeed;
            else
                move.z *= m_possessedObject.m_possessionBackWalkSpeed;
        }
        else
        {
            if (sprinting)
            {
                move.z *= m_possessedObject.m_possessionFwdRunSpeed;
                move.x *= m_possessedObject.m_possessionFwdRunSpeed;
            }
            else
            {
                move.z *= m_possessedObject.m_possessionFwdWalkSpeed;
                move.x *= m_possessedObject.m_possessionFwdWalkSpeed;
            }
        }

        var newMag = Mathf.Max(Mathf.Abs(move.x), Mathf.Abs(move.z));
        move = move.normalized * newMag;
        
        var newVelocity = characterFwd * move.z + characterRight * move.x;
        if (m_possessedCharacter != null && m_possessedCharacter.GameState.m_isUnderground == false)
            CheckSeaLevel(ref newVelocity);

        if (m_possessedObjectVelocityOverride.sqrMagnitude > .1f * .1f)
            newVelocity = m_possessedObjectVelocityOverride;
        
        // var facingDot = PossessedStrafing ? 1 : Mathf.Max(0, Vector3.Dot(charXform.forward.GetXZNorm(), newVelocity.GetXZNorm()));
        // newVelocity *= facingDot;
        m_velocity = Vector3.Lerp(m_velocity, newVelocity, 0.3f);

        if (m_initialPossessCameraBlend >= 1f)
            DoTurning(charXform, characterFwd, m_velocity);

        var nav = m_possessedObject.m_nav;
        var rb = nav.m_body;
            
#if UNITY_EDITOR
        if (Utility.GetKey(KeyCode.RightShift))
        {
            if (Utility.GetKey(KeyCode.Alpha1))
                m_velocity = m_velocity.normalized * 3f;
            else if (Utility.GetKey(KeyCode.Alpha2))
                m_velocity = m_velocity.normalized * 7.5f;
            else if (Utility.GetKey(KeyCode.Alpha3))
                m_velocity = m_velocity.normalized * 12f;
        }
#endif

        nav.SetTargetVelocity(m_velocity);

        m_smoothedSpeed = Mathf.Lerp(m_smoothedSpeed, Vector3.Dot(m_velocity, characterFwd), .2f);

        m_lastControllerInput = move;
        
        return true;
    }

    private bool turning = false;
    private bool stopTurning = false;
    private float turningEndY;

    void DoTurning(Transform _charT, Vector3 _newFwd, Vector3 _newVel)
    {
		if (PossessedStrafing && m_possessedCharacter != null)
		{
            const float turnAmount = 90f;
            const float fakeTurnThreshhold = turnAmount * 1.5f;
            
			if (m_canInstaTurn || (_newVel.xzSqrMagnitude() > 0.1f * 0.1f))
			{
				if (turning)
				{
                    stopTurning = true;
                    m_possessedCharacter.OnTurnFinished();
					if (!m_canInstaTurn)
                    {
                        m_possessedObject.StopCurrentAnimation(true);
                    }
				}
                var t = m_canInstaTurn ? 0.9f : 0.6f;
				_charT.rotation = Quaternion.Slerp(m_possessedObject.m_transform.rotation, Quaternion.LookRotation(_newFwd, Vector3.up), t);
			}
			else
			{
				var currentY = _charT.eulerAngles.y;
				var targetY = Quaternion.FromToRotation(Vector3.forward, _newFwd).eulerAngles.y;
				var angle = Mathf.Repeat((targetY - currentY) + 180f, 360f) - 180f;
				if (Mathf.Abs(angle) > turnAmount && !turning)
				{
					m_possessedCharacter.IsTurnFinished = () =>
                    {
                        this.turning = false;
                        return this.stopTurning;
                    };
					m_possessedCharacter.SetTargetTurnVelocity(angle.Sign() * CurrentPossessionSettings.m_possessedCharacterTurnRate);

					turning = true;
                    stopTurning = false;
					turningEndY = currentY + turnAmount * Mathf.Sign(angle);
				}
				else if (turning)
                {
					var newTurnEndY = Utility.ClampAngleBetween(turningEndY, targetY - fakeTurnThreshhold, targetY + fakeTurnThreshhold);
					var diff = Mathf.Repeat((newTurnEndY - turningEndY) + 180f, 360f) - 180f;
					if (!diff.IsZero())
					{
						var rot = Quaternion.Euler(Vector3.up * diff);
						_charT.rotation *= rot;
						turningEndY = newTurnEndY;
					}
                    stopTurning = Utility.AngleBetween(turningEndY, targetY - turnAmount, targetY + turnAmount);
                }
			}
		}
		else if (_newVel.xzSqrMagnitude() > .01f * .01f)
		{
			float lerpSpeed = PossessedCharacter == null ? 0.1f : CurrentPossessionSettings.m_possessedNonStrafingCharacterRotate;
			m_possessedObject.m_transform.rotation = Quaternion.Slerp(m_possessedObject.m_transform.rotation, Quaternion.LookRotation(_newVel.GetXZ(), Vector3.up), lerpSpeed);
		}
        m_canInstaTurn = false;
    }


	void SetPlaygroundHides(bool _hide)
    {
        GlobalData.Me.m_buildingHolder.gameObject.SetActive(!_hide);
        GlobalData.Me.HideTerrain(_hide);
        RoadManager.Me.m_pathHolder.gameObject.SetActive(!_hide);
    }

    public const float c_playgroundHeight = 200;
    GameObject m_locoAuditionPlatform = null;
    bool m_locoAuditionPlatformStartedInPossess;
    public bool IsInPlayground => m_locoAuditionPlatform != null; 
    void ToggleLocoStage()
    {
        if (m_locoAuditionPlatform != null)
        {
            SetPlaygroundHides(false);
            Destroy(m_locoAuditionPlatform);
            return;
        }
        SetPlaygroundHides(true);
        
        GlobalData.Me.m_navGenerationFrame = Time.frameCount;
        m_locoAuditionPlatformStartedInPossess = m_possessedObject != null;
        m_locoAuditionPlatform = GameObject.CreatePrimitive(PrimitiveType.Cube);
        m_locoAuditionPlatform.layer = GameManager.c_layerTerrain;
        var mr = m_locoAuditionPlatform.GetComponent<MeshRenderer>();
        mr.material = GlobalData.Me.m_testGridMaterial;
        mr.material.SetVector("_Tiling", new Vector4(100, 100, 0, 0));
        m_locoAuditionPlatform.transform.position = new Vector3(0, c_playgroundHeight, 0);
        m_locoAuditionPlatform.transform.localScale = new Vector3(500, .1f, 500);

        if (m_possessedObject != null)
        {
            var handler = m_possessedObject.GetComponentInChildren<AnimationHandler>();
            if (handler == null)
            {
                var anim = m_possessedObject.GetComponentInChildren<Animator>();
                if (anim != null)
                    handler = anim.gameObject.AddComponent<AnimationHandler>();
            }
            handler.SetFootstrikeDebug(m_locoAuditionPlatform != null);
            m_possessedObject.m_transform.position = m_possessedObject.m_transform.position.NewY(201);
        }
    }

    bool UpdatePossessionCamera()
    {
        var cam = m_camera;
        var camXform = cam.transform;
        
        CheckFOV(cam);

        int iActivationKeyUsed = Array.FindIndex(c_activation, Utility.GetKeyDown);
        if (m_possessedBuilding != null)
        {
            if (Utility.GetKeyDown(KeyCode.Escape) || iActivationKeyUsed > -1)
            {
                Unpossess();
                return true;
            }
            return true;
        }

        if (m_possessedObject == null && m_possessedBuilding == null)
        {
            if (iActivationKeyUsed > -1 && Utility.ModifiedKey(c_activation[iActivationKeyUsed], false, false, false, "Possess", false) && IsSubScene == false && InputUtilities.GetCurrentDragObject() == null)
            {
                var ray = cam.RayAtMouse();

                List<MABuilding> buildings = null;
                if (MAUnlocks.Me.m_possessBuildings)
                {
                    buildings = RayCastForBuildings();
                    if (buildings.Count > 0)
                    {
                        foreach (var building in buildings)
                        {
                            if (DistrictManager.Me.IsWithinDistrictBounds(building.transform.position, true) == false)
                                continue;
                            if (building.Possess())
                            {
                                m_possessedBuilding = building;
                            }
                        }
                    }
                }
                
                if(m_possessedBuilding == null)
                {
                    var possessableBuildings = MAQuestManager.Me.RequiredPossessableBuildings();
                    if (possessableBuildings.Count > 0)
                    {
                        if (buildings == null)
                        {
                            buildings = RayCastForBuildings();
                        }
                        foreach (var building in buildings)
                        {
                            if (possessableBuildings.Contains(building) && building.Possess())
                            {
                                m_possessedBuilding = building;
                                break;
                            }
                        }
                    }
                }

                List<MABuilding> RayCastForBuildings()
                {
                    var hits = Physics.RaycastAll(ray, 1000);
                    List<MABuilding> buildingsFound = new();
                    foreach (var h in hits)
                    {
                        var building = h.collider.gameObject.GetComponentInParent<MABuilding>();
                        if (building != null)
                        {
                            buildingsFound.Add(building);
                        }
                    }
                    return buildingsFound;
                }

                if (m_possessedBuilding == null)
                {
                    var hit = PlayerHandManager.GetNearestObjects(MAUnlocks.Me.m_possessWorkers, MAUnlocks.Me.m_possessCreatures, MAUnlocks.Me.m_possessTourists, MAUnlocks.Me.m_possessHeroes, MAUnlocks.Me.m_possessAnimals, MAQuestManager.Me.RequiredPossessableMovingObjects().Count > 0, 1);
                    for (int i = 0; i < hit.Count; ++i)
                    {
                        var obj = hit[i].GetComponent<NGMovingObject>();
                        if (CanPossess(obj) == false) continue;
                        if(obj.CanEnterPossessionMode == false) continue;
                        PossessObject(obj, !Utility.GetKey(KeyCode.O));
                        break;
                    }
                }
            }
            if (m_isPossessionEnding && false)// && (camXform.position - m_possessedCameraBackupPos).sqrMagnitude > 1f * 1f)
            {
                camXform.position = Vector3.Lerp(camXform.position, m_possessedCameraBackupPos, CurrentPossessionSettings.m_possessedCameraBlend);
                camXform.rotation = Quaternion.Lerp(camXform.rotation, m_possessedCameraBackupRot, CurrentPossessionSettings.m_possessedCameraBlend);
                return true;
            }
            m_isPossessionEnding = false;
            return false;
        }

        var activePossessControl = PlayerHandManager.Me.ShowCursor() == false; // if ShowCursor is true we're doing something while in possess mode

        var zoom = GetZoom();
        var prevCamDist = m_possessionCameraDistance;
        m_possessionCameraDistance -= zoom * s_possessionCameraZoomScale;
        m_possessionCameraDistance = Mathf.Clamp(m_possessionCameraDistance, CurrentPossessionSettings.m_possessionCameraDistanceMin, CurrentPossessionSettings.m_possessionCameraDistanceMax);
        if (m_possessionCameraDistance > CurrentPossessionSettings.m_possessionCameraDistanceMax)
            m_possessionCameraDistance = Mathf.Lerp(m_possessionCameraDistance, CurrentPossessionSettings.m_possessionCameraDistanceMax, .2f);
        m_possessedCameraCurrentDistance *= m_possessionCameraDistance / prevCamDist;
        
        if (Utility.GetKeyDown(KeyCode.Escape) || iActivationKeyUsed > -1)
        {
            if (CanEndPossession)
            {
                Unpossess();
                return true;
            }
        }
        if (m_framesPossessed > 10)
        {
            if (activePossessControl)
            {
                if (Utility.GetMouseButtonDown(0))//  || Utility.GetKeyDown(KeyCode.Space)
                    PossessedAction();
                if (Utility.GetMouseButtonDown(1))
                    PossessedSecondaryAction();
                if (Utility.GetKeyDown(KeyCode.Space))
                    PossessedToggleBlockAction(true);
                else if (!Utility.GetKey(KeyCode.Space))
                    PossessedToggleBlockAction(false);
            }
            if (m_possessedObject == null) return false; // in case PossessedAction unpossessed
        }
        
        if (Utility.GetKeyDown(KeyCode.I) || iActivationKeyUsed > -1)
        {
            if (m_isPermanent == false) // don't show info pane for initial character
                UIManager.Me.m_centralInfoPanelManager.ToggleInfoPanel("Character", m_possessedCharacter.transform);
        }
        
        if (m_possessedObject.gameObject.activeSelf == false)
        {
            Unpossess();
            return false;
        }
        var inputActiveTarget = 0.0f;

        var anim = m_possessedObject.GetComponentInChildren<Animator>();
        if(anim != null)
        {
            anim.speed = 1.0f - m_possessModeFreeze;
        }
        
        var xDelta = 0f;
        var yDelta = 0f;
        if (m_possessionRotateRequiresButton == false || Utility.GetMouseButton(0))
        {
            var inputPos = Input.mousePosition;
            if (Utility.GetMouseButtonDown(0) == false)
            {
                xDelta = Utility.MouseXAxis * CurrentPossessionSettings.m_possessedCameraXSensitivity;
                yDelta = Input.GetAxis("Mouse Y") * CurrentPossessionSettings.m_possessedCameraYSensitivity;
            }
            m_lastPossessionInput = inputPos;
        }

        SmoothMouseDeltas(ref xDelta, ref yDelta);
        xDelta *= 1 - m_possessModeFreeze;
        yDelta *= 1 - m_possessModeFreeze;

        bool didRotate = !xDelta.IsZero() || !yDelta.IsZero();

        xDelta += GetKeyboardCameraRotate(ref didRotate);
        if (didRotate && activePossessControl)
        {
            var camEulers = camRot.eulerAngles;

            camEulers.x += yDelta * s_possessionRotationSpeed;
            camEulers.x = Utility.ClampAngleBetween(camEulers.x, CurrentPossessionSettings.m_possessedCameraMinTilt, CurrentPossessionSettings.m_possessedCameraMaxTilt);

            var camYRot = Mathf.Clamp(xDelta * s_possessionRotationSpeed, -CurrentPossessionSettings.m_possessedCameraMaxFrameYTurn, CurrentPossessionSettings.m_possessedCameraMaxFrameYTurn);
            camEulers.y += camYRot;

            float maxDiff = 180f - CurrentPossessionSettings.m_possessedCameraMaxFrameYTurn; //With a max diff of x each side, angle required to alias is > 180 - xww
            var currentCamEulers = camXform.eulerAngles;
            camEulers.y = Utility.ClampAngleBetween(camEulers.y, currentCamEulers.y - maxDiff, currentCamEulers.y + maxDiff);

            camRot = Quaternion.Euler(camEulers);
        }

        if (m_initialPossessCameraBlend >= 1f)
        {
            camXform.rotation = Quaternion.Slerp(camXform.rotation, camRot, smoothToAfterBlend);
            smoothToAfterBlend = Mathf.Clamp(smoothToAfterBlend + (Time.deltaTime * 2f), 0f, CurrentPossessionSettings.m_possessedCameraSmoothing);
        }
        
        return true;
    }

    Quaternion camRot;
    Vector2[] mouseMoves = new Vector2[8];
    int mouseMovesPointer = 0;
    
    private static DebugConsole.Command s_playanimsequencecmd = new ("animseq", _s => Me.PlayAnimationSequence(_s.Split(',')));
    private void PlayAnimationSequence(string[] _sequence)
    {
        if (m_possessedObject == null)
            return;
        int index = 0;
        Action<bool> cb = null;
        cb = (bool b) => 
        {
            if (index < _sequence.Length)
                m_possessedObject.PlaySingleAnimation(_sequence[index++], cb);
        };
        cb(true);
    }

    public void SmoothMouseDeltas(ref float xDelta, ref float yDelta)
    {
		mouseMoves[mouseMovesPointer++] = new(xDelta, yDelta);
		mouseMovesPointer %= mouseMoves.Length;
		xDelta = yDelta = 0f;
		foreach (var v2 in mouseMoves)
		{
			xDelta += v2.x;
			yDelta += v2.y;
		}
		xDelta /= mouseMoves.Length;
		yDelta /= mouseMoves.Length;
	}

    public (int, Vector3, Vector3, float, float) GetPossessedDetails()
    {
        if (m_possessedObject == null)
            return (-1, Vector3.zero, Vector3.zero, 0, 0);
        var cam = m_camera;
        var camXform = cam.transform;
        return (m_possessedObject.m_ID, m_possessedObject.m_transform.position, m_velocity, camXform.eulerAngles.y, m_possessedCameraCurrentDistance);
    }

    private Vector3 m_possessedObjectPositionOverride = Vector3.zero;
    private Vector3 m_possessedObjectVelocityOverride = Vector3.zero;
    private float m_possessedObjectRotationOverride = 0;
    private float m_possessedObjectDistanceOverride = 0;
    private void OverridePossessionState(int _id, Vector3 _pos, Vector3 _vel, float _rot, float _dist)
    {
        if (m_possessedObject == null || m_possessedObject.m_ID != _id)
        {
            Unpossess();
            PossessObject(NGManager.Me.FindCharacterByID(_id));
        }
        m_possessedObjectPositionOverride = _pos;
        m_possessedObjectVelocityOverride = _vel;
        m_possessedObjectRotationOverride = _rot;
        m_possessedObjectDistanceOverride = _dist;
    }

    public void SnapPossessionCamera()
    {
        m_possessionSnapCamera = 4;
        m_initialPossessCameraBlend = 1;
        camRot = m_possessedObject.m_transform.rotation;
        m_camera.transform.rotation = camRot;
    }
    
    string m_possessedActionLabel = null, m_possessedActionLabelMain = null, m_possessedActionLabelSub = null;
    int m_possessionSnapCamera = 0;
    
    public bool GetPossessedActionAvailable() => m_possessedObject != null && m_possessedActionLabelSub != null;
    public string GetPossessedActionLabel() => m_possessedActionLabelSub;

    void ShowPossessionAction(string _visualLabel, string _audioLabel, bool _isLatched)
    {
        if (_audioLabel != null)
            AudioClipManager.Me.PlaySound($"PlaySound_LuminousButtonAppear_{_audioLabel}", gameObject);
        if (_visualLabel != null)
        {
            var text = UIManager.Me.m_possessionInteractionUI.GetComponentInChildren<TMPro.TextMeshProUGUI>();
            text.text = _visualLabel;
        }
    }
    
    void UpdatePossessionActionLabel()
    {
        var label = m_possessedObject.GetStandardInteractionLabel();
        if (label == null) label = m_possessedObject.GetFallbackInteractionLabel();
        if (label != m_possessedActionLabel)
        {
            m_possessedActionLabel = label;
            m_possessedActionLabelSub = label;
            m_possessedActionLabelMain = label;
            if (label != null && label.StartsWith("#"))
            {
                m_possessedActionLabelSub = label.Substring(1);
                m_possessedActionLabelMain = null;
            }
            KeyboardShortcutManager.Me.Refresh();
            ShowPossessionAction(m_possessedActionLabelMain, m_possessedActionLabelSub, m_possessedObject.IsInInteractLatched);
        }
    }
    void UpdatePossessionActionUI()
    {
        var alphaTarget = m_possessedObject == null || m_possessedActionLabelMain == null? 0 : 1;
        var cg = UIManager.Me.m_possessionInteractionUI;
        cg.alpha = Mathf.Lerp(cg.alpha, alphaTarget, .2f);
        var showUI = cg.alpha > .01f || alphaTarget > .01f;
        if (showUI != cg.gameObject.activeSelf)
            cg.gameObject.SetActive(showUI);
    }
    
    void LateUpdate()
    {
        LateUpdateInputRecorder();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        UpdatePossessionActionUI();
#endif
        
        if (m_possessedObject == null)
            return;
        
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        UpdatePossessionActionLabel();
#endif
        
        var cam = m_camera;
        var camXform = cam.transform;
        var camFwd = camXform.forward;
        var camRight = camXform.right;

        var charXform = m_possessedObject.transform;

        if (m_possessedObjectPositionOverride.sqrMagnitude > .1f * .1f)
        {
            charXform.position = m_possessedObjectPositionOverride;
            var angles = camXform.eulerAngles;
            angles.y = m_possessedObjectRotationOverride;
            camXform.eulerAngles = angles;
            m_possessionCameraDistance = m_possessedObjectDistanceOverride;
        }
        var focus = charXform.position + camRight * CurrentPossessionSettings.m_defaultPossessedObjectCamRight + Vector3.up * m_possessedObjectHeight.Height;
				Vector3 normalCamFocus = focus;

				if (m_actionCamTracking)
				{
					var go = m_possessedObject.gameObject.transform.FindChildRecursiveByName("ActionCamTrackingObject");
					Vector3 actionCamFocus = go.transform.position;

					// We don't want the action cam pitching down for attacks where the character's swinging 
					// around their chest or hips or we'd spend all our time looking at the floor.
					// Therefore there's a y-axis distance threshold below which it'll start pitching.
					// Otherwise it just keeps its usual focus on the y-plane.
					if (normalCamFocus.y - actionCamFocus.y < m_actionCamPitchDownThreshold)
					{
						actionCamFocus.y = normalCamFocus.y;
					}

					// Since the action cam's currently on, set the focus target as such.
					focus = actionCamFocus;
					m_actionCamLastActiveTime = Time.time;
				}

		if (m_cameraTargetFocus.sqrMagnitude > 0f && m_possessionSnapCamera == 0)
		{
			float timeSinceActionCamActive = Time.time-m_actionCamLastActiveTime;

			// The action cam lerps slowly between the current camera focus and the target focus, whereas the normal cam tracks
			// immediately to the focus point every frame. When the action cam ends, we need to blend between these two modes.
			float blendFactorXZ = Mathf.Lerp(m_actionCamBlendFactorXZ * Time.deltaTime, 1f, timeSinceActionCamActive/m_actionCamToNormalCamBlendTime);
			float blendFactorY = Mathf.Lerp(m_actionCamBlendFactorY * Time.deltaTime, 1f, timeSinceActionCamActive/m_actionCamToNormalCamBlendTime);

			focus.x = Mathf.Lerp(m_cameraTargetFocus.x, focus.x, blendFactorXZ);
			focus.y = Mathf.Lerp(m_cameraTargetFocus.y, focus.y, blendFactorY);
			focus.z = Mathf.Lerp(m_cameraTargetFocus.z, focus.z, blendFactorXZ);
		}
        m_cameraTargetFocus = focus;

        m_possessedCameraCurrentDistance = GetBestFocusDist(focus, camFwd);
        focus += camRight * (m_possessedCameraCurrentDistance / m_possessionCameraDistance - 1); // shrink offset based on final distance
				normalCamFocus += camRight * (m_possessedCameraCurrentDistance / m_possessionCameraDistance - 1); // shrink offset based on final distance

				// RW-29-JAN-25: Note we're using normalCamFocus here - the action cam shouldn't be changing the camera's position, just changing 
				// its focus.
				var pos = normalCamFocus - camFwd * m_possessedCameraCurrentDistance;
        m_initialPossessCameraBlend = Mathf.Min(1, m_initialPossessCameraBlend + Time.deltaTime);
        camXform.position = Vector3.Lerp(camXform.position, pos, m_initialPossessCameraBlend);
        var finalFwd = focus - camXform.position;
        if (m_initialPossessCameraBlend < 1f)
            finalFwd += camRot * Vector3.forward;
        finalFwd = Vector3.Slerp(camXform.forward, finalFwd, m_initialPossessCameraBlend);
        camXform.LookAt(camXform.position + finalFwd, Vector3.up);
        
        ++m_framesPossessed;
        if (m_possessionSnapCamera > 0) --m_possessionSnapCamera;

        camXform.position += cameraShakeOffset;
    }

    [SerializeField]
    private float cameraShakeMovementInUnits = 0.5f;
    [SerializeField]
    private float cameraShakeDurationInSeconds = 0.5f;
    private float cameraShakeElapsedTime = float.MaxValue;
    private Coroutine cameraShakeCo = null;
    private Vector3 cameraShakeOffset = Vector3.zero;
    public void PlayCameraShake()
    {
        if (cameraShakeCo != null)
            StopCoroutine(cameraShakeCo);
        
		cameraShakeCo = StartCoroutine(Co_PlayCameraShake());
    }

    private IEnumerator Co_PlayCameraShake()
    {
        cameraShakeElapsedTime = 0f;
        while (cameraShakeElapsedTime < cameraShakeDurationInSeconds)
        {
            var right = m_camera.transform.right;
            var up = m_camera.transform.up;
            var random = UnityEngine.Random.insideUnitCircle * cameraShakeMovementInUnits;
            cameraShakeOffset = (right * random.x) + (up * random.y);

            cameraShakeElapsedTime += Time.deltaTime;

            yield return null;
        }

        cameraShakeOffset = Vector3.zero;
        cameraShakeCo = null;
    }

    [SerializeField]
    private float killCamSlowMotionTimeScale = 0.4f;
    [SerializeField]
    private float killCamSlowMotionDurationInSeconds = 1f;
    [SerializeField]
    private float killCamSlowMotionLerpInEnd = 0.2f;
    [SerializeField]
    private float killCamSlowMotionLerpOutStart = 0.9f;
    [SerializeField]
    private float bigAttackSlowMotionTimeScale = 0.07f;
    [SerializeField]
    private float bigAttackSlowMotionLerpInEnd = 0.6f;
    [SerializeField]
    private float bigAttackMotionLerpOutStart = 1f;

		public float caveDeathUnpossessDelay = 3f;
    private float slowMotionElapsedTime = float.MaxValue;
    private Coroutine slowMotionCo = null;

		[SerializeField, Tooltip("The lerp rate between the current camera focus and the Action Cam's target, on the X & Z axes.")]
		private float m_actionCamBlendFactorXZ = 0.625f;

		[SerializeField, Tooltip("The lerp rate between the current camera focus and the Action Cam's target, on the Y axis.")]
		private float m_actionCamBlendFactorY = 2.5f;

		[SerializeField, Tooltip("The time, in seconds, taken to change the lerp rates back to the normal camera (which tracks instantly) when the Action Cam ends.")]
		private float m_actionCamToNormalCamBlendTime = 1.6f;

		[SerializeField, Tooltip("The y-axis distance between the camera's usual focus point and the Action Cam's target at which the Action Cam will start to pitch down.")]
		private float m_actionCamPitchDownThreshold = 2.5f;

		private float m_actionCamLastActiveTime = -1000.0f;

    public void PlaySlowMotionIfPossible()
	{
        if (slowMotionCo != null)
            StopCoroutine(slowMotionCo);
        
		slowMotionCo = StartCoroutine(Co_PlaySlowMotionIfPossible());
	}

	public void PlayBigAttackSlowMotion(float _duration)
	{
		if (slowMotionCo != null)
			StopCoroutine(slowMotionCo);

		slowMotionCo = StartCoroutine(Co_PlaySlowMotion(bigAttackSlowMotionTimeScale, _duration, bigAttackSlowMotionLerpInEnd, bigAttackMotionLerpOutStart));
	}

	public void PlayRuneAvailableSlowMotion()
	{
		if (slowMotionCo != null)
			StopCoroutine(slowMotionCo);

		slowMotionCo = StartCoroutine(Co_PlaySlowMotion(0.05f, 0.05f, 0.021f, 0f, 0.8f));//(0.3f, 0.4f, 0.4f, 0.6f, 0.8f));
	}
    
	private IEnumerator Co_PlaySlowMotionIfPossible()
	{
		var enemies = GlobalData.Me.m_characterHolder.GetComponentsInChildren<MACreatureBase>(true);
		bool allDead = true;
        foreach (var enemy in enemies)
		{
			if (enemy.Health > 0f)
			{
				allDead = false;

				break;
			}
		}
		if (!allDead)
		{
			yield break;
		}
		
    yield return Co_PlaySlowMotion(killCamSlowMotionTimeScale, killCamSlowMotionDurationInSeconds, killCamSlowMotionLerpInEnd, killCamSlowMotionLerpOutStart);
	}

	private IEnumerator Co_PlaySlowMotion(float _minTimescale, float _duration, float _lerpInEnd, float _lerpOutStart)
	{
		slowMotionElapsedTime = 0f;
		float step1 = _duration * _lerpInEnd;
		float step2 = _duration * _lerpOutStart;
		while (slowMotionElapsedTime < _duration)
		{
			float t = 0f;
			if (slowMotionElapsedTime < step1)
				t = slowMotionElapsedTime / step1;
			else if ((slowMotionElapsedTime >= step1) && (slowMotionElapsedTime < step2))
				t = 1f;
			else if (slowMotionElapsedTime >= step2)
				t = 1f - ((slowMotionElapsedTime - step2) / (_duration - step2));
			Time.timeScale = Mathf.Lerp(1f, _minTimescale, t);

			slowMotionElapsedTime += Time.deltaTime;

			yield return null;
		}

		Time.timeScale = 1f;
		slowMotionCo = null;
	}

	private IEnumerator Co_PlaySlowMotion(float _minTimescale, float _duration, float _activationDelay, float _lerpInEnd, float _lerpOutStart)
	{
		yield return new WaitForSeconds(_activationDelay);
		yield return Co_PlaySlowMotion(_minTimescale, _duration, _lerpInEnd, _lerpOutStart);
	}

	public void StopSlowMo()
	{
		if (slowMotionCo != null)
			StopCoroutine(slowMotionCo);
		slowMotionCo = null;
		slowMotionElapsedTime = float.MaxValue;
		Time.timeScale = 1f;
	}

    private void ResetCameraEffects()
    {
        if (cameraShakeCo != null)
            StopCoroutine(cameraShakeCo);
        cameraShakeCo = null;
        cameraShakeElapsedTime = float.MaxValue;
        cameraShakeOffset = Vector3.zero;

        if (slowMotionCo != null)
            StopCoroutine(slowMotionCo);
        slowMotionCo = null;
        slowMotionElapsedTime = float.MaxValue;
        Time.timeScale = 1f;
    }

    private float GetBestFocusDist(Vector3 _focus, Vector3 _camFwd)
    {
        #if THIRDPERSONCAMERA_V1

        const float c_cameraRayRadius = .25f;
        var bestDistance = Mathf.Lerp(m_possessedCameraCurrentDistance, m_possessionCameraDistance, .1f);
        var hits = Physics.SphereCastAll(_focus, c_cameraRayRadius, -_camFwd, bestDistance + c_cameraRayRadius);
        foreach (var hit in hits)
        {
            if (hit.collider.isTrigger) continue;
            if (hit.collider.transform.GetComponentInParent<NGMovingObject>() != null) continue;
            if (hit.collider.gameObject.GetComponentInParent<BlockCage>() != null) continue;
            var dist = hit.distance - c_cameraRayRadius;
            if (dist < bestDistance) bestDistance = dist;
        }
        return Mathf.Max(.25f, bestDistance);

        #elif THIRDPERSONCAMERA_V2

        const float c_ObstacleStepBack = 0.1f;
        const float c_FocusRetryStepBack = 0.5f;
        var stepsBack = 0;
        var bestDistance = 0f;
        while (bestDistance <= 1f)
        {
            bestDistance = m_possessionCameraDistance;
            var hits = Physics.SphereCastAll(_focus, s_cameraRayRadius, -_camFwd, m_possessionCameraDistance + c_ObstacleStepBack);
            foreach (var hit in hits)
            {
                if (hit.collider.isTrigger) continue;
                if (hit.collider.transform.GetComponentInParent<NGMovingObject>() != null) continue;
                if (hit.collider.gameObject.GetComponentInParent<BlockCage>() != null) continue;
                if (hit.collider.gameObject.GetComponentInParent<Terrain>() != null)
                {
                    bestDistance = hit.distance - c_ObstacleStepBack;
                    break;
                }
                var newDist = hit.distance - c_ObstacleStepBack;
                if (newDist < bestDistance)
                    bestDistance = newDist;
            }
            _focus += _camFwd * -c_FocusRetryStepBack;
            ++stepsBack;
        }
        --stepsBack;
        
        if (bestDistance > m_possessionCameraDistance)
            bestDistance = Mathf.Lerp(m_possessedCameraCurrentDistance, m_possessionCameraDistance, .1f);

        bestDistance += stepsBack * c_FocusRetryStepBack;
        
        return bestDistance;

        #elif THIRDPERSONCAMERA_V3

        float z = m_camera.nearClipPlane;
        float x = Mathf.Tan(m_camera.fieldOfView * 3f) * z;
        float y = x / m_camera.aspect;
        var cameraPos = _focus - (m_camera.transform.forward * m_possessionCameraDistance);
        var cameraRot = m_camera.transform.rotation;
        Vector3[] rectangle = new Vector3[5];
        rectangle[0] = cameraPos + (cameraRot * new Vector3(-x, y, z));
        rectangle[1] = cameraPos + (cameraRot * new Vector3(x, y, z));
        rectangle[2] = cameraPos + (cameraRot * new Vector3(-x, -y, z));
        rectangle[3] = cameraPos + (cameraRot * new Vector3(x, -y, z));
        rectangle[4] = cameraPos;
        LayerMask mask = LayerMask.GetMask("Default", "Terrain", "Roads");
        float dist = m_possessionCameraDistance;
        // ClearGizmos("ASDASD0");
        // ClearGizmos("ASDASD1");
        // ClearGizmos("ASDASD2");
        // ClearGizmos("ASDASD3");
        // ClearGizmos("ASDASD4");
        var raycastTarget = _focus;
        if (m_possessedObject != null)
        {
            var character = m_possessedObject.transform;
            raycastTarget = character.position + (character.up * m_possessedObjectHeight.Height * 0.9f);
        }
        for (int i = 0; i < rectangle.Length; ++i)
        {
            var point = rectangle[i];
            Ray ray = new Ray(raycastTarget, point - raycastTarget);
            // AddGizmoLine($"ASDASD{i}", raycastTarget, raycastTarget + ((point - raycastTarget).normalized * m_possessionCameraDistance), Color.cyan);
            RaycastHit raycastHit;
            if (Physics.Raycast(ray, out raycastHit, m_possessionCameraDistance, mask, QueryTriggerInteraction.Ignore))
            {
                if (raycastHit.distance < dist)
                    dist = raycastHit.distance;
            }
        }

        dist *= dist / m_possessionCameraDistance;
        float distMin = 0.5f;
        dist = Mathf.Clamp(dist, distMin, m_possessionCameraDistance);
        if ((m_possessionSnapCamera <= 0) && (m_possessedCameraCurrentDistance >= distMin))
            dist = Mathf.Lerp(dist, m_possessedCameraCurrentDistance, 0.65f);
        return dist;

        #endif
    }

		public void ToggleActionCamTracking(bool _startTracking)
		{
			m_actionCamTracking = _startTracking;
		}
    private class PossessedObjectHeight
    {
        private Transform m_headTransform;
        private Transform m_toeTransform;
        private MACharacterBase m_character = null;

        public float Height
        {
            get
            {
                if (m_character != null) return m_character.m_headHeight;
                if (m_headTransform != null && m_toeTransform != null) return (m_headTransform.position - m_toeTransform.position).y;
                return Me.CurrentPossessionSettings.m_defaultPossessedObjectHeight;
            }
        }
    
        public PossessedObjectHeight(NGMovingObject _object)
        {
            var charBase = _object as MACharacterBase;
            if (charBase == null)
            {
                m_headTransform = _object.m_transform.FindRecursiveByName<Transform>(_object.m_headBoneName);
                m_toeTransform = _object.m_transform.FindRecursiveByName<Transform>(_object.m_toeBoneName);
            }
            else
            {
                m_character = charBase;
            }
        }
    }
}
