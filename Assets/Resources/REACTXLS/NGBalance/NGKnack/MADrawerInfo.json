[{"id": "66d5de088bcd3d02d59ff33e", "m_drawerIndex": "Armour:Arms", "m_number": 3, "m_drawerTitle": "Arms", "m_drawerName": "Armour", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66c33a46c46ee502850debbb", "m_drawerIndex": "Armour:Chest", "m_number": 2, "m_drawerTitle": "Chest", "m_drawerName": "Armour", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66d5cb5ac3b816030025a111", "m_drawerIndex": "Armourer:Aesthetics", "m_number": 2, "m_drawerTitle": "Aesthetics", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66cdb43b7384b80283779398", "m_drawerIndex": "Armourer:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66d5cb41a10a1802b7d8f4bc", "m_drawerIndex": "Armourer:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66d5cbb70de93b0315b32d5f", "m_drawerIndex": "Armourer:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66d5e04992ca5502d4b983fc", "m_drawerIndex": "Armour:Feet", "m_number": 6, "m_drawerTitle": "Feet", "m_drawerName": "Armour", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66d5ddb9756d6302f90089f7", "m_drawerIndex": "Armour:Head", "m_number": 1, "m_drawerTitle": "Head", "m_drawerName": "Armour", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66d5de296ace8102e4d9afed", "m_drawerIndex": "Armour:Hips", "m_number": 4, "m_drawerTitle": "Hips", "m_drawerName": "Armour", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66d5e00ad8b43802c478cfd9", "m_drawerIndex": "Armour:Legs", "m_number": 5, "m_drawerTitle": "Legs", "m_drawerName": "Armour", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "67bee7bd0ff3e902e0772b1d", "m_drawerIndex": "Beacon :Beacon Parts", "m_number": 1, "m_drawerTitle": "Beacon Parts", "m_drawerName": "Beacon ", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "672e47eb7b5e07031844e0fd", "m_drawerIndex": "Buildings:No Blocks", "m_number": 1, "m_drawerTitle": "No Blocks", "m_drawerName": "Buildings", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67d021701cd60602fe614708", "m_drawerIndex": "Clothing:Body", "m_number": 2, "m_drawerTitle": "Body", "m_drawerName": "Clothing", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "67d021761cd60602fe614742", "m_drawerIndex": "Clothing:<PERSON>", "m_number": 4, "m_drawerTitle": "Boots", "m_drawerName": "Clothing", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "67d021031cd60602fe61446c", "m_drawerIndex": "Clothing:Head", "m_number": 1, "m_drawerTitle": "Head", "m_drawerName": "Clothing", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "67d021731cd60602fe614726", "m_drawerIndex": "Clothing:<PERSON><PERSON>", "m_number": 3, "m_drawerTitle": "<PERSON>ts", "m_drawerName": "Clothing", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "67ed3f259921c902e2ad8c16", "m_drawerIndex": "CottonDispatch:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "CottonDispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67ed3f2c49ee0502d7e6432c", "m_drawerIndex": "CottonDispatch:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "CottonDispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67ed3f199921c902e2ad8c01", "m_drawerIndex": "CottonDispatch:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "CottonDispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67ed3f31971d6d02ed895a63", "m_drawerIndex": "CottonDispatch:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "CottonDispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67b857b00a2efe02f8473fd5", "m_drawerIndex": "CottonFarm:Action", "m_number": 1, "m_drawerTitle": "Action", "m_drawerName": "CottonFarm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67b857ae5852e502da873e95", "m_drawerIndex": "CottonFarm:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_drawerName": "CottonFarm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67b857ab34bf3d02c49d8835", "m_drawerIndex": "CottonFarm:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_drawerName": "CottonFarm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67b857b234bf3d02c49d8871", "m_drawerIndex": "CottonFarm:Workers", "m_number": 3, "m_drawerTitle": "Workers", "m_drawerName": "CottonFarm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65770cc1b39aeb00274da6b4", "m_drawerIndex": "CottonMill:New Blocks", "m_number": 4, "m_drawerTitle": "New Blocks", "m_drawerName": "CottonMill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67d4640ddd9850032a1af014", "m_drawerIndex": "CottonTavern:Blocks", "m_number": 1, "m_drawerTitle": "Blocks", "m_drawerName": "CottonTavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65bb94a3f700a4002854c2f9", "m_drawerIndex": "Decorations:Stick<PERSON>", "m_number": 1, "m_drawerTitle": "Stickers", "m_drawerName": "Decorations", "m_drawerType": "Product, Building", "m_unlockAtStart": "False"}, {"id": "673609f76cfacb030a9ca8c0", "m_drawerIndex": "Dispatch:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67360a46b2844002da42199e", "m_drawerIndex": "Dispatch:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67360846eb54f302e7c48feb", "m_drawerIndex": "Dispatch:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67360a7f75689202af920bc7", "m_drawerIndex": "Dispatch:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3b589238940027a7c339", "m_drawerIndex": "Factory:Action", "m_number": 1, "m_drawerTitle": "Action", "m_drawerName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c4936d42ae5002a187bea", "m_drawerIndex": "Factory:Aesthetic", "m_number": 7, "m_drawerTitle": "Aesthetic", "m_drawerName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3b6113f475002aac8d0e", "m_drawerIndex": "Factory:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_drawerName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64b66721ca3be3002a387508", "m_drawerIndex": "Factory:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_drawerName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3bfd83b90d002936ef9d", "m_drawerIndex": "Factory:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_drawerName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3b501691380028863bb8", "m_drawerIndex": "Factory:Workers", "m_number": 3, "m_drawerTitle": "Workers", "m_drawerName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64901f9bdf6f1e0027eb52b8", "m_drawerIndex": "Farm:Action", "m_number": 1, "m_drawerTitle": "Action", "m_drawerName": "Farm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64a3eaafaaf4f60028429459", "m_drawerIndex": "Farm:Aesthetic", "m_number": 7, "m_drawerTitle": "Aesthetic", "m_drawerName": "Farm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3a21d42ae5002a17f8dc", "m_drawerIndex": "Farm:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_drawerName": "Farm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3a8382ba2a00273e3016", "m_drawerIndex": "Farm:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_drawerName": "Farm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3a9a9238940027a7bfa2", "m_drawerIndex": "Farm:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_drawerName": "Farm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c3a3f9238940027a7be14", "m_drawerIndex": "Farm:Workers", "m_number": 3, "m_drawerTitle": "Workers", "m_drawerName": "Farm", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6499606a92389400278f6d3d", "m_drawerIndex": "Food:Bread", "m_number": 1, "m_drawerTitle": "Bread", "m_drawerName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "True"}, {"id": "65bbabfd7cf692002618f7de", "m_drawerIndex": "Food:Cutlery", "m_number": 7, "m_drawerTitle": "Cutlery", "m_drawerName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "649984aea9d9270026a2abbc", "m_drawerIndex": "Food:Dairy", "m_number": 4, "m_drawerTitle": "Dairy", "m_drawerName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "65bbaf5973300f00273b5d4c", "m_drawerIndex": "Food:Fillings", "m_number": 2, "m_drawerTitle": "Fillings", "m_drawerName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "True"}, {"id": "663355c19d06d50028ff115c", "m_drawerIndex": "Food:Pies & Soups", "m_number": 3, "m_drawerTitle": "Pies & Soups", "m_drawerName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "True"}, {"id": "670e8bc29786f202f6ea5d5e", "m_drawerIndex": "Heroes Guild:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "670e8baa02e48a02d3e8f548", "m_drawerIndex": "Heroes Guild:Doors", "m_number": 3, "m_drawerTitle": "Doors", "m_drawerName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67220acd28794f0311397806", "m_drawerIndex": "Heroes Guild:<PERSON><PERSON>", "m_number": 5, "m_drawerTitle": "Healing", "m_drawerName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67289c5f426d54030d6184fa", "m_drawerIndex": "Heroes Guild:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67220b10900d9702f5acc37a", "m_drawerIndex": "Heroes Guild:Support", "m_number": 4, "m_drawerTitle": "Support", "m_drawerName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64999fb0a0093f002a8cd355", "m_drawerIndex": "House:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "House", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67b374dbc5487d02c1b74e5b", "m_drawerIndex": "House: BriarLake:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "House: <PERSON><PERSON><PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67b374f8c5487d02c1b74e89", "m_drawerIndex": "House: BriarLake:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "House: <PERSON><PERSON><PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67b374fab1a8f90324d0a269", "m_drawerIndex": "House: BriarLake:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "House: <PERSON><PERSON><PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64999f9075769b002693b159", "m_drawerIndex": "House:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "House", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66c743a97a6e95027f76c4a4", "m_drawerIndex": "House: Metal:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "House: Metal", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64999fa57525690027437042", "m_drawerIndex": "House:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "House", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6787d6666d2c7f02cb83350d", "m_drawerIndex": "House: Swamp:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "House: Swamp", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6787d64dac9ca502fb1aef7d", "m_drawerIndex": "House: Swamp:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "House: Swamp", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6787d606efac9e030ed64ec9", "m_drawerIndex": "House: Swamp:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "House: Swamp", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67ba430e71c9e50f7d06e381", "m_drawerIndex": "Lumber Mill:Action", "m_number": 5, "m_drawerTitle": "Action", "m_drawerName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "662035070acd2e0027bc6561", "m_drawerIndex": "Lumber Mill:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66203527de3465002790264c", "m_drawerIndex": "Lumber Mill:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6674255c8285f70027ce2450", "m_drawerIndex": "Lumber Mill:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6620353ba78fb80027c83cc3", "m_drawerIndex": "Lumber Mill:Workers", "m_number": 2, "m_drawerTitle": "Workers", "m_drawerName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "678902e575236702f240d689", "m_drawerIndex": "Metal Dispatch:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6761581968f40c02f046017d", "m_drawerIndex": "Metal Dispatch:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "676158b97ff03902de36c3b8", "m_drawerIndex": "Metal Dispatch:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "678903336b2f4502b58c309e", "m_drawerIndex": "Metal Dispatch:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "680766225147a302c7e986af", "m_drawerIndex": "Metal Mine:Action", "m_number": 1, "m_drawerTitle": "Action", "m_drawerName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f838967094c5002881212f", "m_drawerIndex": "Metal Mine:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67efa2c56abaee02fc646726", "m_drawerIndex": "Metal Mine:Grinder", "m_number": 5, "m_drawerTitle": "Grinder", "m_drawerName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f83979542332002802330b", "m_drawerIndex": "Metal Mine:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_drawerName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f83920b5000000276faea9", "m_drawerIndex": "Metal Mine:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_drawerName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f83944199c030027b03d21", "m_drawerIndex": "Metal Mine:Workers", "m_number": 3, "m_drawerTitle": "Workers", "m_drawerName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "680b4e224c8b3202c288808a", "m_drawerIndex": "Metal Smelter:Action", "m_number": 1, "m_drawerTitle": "Action", "m_drawerName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f83e6d18f2e80027468655", "m_drawerIndex": "Metal Smelter:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_drawerName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67eeb23b6abaee02fc63a66d", "m_drawerIndex": "Metal Smelter:Furnace", "m_number": 5, "m_drawerTitle": "Furnace", "m_drawerName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f83f1c67df500027063f5f", "m_drawerIndex": "Metal Smelter:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f83e9b3ce1140025253efe", "m_drawerIndex": "Metal Smelter:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65f83ed9a3f8100026759637", "m_drawerIndex": "Metal Smelter:Workers", "m_number": 2, "m_drawerTitle": "Workers", "m_drawerName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67daa518035233032af1b117", "m_drawerIndex": "Metal Tavern:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67daa54db9d75c0326e462e6", "m_drawerIndex": "Metal Tavern:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67daa55a1eccf702ceadcded", "m_drawerIndex": "Metal Tavern:Fixtures", "m_number": 4, "m_drawerTitle": "Fixtures", "m_drawerName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67daa566035233032af1b1b4", "m_drawerIndex": "Metal Tavern:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649c491c5c28330027644836", "m_drawerIndex": "Mill:Action", "m_number": 1, "m_drawerTitle": "Action", "m_drawerName": "Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649af3b113f475002aa1802f", "m_drawerIndex": "Mill:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_drawerName": "Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649af3e075256900275066db", "m_drawerIndex": "Mill:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_drawerName": "Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649af3dc5ff0510028a3ff78", "m_drawerIndex": "Mill:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_drawerName": "Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "649af3e456fc300029ea36e3", "m_drawerIndex": "Mill:Workers", "m_number": 3, "m_drawerTitle": "Workers", "m_drawerName": "Mill", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6516ab85a6a11700280f9dda", "m_drawerIndex": "Monsters:New Blocks", "m_number": 1, "m_drawerTitle": "New Blocks", "m_drawerName": "Monsters", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66d5cdd7f3c4dd03094da791", "m_drawerIndex": "Shop:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Shop", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "661d136df8826100273567dc", "m_drawerIndex": "Shop:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Shop", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "661d1386fc0230002969ed7e", "m_drawerIndex": "Shop:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "Shop", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "661d3d280c4d67002868157d", "m_drawerIndex": "Shop:Workers", "m_number": 2, "m_drawerTitle": "Workers", "m_drawerName": "Shop", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "678903b8ac9ca502fb1cae2c", "m_drawerIndex": "Swamp Dispatch:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "Swamp Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "678903baefac9e030ed85bf9", "m_drawerIndex": "Swamp Dispatch:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Swamp Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "678903b5efac9e030ed85bed", "m_drawerIndex": "Swamp Dispatch:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Swamp Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "678903bcac9ca502fb1cae44", "m_drawerIndex": "Swamp Dispatch:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "Swamp Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67853d7d6b656302d37e5eb7", "m_drawerIndex": "Swamp Tavern:Blocks", "m_number": 1, "m_drawerTitle": "Blocks", "m_drawerName": "Swamp Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64a5485363c4610028560b90", "m_drawerIndex": "Tavern:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_drawerName": "Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64a548ff56b25e002936c9d9", "m_drawerIndex": "Tavern:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_drawerName": "Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "67360c8f6c3e430324d2b971", "m_drawerIndex": "Tavern:Fixtures", "m_number": 4, "m_drawerTitle": "Fixtures", "m_drawerName": "Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64a5490adcb91a00287e8bfd", "m_drawerIndex": "Tavern:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_drawerName": "Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "661e65bbf018b200294a9024", "m_drawerIndex": "Turret:Actions", "m_number": 1, "m_drawerTitle": "Actions", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "682c855812b72c02f558a798", "m_drawerIndex": "Turret:<PERSON><PERSON>", "m_number": 5, "m_drawerTitle": "Ammo", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "661e6619866a450028878605", "m_drawerIndex": "Turret:Bases", "m_number": 2, "m_drawerTitle": "Bases", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "6812515bab75f002f65cffbd", "m_drawerIndex": "Turret:Shafts", "m_number": 3, "m_drawerTitle": "Shafts", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "661e65f03a14390028a11b05", "m_drawerIndex": "Turret:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "64e70bcab34a18002744f170", "m_drawerIndex": "Weapons:Axes", "m_number": 2, "m_drawerTitle": "Axes", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "64e70b66f017f100293b8041", "m_drawerIndex": "Weapons:Blades", "m_number": 1, "m_drawerTitle": "Blades", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "64e70b610e263b002736c381", "m_drawerIndex": "Weapons:Crossguards", "m_number": 5, "m_drawerTitle": "Crossguards", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "64e70b5b4c69d600285d2301", "m_drawerIndex": "Weapons:Grips", "m_number": 7, "m_drawerTitle": "Grips", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66ded193973ca102f0cad663", "m_drawerIndex": "Weapons:Hammers", "m_number": 4, "m_drawerTitle": "Hammers", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66bb475bc1674d0031f87540", "m_drawerIndex": "Weapons:Maces", "m_number": 3, "m_drawerTitle": "Maces", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "67fabe2e7577bf02d5e8f0bc", "m_drawerIndex": "Weaponsmith:Action", "m_number": 1, "m_drawerTitle": "Action", "m_drawerName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66d5d81e6ace8102e4d98974", "m_drawerIndex": "Weaponsmith:Aesthetics", "m_number": 3, "m_drawerTitle": "Aesthetics", "m_drawerName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66bf3182ddcc9e0287443b7c", "m_drawerIndex": "Weaponsmith:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_drawerName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66d5d7a628df1f02fcac396b", "m_drawerIndex": "Weaponsmith:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_drawerName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "66d5d9f75c1bd702db58b52f", "m_drawerIndex": "Weaponsmith:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_drawerName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False"}, {"id": "65968d478477e90027687568", "m_drawerIndex": "Weapons:<PERSON><PERSON><PERSON>", "m_number": 8, "m_drawerTitle": "<PERSON><PERSON><PERSON>", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}, {"id": "66bb710ec1674d0031f927b6", "m_drawerIndex": "Weapons:Shafts", "m_number": 6, "m_drawerTitle": "Shafts", "m_drawerName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False"}]