[{"id": "66d1ba9201512b02d9a5e135", "m_key": "Commoners::A<PERSON><PERSON>", "m_name": "AHole", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON>|Commoners::G<PERSON><PERSON>_<PERSON>ker", "m_unlock": "m_handGestureAHole=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Gesture: A-Hole", "m_description": "My mother always said, if you can't say anything nice, use an offensive gesture.", "m_position": "(-201.00, 76.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:18", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1c39b2863e402fe7e358c", "m_key": "Lords::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>1", "m_name": "ArmourerParts1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::Weaponary_Parts1", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 1, "m_icon": "ResearchBedroomCapacity", "m_title": "Armory Aesthetics Pack 1", "m_description": "New blocks for the Armoury. Jazz up those javelins.", "m_position": "(463.00, -239.50, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:45", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2d6a278802e3610bf6", "m_key": "Lords::ArmourSalesPricePlus1", "m_name": "ArmourSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus2", "m_unlock": "m_armourSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-758.79, 327.95, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:45", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2d6a278802e3610bf9", "m_key": "Lords::ArmourSalesPricePlus2", "m_name": "ArmourSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus3", "m_unlock": "m_armourSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-838.00, 428.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:46", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2d6a278802e3610bfc", "m_key": "Lords::ArmourSalesPricePlus3", "m_name": "ArmourSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus4", "m_unlock": "m_armourSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-769.00, 551.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:47", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2d6a278802e3610bff", "m_key": "Lords::ArmourSalesPricePlus4", "m_name": "ArmourSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus5", "m_unlock": "m_armourSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-916.00, 627.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:47", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2d6a278802e3610c02", "m_key": "Lords::ArmourSalesPricePlus5", "m_name": "ArmourSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_armourSalesPriceMarkup= 1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-1037.00, 720.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:47", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b042a4", "m_key": "Lords::BedroomL1", "m_name": "BedroomL1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::BedroomL2", "m_unlock": "m_blockBedroomLevel =1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 1, "m_icon": "ResearchBedroomCapacity", "m_title": "Additional Bed", "m_description": "Efficient living is efficient working. Add another bed to a worker bedroom and increase the building's residential capacity by +1", "m_position": "(-93.79, 227.58, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:48", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b042a9", "m_key": "Lords::BedroomL2", "m_name": "BedroomL2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::BedroomL3", "m_unlock": "m_blockBedroomLevel =2", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 2, "m_icon": "ResearchBedroomCapacity", "m_title": "Bunk Bed", "m_description": "Floorspace is at a premium in these small bedrooms. Add this bunk bed and Increase the capacity of all bedrooms by 2!", "m_position": "(250.07, 233.44, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:48", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b042ae", "m_key": "Lords::BedroomL3", "m_name": "BedroomL3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_blockBedroomLevel =3", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 4, "m_icon": "ResearchBedroomCapacity", "m_title": "Stacked Beds", "m_description": "You can fit more workers in here if you add a third bed to the bunks. Increases capacity by 3!", "m_position": "(593.86, 258.49, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:49", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66e465e35d2a8102ba3319b2", "m_key": "Royal::BloodAxe", "m_name": "BloodAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 400, "m_factionCost": 4, "m_icon": "ResearchPaints", "m_title": "Blood Luster", "m_description": "It wants to cut and tear. Feed it blood before it turns on you.", "m_position": "(695.00, -201.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:26", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670cff232fb8e202f7cc4fd1", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "BreadPack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "MA_RL_BreadPack", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Bread Pack", "m_description": "They do dough, though, don't you know? It's a pack of <b>Bread Parts</b>, not a bag of bread. We're not feeding the ducks here.", "m_position": "(-151.00, -36.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:21", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b04268", "m_key": "Commoners::Buildings", "m_name": "Buildings", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>Mill|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Buildings", "m_description": "The Building Blocks of a sucessful development.", "m_position": "(319.15, -118.63, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:24", "m_activatedAtStart": "True", "m_isLocked": ""}, {"id": "670cff25bd7d6702b497ab3b", "m_key": "Commoners::BuildLumberMill", "m_name": "BuildLumberMill", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON>", "m_unlock": "BuildLumberMill=true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Lumber Mill", "m_description": "With the Lumber Mill, you can set workers to chop down trees and gain valuable wood resources.", "m_position": "(343.00, -283.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:25", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67ba29290b1b3e0bee0fca84", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "BuildTurret", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "BuildTurret=true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 5, "m_icon": "ResearchPickup", "m_title": "Build Turrets", "m_description": "Auto-fire cannons that target enemies only. Load them with anything, they shoot at will.", "m_position": "(231.00, -368.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:28", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67cee29d89c8e00314b46c5d", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "BuildWeaponsmith", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "BuildWeaponsmith=true", "m_giftCard": "", "m_dollarCost": 400, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Weaponsmith", "m_description": "Make and equip weapons", "m_position": "(554.00, -280.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:29", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66e465e372ce6202c016cf46", "m_key": "Royal::CastleGuardAxe", "m_name": "CastleGuardAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::BloodAxe", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 275, "m_factionCost": 3, "m_icon": "ResearchPatterns", "m_title": "<PERSON><PERSON>'s Chopper", "m_description": "A squire's best friend. It's ithcing to be buried in the guts of an enemy.", "m_position": "(725.00, -9.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:26", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670cff23f35328031c8aab54", "m_key": "Commoners::Chefs", "m_name": "Chefs", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Chefs", "m_description": "An advanced worker is a skilled worker and a skilled worker works fast.  The <b>Chef</b> is the perfect worker for a factory that makes Food products.", "m_position": "(-201.00, -353.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:29", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e143111c82d602c3d27c4f", "m_key": "Lords::ClickBoost1", "m_name": "ClickBoost1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClickBoost2", "m_unlock": "m_tapHoldMultiplier=1.05", "m_giftCard": "", "m_dollarCost": 300, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 1", "m_description": "tbd.", "m_position": "(158.00, 357.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:52", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e14315f2ddf9030f403653", "m_key": "Lords::ClickBoost2", "m_name": "ClickBoost2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClickBoost3", "m_unlock": "m_tapHoldMultiplier=1.07", "m_giftCard": "", "m_dollarCost": 900, "m_factionCost": 12, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 2", "m_description": "tbd.", "m_position": "(220.00, 460.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:52", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e1431d59a34102d2776fc3", "m_key": "Lords::ClickBoost3", "m_name": "ClickBoost3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::<PERSON>lickB<PERSON>t4", "m_unlock": "m_tapHoldMultiplier=1.09", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 18, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 3", "m_description": "tbd.", "m_position": "(156.00, 588.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:53", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e143261c82d602c3d27d25", "m_key": "Lords::<PERSON>lickB<PERSON>t4", "m_name": "ClickBoost4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_tapHoldMultiplier=1.12", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 27, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 4", "m_description": "tbd.", "m_position": "(275.00, 680.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:53", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67d42f2c6a278802e3610bd8", "m_key": "Lords::ClothesSalesPricePlus1", "m_name": "ClothesSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClothesSalesPricePlus2", "m_unlock": "m_clothesSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-628.79, -3.05, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:54", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610bdb", "m_key": "Lords::ClothesSalesPricePlus2", "m_name": "ClothesSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClothesSalesPricePlus3", "m_unlock": "m_clothesSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-830.00, -97.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:54", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610bde", "m_key": "Lords::ClothesSalesPricePlus3", "m_name": "ClothesSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClothesSalesPricePlus4", "m_unlock": "m_clothesSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-939.00, 14.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:54", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610be1", "m_key": "Lords::ClothesSalesPricePlus4", "m_name": "ClothesSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::<PERSON><PERSON><PERSON>SalesPricePlus5", "m_unlock": "m_clothesSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-1126.00, -71.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:55", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610be4", "m_key": "Lords::<PERSON><PERSON><PERSON>SalesPricePlus5", "m_name": "ClothesSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_clothesSalesPriceMarkup  1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-1197.00, 66.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:55", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1bf1571545702ce232a48", "m_key": "Commoners::<PERSON><PERSON><PERSON>", "m_name": "Cross<PERSON>inger", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureCrossFingers=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Gesture: Cross Fingers", "m_description": "<b>Cross Fingers</b>, not angry ones. No, no, no. These are for wishing good luck on passing workers. Good luck getting a pay rise, you filthy oiks!", "m_position": "(-334.00, 433.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:30", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66e465e572ce6202c016cf6b", "m_key": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_name": "DeathH<PERSON>mer", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 1750, "m_factionCost": 10, "m_icon": "ResearchPaints", "m_title": "Death Claw", "m_description": "Glides through the air, glides through the face.", "m_position": "(-178.00, -272.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:27", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670d1071e76a77031e647858", "m_key": "Commoners::DisciplineHeroes ", "m_name": "DisciplineHeroes ", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Discipline Heroes ", "m_description": "Oh sure, they're big and muscular, but you have a hand bigger than their faces! <b>Discipline Heroes</b> like a god.", "m_position": "(295.35, 469.25, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:30", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e504db4a167031102c712", "m_key": "Commoners::DisciplineWorkers", "m_name": "DisciplineWorkers", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Discipline Workers", "m_description": "The common folk, don't let them step out of line. Always <b>Discipline Workers</b> before they get out of line.", "m_position": "(745.00, 398.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:30", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670d1071f35328031c8d5f06", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "m_name": "EncourageHeroes ", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::DisciplineHeroes ", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Encourage Heroes ", "m_description": "Give them a boost of self confidence with the glory that is <b>Encourage Heroes</b>.", "m_position": "(248.21, 275.67, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:31", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e504dcb288b0318c68001", "m_key": "Commoners::En<PERSON><PERSON>W<PERSON>kers", "m_name": "EncourageWorkers", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::DisciplineWorkers", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Encourage Workers", "m_description": "There's more than one way to skin a cat. And there's more than one way to <b>Encourage Workers</b>, but none are as kind as this.", "m_position": "(544.00, 441.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:32", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4e40cb288b0318c64867", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "ExpertChefs", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Expert Chefs", "m_description": "They're <b>Expert Chefs</b> because they've been in the job for years and can mash a potato in seconds with their hands tied behind their backs. But can they tidy up after themselves? Can they feck.", "m_position": "(-413.00, -393.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:32", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b04295", "m_key": "Lords::FactoryBlockL1", "m_name": "FactoryBlockL1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_blockFactoryLevel=1", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 1, "m_icon": "ResearchFactoryLevel", "m_title": "Improved Factory Productivity", "m_description": "Increase efficiency of the Factory block by 2%.\n<b>Faster = Better</b>", "m_position": "(-288.94, -0.91, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:55", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b0429a", "m_key": "Lords::FactoryBlockL2", "m_name": "FactoryBlockL2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_blockFactoryLevel=2", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Superior Factory Productivity", "m_description": "Squeeze more out of your Factory, increase its efficiency by 4%. \n<b>Even Faster = Even Better!</b>", "m_position": "(144.60, -17.04, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:56", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b0429f", "m_key": "Lords::FactoryBlockL3", "m_name": "FactoryBlockL3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::Weaponary_Parts1", "m_unlock": "m_blockFactoryLevel=3", "m_giftCard": "", "m_dollarCost": 4000, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Ultimate Factory Productivity", "m_description": "Wring every last drop of blood, sweat and tears out of your factory and its workforce. Increase the efficiency of the Factory block by 6%.\n<b>Fastest = Bestest!</b>", "m_position": "(495.80, -15.89, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:56", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4e3fcb288b0318c64856", "m_key": "Commoners::Farmer", "m_name": "<PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "<PERSON>", "m_description": "Put this <b>Farmer</b> in the factory, you're wasting your time. Put this <b><PERSON></b> in your Farm, you're making hay while the sun shines.", "m_position": "(38.00, -490.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:32", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66d1bf168ad9f402fc0a896b", "m_key": "Commoners::<PERSON><PERSON><PERSON>", "m_name": "FOff", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureFOff=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Gesture: F-Off", "m_description": "Do you shake your mother's hand with that dirty mitt? Filthy.", "m_position": "(-440.50, 81.50, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:33", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42bc944847c031e10ad6e", "m_key": "Lords::FoodSalesPricePlus1", "m_name": "FoodSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus2", "m_unlock": "m_foodSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-654.79, 144.95, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:57", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42bca44847c031e10ad78", "m_key": "Lords::FoodSalesPricePlus2", "m_name": "FoodSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus3", "m_unlock": "m_foodSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-801.00, 220.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:57", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42bca44847c031e10ad82", "m_key": "Lords::FoodSalesPricePlus3", "m_name": "FoodSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus4", "m_unlock": "m_foodSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-926.00, 144.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:58", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42bca44847c031e10ad8c", "m_key": "Lords::FoodSalesPricePlus4", "m_name": "FoodSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus5", "m_unlock": "m_foodSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-1058.00, 272.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:58", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42bca44847c031e10ad96", "m_key": "Lords::FoodSalesPricePlus5", "m_name": "FoodSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_foodSalesPriceMarkup= 1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-1222.00, 373.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:58", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1bf17373f5002c5c6e38a", "m_key": "Commoners::<PERSON><PERSON><PERSON>_<PERSON><PERSON>", "m_name": "Gesture_Loose", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureHangLoose=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Gesture: <PERSON><PERSON>", "m_description": "Tell your people to <b><PERSON></b>. Then ask yourself what your people think it means.", "m_position": "(-619.00, 142.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:33", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1bf168ad9f402fc0a8976", "m_key": "Commoners::G<PERSON><PERSON>_Loser", "m_name": "Gesture_Loser", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureLoser=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Gesture: Loser", "m_description": "How to win enemies and influence people's disdain. Get the <b>Loser</b> gesture!", "m_position": "(-508.00, 242.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:33", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1bf17373f5002c5c6e35d", "m_key": "Commoners::G<PERSON><PERSON>_<PERSON>", "m_name": "Gesture_Peace", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGesturePeace=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Gesture: Peace", "m_description": "Wish <b>Peace</b> upon your enemies, perhaps they'll think twice about attacking…", "m_position": "(-396.00, 328.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:34", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b0427c", "m_key": "Commoners::Gest<PERSON>", "m_name": "Gestures", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Gesture_ThumbsUp|Commoners::Gesture_ThumbsDown|Commoners::A<PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Gestures", "m_description": "Follow this path to unlock a myriad of mood enhancing <b>Gestures</b>.", "m_position": "(83.46, 81.75, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:35", "m_activatedAtStart": "True", "m_isLocked": ""}, {"id": "66d1bf168ad9f402fc0a8985", "m_key": "Commoners::Gesture_ThumbsDown", "m_name": "Gesture_ThumbsDown", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Gesture_Loser|Commoners::Gest<PERSON>_<PERSON>ose", "m_unlock": "m_handGestureThumbsDown=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Gesture: Thumbs Down", "m_description": "In Rome, <b>Thumbs Down</b> meant don't kill him. Stupid Romans, getting it wrong like that.", "m_position": "(-261.00, 192.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:34", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1bf17373f5002c5c6e33e", "m_key": "Commoners::Gesture_ThumbsUp", "m_name": "Gesture_ThumbsUp", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON>|Commoners::Gesture_Peace", "m_unlock": "m_handGestureThumbsUp=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Gesture: Thumbs Up", "m_description": "Kill him!", "m_position": "(-100.00, 261.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:34", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1bf16dcba5702dc96cf4a", "m_key": "Commoners::<PERSON><PERSON><PERSON>_<PERSON><PERSON>", "m_name": "Gesture_Wanker", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureWanker=true", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Gesture: <PERSON><PERSON>", "m_description": "This is an R18 gesture and, as such, we shall refrain from describing it.", "m_position": "(-605.50, -33.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:35", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67e137aa834277030d91a058", "m_key": "Lords::GrowCrops1", "m_name": "GrowCrops1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops2", "m_unlock": "m_wheatUpgradeLevel=1", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 1", "m_description": "tbd.", "m_position": "(-139.00, 369.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:58", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e137ebf2ddf9030f3ff9e2", "m_key": "Lords::GrowCrops2", "m_name": "GrowCrops2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops3", "m_unlock": "m_wheatUpgradeLevel=2", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 9, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 2", "m_description": "tbd.", "m_position": "(-51.00, 479.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:00", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e137f0f2ddf9030f3ffa0d", "m_key": "Lords::GrowCrops3", "m_name": "GrowCrops3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops4", "m_unlock": "m_wheatUpgradeLevel=3", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 12, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 3", "m_description": "tbd.", "m_position": "(-124.00, 590.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:01", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e137f4834277030d91a2fc", "m_key": "Lords::GrowCrops4", "m_name": "GrowCrops4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops5", "m_unlock": "m_wheatUpgradeLevel=4", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 15, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 4", "m_description": "tbd.", "m_position": "(-35.00, 694.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:01", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67e137faf2ddf9030f3ffa31", "m_key": "Lords::GrowCrops5", "m_name": "GrowCrops5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_wheatUpgradeLevel=5", "m_giftCard": "", "m_dollarCost": 4000, "m_factionCost": 20, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 5", "m_description": "tbd.", "m_position": "(-136.00, 782.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:01", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66e465e5c8409802f420099d", "m_key": "Royal::<PERSON><PERSON><PERSON>", "m_name": "HammerHard", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 5, "m_icon": "ResearchPaints", "m_title": "<PERSON>", "m_description": "Strikes like fabled pugilist <PERSON>. ", "m_position": "(-356.00, -127.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:27", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66e465e5c8409802f420098e", "m_key": "Royal::<PERSON><PERSON><PERSON>", "m_name": "HammerPoor", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 3, "m_icon": "ResearchPaints", "m_title": "Thumper", "m_description": "Kicks like a mule on fire.", "m_position": "(-463.00, 36.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:27", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec94182bfe1fd6b04258", "m_key": "Commoners::Hand", "m_name": "Hand", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>|Commoners::PickupProduce|Commoners::Pickup<PERSON><PERSON>|Commoners::<PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Hand", "m_description": "Power of the God Hand compels you!", "m_position": "(368.18, 73.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:35", "m_activatedAtStart": "True", "m_isLocked": ""}, {"id": "66700c76da5fdc6d5d0182bc", "m_key": "Mystic::HandPowerDigL1", "m_name": "HandPowerDigL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerDig=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 5, "m_icon": "ResearchHandPowerDig", "m_title": "Dig it baby", "m_description": "Dig out treasures", "m_position": "(137.00, 172.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:06", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66700c76da5fdc6d5d01828f", "m_key": "Mystic::HandPowerFireballL1", "m_name": "HandPowerFireballL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL2", "m_unlock": "m_handPowerFireball=true", "m_giftCard": "", "m_dollarCost": 8000, "m_factionCost": 3, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(39.69, 32.77, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:06", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66700c76da5fdc6d5d018294", "m_key": "Mystic::HandPowerFireballL2", "m_name": "HandPowerFireballL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL3", "m_unlock": "m_handPowerFireballLevel=2", "m_giftCard": "", "m_dollarCost": 11200, "m_factionCost": 6, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 2", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-157.09, 127.39, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:06", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66700c76da5fdc6d5d018299", "m_key": "Mystic::HandPowerFireballL3", "m_name": "HandPowerFireballL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL4", "m_unlock": "m_handPowerFireballLevel=3", "m_giftCard": "", "m_dollarCost": 15000, "m_factionCost": 12, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 3", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-328.39, 224.96, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:07", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6798fb39b5470e02f370b2b7", "m_key": "Mystic::HandPowerFireballL4", "m_name": "HandPowerFireballL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL5", "m_unlock": "m_handPowerFireballLevel=4", "m_giftCard": "", "m_dollarCost": 20000, "m_factionCost": 20, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 4", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-529.39, 330.96, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:07", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6798fb400d763303141dc34b", "m_key": "Mystic::HandPowerFireballL5", "m_name": "HandPowerFireballL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerFireballLevel=5", "m_giftCard": "", "m_dollarCost": 25000, "m_factionCost": 30, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 5", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-721.39, 448.96, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:08", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b042fa", "m_key": "Mystic::HandPowers", "m_name": "HandPowers", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL1|Mystic::<PERSON><PERSON><PERSON><PERSON>|Mystic::HandPowerFireballL1|Mystic::HandPowersFlameThrowerL1|Mystic::HandPowerWaterBlobL1|Mystic::HandPowerWaterSpoutL1|Mystic::HandPowerDigL1|Mystic::HarvestMysticRunes|Mystic::ManaIncrease1|Mystic::UnlockBeacons", "m_unlock": "m_handPowers=true", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Sorcerer's Hand", "m_description": "Open up a new world of possibilities with this magical unlock. Allows the owner to conjure  mystical powers against the armies of the night.", "m_position": "(190.74, -71.71, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:08", "m_activatedAtStart": "True", "m_isLocked": ""}, {"id": "66700c76da5fdc6d5d018280", "m_key": "Mystic::HandPowersFlameThrowerL1", "m_name": "HandPowersFlameThrowerL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL2", "m_unlock": "m_handPowerFlamethrower=true", "m_giftCard": "", "m_dollarCost": 6000, "m_factionCost": 2, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire", "m_description": "TODO: Touch your enemies", "m_position": "(344.00, -182.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:08", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66700c76da5fdc6d5d018285", "m_key": "Mystic::HandPowersFlameThrowerL2", "m_name": "HandPowersFlameThrowerL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL3", "m_unlock": "m_handPowerFlamethrowerLevel=2", "m_giftCard": "", "m_dollarCost": 8400, "m_factionCost": 4, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 2", "m_description": "TODO: Touch More enemies", "m_position": "(517.00, -293.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:08", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66700c76da5fdc6d5d01828a", "m_key": "Mystic::HandPowersFlameThrowerL3", "m_name": "HandPowersFlameThrowerL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL4", "m_unlock": "m_handPowerFlamethrowerLevel=3", "m_giftCard": "", "m_dollarCost": 11760, "m_factionCost": 8, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 3", "m_description": "TODO: Touch Many More enemies", "m_position": "(701.00, -392.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:09", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6798fb54aa5b9d02aa02892c", "m_key": "Mystic::HandPowersFlameThrowerL4", "m_name": "HandPowersFlameThrowerL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL5", "m_unlock": "m_handPowerFlamethrowerLevel=4", "m_giftCard": "", "m_dollarCost": 15000, "m_factionCost": 15, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 4", "m_description": "TODO: Touch Many More enemies", "m_position": "(778.00, -536.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:09", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6798fb5bce1cf20302d4b53d", "m_key": "Mystic::HandPowersFlameThrowerL5", "m_name": "HandPowersFlameThrowerL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerFlamethrowerLevel=5", "m_giftCard": "", "m_dollarCost": 20000, "m_factionCost": 25, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 5", "m_description": "TODO: Touch Many More enemies", "m_position": "(968.00, -668.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:10", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b042ff", "m_key": "Mystic::HandPowersLightningL1", "m_name": "HandPowersLightningL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL2", "m_unlock": "UnlockLightning=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 0, "m_icon": "ResearchHandPowerLightning", "m_title": "<PERSON> Bolts", "m_description": "Fire an earth scorching <b>Bolt of Lightning</b> from your fingertip!", "m_position": "(344.64, 91.90, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:14", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b04304", "m_key": "Mystic::HandPowersLightningL2", "m_name": "HandPowersLightningL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL3", "m_unlock": "m_handPowerLightningLevel=2", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 2, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 2", "m_description": "Frazzle fry friend and foe with the <b>Level 2 Bolt of Lightning</b>.", "m_position": "(560.57, 195.73, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:14", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b04309", "m_key": "Mystic::HandPowersLightningL3", "m_name": "HandPowersLightningL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL4", "m_unlock": "m_handPowerLightningLevel=3", "m_giftCard": "", "m_dollarCost": 8000, "m_factionCost": 10, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 3", "m_description": "Level the playing field with the <b>Level 3 Bolt of Lightning</b>.", "m_position": "(766.03, 292.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:15", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6798fb7b0d763303141dc3dd", "m_key": "Mystic::HandPowersLightningL4", "m_name": "HandPowersLightningL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL5", "m_unlock": "m_handPowerLightningLevel=4", "m_giftCard": "", "m_dollarCost": 15000, "m_factionCost": 15, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 4", "m_description": "Level the playing field with the <b>Level 4 Bolt of Lightning</b>.", "m_position": "(982.03, 419.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:15", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6798fb820d763303141dc3e3", "m_key": "Mystic::HandPowersLightningL5", "m_name": "HandPowersLightningL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerLightningLevel=5", "m_giftCard": "", "m_dollarCost": 25000, "m_factionCost": 25, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 5", "m_description": "The <b>Level 5 Bolt of Lightning</b> makes even the god of thunder quake in his boots.", "m_position": "(1165.03, 298.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:16", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66700c76da5fdc6d5d0182ad", "m_key": "Mystic::HandPowerWaterBlobL1", "m_name": "HandPowerWaterBlobL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL2", "m_unlock": "m_handPowerWaterBlob=true", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 1, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob", "m_description": "Jet stream power wash your enemies into submission!", "m_position": "(564.00, -60.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:16", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66700c76da5fdc6d5d0182b2", "m_key": "Mystic::HandPowerWaterBlobL2", "m_name": "HandPowerWaterBlobL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL3", "m_unlock": "m_handPowerWaterBlobLevel=2", "m_giftCard": "", "m_dollarCost": 3500, "m_factionCost": 5, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 2", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(790.00, -119.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:16", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66700c76da5fdc6d5d0182b7", "m_key": "Mystic::HandPowerWaterBlobL3", "m_name": "HandPowerWaterBlobL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL4", "m_unlock": "m_handPowerWaterBlobLevel=3", "m_giftCard": "", "m_dollarCost": 4900, "m_factionCost": 10, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 3", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(934.00, -237.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:16", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6798fb92b090ee02ca13dba5", "m_key": "Mystic::HandPowerWaterBlobL4", "m_name": "HandPowerWaterBlobL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL5", "m_unlock": "m_handPowerWaterBlobLevel=4", "m_giftCard": "", "m_dollarCost": 7800, "m_factionCost": 15, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 4", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(1192.00, -167.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:17", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6798fb99b090ee02ca13dbbf", "m_key": "Mystic::HandPowerWaterBlobL5", "m_name": "HandPowerWaterBlobL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerWaterBlobLevel=5", "m_giftCard": "", "m_dollarCost": 12000, "m_factionCost": 25, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 5", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(1324.00, -313.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:17", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66700c76da5fdc6d5d01829e", "m_key": "Mystic::HandPowerWaterSpoutL1", "m_name": "HandPowerWaterSpoutL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL2", "m_unlock": "m_handPowerWaterSpout=true", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 1, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(124.00, -246.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:17", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66700c76da5fdc6d5d0182a3", "m_key": "Mystic::HandPowerWaterSpoutL2", "m_name": "HandPowerWaterSpoutL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL3", "m_unlock": "m_handPowerWaterSpoutLevel=2", "m_giftCard": "", "m_dollarCost": 2800, "m_factionCost": 2, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 2", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(31.00, -359.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:18", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66700c76da5fdc6d5d0182a8", "m_key": "Mystic::HandPowerWaterSpoutL3", "m_name": "HandPowerWaterSpoutL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL4", "m_unlock": "m_handPowerWaterSpoutLevel=3", "m_giftCard": "", "m_dollarCost": 3920, "m_factionCost": 4, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 3", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(-124.00, -457.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:18", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6798fbb0b090ee02ca13dc47", "m_key": "Mystic::HandPowerWaterSpoutL4", "m_name": "HandPowerWaterSpoutL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL5", "m_unlock": "m_handPowerWaterSpoutLevel=4", "m_giftCard": "", "m_dollarCost": 5000, "m_factionCost": 10, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 4", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(-277.00, -569.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:18", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6798fbb7aa5b9d02aa028ba2", "m_key": "Mystic::HandPowerWaterSpoutL5", "m_name": "HandPowerWaterSpoutL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerWaterSpoutLevel=5", "m_giftCard": "", "m_dollarCost": 9000, "m_factionCost": 15, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 5", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(-501.00, -485.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:19", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "67c0b1ff39e1c702e42823c9", "m_key": "Mystic::HarvestMysticRunes", "m_name": "HarvestMysticRunes", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_harvestMysticRunes=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 0, "m_icon": "ResearchHandPowerLightning", "m_title": "Harvest Mystic Runes", "m_description": "Creatures have a chance to drop mystic runes", "m_position": "(615.00, 65.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:19", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b0428b", "m_key": "Commoners::Heroes", "m_name": "Heroes", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Heroes<PERSON><PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Heroes", "m_description": "They're rough, they're tough, they'll tear your head off. <b>Heroes</b>!", "m_position": "(214.56, 157.32, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:36", "m_activatedAtStart": "True", "m_isLocked": ""}, {"id": "670cff252fb7f9030fcee0ff", "m_key": "Commoners::HeroesGuild", "m_name": "HeroesGuild", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Train<PERSON><PERSON>es", "m_unlock": "BuildHeroGuild=true", "m_giftCard": "", "m_dollarCost": 50, "m_factionCost": 4, "m_icon": "ResearchPickup", "m_title": "Unlock: Heroes Guild", "m_description": "With this power you will be gifted the ability to build a Hero Guild. The Guild acts as a home for any hero you hire, giving them a base for all of their operations. ", "m_position": "(73.00, 355.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:36", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b042eb", "m_key": "Mystic::<PERSON><PERSON><PERSON><PERSON>", "m_name": "HitZombie", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerHitZombie=true", "m_giftCard": "", "m_dollarCost": 5000, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Zombie Flick", "m_description": "Line them up like dominoes, watch them fall like flies. <b>Flick Zombie</b> power enabled!", "m_position": "(-197.99, 7.29, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:19", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66e465e45d2a8102ba3319ca", "m_key": "Royal::IronMace", "m_name": "IronMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 245, "m_factionCost": 3, "m_icon": "ResearchPaints", "m_title": "<PERSON><PERSON><PERSON> Finder", "m_description": "A precision mace. Will crush a windpipe with ease.", "m_position": "(58.00, -150.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:27", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4e41cb288b0318c648b6", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Journeyman <PERSON>", "m_description": "This one's been round the fields more than once.", "m_position": "(-130.00, -567.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:36", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4a13bd7c8c02d3f35cd0", "m_key": "Commoners::Journeyman<PERSON><PERSON>ber<PERSON>", "m_name": "JourneymanLumberjack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Master<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Journeyman <PERSON>", "m_description": "Three fellas in one.", "m_position": "(620.00, -471.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:37", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4e40cb288b0318c6488d", "m_key": "Commoners::Journeyman<PERSON>iller", "m_name": "JourneymanMiller", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Journey<PERSON>", "m_description": "You'd be mad to sack this one off. A hardened miller with more experience at the grind stone than any other.", "m_position": "(-259.00, -453.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:37", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66e465e372ce6202c016cf51", "m_key": "Royal::LeadMace", "m_name": "LeadMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::IronMace|Royal::SteelMace", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 180, "m_factionCost": 3, "m_icon": "ResearchStickers", "m_title": "Lead Mace", "m_description": "Heavy in the hands, easy to drop. Deadly when it connects.", "m_position": "(218.00, -20.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:28", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66e465e4c8409802f4200981", "m_key": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_name": "LegendaryMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 600, "m_factionCost": 8, "m_icon": "ResearchStickers", "m_title": "The Face of Doom", "m_description": "A mace that needs no introduction because once you meet it, you're dead.", "m_position": "(252.00, -322.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:28", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4a13cb288b0318c557df", "m_key": "Commoners::<PERSON><PERSON><PERSON>", "m_name": "Lumber<PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Journeyman<PERSON><PERSON>ber<PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Lumber<PERSON>", "m_description": "tbd.", "m_position": "(499.00, -378.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:38", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6808b1fc7a122102c939ed10", "m_key": "Mystic::ManaIncrease1", "m_name": "ManaIncrease1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease2", "m_unlock": "m_manaStorageMultiplier= 1.1", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchPickup", "m_title": "Mana Increase 1", "m_description": "Increase your Mana Pool by 10%.", "m_position": "(-94.00, -107.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:19", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b22b61196602d27d0356", "m_key": "Mystic::ManaIncrease10", "m_name": "ManaIncrease10", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_manaStorageMultiplier= 3", "m_giftCard": "", "m_dollarCost": 15000, "m_factionCost": 75, "m_icon": "ResearchPickup", "m_title": "Mana Increase 10", "m_description": "Increase your Mana Pool by 200%.", "m_position": "(-1097.00, 16.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:20", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b200eca77c02fd1f4d83", "m_key": "Mystic::ManaIncrease2", "m_name": "ManaIncrease2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease3", "m_unlock": "m_manaStorageMultiplier= 1.25", "m_giftCard": "", "m_dollarCost": 350, "m_factionCost": 4, "m_icon": "ResearchPickup", "m_title": "Mana Increase 2", "m_description": "Increase your Mana Pool by 25%.", "m_position": "(-302.00, -186.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:20", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b203eca77c02fd1f4d92", "m_key": "Mystic::ManaIncrease3", "m_name": "ManaIncrease3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease4", "m_unlock": "m_manaStorageMultiplier= 1.5", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 8, "m_icon": "ResearchPickup", "m_title": "Mana Increase 3", "m_description": "Increase your Mana Pool by 50%.", "m_position": "(-413.00, -70.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:20", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b20a7a122102c939ed72", "m_key": "Mystic::ManaIncrease4", "m_name": "ManaIncrease4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON>aIncrease5", "m_unlock": "m_manaStorageMultiplier= 1.75", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 12, "m_icon": "ResearchPickup", "m_title": "Mana Increase 4", "m_description": "Increase your Mana Pool by 75%.", "m_position": "(-548.00, -244.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:21", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b20eeca77c02fd1f4d98", "m_key": "Mystic::<PERSON>aIncrease5", "m_name": "ManaIncrease5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>6", "m_unlock": "m_manaStorageMultiplier= 2", "m_giftCard": "", "m_dollarCost": 1250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Mana Increase 5", "m_description": "Increase your Mana Pool by 100%.", "m_position": "(-725.00, -159.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:21", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b21361196602d27d02c0", "m_key": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>6", "m_name": "ManaIncrease6", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON>Increase7", "m_unlock": "m_manaStorageMultiplier= 2.1", "m_giftCard": "", "m_dollarCost": 1800, "m_factionCost": 20, "m_icon": "ResearchPickup", "m_title": "Mana Increase 6", "m_description": "Increase your Mana Pool by 110%.", "m_position": "(-744.00, -318.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:21", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b217d86acd02f461198e", "m_key": "Mystic::<PERSON><PERSON>Increase7", "m_name": "ManaIncrease7", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON><PERSON><PERSON><PERSON>8", "m_unlock": "m_manaStorageMultiplier= 2.25", "m_giftCard": "", "m_dollarCost": 2600, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Mana Increase 7", "m_description": "Increase your Mana Pool by 125%.", "m_position": "(-938.00, -83.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:22", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b21bd86acd02f4611994", "m_key": "Mystic::<PERSON><PERSON><PERSON><PERSON><PERSON>8", "m_name": "ManaIncrease8", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>9", "m_unlock": "m_manaStorageMultiplier= 2.5", "m_giftCard": "", "m_dollarCost": 5000, "m_factionCost": 35, "m_icon": "ResearchPickup", "m_title": "Mana Increase 8", "m_description": "Increase your Mana Pool by 150%.", "m_position": "(-1004.00, -271.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:22", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6808b21f61196602d27d0310", "m_key": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>9", "m_name": "ManaIncrease9", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease10", "m_unlock": "m_manaStorageMultiplier= 2.75", "m_giftCard": "", "m_dollarCost": 8000, "m_factionCost": 50, "m_icon": "ResearchPickup", "m_title": "Mana Increase 9", "m_description": "Increase your Mana Pool by 175%.", "m_position": "(-1172.00, -146.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:23", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "670e4e40cb288b0318c64878", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master Chefs", "m_description": "tbd.", "m_position": "(-629.00, -310.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:38", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4e42cb288b0318c648cb", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "Master<PERSON>armer", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master Farmer", "m_description": "tbd.", "m_position": "(-377.00, -618.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:39", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4a144e9c4302c41e7e47", "m_key": "Commoners::Master<PERSON><PERSON><PERSON><PERSON>", "m_name": "MasterLumberjack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master <PERSON><PERSON><PERSON>", "m_description": "tbd.", "m_position": "(798.00, -376.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:40", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4e41cb288b0318c6489e", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "Master<PERSON><PERSON><PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master <PERSON>", "m_description": "tbd.", "m_position": "(-518.00, -517.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:40", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66e465e272ce6202c016cf3b", "m_key": "Royal::MediocreAxe", "m_name": "MediocreAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::CastleGuardAxe", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 220, "m_factionCost": 3, "m_icon": "ResearchPaints", "m_title": " <PERSON><PERSON><PERSON>'s Hatchet", "m_description": "Will bite through leather armour like a hammer through lard.", "m_position": "(509.00, 95.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:28", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4e3e8e639602c429c08d", "m_key": "Commoners::<PERSON>", "m_name": "<PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Journeyman<PERSON>iller", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "<PERSON>", "m_description": "tbd.", "m_position": "(10.00, -386.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:40", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "66d1ae1801512b02d9a475be", "m_key": "Commoners::PickupDecorations", "m_name": "PickupDecorations", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 13, "m_icon": "ResearchPickup", "m_title": "Pickup Decorations", "m_description": "tbd.", "m_position": "(975.92, 8.92, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:41", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b04277", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupLogs", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::PickupDecorations|Commoners::PickupTree", "m_unlock": "m_handPowerPickupLogs=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Pickup Logs", "m_description": "Wield freshly felled trees like they were toothpicks with the power to <b>Pickup Logs</b>!", "m_position": "(735.57, 97.64, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:42", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b04281", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupOre", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerPickupOre=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Pickup Metal Ore", "m_description": "Straight from the seam the ore it comes. Don't bother your bods, put your hand where the hole is and <b>Pickup <PERSON>e</b> straight from the Metal Mine!", "m_position": "(616.96, -83.71, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:42", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b04272", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupProduce", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerPickupProduce=true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Pickup Produce", "m_description": "Enjoy the pleasure of grabbing and dragging from the Mill using the <b>PickUp Produce</b> hand power!", "m_position": "(546.49, -173.20, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:42", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e504ccb288b0318c67fee", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupTree", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Pickup Tree", "m_description": "tbd.", "m_position": "(1263.00, 134.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:43", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670cff242fb8e202f7cc4fe1", "m_key": "Commoners::PiesAndTartsPack", "m_name": "PiesAndTartsPack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "MA_RL_PiesAndTartsPack", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Pies & Tarts Pack", "m_description": "tbd.", "m_position": "(-452.00, -210.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:43", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66e465e272ce6202c016cf30", "m_key": "Royal::PoorAxe", "m_name": "PoorAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::MediocreAxe", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 150, "m_factionCost": 2, "m_icon": "ResearchStickers", "m_title": "<PERSON><PERSON>'s Axe", "m_description": "A skirmisher's weapon. Useful against bandits.", "m_position": "(287.00, 203.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:29", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b0426d", "m_key": "Commoners::Products", "m_name": "Products", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>|Commoners::PiesAndTartsPack|Commoners::Slices<PERSON><PERSON>|Commoners::Chefs|Commoners::<PERSON>|Commoners::<PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Products", "m_description": "Add a wealth of valuable product parts to your design drawers.", "m_position": "(116.95, -118.26, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:43", "m_activatedAtStart": "True", "m_isLocked": ""}, {"id": "670cff24bd7d6702b497ab2c", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "SlicesPack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "MA_RL_SlicesPack", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Slices Pack", "m_description": "tbd.", "m_position": "(-219.00, -238.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:43", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66e465e45d2a8102ba3319d7", "m_key": "Royal::SteelMace", "m_name": "SteelMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 350, "m_factionCost": 5, "m_icon": "ResearchPaints", "m_title": "Crack of Thunder", "m_description": "You'll hear it before you feel it. And by then it's all over.", "m_position": "(410.00, -174.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:29", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "670e4c00bd7c8c02d3f3a33a", "m_key": "Commoners::Train<PERSON><PERSON>es", "m_name": "TrainHeroes", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Train Heroes", "m_description": "Grants the Hero Guild Training Block. With this block on your guild, you can train your heroes up faster than they would during normal play.", "m_position": "(-290.00, 562.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:44", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d0a10cb484cb0320be98a0", "m_key": "Mystic::<PERSON><PERSON>Bea<PERSON><PERSON>", "m_name": "UnlockBeacons", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "UnlockBeacons=true", "m_giftCard": "", "m_dollarCost": 300, "m_factionCost": 4, "m_icon": "ResearchHandPowerLightning", "m_title": "Beacons", "m_description": "The power of the hand expands", "m_position": "(308.00, -312.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:26", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "673c748fbb0eb002c7e4e935", "m_key": "Commoners::<PERSON>lockBuildWall", "m_name": "UnlockBuildWall", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_designWalls = true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Unlock Wall Building", "m_description": "Unlock the ability to place and build walls. Walls are an excellent form of defence against the horrors of the night, use them to block and slow down your enemies, buying time for your townsfolk and heroes to prepare.", "m_position": "(704.58, 2.63, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:05:44", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b04318", "m_key": "Royal::<PERSON><PERSON>", "m_name": "Unlock Paints", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON>|Royal::<PERSON><PERSON><PERSON>", "m_unlock": "m_designPaints=true", "m_giftCard": "", "m_dollarCost": 900, "m_factionCost": 1, "m_icon": "ResearchPaints", "m_title": "Unlock Paints", "m_description": "Give all of your designs a unique streak with <b>Paints</b>!", "m_position": "(-532.83, 282.41, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:29", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b04313", "m_key": "Royal::<PERSON><PERSON>", "m_name": "<PERSON><PERSON> Pat<PERSON>s", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON>|Royal::PoorA<PERSON>", "m_unlock": "m_designPatterns=true", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 2, "m_icon": "ResearchPatterns", "m_title": "<PERSON><PERSON> Pat<PERSON>s", "m_description": "Bring some new looks to your patented designs with <b>Patterns</b>!", "m_position": "(-152.87, 218.22, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:30", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b0431d", "m_key": "Royal::<PERSON><PERSON>", "m_name": "Unlock Stickers", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::LeadMace", "m_unlock": "m_designStickers=true", "m_giftCard": "", "m_dollarCost": 300, "m_factionCost": 2, "m_icon": "ResearchStickers", "m_title": "Unlock Runes", "m_description": "Mark your manufactured goods with the awesome power of magic. <b>Runes</b>", "m_position": "(-64.20, 51.71, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:30", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "66d1c39c2863e402fe7e359b", "m_key": "Lords::Weaponary_Parts1", "m_name": "Weaponary_Parts1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 1200, "m_factionCost": 1, "m_icon": "ResearchGetSmelter", "m_title": "Weaponsmith Aesthetics Pack 1", "m_description": "A pack containing decoretive parts for your weaponsmith.", "m_position": "(623.50, -131.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:01", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610be7", "m_key": "Lords::WeaponsSalesPricePlus1", "m_name": "WeaponsSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus2", "m_unlock": "m_weaponsSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-453.79, 338.95, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:02", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610bea", "m_key": "Lords::WeaponsSalesPricePlus2", "m_name": "WeaponsSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus3", "m_unlock": "m_weaponsSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-449.00, 488.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:04", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610bed", "m_key": "Lords::WeaponsSalesPricePlus3", "m_name": "WeaponsSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus4", "m_unlock": "m_weaponsSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-443.00, 629.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:04", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2c6a278802e3610bf0", "m_key": "Lords::WeaponsSalesPricePlus4", "m_name": "WeaponsSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus5", "m_unlock": "m_weaponsSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-547.00, 733.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:04", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "67d42f2d6a278802e3610bf3", "m_key": "Lords::WeaponsSalesPricePlus5", "m_name": "WeaponsSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_weaponsSalesPriceMarkup= 1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-710.00, 813.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:04", "m_activatedAtStart": "False", "m_isLocked": "False"}, {"id": "6666ec95182bfe1fd6b042b3", "m_key": "Lords::WorkerStationL1", "m_name": "WorkerStationL1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::BedroomL1|Lords::WorkerStationL2", "m_unlock": "m_blockWorkerStationLevel=1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 1, "m_icon": "ResearchBedroomCapacity", "m_title": "Boosted Worker Station", "m_description": "Increase production with a boosted Worker Station, increase its Capacity By 1", "m_position": "(-131.06, 107.11, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:05", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b042b8", "m_key": "Lords::WorkerStationL2", "m_name": "WorkerStationL2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WorkerStationL3", "m_unlock": "m_blockWorkerStationLevel=2", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 2, "m_icon": "ResearchBedroomCapacity", "m_title": "Bolstered Worker Station", "m_description": "Bolster production at your Worker Station, increase its Capacity By 2", "m_position": "(227.67, 111.01, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:05", "m_activatedAtStart": "False", "m_isLocked": "True"}, {"id": "6666ec95182bfe1fd6b042bd", "m_key": "Lords::WorkerStationL3", "m_name": "WorkerStationL3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_blockWorkerStationLevel=3", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 4, "m_icon": "ResearchBedroomCapacity", "m_title": "Bettered Worker Station Level 3", "m_description": "Batter the Worker Station productivity reports, increase the capacity by 3", "m_position": "(571.22, 114.30, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-05-28 13:06:05", "m_activatedAtStart": "False", "m_isLocked": "True"}]