This read me explains how the flow system works.

Flows are held in the Resources/Flows folder. 
Each flow is a .MOA file. The flow file is a text file that contains a series of commands that are executed in order. 
Each command is on a new line. The commands are executed in order from top to bottom.
Jetbrains Rider is a good editor for editing these files, it has a plugin that will format the file to make it easier to read.
In the Flows folder the flows are seperated into chapters. 

In the 22Cans/Debug Windows/Show Parser Flows window you can see the flows that are currently loaded and active.

To Execute a flow call MAPareserManager.Me.ExecuteFlow(<Chapter?>/<fileName>, <StartAtLineNumber>) from a script.
To view all the commands posible to call. Launch  22Cans/Debug Windows/Show Function Window.
